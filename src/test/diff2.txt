 pom.xml                                            |   86 +-
 .../java/org/swallow/control/dao/ArchiveDAO.java   |   20 +
 .../swallow/control/dao/MultipleMvtUpdatesDAO.java |   39 +
 .../control/dao/hibernate/ArchiveDAOHibernate.java |   26 +
 .../dao/hibernate/ArchiveDAOHibernatePCM.java      |   26 +
 .../dao/hibernate/ChangePasswordDAOHibernate.java  |   66 +-
 .../hibernate/MultipleMvtUpdatesDAOHibernate.java  |  419 +++++
 .../org/swallow/control/model/ProcessStatus.java   |   68 +
 .../java/org/swallow/control/model/Role.hbm.xml    |    3 +
 src/main/java/org/swallow/control/model/Role.java  |    9 +
 .../swallow/control/service/ArchiveManager.java    |   20 +
 .../control/service/BackgroundTaskManager.java     |   44 +
 .../control/service/MultipleMvtUpdatesManager.java |   45 +
 .../control/service/impl/ArchiveManagerImpl.java   |   40 +
 .../impl/MultipleMvtUpdatesManagerImpl.java        |  208 +++
 .../control/web/ConnectionPoolControlAction.java   |    5 +-
 .../control/web/MultipleMovementUpdatesAction.java | 1802 ++++++++++++++++++++
 .../java/org/swallow/control/web/RoleAction.java   |    6 +-
 .../MultipleMovementExcelReportGenerator.java      |  367 ++++
 .../web/AcctSpecificSweepFormatAction.java         |    1 -
 .../maintenance/web/CurrencyExchangeAction.java    |   23 +-
 src/main/java/org/swallow/mfa/RegisterMFA.java     |   36 +-
 src/main/java/org/swallow/mfa/SamlService.java     |  190 ++-
 .../reports/dao/hibernate/ReportsDAOHibernate.java |  639 ++++---
 .../org/swallow/reports/web/ReportsAction.java     |  282 ++-
 .../swallow/util/ApplicationContextListner.java    |    2 +-
 src/main/java/org/swallow/util/SessionManager.java |   13 +-
 src/main/java/org/swallow/util/SwtConstants.java   |   60 +-
 src/main/java/org/swallow/util/SwtUtil.java        |  109 +-
 src/main/java/org/swallow/util/XSSUtil.java        |    4 +-
 src/main/java/org/swallow/web/AngularFilter.java   |  133 +-
 src/main/java/org/swallow/web/LogonAction.java     |   13 +-
 src/main/java/org/swallow/web/XSSFilter.java       |   10 +-
 .../dao/hibernate/MatchDisplayDAOHibernate.java    |   25 +-
 .../work/dao/hibernate/MovementDAOHibernate.java   |   38 +-
 .../work/dao/hibernate/NotesDAOHibernate.java      |    3 +-
 .../dao/hibernate/SweepDetailDAOHibernate.java     |    5 +-
 .../dao/hibernate/SweepSearchDAOHibernate.java     |   42 +-
 src/main/java/org/swallow/work/model/Movement.java |   20 +
 .../org/swallow/work/model/MovementExt.hbm.xml     |    3 +-
 .../java/org/swallow/work/model/MovementExt.java   |   17 +
 .../java/org/swallow/work/web/NotesAction.java     |   16 +-
 .../swallow/work/web/OutStandingMvmtAction.java    |  126 +-
 .../org/swallow/work/web/SweepDetailAction.java    |    6 +-
 .../org/swallow/work/web/SweepSearchAction.java    |   26 +-
 src/main/resources/default_predict.properties      |    7 +-
 src/main/resources/dictionary_en.properties        |  107 +-
 src/main/webapp/jsp/control/multipleMvtUpdates.jsp |  103 ++
 src/main/webapp/jsp/control/rolemaintenance.jsp    |    6 +-
 src/main/webapp/jsp/control/rolemaintenanceadd.jsp |   53 +-
 .../maintenance/currencyexchangemaintenance.jsp    |   62 +-
 .../webapp/jsp/maintenance/entitymaintenance.jsp   |   64 +-
 src/main/webapp/jsp/reports/ilmReport.jsp          |   24 +-
 .../webapp/jsp/work/movementsummarydisplayflex.jsp |   17 +-
 54 files changed, 4731 insertions(+), 853 deletions(-)

diff --git a/pom.xml b/pom.xml
index c3b00307..9d5621d1 100644
--- a/pom.xml
+++ b/pom.xml
@@ -41,13 +41,17 @@


 	</properties>
-<repositories>
-	<repository>
-		<id>repository-swallowtech</id>
-		<url>https://predict:<EMAIL>:8181/repository/swallowtech/</url>
-	</repository>
-
-</repositories>
+	<repositories>
+		<repository>
+			<id>repository-swallowtech</id>
+			<url>https://predict:<EMAIL>:8181/repository/swallowtech/</url>
+		</repository>
+		<repository>
+			<id>djmaven2</id>
+			<url>http://www.fdvs.com.ar/djmaven2</url>
+			<name>DynamicJasper public Repository</name>
+		</repository>
+	</repositories>

 	<dependencyManagement>
 		<dependencies>
@@ -151,6 +155,16 @@
 				<artifactId>log4j-api</artifactId>
 				<version>${log4j.version}</version>
 			</dependency>
+			<dependency>
+				<groupId>org.apache.logging.log4j</groupId>
+				<artifactId>log4j-core</artifactId>
+				<version>2.17.2</version>
+			</dependency>
+			<dependency>
+				<groupId>org.apache.logging.log4j</groupId>
+				<artifactId>log4j-jcl</artifactId>
+				<version>2.17.2</version>
+			</dependency>
 			<dependency>
 				<groupId>org.apache.logging.log4j</groupId>
 				<artifactId>log4j-to-slf4j</artifactId>
@@ -159,19 +173,19 @@

 			<!--Spring Security-->
 			<dependency>
-			    <groupId>org.springframework.security</groupId>
-			    <artifactId>spring-security-core</artifactId>
-			    <version>${spring.security.version}</version>
+				<groupId>org.springframework.security</groupId>
+				<artifactId>spring-security-core</artifactId>
+				<version>${spring.security.version}</version>
 			</dependency>
 			<dependency>
-			    <groupId>org.springframework.security</groupId>
-			    <artifactId>spring-security-config</artifactId>
-			    <version>${spring.security.version}</version>
+				<groupId>org.springframework.security</groupId>
+				<artifactId>spring-security-config</artifactId>
+				<version>${spring.security.version}</version>
 			</dependency>
 			<dependency>
-			    <groupId>org.springframework.security</groupId>
-			    <artifactId>spring-security-web</artifactId>
-			    <version>${spring.security.version}</version>
+				<groupId>org.springframework.security</groupId>
+				<artifactId>spring-security-web</artifactId>
+				<version>${spring.security.version}</version>
 			</dependency>

 		</dependencies>
@@ -209,9 +223,9 @@
 		</dependency>

 		<dependency>
-	        <groupId>org.springframework.boot</groupId>
-	        <artifactId>spring-boot-starter-security</artifactId>
-	    </dependency>
+			<groupId>org.springframework.boot</groupId>
+			<artifactId>spring-boot-starter-security</artifactId>
+		</dependency>

 		<!--<dependency>
             <groupId>org.springframework.boot</groupId>
@@ -360,6 +374,18 @@
 			<version>1.1.3</version>
 		</dependency>

+		<!-- Log4j dependencies -->
+		<dependency>
+			<groupId>org.apache.logging.log4j</groupId>
+			<artifactId>log4j-core</artifactId>
+			<version>2.17.2</version>
+		</dependency>
+		<dependency>
+			<groupId>org.apache.logging.log4j</groupId>
+			<artifactId>log4j-jcl</artifactId>
+			<version>2.17.2</version>
+		</dependency>
+

 		<dependency>
 			<groupId>net.minidev</groupId>
@@ -495,13 +521,13 @@
     <version>2.2.1.RELEASE</version>
 </dependency> -->

-		<!--
+		<!--
 <dependency>
     <groupId>org.springframework.cloud</groupId>
     <artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
     <version>2.2.1.RELEASE</version>
 </dependency>
-
+
 <dependency>
     <groupId>commons-lang</groupId>
     <artifactId>commons-lang</artifactId>
@@ -738,14 +764,14 @@
 		<dependency>
 			<groupId>org.apache.poi</groupId>
 			<artifactId>poi</artifactId>
-			<version>5.3.0</version>
+			<version>5.1.0</version>
 		</dependency>

 		<!-- Apache POI for Excel (XSSF and HSSF) -->
 		<dependency>
 			<groupId>org.apache.poi</groupId>
 			<artifactId>poi-ooxml</artifactId>
-			<version>5.3.0</version>
+			<version>5.1.0</version>
 		</dependency>


@@ -763,6 +789,18 @@
 			<version>1.7</version>
 		</dependency>

+		<dependency>
+			<groupId>org.apache.commons</groupId>
+			<artifactId>commons-jexl</artifactId>
+			<version>2.1.1</version>
+		</dependency>
+
+		<dependency>
+			<groupId>net.sf.jagg</groupId>
+			<artifactId>jagg-core</artifactId>
+			<version>0.9.0</version>
+		</dependency>
+
 	</dependencies>


@@ -938,4 +976,4 @@
 	</build>


-</project>
\ No newline at end of file
+</project>
diff --git a/src/main/java/org/swallow/control/dao/ArchiveDAO.java b/src/main/java/org/swallow/control/dao/ArchiveDAO.java
index 20b2e91e..ad5ed6f1 100644
--- a/src/main/java/org/swallow/control/dao/ArchiveDAO.java
+++ b/src/main/java/org/swallow/control/dao/ArchiveDAO.java
@@ -94,4 +94,24 @@ public interface ArchiveDAO extends DAO {

 	public Archive getCurrentArchiveDb(String hostId) throws SwtException ;

+	/**
+	 * Get current archive database with optional archiveId parameter
+	 * If archiveId is provided, get the archive record for that archiveId without defaultDb condition
+	 * If archiveId is not provided, get the default archive database
+	 * @param hostId
+	 * @param archiveId
+	 * @return Archive
+	 * @throws SwtException
+	 */
+	public Archive getCurrentArchiveDb(String hostId, String archiveId) throws SwtException ;
+
+	/**
+	 * Get archive record by archiveId
+	 * @param hostId
+	 * @param archiveId
+	 * @return Archive
+	 * @throws SwtException
+	 */
+	public Archive getArchiveById(String hostId, String archiveId) throws SwtException;
+
 }
diff --git a/src/main/java/org/swallow/control/dao/MultipleMvtUpdatesDAO.java b/src/main/java/org/swallow/control/dao/MultipleMvtUpdatesDAO.java
new file mode 100644
index ********..baa8a009
--- /dev/null
+++ b/src/main/java/org/swallow/control/dao/MultipleMvtUpdatesDAO.java
@@ -0,0 +1,39 @@
+/*
+ * @(#)MovementRecoveryDAO.java 03/05/06
+ *
+ * Copyright (c) 2006-2012 SwallowTech, Inc.
+ * 14 Lion Yard ,Tremadoc Road,  London  UK
+ * All Rights Reserved.
+ *
+ * This software is the confidential and proprietary information of
+ * SwallowTech Inc. ("Confidential Information"). You shall not
+ * disclose such Confidential Information and shall use it only in
+ * accordance with the terms of the license agreement you entered into
+ * with SwallowTech.
+ */
+
+package org.swallow.control.dao;
+
+import org.swallow.control.model.ProcessStatus;
+import org.swallow.dao.DAO;
+import org.swallow.exception.SwtException;
+import org.swallow.work.model.Movement;
+
+import java.util.Collection;
+import java.util.List;
+
+public interface MultipleMvtUpdatesDAO extends DAO {
+
+    public boolean checkIfMvtExists(String hostId, String entityId, String movementId) throws SwtException;
+
+    public Collection<Movement> getMovementList(List<Long> movementIds, String hostId, String entityId)throws SwtException;
+
+    public boolean checkIfEntityExists(String hostId, String entityId) throws SwtException;
+    public Collection<Movement> getMovementList(List<Long> movementIds) throws SwtException;
+    public boolean getRoleAccessDetails(String hostId, String roleId) throws SwtException;
+
+    void executeMainProcedure(String listMovements, String action, String jsonValues, String noteText, String userId, String seq) throws SwtException;
+    int getProcessStatus(String seq) throws SwtException;
+    void cleanTempProcess(String seq) throws SwtException;
+    ProcessStatus getProcessDetails(String seq, String includeMovements) throws SwtException;
+}
diff --git a/src/main/java/org/swallow/control/dao/hibernate/ArchiveDAOHibernate.java b/src/main/java/org/swallow/control/dao/hibernate/ArchiveDAOHibernate.java
index f22cb856..c4989b49 100644
--- a/src/main/java/org/swallow/control/dao/hibernate/ArchiveDAOHibernate.java
+++ b/src/main/java/org/swallow/control/dao/hibernate/ArchiveDAOHibernate.java
@@ -353,4 +353,30 @@ public class ArchiveDAOHibernate extends HibernateDaoSupport implements
 			else
 				return ((Archive)dbColl.get(0) );
 	}
+
+	@Override
+	public Archive getCurrentArchiveDb(String hostId, String archiveId) throws SwtException {
+		log.debug(this.getClass().getName() + " - [ getCurrentArchiveDb with archiveId ]- Entry");
+
+		if (SwtUtil.isEmptyOrNull(archiveId)) {
+			// If archiveId is not provided, use the default behavior
+			return getCurrentArchiveDb(hostId);
+		} else {
+			// If archiveId is provided, get the archive record without defaultDb condition
+			return getArchiveById(hostId, archiveId);
+		}
+	}
+
+	@Override
+	public Archive getArchiveById(String hostId, String archiveId) throws SwtException {
+		log.debug(this.getClass().getName() + " - [ getArchiveById ]- Entry");
+		String HQL_DBDETAILS = "from Archive a where a.id.hostId = ?0 and a.id.archiveId = ?1";
+		List dbColl = getHibernateTemplate().find(HQL_DBDETAILS,
+				new Object[] { hostId, archiveId });
+		log.debug(this.getClass().getName() + " - [ getArchiveById ]- Exit");
+		if(dbColl.size()==0)
+			return null;
+		else
+			return ((Archive)dbColl.get(0) );
+	}
 }
\ No newline at end of file
diff --git a/src/main/java/org/swallow/control/dao/hibernate/ArchiveDAOHibernatePCM.java b/src/main/java/org/swallow/control/dao/hibernate/ArchiveDAOHibernatePCM.java
index 77f51229..5334861f 100644
--- a/src/main/java/org/swallow/control/dao/hibernate/ArchiveDAOHibernatePCM.java
+++ b/src/main/java/org/swallow/control/dao/hibernate/ArchiveDAOHibernatePCM.java
@@ -337,4 +337,30 @@ public class ArchiveDAOHibernatePCM extends HibernateDaoSupport implements
 		else
 			return ((Archive)dbColl.get(0) );
 	}
+
+	@Override
+	public Archive getCurrentArchiveDb(String hostId, String archiveId) throws SwtException {
+		log.debug(this.getClass().getName() + " - [ getCurrentArchiveDb with archiveId ]- Entry");
+
+		if (SwtUtil.isEmptyOrNull(archiveId)) {
+			// If archiveId is not provided, use the default behavior
+			return getCurrentArchiveDb(hostId);
+		} else {
+			// If archiveId is provided, get the archive record without defaultDb condition
+			return getArchiveById(hostId, archiveId);
+		}
+	}
+
+	@Override
+	public Archive getArchiveById(String hostId, String archiveId) throws SwtException {
+		log.debug(this.getClass().getName() + " - [ getArchiveById ]- Entry");
+		String HQL_DBDETAILS = "from Archive a where a.id.hostId = ?0 and a.id.archiveId = ?1";
+		List dbColl = getHibernateTemplate().find(HQL_DBDETAILS,
+				new Object[] { hostId, archiveId });
+		log.debug(this.getClass().getName() + " - [ getArchiveById ]- Exit");
+		if(dbColl.size()==0)
+			return null;
+		else
+			return ((Archive)dbColl.get(0) );
+	}
 }
\ No newline at end of file
diff --git a/src/main/java/org/swallow/control/dao/hibernate/ChangePasswordDAOHibernate.java b/src/main/java/org/swallow/control/dao/hibernate/ChangePasswordDAOHibernate.java
index 90cf24e1..c848f6ec 100644
--- a/src/main/java/org/swallow/control/dao/hibernate/ChangePasswordDAOHibernate.java
+++ b/src/main/java/org/swallow/control/dao/hibernate/ChangePasswordDAOHibernate.java
@@ -210,47 +210,41 @@ public class ChangePasswordDAOHibernate extends HibernateDaoSupport implements C
 		Session session = null;
 		Transaction tx = null;
 		SwtInterceptor interceptor = null;
-		List pwdList = getHibernateTemplate().find("FROM PasswordHistory p where p.id.hostId=?0 " +
-				"and p.id.userId=?1 and p.id.seqNo=0"
-       				,new Object[]{pwdhis.getId().getHostId(),pwdhis.getId().getUserId()});
-                /*Reference to :Raghav's Chat */
-                /*code modified for avoid unique constraint error on s_password_history table */
-                /*Start Code:Modified by Balaji on 01-July-2008 */
-
-		if (pwdList != null && pwdList.size() > 0){
-			try {
-				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
-				session = getHibernateTemplate()
-						.getSessionFactory()
-						.withOptions().interceptor(interceptor).openSession();
-				tx = session.beginTransaction();
-				session.delete((PasswordHistory)pwdList.iterator().next());
-				tx.commit();
-				session.save(pwdhis);
-				tx.commit();
-				session.close();
-			} catch (HibernateException e) {
-				log.error(this.getClass().getName() + "- [savePasswordHistory] - Exception " + e.getMessage());
+
+		List pwdList = getHibernateTemplate().find(
+				"FROM PasswordHistory p where p.id.hostId=?0 and p.id.userId=?1 and p.id.seqNo=0",
+				new Object[]{pwdhis.getId().getHostId(), pwdhis.getId().getUserId()}
+		);
+
+		try {
+			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
+			session = getHibernateTemplate()
+					.getSessionFactory()
+					.withOptions()
+					.interceptor(interceptor)
+					.openSession();
+			tx = session.beginTransaction();
+
+			if (pwdList != null && !pwdList.isEmpty()) {
+				session.delete(pwdList.iterator().next());
 			}
-		}else{
-			try {
-				interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
-				session = getHibernateTemplate()
-						.getSessionFactory()
-						.withOptions().interceptor(interceptor).openSession();
-				tx = session.beginTransaction();
-				session.save(pwdhis);
-				tx.commit();
+
+			session.save(pwdhis);
+
+			tx.commit();
+		} catch (HibernateException e) {
+			if (tx != null) tx.rollback();
+			log.error(this.getClass().getName() + "- [savePasswordHistory] - Exception " + e.getMessage(), e);
+		} finally {
+			if (session != null && session.isOpen()) {
 				session.close();
-			} catch (HibernateException e) {
-				log.error(this.getClass().getName() + "- [savePasswordHistory] - Exception " + e.getMessage());
 			}
 		}
-                  /*End Code:Modified by Balaji on 01-July-2008  */
-
+
 		log.debug("Exiting 'savePasswordHistory' method");
 	}
-
+
+
 	/* to remove unique constraint */

 	/*public void savePasswordHistory(PasswordHistory pwdhis) throws DataAccessException {
@@ -287,4 +281,4 @@ public class ChangePasswordDAOHibernate extends HibernateDaoSupport implements C
 		}
 	}

-}
\ No newline at end of file
+}
diff --git a/src/main/java/org/swallow/control/dao/hibernate/MultipleMvtUpdatesDAOHibernate.java b/src/main/java/org/swallow/control/dao/hibernate/MultipleMvtUpdatesDAOHibernate.java
new file mode 100644
index ********..c91f57d4
--- /dev/null
+++ b/src/main/java/org/swallow/control/dao/hibernate/MultipleMvtUpdatesDAOHibernate.java
@@ -0,0 +1,419 @@
+/*
+ * @(#)MultipleMvtUpdateDAOHibernate.java 1.0 03/05/06
+ *
+ * Copyright (c) 2006-2012 SwallowTech, Inc.
+ * 14 Lion Yard ,Tremadoc Road,  London  UK
+ * All Rights Reserved.
+ *
+ * This software is the confidential and proprietary information of
+ * SwallowTech Inc. ("Confidential Information"). You shall not
+ * disclose such Confidential Information and shall use it only in
+ * accordance with the terms of the license agreement you entered into
+ * with SwallowTech.
+ */
+
+package org.swallow.control.dao.hibernate;
+
+import java.io.BufferedReader;
+import java.io.IOException;
+import java.io.Reader;
+import java.lang.*;
+import java.sql.CallableStatement;
+import java.sql.Clob;
+import java.sql.Connection;
+import java.sql.PreparedStatement;
+import java.sql.ResultSet;
+import java.sql.SQLException;
+import java.sql.Types;
+import java.util.*;
+import org.apache.commons.logging.Log;
+import org.apache.commons.logging.LogFactory;
+import org.hibernate.Interceptor;
+import org.hibernate.Session;
+import org.hibernate.SessionFactory;
+import org.hibernate.Transaction;
+import org.hibernate.query.Query;
+import org.springframework.beans.factory.annotation.Qualifier;
+import org.springframework.context.annotation.Lazy;
+import org.springframework.orm.hibernate5.support.HibernateDaoSupport;
+import org.springframework.stereotype.Repository;
+import org.springframework.transaction.annotation.Transactional;
+import org.swallow.batchScheduler.ConnectionManager;
+import org.swallow.control.dao.MultipleMvtUpdatesDAO;
+import org.swallow.control.model.ProcessStatus;
+import org.swallow.control.model.Role;
+import org.swallow.exception.SwtErrorHandler;
+import org.swallow.exception.SwtException;
+import org.swallow.util.JDBCCloser;
+import org.swallow.util.SwtInterceptor;
+import org.swallow.util.SwtUtil;
+import org.swallow.work.model.Movement;
+
+
+@Repository ("multipleMvtUpdatesDAO")
+@Transactional
+public class MultipleMvtUpdatesDAOHibernate extends HibernateDaoSupport implements MultipleMvtUpdatesDAO {
+	private final Log log = LogFactory.getLog(MultipleMvtUpdatesDAOHibernate.class);
+
+	public MultipleMvtUpdatesDAOHibernate(@Lazy @Qualifier("sessionFactory") SessionFactory sessionfactory) {
+	    setSessionFactory(sessionfactory);
+	}
+
+
+	public boolean checkIfMvtExists(String hostId, String entityId, String movementId) throws SwtException {
+		try {
+			log.debug("MultipleMvtUpdatesDAOHibernate - [checkIfMvtExists] - Enter");
+
+			java.util.List list = getHibernateTemplate().find(
+				"select count(mvt) from Movement mvt where mvt.id.hostId=?0 and mvt.id.entityId=?1 and mvt.id.movementId=?2",
+				new Object[] { hostId, entityId, movementId });
+
+			boolean result = false;
+			if (list != null && !list.isEmpty()) {
+				Long count = (Long) list.get(0);
+				result = (count != null && count > 0);
+			}
+
+			log.debug("MultipleMvtUpdatesDAOHibernate - [checkIfMvtExists] - Exit");
+			return result;
+		} catch (Exception e) {
+			log.error("An exception occurred in MultipleMvtUpdatesDAOHibernate: checkIfMvtExists " + e.getMessage());
+			throw new SwtException(e.getMessage());
+		}
+	}
+
+
+	public Collection<Movement> getMovementList(List<Long> movementIds, String hostId, String entityId) throws SwtException {
+		try {
+			log.debug(this.getClass().getName() + " - [getMovementList] - Entry");
+
+			StringBuilder hql = new StringBuilder("from Movement m where m.id.entityId = ?0 and m.id.hostId = ?1 and m.id.movementId in (");
+
+			// Build the IN clause with positional parameters
+			for (int i = 0; i < movementIds.size(); i++) {
+				if (i > 0) {
+					hql.append(", ");
+				}
+				hql.append("?").append(i + 2);
+			}
+			hql.append(") order by m.id.movementId");
+
+			// Create parameter array
+			Object[] params = new Object[movementIds.size() + 2];
+			params[0] = entityId;
+			params[1] = hostId;
+			for (int i = 0; i < movementIds.size(); i++) {
+				params[i + 2] = movementIds.get(i);
+			}
+
+			@SuppressWarnings("unchecked")
+			List<Movement> movements = (List<Movement>) getHibernateTemplate().find(hql.toString(), params);
+
+			log.debug(this.getClass().getName() + " - [getMovementList] - Exit");
+			return movements;
+		} catch (Exception e) {
+			log.error(this.getClass().getName() + " - Exception in [getMovementList] method: " + e.getMessage());
+			throw SwtErrorHandler.getInstance().handleException(e, "getMovementList", MultipleMvtUpdatesDAOHibernate.class);
+		}
+	}
+
+
+	public Collection<Movement> getMovementList(List<Long> movementIds) throws SwtException {
+		try {
+			log.debug(this.getClass().getName() + " - [getMovementList] - Entry");
+
+			StringBuilder hql = new StringBuilder("from Movement m where m.id.movementId in (");
+
+			// Build the IN clause with positional parameters
+			for (int i = 0; i < movementIds.size(); i++) {
+				if (i > 0) {
+					hql.append(", ");
+				}
+				hql.append("?").append(i);
+			}
+			hql.append(") order by m.id.movementId");
+
+			// Create parameter array
+			Object[] params = new Object[movementIds.size()];
+			for (int i = 0; i < movementIds.size(); i++) {
+				params[i] = movementIds.get(i);
+			}
+
+			@SuppressWarnings("unchecked")
+			List<Movement> movements = (List<Movement>) getHibernateTemplate().find(hql.toString(), params);
+
+			log.debug(this.getClass().getName() + " - [getMovementList] - Exit");
+			return movements;
+		} catch (Exception e) {
+			log.error(this.getClass().getName() + " - Exception in [getMovementList] method: " + e.getMessage());
+			throw SwtErrorHandler.getInstance().handleException(e, "getMovementList", MultipleMvtUpdatesDAOHibernate.class);
+		}
+	}
+
+	public boolean checkIfEntityExists(String hostId, String entityId)
+			throws SwtException {
+		Connection conn = null;
+		ResultSet res = null;
+		PreparedStatement stmt = null;
+		ResultSet rs = null;
+		boolean result = false;
+
+		try {
+			log.debug("MultipleMvtUpdatesDAOHibernate  - [checkIfEntityExists] - Enter");
+
+			conn = ConnectionManager.getInstance().databaseCon();
+			String checkEntityQuery =  "select 1 from s_entity ent where ent.host_id=? and ent.entity_id=?";
+
+			stmt = conn.prepareStatement(checkEntityQuery);
+			stmt.setString(1, hostId);
+			stmt.setString(2, entityId);
+			stmt.execute();
+			/* Fetching the Result */
+			rs = (ResultSet) stmt.getResultSet();
+			if (rs != null) {
+				if(rs.next() == false) {
+					result=false;
+				}else {
+					result=true;
+				}
+
+			}
+
+			log.debug("MultipleMvtUpdatesDAOHibernate  - [checkIfEntityExists] - Exit");
+		} catch (SQLException ex) {
+			try {
+				log.error("MultipleMvtUpdatesDAOHibernate  - [checkIfEntityExists]  Exception: " + ex.getMessage());
+				// rollBack
+				conn.rollback();
+			} catch (SQLException e) {
+
+			}
+
+		} catch (Exception e) {
+			log.error("An exception occured in " + "MultipleMvtUpdatesDAOHibernate : checkIfEntityExists "
+					+ e.getMessage());
+			throw new SwtException(e.getMessage());
+
+		} finally {
+			// Close conn and nullify objects
+			Object[] exceptions = JDBCCloser.close(res, stmt, conn, null);
+		}
+
+		return result;
+	}
+
+
+	public boolean getRoleAccessDetails(String hostId, String roleId) throws SwtException {
+		try {
+			log.debug(this.getClass().getName() + " - [getRoleAccessDetails] - Entry");
+
+			java.util.List list = getHibernateTemplate().find(
+				"FROM Role r WHERE r.allowMsdMultiMvtUpdates = 'Y' AND r.hostId = ?0 AND r.roleId = ?1",
+				new Object[] { hostId, roleId });
+
+			boolean accessFlag = list != null && !list.isEmpty();
+
+			log.debug(this.getClass().getName() + " - [getRoleAccessDetails] - Exit with accessFlag: " + accessFlag);
+			return accessFlag;
+		} catch (Exception exp) {
+			log.error(this.getClass().getName() + " - Exception in [getRoleAccessDetails] method: ", exp);
+			throw SwtErrorHandler.getInstance().handleException(exp, "getRoleAccessDetails", MultipleMvtUpdatesDAOHibernate.class);
+		}
+	}
+
+
+	@Override
+	public void executeMainProcedure(String listMovements, String action, String jsonValues, String noteText, String userId, String seq) throws SwtException {
+		Session session = null;
+		Connection conn = null;
+		CallableStatement cstmt = null;
+		try {
+			session = getSessionFactory().openSession();
+			conn = SwtUtil.connection(session);
+			cstmt = conn.prepareCall("{call PKG_MULTIPLE_MOVS_ACTIONS.SP_MAIN(?,?,?,?,?,?)}");
+
+			cstmt.setString(1, seq);
+			cstmt.setString(2, listMovements);
+			cstmt.setString(3, action);
+			cstmt.setString(4, jsonValues);
+			cstmt.setString(5, noteText);
+			cstmt.setString(6, userId);
+			cstmt.execute();
+		} catch (Exception e) {
+			throw new SwtException("Error executing stored procedure SP_MAIN", e);
+		} finally {
+			try {
+				JDBCCloser.close(null, cstmt, conn, session);
+			} catch (Exception e) {
+				log.error(this.getClass().getName() + " - Exception in [executeMainProcedure] method: " + e.getMessage());
+			}
+		}
+	}
+
+	@Override
+	public int getProcessStatus(String seq) throws SwtException {
+		Session session = null;
+		Connection conn = null;
+		CallableStatement cstmt = null;
+		try {
+			session = getSessionFactory().openSession();
+			conn = SwtUtil.connection(session);
+			cstmt = conn.prepareCall("{? = call PKG_MULTIPLE_MOVS_ACTIONS.FN_PROCESS_STATUS(?)}");
+
+			cstmt.registerOutParameter(1, Types.INTEGER);
+			cstmt.setString(2, seq);
+			cstmt.execute();
+			return cstmt.getInt(1);
+		} catch (Exception e) {
+			throw new SwtException("Error executing function FN_PROCESS_STATUS", e);
+		} finally {
+			try {
+				JDBCCloser.close(null, cstmt, conn, session);
+			} catch (Exception e) {
+				log.error(this.getClass().getName() + " - Exception in [getProcessStatus] method: " + e.getMessage());
+			}
+		}
+	}
+
+	@Override
+	public void cleanTempProcess(String seq) throws SwtException {
+		Session session = null;
+		Connection conn = null;
+		CallableStatement cstmt = null;
+		try {
+			session = getSessionFactory().openSession();
+			conn = SwtUtil.connection(session);
+			cstmt = conn.prepareCall("{call PKG_MULTIPLE_MOVS_ACTIONS.CLEAN_TEMP_PROCESS(?)}");
+			cstmt.setString(1, seq);
+			cstmt.execute();
+		} catch (Exception e) {
+			throw new SwtException("Error executing stored procedure CLEAN_TEMP_PROCESS", e);
+		} finally {
+			try {
+				JDBCCloser.close(null, cstmt, conn, session);
+			} catch (Exception e) {
+				log.error(this.getClass().getName() + " - Exception in [cleanTempProcess] method: " + e.getMessage());
+			}
+		}
+	}
+
+	@Override
+	public ProcessStatus getProcessDetails(String seq, String includeMovements) throws SwtException {
+		ProcessStatus processStatus = new ProcessStatus();
+		Session session = null;
+		Connection conn = null;
+		CallableStatement cstmt = null;
+		ResultSet failedRs = null;
+		ResultSet successRs = null;
+		try {
+			session = getSessionFactory().openSession();
+			conn = SwtUtil.connection(session);
+			cstmt = conn.prepareCall("{call PKG_MULTIPLE_MOVS_ACTIONS.SP_GET_PROCESS_STATUS(?,?,?,?,?,?,?,?)}");
+
+			cstmt.setString(1, seq);
+			cstmt.setString(2, includeMovements); // New parameter to determine if movement details are fetched
+			cstmt.registerOutParameter(3, Types.INTEGER);
+			cstmt.registerOutParameter(4, Types.INTEGER);
+			cstmt.registerOutParameter(5, Types.INTEGER);
+			cstmt.registerOutParameter(6, Types.INTEGER);
+			cstmt.registerOutParameter(7, Types.REF_CURSOR);
+			cstmt.registerOutParameter(8, Types.REF_CURSOR);
+
+			cstmt.execute();
+			processStatus.setTotalCount(cstmt.getInt(3));
+			processStatus.setSuccessCount(cstmt.getInt(4));
+			processStatus.setFailedCount(cstmt.getInt(5));
+			processStatus.setPendingCount(cstmt.getInt(6));
+
+			if ("Y".equalsIgnoreCase(includeMovements)) {
+				failedRs = (ResultSet) cstmt.getObject(8);
+				while (failedRs.next()) {
+					processStatus.getFailedMovements().add(mapResultSetToMovement(failedRs, true));
+				}
+
+				successRs = (ResultSet) cstmt.getObject(7);
+				while (successRs.next()) {
+					processStatus.getSuccessfulMovements().add(mapResultSetToMovement(successRs, false));
+				}
+			}
+		} catch (Exception e) {
+			throw new SwtException("Error executing stored procedure GET_PROCESS_STATUS", e);
+		} finally {
+			try {
+				JDBCCloser.close(successRs);
+				JDBCCloser.close(failedRs, cstmt, conn, session);
+			} catch (Exception e) {
+				log.error(this.getClass().getName() + " - Exception in [getProcessDetails] method: " + e.getMessage());
+			}
+		}
+		return processStatus;
+	}
+
+	private Movement mapResultSetToMovement(ResultSet rs, boolean isFailedRecord) throws SQLException {
+		Movement movement = new Movement();
+		movement.getId().setMovementId(rs.getLong("MOVEMENT_ID"));
+		movement.getId().setEntityId(rs.getString("ENTITY_ID"));
+		movement.setPositionLevel(rs.getInt("POSITION_LEVEL"));
+		movement.setAmount(rs.getDouble("AMOUNT"));
+		movement.setSign(rs.getString("SIGN"));
+		movement.setCurrencyCode(rs.getString("CURRENCY_CODE"));
+		movement.setAccttype(rs.getString("MOVEMENT_TYPE"));
+		movement.setAccountId(rs.getString("ACCOUNT_ID"));
+		movement.setBookCode(rs.getString("BOOKCODE"));
+		movement.setCounterPartyId(rs.getString("COUNTERPARTY_ID"));
+		movement.setCounterPartyText1(rs.getString("COUNTERPARTY_TEXT1"));
+		movement.setCounterPartyText2(rs.getString("COUNTERPARTY_TEXT2"));
+		movement.setCounterPartyText3(rs.getString("COUNTERPARTY_TEXT3"));
+		movement.setCounterPartyText4(rs.getString("COUNTERPARTY_TEXT4"));
+		movement.setBeneficiaryId(rs.getString("BENEFICIARY_ID"));
+		movement.setBeneficiaryText1(rs.getString("BENEFICIARY_TEXT1"));
+		movement.setBeneficiaryText2(rs.getString("BENEFICIARY_TEXT2"));
+		movement.setBeneficiaryText3(rs.getString("BENEFICIARY_TEXT3"));
+		movement.setBeneficiaryText4(rs.getString("BENEFICIARY_TEXT4"));
+		movement.setMatchingParty(rs.getString("MATCHING_PARTY"));
+		movement.setProductType(rs.getString("PRODUCT_TYPE"));
+		movement.setPostingDate(rs.getDate("POSTING_DATE"));
+		movement.setInputSource(rs.getString("INPUT_SOURCE"));
+		movement.setMessageFormat(rs.getString("MESSAGE_FORMAT"));
+		movement.setPredictStatus(rs.getString("PREDICT_STATUS"));
+		movement.setExtBalStatus(rs.getString("EXT_BAL_STATUS"));
+		movement.setMatchStatus(rs.getString("MATCH_STATUS"));
+		movement.setReference1(rs.getString("REFERENCE1"));
+		movement.setReference2(rs.getString("REFERENCE2"));
+		movement.setReference3(rs.getString("REFERENCE3"));
+		movement.setReference4(rs.getString("REFERENCE4"));
+		movement.setMatchId(rs.getLong("MATCH_ID") != 0 ? rs.getLong("MATCH_ID") : null);
+		movement.setNotesCount(rs.getInt("NOTES_COUNT"));
+		movement.setValueDate(rs.getDate("VALUE_DATE"));
+		movement.setSettlementDateTime(rs.getTimestamp("SETTLEMENT_DATETIME"));
+		movement.setExpectedSettlementDateTime(rs.getTimestamp("EXPECTED_SETTLEMENT_DATETIME"));
+		movement.setCriticalPaymentType(rs.getString("CRITICAL_PAYMENT_TYPE"));
+		movement.setCustodianId(rs.getString("CUSTODIAN_ID"));
+		movement.setCustodianText1(rs.getString("CUSTODIAN_TEXT1"));
+		movement.setCustodianText2(rs.getString("CUSTODIAN_TEXT2"));
+		movement.setCustodianText3(rs.getString("CUSTODIAN_TEXT3"));
+		movement.setCustodianText4(rs.getString("CUSTODIAN_TEXT4"));
+		movement.setCustodianText5(rs.getString("CUSTODIAN_TEXT5"));
+		movement.setCounterPartyText5(rs.getString("COUNTERPARTY_TEXT5"));
+		movement.setBeneficiaryText5(rs.getString("BENEFICIARY_TEXT5"));
+		movement.setOrderingCustomerId(rs.getString("ORDERING_CUSTOMER"));
+		movement.setOrderingInstitutionId(rs.getString("ORDERING_INSTITUTION"));
+		movement.setSenderCorrespondentId(rs.getString("SENDERS_CORRES"));
+		movement.setReceiverCorrespondentId(rs.getString("RECEIVERS_CORRES"));
+		movement.setIntermediaryInstitutionId(rs.getString("INTMDRY_INSTITUTION_ID"));
+		movement.setAccountWithInstitutionId(rs.getString("ACC_WITH_INSTITUTION_ID"));
+		movement.setBeneficiaryCustomerId(rs.getString("BENEFICIARY_CUST"));
+		//movement.setExtraText1(rs.getString("EXTRA_TEXT1"));
+		movement.setIlmFcastStatus(rs.getString("ILM_FCAST_STATUS"));
+		movement.setUetr(rs.getString("UETR"));
+		if(isFailedRecord) {
+			movement.setFailingCause(rs.getString("ERROR_DESC"));
+		}
+
+		return movement;
+	}
+
+
+
+
+}
diff --git a/src/main/java/org/swallow/control/model/ProcessStatus.java b/src/main/java/org/swallow/control/model/ProcessStatus.java
new file mode 100644
index ********..a0b38243
--- /dev/null
+++ b/src/main/java/org/swallow/control/model/ProcessStatus.java
@@ -0,0 +1,68 @@
+package org.swallow.control.model;
+
+import org.swallow.work.model.Movement;
+
+import java.util.ArrayList;
+import java.util.List;
+
+public class ProcessStatus {
+    private int failedCount;
+    private int successCount;
+    private List<Movement> failedMovements;
+    private List<Movement> successfulMovements;
+    private int pendingCount;
+    private int totalCount;
+
+    public ProcessStatus() {
+        this.failedMovements = new ArrayList<>();
+        this.successfulMovements = new ArrayList<>();
+    }
+
+    public int getFailedCount() {
+        return failedCount;
+    }
+
+    public void setFailedCount(int failedCount) {
+        this.failedCount = failedCount;
+    }
+
+    public int getSuccessCount() {
+        return successCount;
+    }
+
+    public void setSuccessCount(int successCount) {
+        this.successCount = successCount;
+    }
+
+    public List<Movement> getFailedMovements() {
+        return failedMovements;
+    }
+
+    public void setFailedMovements(List<Movement> failedMovements) {
+        this.failedMovements = failedMovements;
+    }
+
+    public List<Movement> getSuccessfulMovements() {
+        return successfulMovements;
+    }
+
+    public void setSuccessfulMovements(List<Movement> successfulMovements) {
+        this.successfulMovements = successfulMovements;
+    }
+
+    public void setPendingCount(int pendingCount) {
+        this.pendingCount = pendingCount;
+    }
+
+    public int getPendingCount() {
+        return pendingCount;
+    }
+
+    public int getTotalCount() {
+        return totalCount;
+    }
+
+    public void setTotalCount(int totalCount) {
+        this.totalCount = totalCount;
+    }
+}
diff --git a/src/main/java/org/swallow/control/model/Role.hbm.xml b/src/main/java/org/swallow/control/model/Role.hbm.xml
index 98945482..26b80680 100644
--- a/src/main/java/org/swallow/control/model/Role.hbm.xml
+++ b/src/main/java/org/swallow/control/model/Role.hbm.xml
@@ -15,6 +15,9 @@
 		<property name="updateUser" column="UPDATE_USER" not-null="false"/>
 		<property name="authorizeInput" column="AUTHORIZE_INPUT" not-null="true"/>

+		<property name="allowMsdMultiMvtUpdates" column="ALLOW_MSD_MULTI_MOV_UPDATE" not-null="true"/>
+
+
 		<!-- START: CODE ADDED FOR SRS CURRENCY MONITOR 1.3 ON 11 - JUL - 2007 BY AJESH SINGH-->
 		<property name="restrictLocations" column="RESTRICT_LOCATIONS" not-null="true"/>
     		<!-- END: CODE ADDED FOR SRS CURRENCY MONITOR 1.3 ON 11 - JUL - 2007 BY AJESH SINGH-->
diff --git a/src/main/java/org/swallow/control/model/Role.java b/src/main/java/org/swallow/control/model/Role.java
index 09908d6c..cf0c413e 100644
--- a/src/main/java/org/swallow/control/model/Role.java
+++ b/src/main/java/org/swallow/control/model/Role.java
@@ -26,6 +26,7 @@ public class Role extends BaseObject implements
 	private Date updateDate = new Date();
 	private String updateUser;
 	private String authorizeInput;
+	private String allowMsdMultiMvtUpdates = "N";
 	/*Start code added by arumugam for mantis 1029 on 23-Nov-2009:Added account access in Role screen*/
 	private String accountAccessControl;
 	/*End code added by arumugam for mantis 1029 on 23-Nov-2009:Added account access in Role screen*/
@@ -316,5 +317,13 @@ public class Role extends BaseObject implements
 		this.inputInterruptionAlertType = inputInterruptionAlertType;
 	}

+	public String getAllowMsdMultiMvtUpdates() {
+		return allowMsdMultiMvtUpdates;
+	}

+	public void setAllowMsdMultiMvtUpdates(String allowMsdMultiMvtUpdates) {
+		if(SwtUtil.isEmptyOrNull(allowMsdMultiMvtUpdates))
+			allowMsdMultiMvtUpdates = "N";
+		this.allowMsdMultiMvtUpdates = allowMsdMultiMvtUpdates;
+	}
 }
diff --git a/src/main/java/org/swallow/control/service/ArchiveManager.java b/src/main/java/org/swallow/control/service/ArchiveManager.java
index 5f3f3e5a..7814b7a2 100644
--- a/src/main/java/org/swallow/control/service/ArchiveManager.java
+++ b/src/main/java/org/swallow/control/service/ArchiveManager.java
@@ -100,4 +100,24 @@ public interface ArchiveManager {
 	 * @throws SwtException
 	 */
 	public Archive getCurrentArchiveDb(String hostId) throws SwtException;
+
+	/**
+	 * Get current archive database with optional archiveId parameter
+	 * If archiveId is provided, get the archive record for that archiveId without defaultDb condition
+	 * If archiveId is not provided, get the default archive database
+	 * @param hostId
+	 * @param archiveId
+	 * @return Archive
+	 * @throws SwtException
+	 */
+	public Archive getCurrentArchiveDb(String hostId, String archiveId) throws SwtException;
+
+	/**
+	 * Get archive record by archiveId
+	 * @param hostId
+	 * @param archiveId
+	 * @return Archive
+	 * @throws SwtException
+	 */
+	public Archive getArchiveById(String hostId, String archiveId) throws SwtException;
 }
diff --git a/src/main/java/org/swallow/control/service/BackgroundTaskManager.java b/src/main/java/org/swallow/control/service/BackgroundTaskManager.java
new file mode 100644
index ********..9e23ed0f
--- /dev/null
+++ b/src/main/java/org/swallow/control/service/BackgroundTaskManager.java
@@ -0,0 +1,44 @@
+package org.swallow.control.service;
+
+import java.util.Map;
+import java.util.concurrent.*;
+
+public class BackgroundTaskManager {
+    private static final ExecutorService executor = Executors.newCachedThreadPool();
+    private static final Map<String, Future<?>> tasks = new ConcurrentHashMap<>();
+
+    public static void submitTask(String taskId, Runnable task) {
+        Future<?> future = executor.submit(task);
+        tasks.put(taskId, future);
+    }
+
+    public static boolean cancelTask(String taskId) {
+        Future<?> future = tasks.get(taskId);
+        if (future != null) {
+            boolean result = future.cancel(true);
+            if (result) {
+                tasks.remove(taskId);
+            }
+            return result;
+        }
+        return false;
+    }
+
+    public static boolean isTaskRunning(String taskId) {
+        Future<?> future = tasks.get(taskId);
+        return future != null && !future.isDone() && !future.isCancelled();
+    }
+
+    // Call this method when shutting down the application
+    public static void shutdown() {
+        executor.shutdown();
+        try {
+            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
+                executor.shutdownNow();
+            }
+        } catch (InterruptedException e) {
+            executor.shutdownNow();
+            Thread.currentThread().interrupt();
+        }
+    }
+}
diff --git a/src/main/java/org/swallow/control/service/MultipleMvtUpdatesManager.java b/src/main/java/org/swallow/control/service/MultipleMvtUpdatesManager.java
new file mode 100644
index ********..6c2683fb
--- /dev/null
+++ b/src/main/java/org/swallow/control/service/MultipleMvtUpdatesManager.java
@@ -0,0 +1,45 @@
+/*
+ * @(#)MovementRecoveryManager .java 03/05/06
+ *
+ * Copyright (c) 2006-2012 SwallowTech, Inc.
+ * 14 Lion Yard ,Tremadoc Road,  London  UK
+ * All Rights Reserved.
+ *
+ * This software is the confidential and proprietary information of
+ * SwallowTech Inc. ("Confidential Information"). You shall not
+ * disclose such Confidential Information and shall use it only in
+ * accordance with the terms of the license agreement you entered into
+ * with SwallowTech.
+ */
+
+package org.swallow.control.service;
+
+import org.swallow.control.dao.MultipleMvtUpdatesDAO;
+import org.swallow.control.model.ProcessStatus;
+import org.swallow.exception.SwtException;
+import org.swallow.work.model.Movement;
+
+import java.util.Collection;
+import java.util.List;
+
+
+public interface MultipleMvtUpdatesManager {
+
+	/**
+	 *
+	 */
+	public void setMultipleMvtUpdatesDAO(MultipleMvtUpdatesDAO dao);
+
+	public boolean checkIfMvtExists(String hostId, String entityId, String movementId) throws SwtException;
+
+	public Collection<Movement> getMovementList(List<Long> movementIds, String hostId, String entityId) throws SwtException;
+	public boolean checkIfEntityExists(String hostId, String entityId) throws SwtException;
+	public boolean checkMvmtLockStatus(String hostId, String entityId, String ccyCode, Long movementId, String userId) throws SwtException;
+	public Collection<Movement> getMovementList(List<Long> movementIds) throws SwtException;
+	public boolean getRoleAccessDetails(String hostId, String roleId) throws SwtException;
+
+	void executeMainProcedure(String listMovements, String action, String jsonValues, String noteText, String userId, String seq) throws SwtException;
+	int getProcessStatus(String seq) throws SwtException;
+	void cleanTempProcess(String seq) throws SwtException;
+	ProcessStatus getProcessDetails(String seq, String includeMovements) throws SwtException;
+}
diff --git a/src/main/java/org/swallow/control/service/impl/ArchiveManagerImpl.java b/src/main/java/org/swallow/control/service/impl/ArchiveManagerImpl.java
index abf129ac..95af653e 100644
--- a/src/main/java/org/swallow/control/service/impl/ArchiveManagerImpl.java
+++ b/src/main/java/org/swallow/control/service/impl/ArchiveManagerImpl.java
@@ -257,4 +257,44 @@ public class ArchiveManagerImpl implements ArchiveManager {
 			throw swtexp;
 		}
 	}
+
+	public Archive getCurrentArchiveDb(String hostId, String archiveId) throws SwtException {
+		try {
+			log.debug("Entering Manager Implementation of  'getCurrentArchiveDb with archiveId' method");
+
+			Archive archive = archiveDAO.getCurrentArchiveDb(hostId, archiveId);
+
+			log.debug("Exiting 'getCurrentArchiveDb with archiveId' Method");
+
+			return archive;
+		} catch (Exception e) {
+			log.error(this.getClass().getName()
+					+ "- [getCurrentArchiveDb with archiveId] - Exception " + e.getMessage());
+			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
+			SwtException swtexp = swtErrorHandler.handleException(e,
+					"getCurrentArchiveDb", ArchiveManagerImpl.class);
+
+			throw swtexp;
+		}
+	}
+
+	public Archive getArchiveById(String hostId, String archiveId) throws SwtException {
+		try {
+			log.debug("Entering Manager Implementation of  'getArchiveById' method");
+
+			Archive archive = archiveDAO.getArchiveById(hostId, archiveId);
+
+			log.debug("Exiting 'getArchiveById' Method");
+
+			return archive;
+		} catch (Exception e) {
+			log.error(this.getClass().getName()
+					+ "- [getArchiveById] - Exception " + e.getMessage());
+			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
+			SwtException swtexp = swtErrorHandler.handleException(e,
+					"getArchiveById", ArchiveManagerImpl.class);
+
+			throw swtexp;
+		}
+	}
 }
diff --git a/src/main/java/org/swallow/control/service/impl/MultipleMvtUpdatesManagerImpl.java b/src/main/java/org/swallow/control/service/impl/MultipleMvtUpdatesManagerImpl.java
new file mode 100644
index ********..9b0815ef
--- /dev/null
+++ b/src/main/java/org/swallow/control/service/impl/MultipleMvtUpdatesManagerImpl.java
@@ -0,0 +1,208 @@
+/*
+ * Created on May 3, 2006
+ *
+ * TODO To change the template for this generated file go to
+ * Window - Preferences - Java - Code Style - Code Templates
+ */
+package org.swallow.control.service.impl;
+
+import org.apache.commons.logging.Log;
+import org.apache.commons.logging.LogFactory;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.stereotype.Component;
+import org.swallow.control.dao.MultipleMvtUpdatesDAO;
+import org.swallow.control.model.ProcessStatus;
+import org.swallow.control.service.MultipleMvtUpdatesManager;
+import org.swallow.exception.SwtErrorHandler;
+import org.swallow.exception.SwtException;
+import org.swallow.util.SwtUtil;
+import org.swallow.work.dao.MovementLockDAO;
+import org.swallow.work.model.Movement;
+import org.swallow.work.model.MovementLock;
+import org.swallow.work.service.impl.MovementManagerImpl;
+
+import java.util.ArrayList;
+import java.util.Collection;
+import java.util.List;
+
+/**
+ * <AUTHOR>
+ *
+ * TODO To change the template for this generated type comment go to
+ * Window - Preferences - Java - Code Style - Code Templates
+ */
+@Component ("multiMvtUpdatesMgr")
+public class MultipleMvtUpdatesManagerImpl implements MultipleMvtUpdatesManager {
+	private final Log log = LogFactory.getLog(MultipleMvtUpdatesManagerImpl.class);
+	@Autowired
+	private MultipleMvtUpdatesDAO multipleMvtUpdatesDAO ;
+
+	public void setMultipleMvtUpdatesDAO(MultipleMvtUpdatesDAO dao)
+	{
+		this.multipleMvtUpdatesDAO = dao;
+	}
+
+
+	@Override
+	public boolean checkIfMvtExists(String hostId, String entityId, String movementId) throws SwtException {
+		try {
+			// log debug message
+			log.debug(this.getClass().getName() + " - [ checkIfMvtExists ] - " + "Entry");
+			return multipleMvtUpdatesDAO.checkIfMvtExists(hostId, entityId, movementId);
+		} catch (Exception ex) {
+			// log error message
+			log.error(this.getClass().getName() + " - Exception Caught in [checkIfMvtExists] method : - "
+					+ ex.getMessage());
+			throw SwtErrorHandler.getInstance().handleException(ex, "checkIfMvtExists", this.getClass());
+		} finally {
+			// log debug message
+			log.debug(this.getClass().getName() + " - [checkIfMvtExists] - Exit");
+		}
+	}
+
+	public Collection<Movement> getMovementList(List<Long> movementIds, String hostId, String entityId)
+			throws SwtException {
+
+			Collection<Movement> movementsList = new ArrayList<Movement>();
+
+			log.debug(this.getClass().getName() + " - getMovementList () - " + " Entry ");
+
+		movementsList = multipleMvtUpdatesDAO.getMovementList(movementIds, hostId, entityId);
+			return movementsList;
+	}
+
+
+	public boolean checkIfEntityExists(String hostId, String entityId) throws SwtException {
+		log.debug(this.getClass().getName() + " - checkIfEntityExists () - " + " Entry ");
+		return multipleMvtUpdatesDAO.checkIfEntityExists(hostId, entityId);
+	}
+
+
+	public boolean checkMvmtLockStatus(String hostId, String entityId, String ccyCode,
+									   Long movementId, String userId) throws SwtException {
+		log.debug("entering checkMvmtLockStatus method ");
+		boolean isLocked = true;
+
+		try {
+			Movement mvmnt = new Movement();
+			ArrayList usersList = new ArrayList();
+			MovementLockDAO movementLockDAOObj = (MovementLockDAO) SwtUtil
+					.getBean("movementLockDAO");
+
+				String updateUser = movementLockDAOObj.getLockUpdateUser(
+						hostId, movementId);
+				usersList.add(updateUser);
+
+				if ((updateUser != null)
+						&& (!(updateUser.equalsIgnoreCase(userId)))) {
+					return false;
+				}
+
+
+			int i = 0;
+			//String updateUser = "";
+
+				if (usersList.get(i) != null) {
+				} else {
+					MovementLock movementLockObj = new MovementLock();
+					movementLockObj.getId().setMovementId(movementId);
+					movementLockObj.getId().setHostId(hostId);
+					movementLockObj.setUpdateUser(userId);
+					movementLockObj.setUpdateDate(SwtUtil
+							.getSystemDatewithTime());
+					movementLockObj.getId().setEntityId(entityId);
+					movementLockObj.setCurrCode(ccyCode);
+					isLocked = movementLockDAOObj.lockMovement(movementLockObj);
+
+				}
+
+				i++;
+
+		} catch (Exception exp) {
+			log
+					.debug("Exception Catch in MultipleMvtUpdatesManagerImpl.'checkMvmtLockStatus' method : "
+							+ exp.getMessage());
+			throw SwtErrorHandler.getInstance().handleException(exp,
+					"checkMvmtLockStatus", MovementManagerImpl.class);
+		}
+
+		log.debug("exiting checkMvmtLockStatus method ");
+		return isLocked;
+	}
+
+	public Collection<Movement> getMovementList(List<Long> movementIds)
+			throws SwtException {
+
+		Collection<Movement> movementsList = new ArrayList<Movement>();
+
+		log.debug(this.getClass().getName() + " - getMovementList () - " + " Entry ");
+
+		movementsList = multipleMvtUpdatesDAO.getMovementList(movementIds);
+		return movementsList;
+	}
+
+	public boolean getRoleAccessDetails(String hostId, String roleId) throws SwtException {
+			/* Method's local variable declaration */
+			boolean accessFlag = false;
+			try {
+				log.debug(this.getClass().getName() + " - [getRoleAccessDetails] - "
+						+ "Entry");
+				/* Fetching role details by calling DAO class */
+				accessFlag = (multipleMvtUpdatesDAO.getRoleAccessDetails(hostId, roleId));
+				/* Loop to iterate the details */
+			} catch (Exception e) {
+				log.debug(this.getClass().getName()
+						+ " - Exception Catched in [getRoleAccessDetails] method : - "
+						+ e.getMessage());
+				log.error(this.getClass().getName()
+						+ " - Exception Catched in [getRoleAccessDetails] method : - "
+						+ e.getMessage());
+				e.printStackTrace();
+				throw SwtErrorHandler.getInstance().handleException(e,
+						"getRoleAccessDetails", MultipleMvtUpdatesManagerImpl.class);
+			}
+			log
+					.debug(this.getClass().getName() + " - [getRoleAccessDetails] - "
+							+ "Exit");
+			return accessFlag;
+		}
+
+
+	@Override
+	public void executeMainProcedure(String listMovements, String action, String jsonValues, String noteText, String userId, String seq) throws SwtException {
+		try {
+			multipleMvtUpdatesDAO.executeMainProcedure(listMovements, action, jsonValues, noteText, userId, seq);
+		} catch (Exception e) {
+			throw SwtErrorHandler.getInstance().handleException(e, "executeMainProcedure", MultipleMvtUpdatesManagerImpl.class);
+		}
+	}
+
+	@Override
+	public int getProcessStatus(String seq) throws SwtException {
+		try {
+			return multipleMvtUpdatesDAO.getProcessStatus(seq);
+		} catch (Exception e) {
+			throw SwtErrorHandler.getInstance().handleException(e, "getProcessStatus", MultipleMvtUpdatesManagerImpl.class);
+		}
+	}
+
+	@Override
+	public void cleanTempProcess(String seq) throws SwtException {
+		try {
+			multipleMvtUpdatesDAO.cleanTempProcess(seq);
+		} catch (Exception e) {
+			throw SwtErrorHandler.getInstance().handleException(e, "cleanTempProcess", MultipleMvtUpdatesManagerImpl.class);
+		}
+	}
+
+	@Override
+	public ProcessStatus getProcessDetails(String seq, String includeMovements) throws SwtException {
+		try {
+			return multipleMvtUpdatesDAO.getProcessDetails(seq, includeMovements);
+		} catch (Exception e) {
+			throw SwtErrorHandler.getInstance().handleException(e, "getProcessDetails", MultipleMvtUpdatesManagerImpl.class);
+		}
+	}
+
+
+}
diff --git a/src/main/java/org/swallow/control/web/ConnectionPoolControlAction.java b/src/main/java/org/swallow/control/web/ConnectionPoolControlAction.java
index c9218aee..3aca13a3 100644
--- a/src/main/java/org/swallow/control/web/ConnectionPoolControlAction.java
+++ b/src/main/java/org/swallow/control/web/ConnectionPoolControlAction.java
@@ -394,6 +394,7 @@ HttpServletResponse response = ServletActionContext.getResponse();
 						connectionStats.get(SwtConstants.CONNECTION_POOL_IDLE_ID));
 			}
 			responseConstructor.createElement(SwtConstants.CONNECTION_POOL_IS_FRESH_VIEWS_DATA, ""+freshViewsData);
+
 			xmlWriter.endElement(SwtConstants.SINGLETONS);


@@ -671,7 +672,7 @@ HttpServletResponse response = ServletActionContext.getResponse();
 				if (order.equals(SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME)) {
 					tmpColumnInfo = new ColumnInfo(
 							SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_CONNECTIONID_HEADING, request),
-							SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
+							SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME, SwtConstants.TYPE_INTEGER, 1,
 							Integer.parseInt(widths.get(SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME)), false,
 							true, hiddenColumnsMap.get(SwtConstants.CONNECTION_POOL_CONNECTIONID_TAGNAME));
 					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CONNECTION_POOL_CONNECTIONID_HEADING_TOOLTIP, request));
@@ -1074,4 +1075,4 @@ HttpServletResponse response = ServletActionContext.getResponse();
 		}
 		return ("statechange");
 	}
-}
\ No newline at end of file
+}
diff --git a/src/main/java/org/swallow/control/web/MultipleMovementUpdatesAction.java b/src/main/java/org/swallow/control/web/MultipleMovementUpdatesAction.java
new file mode 100644
index ********..41bdb80c
--- /dev/null
+++ b/src/main/java/org/swallow/control/web/MultipleMovementUpdatesAction.java
@@ -0,0 +1,1802 @@
+/*
+ * @(#)MovementRecoveryAction 03/05/06
+ *
+ * Copyright (c) 2006-2012 SwallowTech, Inc.
+ * 14 Lion Yard ,Tremadoc Road,  London  UK
+ * All Rights Reserved.
+ *
+ * This software is the confidential and proprietary information of
+ * SwallowTech Inc. ("Confidential Information"). You shall not
+ * disclose such Confidential Information and shall use it only in
+ * accordance with the terms of the license agreement you entered into
+ * with SwallowTech.
+ */
+
+package org.swallow.control.web;
+
+import org.apache.commons.logging.Log;
+import org.apache.commons.logging.LogFactory;
+import org.apache.struts2.ServletActionContext;
+import org.apache.struts2.convention.annotation.Action;
+import org.apache.struts2.convention.annotation.AllowedMethods;
+import org.apache.struts2.convention.annotation.Result;
+import org.json.JSONArray;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.context.annotation.Scope;
+import org.springframework.stereotype.Controller;
+import org.springframework.web.bind.annotation.RequestMapping;
+import org.springframework.web.bind.annotation.RequestMethod;
+import org.springframework.web.bind.annotation.RequestParam;
+import org.swallow.control.model.ProcessStatus;
+import org.swallow.control.service.BackgroundTaskManager;
+import org.swallow.control.service.MultipleMvtUpdatesManager;
+import org.swallow.exception.SwtErrorHandler;
+import org.swallow.exception.SwtException;
+import org.swallow.export.service.MultipleMovementExcelReportGenerator;
+import org.swallow.maintenance.model.BookCode;
+import org.swallow.maintenance.model.EntityUserAccess;
+import org.swallow.maintenance.service.CurrencyManager;
+import org.swallow.pcm.maintenance.model.core.ColumnInfo;
+import org.swallow.pcm.maintenance.model.core.OptionInfo;
+import org.swallow.pcm.maintenance.model.core.SelectInfo;
+import org.swallow.pcm.maintenance.web.ResponseHandler;
+import org.swallow.util.*;
+import org.swallow.util.pcm.PCMConstant;
+import org.swallow.util.struts.CustomActionSupport;
+import org.swallow.util.xml.SwtResponseConstructor;
+import org.swallow.util.xml.SwtXMLWriter;
+import org.swallow.work.model.Movement;
+import org.swallow.work.service.MovementManager;
+import org.swallow.work.web.OutStandingMvmtAction;
+
+import javax.servlet.http.HttpServletRequest;
+import javax.servlet.http.HttpServletResponse;
+import javax.servlet.http.HttpSession;
+import java.io.IOException;
+import java.math.BigDecimal;
+import java.util.*;
+
+
+@Action(value = "/multipleMvtActions", results = {
+		@Result(name = "success", location = "/jsp/control/multipleMvtUpdates.jsp"),
+		@Result(name = "multiMvtUpdate", location = "/jsp/control/multipleMvtUpdates.jsp"),
+		@Result(name = "data", location = "/jsp/data.jsp"),
+		@Result(name = "fail", location = "/error.jsp"),
+})
+
+@AllowedMethods({"unspecified", "display", "checkMovements", "openMultiMvtActions", "processAction", "checkMvtAccess", "checkProcessStatus", "exportMovements", "cancelAction", "cleanTempProcess"})
+public class MultipleMovementUpdatesAction extends CustomActionSupport {
+
+
+
+	private final Log log = LogFactory.getLog(MultipleMovementUpdatesAction.class);
+
+	/* Creating and Setting instance of MovmentRecovery Manager */
+	@Autowired
+	private MultipleMvtUpdatesManager multiMvtUpdatesMgr = null;
+
+	public void setMultipleMovementUpdatesManager(
+			MultipleMvtUpdatesManager multiMvtUpdatesMgr) {
+		this.multiMvtUpdatesMgr = multiMvtUpdatesMgr;
+	}
+	public String execute() throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+
+		// List of methods
+		String method = String.valueOf(request.getParameter("method"));
+		switch (method) {
+			case "unspecified":
+				return unspecified();
+			case "display":
+				return display();
+			case "checkMovements":
+				return checkMovements();
+			case "openMultiMvtActions":
+				return openMultiMvtActions();
+			case "processAction":
+				return processAction();
+			case "checkMvtAccess":
+				return checkMvtAccess();
+			case "checkProcessStatus":
+				return checkProcessStatus();
+			case "exportMovements":
+				return exportMovements();
+			case "cancelAction":
+				return cancelAction();
+			case "cleanTempProcess":
+				cleanTempProcess();
+
+
+
+		}
+
+
+		return unspecified();
+	}
+
+	/**
+	 * This method is called when no method available in the request
+	 *
+	 * @return findForward
+	 * @throws Exception
+	 */
+
+	public String unspecified() throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+		HttpServletResponse response = ServletActionContext.getResponse();
+
+		try {
+			request.setAttribute("fromMenu", "true");
+			return SUCCESS;
+
+		} catch (Exception e) {
+			log.error("Exception Catch in MultipleMovementUpdatesAction.'unspecified' method : " + e.getMessage());
+			SwtUtil.logErrorInDatabase(
+					SwtErrorHandler.getInstance().handleException(e, "unspecified", MultipleMovementUpdatesAction.class));
+			return ERROR;
+		}
+	}
+
+
+	private String putHostIdListInReq(HttpServletRequest request)
+			throws SwtException {
+		log.debug("entering 'putHostIdListInReq' method");
+		CacheManager cacheManagerInst = CacheManager.getInstance();
+		String hostId = cacheManagerInst.getHostId();
+		log.debug("exiting 'putHostIdListInReq' method");
+		return hostId;
+
+	}
+
+
+	/**
+	 *
+	 * @param request
+	 * @throws SwtException
+	 */
+
+	private void putEntityListInReq(HttpServletRequest request)
+			throws SwtException {
+		log.debug("entering 'putEntityListInReq' method");
+		Collection coll = SwtUtil.getUserEntityAccessList(request.getSession());
+		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, request
+				.getSession());
+		request.setAttribute("entities", coll);
+		log.debug("exiting 'putEntityListInReq' method");
+	}
+
+
+	public String display()
+			throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+		HttpServletResponse response = ServletActionContext.getResponse();;
+
+		/* Local variable declaration */
+		// To host for application
+		String hostId = null;
+		// To hold the current entity from screen
+		String entityId = null;
+		ResponseHandler responseHandler = null;
+		SwtResponseConstructor responseConstructor = null;
+		SwtXMLWriter xmlWriter = null;
+		String width = null;
+		String columnOrder = null;
+		String hiddenColumns = null;
+		SystemFormats sysFormat=null;
+		String dateFormat=null;
+		String displayedDate=null;
+		String fromMenu= null;
+		String selectedMvtIdsList= null;
+		List<Long> movementIds = new ArrayList<>();
+		Collection<BookCode> collBookCode = null;
+		Iterator<BookCode> itrBookCode = null;
+		Collection<Movement> listMovements = new ArrayList<Movement>();
+		String amountFormat = null;
+		boolean isLocked=false;
+		try {
+			log.debug(this.getClass().getName() + " - [display()] - Entry");
+			fromMenu = request.getParameter("fromMenu");
+			selectedMvtIdsList = request.getParameter("selectedMvtIdsList");
+			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
+			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
+			amountFormat=sysFormat.getCurrencyFormat();
+			//get the entity label of the bottom section
+			entityId = SwtUtil.getUserCurrentEntity(request.getSession());
+			displayedDate = SwtUtil.formatDate(SwtUtil
+					.getSysParamDateWithEntityOffset(entityId), SwtUtil
+					.getCurrentSystemFormats(request.getSession())
+					.getDateFormatValue());
+			// to get the host id for application
+			hostId = CacheManager.getInstance().getHostId();
+
+			//convert to list of Longs
+			if (selectedMvtIdsList != null && !selectedMvtIdsList.isEmpty()) {
+				// Split the string by commas
+				String[] idsArray = selectedMvtIdsList.split(",");
+
+				// Convert each string to Long and add to the list
+				for (String id : idsArray) {
+					try {
+						movementIds.add(Long.parseLong(id.trim())); // trim to remove any leading/trailing spaces
+					} catch (NumberFormatException e) {
+						// Handle the case where the string cannot be parsed to a Long
+						log.error("Invalid movement ID: " + id, e);
+					}
+				}
+			}
+
+			if ("false".equalsIgnoreCase(fromMenu) && movementIds.size() > 0 && !SwtUtil.isEmptyOrNull(entityId)){
+				//get the list of movements that have been selected from the MSD screen
+				listMovements = multiMvtUpdatesMgr.getMovementList(movementIds, hostId, entityId);
+				List<Hashtable<String, Integer>> editFlagArrList = new ArrayList<>();
+
+				for (Movement mvt : listMovements) {
+					// Initialize the array for movement field collection
+					Hashtable<String, Integer> editFlagArr = new Hashtable<>();
+
+					// Get the access list for movement field based on the entityId
+					editFlagArr = ((MovementManager) (SwtUtil.getBean("movementManager")))
+							.getEditFlagDetails(hostId, mvt.getId().getEntityId(), mvt.getInputSource());
+
+					// Add the edit flag Hashtable to the list
+					editFlagArrList.add(editFlagArr);
+				}
+
+				// Now call the method with the list of edit flags
+				putEditFlagsInRequest(request, editFlagArrList);
+			}
+
+			responseHandler = new ResponseHandler();
+			responseConstructor = new SwtResponseConstructor();
+			xmlWriter = responseConstructor.getXMLWriter();
+
+			xmlWriter.startElement(SwtConstants.MULTI_MVT_ACTIONS);
+
+			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
+					SwtConstants.DATA_FETCH_OK);
+
+			// forms singleton node
+			xmlWriter.startElement(SwtConstants.SINGLETONS);
+			responseConstructor.createElement("displayedDate", displayedDate);
+			responseConstructor.createElement("dateFormat", dateFormat);
+			responseConstructor.createElement("preStatusEditStatus", (String) request.getAttribute("preStatusEditStatus"));
+			responseConstructor.createElement("extBalStatus",(String) request.getAttribute("extBalStatus"));
+			responseConstructor.createElement("ilmFcastStatusEditStatus",(String) request.getAttribute("ilmFcastStatusEditStatus"));
+			responseConstructor.createElement("bookCodeEditStatus",(String) request.getAttribute("bookCodeEditStatus"));
+			responseConstructor.createElement("orederInstEditStatus",(String) request.getAttribute("orederInstEditStatus"));
+			responseConstructor.createElement("critPayTypeEditStatus",(String) request.getAttribute("critPayTypeEditStatus"));
+			responseConstructor.createElement("cpartyEditStatus",(String) request.getAttribute("cpartyEditStatus"));
+			responseConstructor.createElement("expSettEditStatus",(String) request.getAttribute("expSettEditStatus"));
+			responseConstructor.createElement("actualSettEditStatus",(String) request.getAttribute("actualSettEditStatus"));
+			xmlWriter.endElement(SwtConstants.SINGLETONS);
+
+
+
+			/******* mvtGrid ******/
+			responseConstructor.formGridStart("mvtGrid");
+			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request));
+			Map<String, Integer> entityIdCounts = new HashMap<>();
+			if(listMovements.size()>0) {
+				Integer mvtAccess=null;
+				String userId = SwtUtil.getCurrentUserId(request.getSession());
+				int unlockedMvtCount = 0; // Counter for locked movements
+				StringBuilder unlockedMovements = new StringBuilder();
+				// form rows (records)
+				responseConstructor.formRowsStart(listMovements.size());
+				for (Iterator<Movement> it = listMovements.iterator(); it.hasNext();) {
+					// Obtain rules definition tag from iterator
+					Movement mvt = (Movement) it.next();
+					responseConstructor.formRowStart();
+					//check if movement is locked
+					isLocked = multiMvtUpdatesMgr.checkMvmtLockStatus(hostId, entityId, mvt.getCurrencyCode(), Long.valueOf(mvt.getId().getMovementId()), userId);
+					if (!isLocked) {
+						mvtAccess = -1;
+						unlockedMvtCount++;
+						unlockedMovements.append(mvt.getId().getMovementId()).append(",");
+					} else {
+						mvtAccess = 0;
+					}
+					String currentEntityId = mvt.getId().getEntityId();
+					entityIdCounts.put(currentEntityId, entityIdCounts.getOrDefault(currentEntityId, 0) + 1);
+
+					if(mvt
+							   .getExpectedSettlementDateTime() != null) {
+						mvt.setExpectedSettlementDateTimeAsString((SwtUtil.formatDate(mvt
+								.getExpectedSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
+								request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
+					}
+					if(mvt
+							   .getSettlementDateTime() != null) {
+						mvt.setSettlementDateTimeAsString((SwtUtil.formatDate(mvt
+								.getSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
+								request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
+					}
+
+
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_SELECT, mvtAccess==0?"Y":"N");
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ACCESS, mvtAccess==0?"Y":"N");
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_MOVEMENT_ID, mvt.getId().getMovementId().toString());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ENTITY, mvt.getId().getEntityId());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_CCY,mvt.getCurrencyCode());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_VDate,   SwtUtil.formatDate(mvt.getValueDate(), dateFormat ));
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ACCOUNT, mvt.getAccountId());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_AMOUNT, SwtUtil.formatCurrency(mvt
+							.getCurrencyCode(), new BigDecimal(mvt.getAmount()),amountFormat));
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_SIGN, mvt.getSign());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_PRED, mvt.getPredictStatus());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_EXT, mvt.getExtractStatus());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ILMFCAST, mvt.getIlmFcastStatus());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_MATCH_STATUS, mvt.getMatchStatus());
+
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_REF1, mvt.getReference1());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_REF2, mvt.getReference2());
+					//to be checked
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_EXTRA_REF, mvt.getReference4());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_BOOK, mvt.getBookCode());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_MATCH_ID, mvt.getMatchId()!=null?String.valueOf(mvt.getMatchId()):"");
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_SOURCE, mvt.getInputSource());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_FORMAT, mvt.getMessageFormat());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_CPARTY, mvt.getCounterPartyId());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ORD_INST, mvt.getOrderingInstitutionId());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_EXP_SETTLEMENT, mvt.getExpectedSettlementDateTimeAsString());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ACT_SETTLEMENT, mvt.getSettlementDateTimeAsString());
+					responseConstructor.createRowElement(SwtConstants.MULTI_MVT_CRIT_PAY_TYPE, mvt.getCriticalPaymentType());
+					responseConstructor.formRowEnd();
+				}
+
+				responseConstructor.formRowsEnd();
+			}
+			responseConstructor.formGridEnd();
+
+
+			int maxCount = 0;
+			String mostFrequentEntityId = null;
+			for (Map.Entry<String, Integer> entry : entityIdCounts.entrySet()) {
+				if (entry.getValue() > maxCount) {
+					maxCount = entry.getValue();
+					mostFrequentEntityId = entry.getKey();
+				}
+			}
+
+			if (mostFrequentEntityId != null) {
+				entityId = mostFrequentEntityId;
+			}
+
+
+			/***** Data source Combo Start ***********/
+			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
+			// options drop down list
+			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
+			lstOptions = new ArrayList<OptionInfo>();
+			List<String> collDataSrc=null;
+			if("true".equalsIgnoreCase(fromMenu)) {
+				collDataSrc = new ArrayList<String>(Arrays.asList("CSV", "Excel"));
+			}else{
+				collDataSrc = new ArrayList<String>(Arrays.asList("Movement Summary", "CSV", "Excel"));
+			}
+			for (int i = 0; i < collDataSrc.size(); i++) {
+				lstOptions.add(new OptionInfo(collDataSrc.get(i), collDataSrc.get(i), false));
+			}
+			lstSelect.add(new SelectInfo("dataSourcesList", lstOptions));
+			/***** Data source Combo End ***********/
+			/***** Book Code list Combo Start ***********/
+
+			// get the collection of book code for host id,entity id
+			collBookCode = ((MovementManager) (SwtUtil.getBean("movementManager")))
+					.getBookCodeList(CacheManager.getInstance().getHostId(), entityId);
+			// options drop down list
+			lstOptions = new ArrayList<OptionInfo>();
+			if (collBookCode != null) {
+				// iterate the book code collection
+				itrBookCode = collBookCode.iterator();
+				lstOptions.add(new OptionInfo("","", false));
+				while (itrBookCode.hasNext()) {
+					// get the bookcode
+					BookCode bookCode = itrBookCode.next();
+					lstOptions.add(new OptionInfo(bookCode.getId().getBookCode(),bookCode.getBookName(), false));
+
+				}
+				lstSelect.add(new SelectInfo("bookCodeList", lstOptions));
+			}
+			/***** Book Code list Combo End ***********/
+
+			responseConstructor.formSelect(lstSelect);
+
+
+			xmlWriter.endElement(SwtConstants.MULTI_MVT_ACTIONS);
+			request.setAttribute("data", xmlWriter.getData());
+
+			// set the method name,last ref time and access level on request
+			// attribute which are used in front end
+			request.setAttribute("methodName", "success");
+			log.debug(this.getClass().getName() + " - [display()] - Exit");
+			return ("data");
+		} catch (SwtException swtexp) {
+			log
+					.error("SwtException Catch in MultipleMovementUpdatesAction.'display' method : "
+						   + swtexp.getMessage());
+			SwtUtil.logException(swtexp, request, "");
+			return ("fail");
+		} catch (Exception exp) {
+			log
+					.error("Exception Catch in MultipleMovementUpdatesAction.'display' method : "
+						   + exp.getMessage());
+			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
+					exp, "display", MultipleMovementUpdatesAction.class), request, "");
+			return ("fail");
+		} finally {
+			/* null the objects created already. */
+			hostId = null;
+			entityId = null;
+		}
+	}
+
+
+	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns,HttpServletRequest request)
+			throws SwtException {
+		/* Array list to hold column order */
+		ArrayList<String> orders = null;
+		/* String array variable to hold column order property */
+		String[] columnOrderProp = null;
+		/* Iterator variable to hold column order */
+		Iterator<String> columnOrderItr = null;
+		/* Hash map to hold column width */
+		HashMap<String, String> widths = null;
+		/* String array variable to hold width property */
+		String[] columnWidthProperty = null;
+		// List for column info
+		List<ColumnInfo> lstColumns = null;
+
+		/* Hash map to hold column hidden_Columns */
+		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
+		/* Array list to hold hidden Column array */
+		ArrayList<String> lstHiddenColunms = null;
+		/* String array variable to hold hidden columns property */
+		String[] hiddenColumnsProp = null;
+
+		try {
+			// log debug message
+			log.debug(this.getClass().getName()
+					  + " - [ getGridColumns ] - Entry");
+			/* Condition to check width is null */
+
+			if (SwtUtil.isEmptyOrNull(width)) {
+
+				width = SwtConstants.MULTI_MVT_SELECT + "=50,"
+						+ SwtConstants.MULTI_MVT_MOVEMENT_ID + "=180,"
+						+ SwtConstants.MULTI_MVT_ENTITY + "=120,"
+						+ SwtConstants.MULTI_MVT_CCY + "=100,"
+						+ SwtConstants.MULTI_MVT_VDate + "=120,"
+						+ SwtConstants.MULTI_MVT_ACCOUNT+ "=150,"
+						+ SwtConstants.MULTI_MVT_AMOUNT + "=150,"
+						+ SwtConstants.MULTI_MVT_SIGN + "=80,"
+						+ SwtConstants.MULTI_MVT_PRED + "=80,"
+						+ SwtConstants.MULTI_MVT_EXT + "=80,"
+						+ SwtConstants.MULTI_MVT_ILMFCAST+ "=120,"
+						+ SwtConstants.MULTI_MVT_MATCH_STATUS + "=150,"
+						+ SwtConstants.MULTI_MVT_REF1 + "=180,"
+						+ SwtConstants.MULTI_MVT_REF2 + "=180,"
+						+ SwtConstants.MULTI_MVT_EXTRA_REF + "=180,"
+						+ SwtConstants.MULTI_MVT_BOOK + "=120,"
+						+ SwtConstants.MULTI_MVT_MATCH_ID + "=120,"
+						+ SwtConstants.MULTI_MVT_SOURCE + "=100,"
+						+ SwtConstants.MULTI_MVT_FORMAT + "=100,"
+						+ SwtConstants.MULTI_MVT_CPARTY + "=180,"
+						+ SwtConstants.MULTI_MVT_ORD_INST+ "=100,"
+						+ SwtConstants.MULTI_MVT_EXP_SETTLEMENT + "=150,"
+						+ SwtConstants.MULTI_MVT_ACT_SETTLEMENT  + "=150,"
+						+ SwtConstants.MULTI_MVT_CRIT_PAY_TYPE + "=150";
+			}
+
+			/* Obtain width for each column */
+			columnWidthProperty = width.split(",");
+
+			/* Loop to insert each column in hash map */
+			widths = new HashMap<String, String>();
+			for (String s : columnWidthProperty) {
+				/* Condition to check index of = is -1 */
+
+				if (s.indexOf("=") != -1) {
+					String[] propval = s.split("=");
+
+					widths.put(propval[0], propval[1]);
+				}
+			}
+
+			/* Condition to check column order is null or empty */
+			if (SwtUtil.isEmptyOrNull(columnOrder)) {
+				/* Default values for column order */
+				columnOrder =  SwtConstants.MULTI_MVT_SELECT + ","
+							   + SwtConstants.MULTI_MVT_MOVEMENT_ID + ","
+							   + SwtConstants.MULTI_MVT_ENTITY + ","
+							   + SwtConstants.MULTI_MVT_CCY + ","
+							   + SwtConstants.MULTI_MVT_VDate + ","
+							   + SwtConstants.MULTI_MVT_ACCOUNT+ ","
+							   + SwtConstants.MULTI_MVT_AMOUNT + ","
+							   + SwtConstants.MULTI_MVT_SIGN + ","
+							   + SwtConstants.MULTI_MVT_PRED + ","
+							   + SwtConstants.MULTI_MVT_EXT + ","
+							   + SwtConstants.MULTI_MVT_ILMFCAST+ ","
+							   + SwtConstants.MULTI_MVT_MATCH_STATUS + ","
+							   + SwtConstants.MULTI_MVT_REF1 + ","
+							   + SwtConstants.MULTI_MVT_REF2 + ","
+							   + SwtConstants.MULTI_MVT_EXTRA_REF + ","
+							   + SwtConstants.MULTI_MVT_BOOK + ","
+							   + SwtConstants.MULTI_MVT_MATCH_ID + ","
+							   + SwtConstants.MULTI_MVT_SOURCE + ","
+							   + SwtConstants.MULTI_MVT_FORMAT + ","
+							   + SwtConstants.MULTI_MVT_CPARTY + ","
+							   + SwtConstants.MULTI_MVT_ORD_INST+ ","
+							   + SwtConstants.MULTI_MVT_EXP_SETTLEMENT + ","
+							   + SwtConstants.MULTI_MVT_ACT_SETTLEMENT + ","
+							   + SwtConstants.MULTI_MVT_CRIT_PAY_TYPE ;
+			}
+			orders = new ArrayList<String>();
+			/* Split the columns using , and save in string array */
+			columnOrderProp = columnOrder.split(",");
+			/* Loop to enter column order in array list */
+
+			for (int i = 0; i < columnOrderProp.length; i++) {
+				/* Adding the Column values to ArrayList */
+				orders.add(columnOrderProp[i]);
+				hiddenColumnsMap.put(columnOrderProp[i], true);
+			}
+
+			/* Condition to check column hidden is null or empty */
+			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
+				lstHiddenColunms = new ArrayList<String>();
+				/* Split the hidden columns , and save in string array */
+				hiddenColumnsProp = hiddenColumns.split(",");
+
+				for (int i = 0; i < hiddenColumnsProp.length; i++) {
+					/* Adding the Column values to ArrayList */
+					lstHiddenColunms.add(hiddenColumnsProp[i]);
+				}
+
+				Iterator<String> listKeys = hiddenColumnsMap.keySet()
+						.iterator();
+				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
+					String columnKey = listKeys.next();
+					// boolean found = false;
+					for (int j = 0; j < lstHiddenColunms.size(); j++) {
+						if (columnKey.equals(lstHiddenColunms.get(j))) {
+							hiddenColumnsMap.put(columnKey, false);
+							break;
+						}
+					}
+				}
+			}
+
+			columnOrderItr = orders.iterator();
+			lstColumns = new ArrayList<ColumnInfo>();
+			while (columnOrderItr.hasNext()) {
+				String order = (String) columnOrderItr.next();
+				if (order.equals(SwtConstants.MULTI_MVT_SELECT)) {
+					lstColumns.add(new ColumnInfo("", SwtConstants.MULTI_MVT_SELECT,
+							PCMConstant.COLUMN_TYPE_CHECK, 0, Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_SELECT)), false,
+							false, true));
+				}else if (order.equals(SwtConstants.MULTI_MVT_MOVEMENT_ID)) {
+					// column Alert Date
+					lstColumns.add(new ColumnInfo(
+							SwtUtil.getMessage(SwtConstants.MULTI_MVT_MOVEMENT_ID_HEADER, request),
+							SwtConstants.MULTI_MVT_MOVEMENT_ID,
+							SwtConstants.COLUMN_TYPE_NUMBER, 1,
+							Integer.parseInt(widths
+									.get(SwtConstants.MULTI_MVT_MOVEMENT_ID)),
+							true, true, true));
+				} else if (order.equals(SwtConstants.MULTI_MVT_ENTITY)) {
+					// column Alert Date
+					lstColumns.add(new ColumnInfo(
+							SwtUtil.getMessage(SwtConstants.MULTI_MVT_ENTITY_HEADER, request),
+							SwtConstants.MULTI_MVT_ENTITY,
+							SwtConstants.COLUMN_TYPE_STRING, 2,
+							Integer.parseInt(widths
+									.get(SwtConstants.MULTI_MVT_ENTITY)),
+							true, true, hiddenColumnsMap
+							.get(SwtConstants.MULTI_MVT_ENTITY)));
+				} else if (order.equals(SwtConstants.MULTI_MVT_CCY )) {
+					// column Alert Date
+					lstColumns.add(new ColumnInfo(
+							SwtUtil.getMessage(SwtConstants.MULTI_MVT_CCY_HEADER, request),
+							SwtConstants.MULTI_MVT_CCY ,
+							SwtConstants.COLUMN_TYPE_STRING, 3,
+							Integer.parseInt(widths
+									.get(SwtConstants.MULTI_MVT_CCY )),
+							true, true, hiddenColumnsMap
+							.get(SwtConstants.MULTI_MVT_CCY )));
+				} else if (order.equals(SwtConstants.MULTI_MVT_VDate)) {
+					// column Alert Date
+					lstColumns.add(new ColumnInfo(
+							SwtUtil.getMessage(SwtConstants.MULTI_MVT_VDate_HEADER, request),
+							SwtConstants.MULTI_MVT_VDate,
+							SwtConstants.COLUMN_TYPE_DATE, 4,
+							Integer.parseInt(widths
+									.get(SwtConstants.MULTI_MVT_VDate)),
+							true, true, true));
+				}else if (order.equals(SwtConstants.MULTI_MVT_ACCOUNT)) {
+					// column Alert Time
+					lstColumns.add(new ColumnInfo(
+							SwtUtil.getMessage(SwtConstants.MULTI_MVT_ACCOUNT_HEADER, request),
+							SwtConstants.MULTI_MVT_ACCOUNT,
+							SwtConstants.COLUMN_TYPE_STRING, 5,
+							Integer.parseInt(widths
+									.get(SwtConstants.MULTI_MVT_ACCOUNT)),
+							true, true, hiddenColumnsMap
+							.get(SwtConstants.MULTI_MVT_ACCOUNT)));
+				} else if (order.equals(SwtConstants.MULTI_MVT_AMOUNT)) {
+					// column Alert Status
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_AMOUNT_HEADER, request),
+									SwtConstants.MULTI_MVT_AMOUNT,
+									SwtConstants.COLUMN_TYPE_NUMBER,
+									6,
+									Integer.parseInt(widths
+											.get(SwtConstants.MULTI_MVT_AMOUNT)),
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_AMOUNT)));
+				} else if (order.equals(SwtConstants.MULTI_MVT_SIGN )) {
+					// column Alert Type
+					lstColumns.add(new ColumnInfo(
+							SwtUtil.getMessage(SwtConstants.MULTI_MVT_SIGN_HEADER, request),
+							SwtConstants.MULTI_MVT_SIGN ,
+							SwtConstants.COLUMN_TYPE_STRING, 7,
+							Integer.parseInt(widths
+									.get(SwtConstants.MULTI_MVT_SIGN )),
+							true, true, hiddenColumnsMap
+							.get(SwtConstants.MULTI_MVT_SIGN )));
+				} else if (order
+						.equals(SwtConstants.MULTI_MVT_PRED)) {
+					// column Risk Weight
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_PRED_HEADER, request),
+									SwtConstants.MULTI_MVT_PRED,
+									SwtConstants.COLUMN_TYPE_STRING,
+									8,
+									Integer.parseInt(widths
+											.get(SwtConstants.MULTI_MVT_PRED)),
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_PRED)));
+				} else if (order
+						.equals(SwtConstants.MULTI_MVT_EXT)) {
+					// column Account ID
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_EXT_HEADER, request),
+									SwtConstants.MULTI_MVT_EXT,
+									SwtConstants.COLUMN_TYPE_STRING,
+									9,
+									Integer.parseInt(widths
+											.get(SwtConstants.MULTI_MVT_EXT)),
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_EXT)));
+				} else if (order
+						.equals(SwtConstants.MULTI_MVT_ILMFCAST)) {
+					// column Client ID
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_ILMFCAST_HEADER, request),
+									SwtConstants.MULTI_MVT_ILMFCAST,
+									SwtConstants.COLUMN_TYPE_STRING,
+									10,
+									Integer.parseInt(widths
+											.get(SwtConstants.MULTI_MVT_ILMFCAST)),
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_ILMFCAST)));
+				} else if (order
+						.equals(SwtConstants.MULTI_MVT_MATCH_STATUS)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_MATCH_STATUS_HEADER, request),
+									SwtConstants.MULTI_MVT_MATCH_STATUS,
+									SwtConstants.COLUMN_TYPE_STRING,
+									11,
+									Integer.parseInt(widths
+											.get(SwtConstants.MULTI_MVT_MATCH_STATUS)),
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_MATCH_STATUS)));
+
+				} else if (order
+						.equals(SwtConstants.MULTI_MVT_REF1)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_REF1_HEADER, request),
+									SwtConstants.MULTI_MVT_REF1,
+									SwtConstants.COLUMN_TYPE_STRING,
+									12,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_REF1))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_REF1))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_REF1)));
+				}else if (order
+						.equals(SwtConstants.MULTI_MVT_REF2)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_REF2_HEADER, request),
+									SwtConstants.MULTI_MVT_REF2,
+									SwtConstants.COLUMN_TYPE_STRING,
+									13,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_REF2))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_REF2))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_REF2)));
+				}
+				else if (order
+						.equals(SwtConstants.MULTI_MVT_EXTRA_REF)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_EXTRA_REF_HEADER, request),
+									SwtConstants.MULTI_MVT_EXTRA_REF,
+									SwtConstants.COLUMN_TYPE_STRING,
+									14,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_EXTRA_REF))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_EXTRA_REF))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_EXTRA_REF)));
+				}	else if (order
+						.equals(SwtConstants.MULTI_MVT_BOOK)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_BOOK_HEADER, request),
+									SwtConstants.MULTI_MVT_BOOK,
+									SwtConstants.COLUMN_TYPE_STRING,
+									15,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_BOOK))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_BOOK))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_BOOK)));
+				}	else if (order
+						.equals(SwtConstants.MULTI_MVT_MATCH_ID)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_MATCH_ID_HEADER, request),
+									SwtConstants.MULTI_MVT_MATCH_ID,
+									SwtConstants.COLUMN_TYPE_NUMBER,
+									16,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_MATCH_ID))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_MATCH_ID))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_MATCH_ID)));
+				}else if (order
+						.equals(SwtConstants.MULTI_MVT_SOURCE)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_SOURCE_HEADER, request),
+									SwtConstants.MULTI_MVT_SOURCE,
+									SwtConstants.COLUMN_TYPE_STRING,
+									17,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_SOURCE))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_SOURCE))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_SOURCE)));
+				}else if (order
+						.equals(SwtConstants.MULTI_MVT_FORMAT)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_FORMAT_HEADER, request),
+									SwtConstants.MULTI_MVT_FORMAT,
+									SwtConstants.COLUMN_TYPE_STRING,
+									18,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_FORMAT))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_FORMAT))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_FORMAT)));
+				}else if (order
+						.equals(SwtConstants.MULTI_MVT_CPARTY)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_CPARTY_HEADER, request),
+									SwtConstants.MULTI_MVT_CPARTY,
+									SwtConstants.COLUMN_TYPE_STRING,
+									19,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_CPARTY))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_CPARTY))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_CPARTY)));
+				}else if (order
+						.equals(SwtConstants.MULTI_MVT_ORD_INST)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_ORD_INST_HEADER, request),
+									SwtConstants.MULTI_MVT_ORD_INST,
+									SwtConstants.COLUMN_TYPE_STRING,
+									20,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_ORD_INST))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_ORD_INST))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_ORD_INST)));
+				}else if (order
+						.equals(SwtConstants.MULTI_MVT_EXP_SETTLEMENT)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_EXP_SETTLEMENT_HEADER, request),
+									SwtConstants.MULTI_MVT_EXP_SETTLEMENT,
+									SwtConstants.COLUMN_TYPE_STRING,
+									21,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_EXP_SETTLEMENT))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_EXP_SETTLEMENT))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_EXP_SETTLEMENT)));
+				}else if (order
+						.equals(SwtConstants.MULTI_MVT_ACT_SETTLEMENT)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_ACT_SETTLEMENT_HEADER, request),
+									SwtConstants.MULTI_MVT_ACT_SETTLEMENT,
+									SwtConstants.COLUMN_TYPE_STRING,
+									22,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_ACT_SETTLEMENT))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_ACT_SETTLEMENT))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_ACT_SETTLEMENT)));
+				}else if (order
+						.equals(SwtConstants.MULTI_MVT_CRIT_PAY_TYPE)) {
+					// column Client Name
+					lstColumns
+							.add(new ColumnInfo(
+									SwtUtil.getMessage(SwtConstants.MULTI_MVT_CRIT_PAY_TYPE_HEADER, request),
+									SwtConstants.MULTI_MVT_CRIT_PAY_TYPE,
+									SwtConstants.COLUMN_TYPE_STRING,
+									23,
+									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MULTI_MVT_CRIT_PAY_TYPE))
+											? Integer.parseInt(widths.get(SwtConstants.MULTI_MVT_CRIT_PAY_TYPE))
+											: 125,
+									true,
+									true,
+									hiddenColumnsMap
+											.get(SwtConstants.MULTI_MVT_CRIT_PAY_TYPE)));
+				}
+
+
+			}
+		} catch (Exception ex) {
+			// log error message
+			log.error(this.getClass().getName()
+					  + " - Exception Caught in [getGridColumns] method : - "
+					  + ex.getMessage());
+
+			throw SwtErrorHandler.getInstance().handleException(ex,
+					"getGridColumns", this.getClass());
+
+		} finally {
+			// Nullify objects
+			orders = null;
+			columnOrderProp = null;
+			columnOrderItr = null;
+			widths = null;
+			columnWidthProperty = null;
+
+			// log debug message
+			log.debug(this.getClass().getName()
+					  + " - [ getGridColumns ] - Exit");
+		}
+		// return xml column
+		return lstColumns;
+	}
+
+
+	public String checkMovements() throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+		HttpServletResponse response = ServletActionContext.getResponse();;
+		ResponseHandler responseHandler = null;
+		SwtResponseConstructor responseConstructor = null;
+		SwtXMLWriter xmlWriter = null;
+		String width = null;
+		String columnOrder = null;
+		String hiddenColumns = null;
+		Integer currencyFound=null;
+		// To hold user entity access
+		Long UserEntityAccess;
+		String entityId=null;
+		String movementId=null;
+		String currencyCode= null;
+		String componentId = null;
+		Movement  movement = null;
+		String hostId=null;
+		String userId= null;
+		HashMap<String, Boolean> hasAccesstoEntityCcyMap = new HashMap<String, Boolean>();
+		// To hold the array of preAdvices
+		String data;
+		// To hold the collection for user entity access list
+		Collection<EntityUserAccess> collUserEntity = null;
+		Integer mvtAccess=null;
+		boolean hasAccesstoEntity=false;
+		boolean isLocked=false;
+		String mvtIdLabelInFile= null;
+		String amountFormat= null;
+		SystemFormats sysFormat=null;
+		String dateFormat= null;
+		int mvtNotFoundCount = 0; // Counter for movements not found
+		int viewAccessCount = 0; // Counter for view access
+		int unlockedMvtCount = 0; // Counter for locked movements
+		int successMvtCount = 0; // Counter for successful movements
+
+		Collection<BookCode> collBookCode = null;
+		Iterator<BookCode> itrBookCode = null;
+
+		// StringBuilder to store movement IDs with view access or not found
+		StringBuilder movementIdsNotFound = new StringBuilder();
+		StringBuilder recordsWithViewAccess = new StringBuilder();
+		StringBuilder unlockedMovements = new StringBuilder();
+		StringBuilder lockedMovements = new StringBuilder();
+		Map<String, Integer> entityIdCounts = new HashMap<>();
+		try {
+			// debug message
+			log.debug(this.getClass().getName()
+					  + " - [ checkMovements ] - Entry");
+			// to get the host id for application
+			hostId = CacheManager.getInstance().getHostId();
+			userId = SwtUtil.getCurrentUserId(request.getSession());
+			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
+			amountFormat=sysFormat.getCurrencyFormat();
+			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
+			//get the array of preAdvices from request
+			data = request.getParameter("movementList");
+			mvtIdLabelInFile=!SwtUtil.isEmptyOrNull(request.getParameter("mvtIdLabelInFile"))?request.getParameter("mvtIdLabelInFile"):"MovementID";
+			CurrencyManager currencyManager = (CurrencyManager) SwtUtil
+					.getBean("currencyManager");
+			if(!SwtUtil.isEmptyOrNull(data)) {
+				JSONArray mvtJSONArray = new JSONArray(data);
+				responseHandler = new ResponseHandler();
+				responseConstructor = new SwtResponseConstructor();
+				xmlWriter = responseConstructor.getXMLWriter();
+
+				xmlWriter.startElement(SwtConstants.MULTI_MVT_ACTIONS);
+
+				responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
+						SwtConstants.DATA_FETCH_OK);
+
+				/******* mvtGrid ******/
+				responseConstructor.formGridStart("mvtGrid");
+				responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request));
+				responseConstructor.formRowsStart(mvtJSONArray.length());
+
+				for (int i = 0; i < mvtJSONArray.length(); i++) {
+					List<String> toolTip = new ArrayList<String>();
+					movementId = String.valueOf(mvtJSONArray.get(i));
+					MovementManager movementManager = (MovementManager) SwtUtil.getBean("movementManager");
+					Long movId = new Long(movementId);
+					movement = movementManager.getMovementDetails(hostId, movId);
+					if (movement != null) {
+						entityId = movement.getId().getEntityId();
+						currencyCode = movement.getCurrencyCode();
+						/*******check full entity/currency access******/
+						if (hasAccesstoEntityCcyMap.get(hostId + entityId + currencyCode) != null) {
+							hasAccesstoEntity = hasAccesstoEntityCcyMap.get(hostId + entityId + currencyCode);
+						} else {
+							hasAccesstoEntity = SwtUtil.getFullAccesOnCurrencyAndEntity(request, hostId, entityId, currencyCode);
+							hasAccesstoEntityCcyMap.put(hostId + entityId + currencyCode, hasAccesstoEntity);
+						}
+						if (hasAccesstoEntity) {
+							//check if movement is locked
+							isLocked = multiMvtUpdatesMgr.checkMvmtLockStatus(hostId, entityId, currencyCode, Long.valueOf(movementId), userId);
+							if (!isLocked) {
+								mvtAccess = -1;
+								unlockedMvtCount++;
+								unlockedMovements.append(movementId).append(",");
+							} else {
+								mvtAccess = 0;
+								successMvtCount++;
+								lockedMovements.append(movementId).append(",");
+							}
+
+						} else {
+							mvtAccess = -1;
+							viewAccessCount++;
+							recordsWithViewAccess.append(movementId).append(",");
+						}
+
+						responseConstructor.formRowStart();
+
+						String currentEntityId = movement.getId().getEntityId();
+						entityIdCounts.put(currentEntityId, entityIdCounts.getOrDefault(currentEntityId, 0) + 1);
+
+
+						if(movement
+								   .getExpectedSettlementDateTime() != null) {
+							movement.setExpectedSettlementDateTimeAsString((SwtUtil.formatDate(movement
+									.getExpectedSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
+									request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
+						}
+						if(movement
+								   .getSettlementDateTime() != null) {
+							movement.setSettlementDateTimeAsString((SwtUtil.formatDate(movement
+									.getSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
+									request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
+						}
+
+
+
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_SELECT, mvtAccess==0?"Y":"N");
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ACCESS, mvtAccess==0?"Y":"N");
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_MOVEMENT_ID, movement.getId().getMovementId().toString());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ENTITY, movement.getId().getEntityId());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_CCY,movement.getCurrencyCode());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_VDate,   SwtUtil.formatDate(movement.getValueDate(), dateFormat ));
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ACCOUNT, movement.getAccountId());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_AMOUNT, SwtUtil.formatCurrency(movement
+								.getCurrencyCode(), new BigDecimal(movement.getAmount()),amountFormat));
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_SIGN, movement.getSign());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_PRED, movement.getPredictStatus());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_EXT, movement.getExtractStatus());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ILMFCAST, movement.getIlmFcastStatus());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_MATCH_STATUS, movement.getMatchStatus());
+
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_REF1, movement.getReference1());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_REF2, movement.getReference2());
+						//to be checked
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_EXTRA_REF, movement.getReference4());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_BOOK, movement.getBookCode());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_MATCH_ID, movement.getMatchId()!=null?String.valueOf(movement.getMatchId()):"");
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_SOURCE, movement.getInputSource());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_FORMAT, movement.getMessageFormat());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_CPARTY, movement.getCounterPartyId());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ORD_INST, movement.getOrderingInstitutionId());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_EXP_SETTLEMENT, movement.getExpectedSettlementDateTimeAsString());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_ACT_SETTLEMENT, movement.getSettlementDateTimeAsString());
+						responseConstructor.createRowElement(SwtConstants.MULTI_MVT_CRIT_PAY_TYPE, movement.getCriticalPaymentType());
+						responseConstructor.formRowEnd();
+
+
+
+					} else {
+						mvtAccess = -1;
+						mvtNotFoundCount++;
+						movementIdsNotFound.append(movementId).append(",");
+					}
+					//mvtJSONArray.getJSONObject(i).put("access", mvtAccess);
+				}
+				responseConstructor.formRowsEnd();
+				responseConstructor.formGridEnd();
+
+			}
+
+
+			int maxCount = 0;
+			String mostFrequentEntityId = null;
+			for (Map.Entry<String, Integer> entry : entityIdCounts.entrySet()) {
+				if (entry.getValue() > maxCount) {
+					maxCount = entry.getValue();
+					mostFrequentEntityId = entry.getKey();
+				}
+			}
+
+			if (mostFrequentEntityId != null) {
+				entityId = mostFrequentEntityId;
+			}
+
+
+			/***** Data source Combo Start ***********/
+			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
+			// options drop down list
+			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();
+			lstOptions = new ArrayList<OptionInfo>();
+			/***** Book Code list Combo Start ***********/
+
+			// get the collection of book code for host id,entity id
+			collBookCode = ((MovementManager) (SwtUtil.getBean("movementManager")))
+					.getBookCodeList(CacheManager.getInstance().getHostId(), entityId);
+			// options drop down list
+			lstOptions = new ArrayList<OptionInfo>();
+			if (collBookCode != null) {
+				// iterate the book code collection
+				itrBookCode = collBookCode.iterator();
+				lstOptions.add(new OptionInfo("","", false));
+				while (itrBookCode.hasNext()) {
+					// get the bookcode
+					BookCode bookCode = itrBookCode.next();
+					lstOptions.add(new OptionInfo(bookCode.getId().getBookCode(),bookCode.getBookName(), false));
+
+				}
+				lstSelect.add(new SelectInfo("bookCodeList", lstOptions));
+			}
+			/***** Book Code list Combo End ***********/
+
+			responseConstructor.formSelect(lstSelect);
+
+
+			// forms singleton node
+			xmlWriter.startElement(SwtConstants.SINGLETONS);
+			responseConstructor.createElement("viewAccessCount", viewAccessCount);
+			responseConstructor.createElement("mvtNotFoundCount", mvtNotFoundCount);
+			responseConstructor.createElement("unlockedMvtCount", unlockedMvtCount);
+			responseConstructor.createElement("successMvtCount", successMvtCount);
+			responseConstructor.createElement("movementIdsNotFound", movementIdsNotFound.toString());
+			responseConstructor.createElement("recordsWithViewAccess", recordsWithViewAccess.toString());
+			responseConstructor.createElement("unlockedMovements", unlockedMovements.toString());
+			responseConstructor.createElement("lockedMovements", lockedMovements.toString());
+			xmlWriter.endElement(SwtConstants.SINGLETONS);
+
+			xmlWriter.endElement(SwtConstants.MULTI_MVT_ACTIONS);
+
+			request.setAttribute("data", xmlWriter.getData());
+			return ("data");
+		} catch (SwtException swtexp) {
+			log.error(this.getClass().getName() + " - Exception Catched in [checkMovements] method : - " + swtexp.getMessage());
+			SwtUtil.logException(swtexp, request, "");
+			request.setAttribute("reply_status_ok", "false");
+			request.setAttribute("reply_message", swtexp.getMessage());
+			return getExceptionXmlData(swtexp.getMessage(), request);
+
+		} catch (Exception exp) {
+
+			log.error(this.getClass().getName() + " - Exception Catched in [checkMovements] method : - " + exp.getMessage());
+
+			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "checkPreAdviceInputAccess", MultipleMovementUpdatesAction.class),
+					request, "");
+			request.setAttribute("reply_status_ok", "false");
+			request.setAttribute("reply_message", exp.getMessage());
+			return getExceptionXmlData(exp.getMessage(), request);
+		} finally {
+			responseConstructor = null;
+			xmlWriter=null;
+			entityId=null;
+			currencyCode= null;
+			componentId = null;
+			movement = null;
+			hostId=null;
+			log.debug(this.getClass().getName() + "- [checkMovements] - Exit");
+		}
+
+	}
+
+	public String exportMovements() throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+		HttpServletResponse response = ServletActionContext.getResponse();;
+
+		List<Movement> movements = new ArrayList<>();
+		try {
+			log.debug(this.getClass().getName() + " - [exportMovements()] - Entry");
+
+			// Get the movement IDs from request
+		/*	String movementListParam = request.getParameter("movementList");
+			if (movementListParam == null || movementListParam.trim().isEmpty()) {
+				log.error("No movement IDs provided!");
+				return ("fail");
+			}
+
+			// Convert the movement IDs string into a List<Long>
+			List<Long> movementIds = new ArrayList<>();
+			for (String id : movementListParam.split(",")) {
+				try {
+					movementIds.add(Long.parseLong(id.trim()));
+				} catch (NumberFormatException e) {
+					log.error("Invalid movement ID: " + id);
+				}
+			}
+
+			// If no valid IDs were found, return failure
+			if (movementIds.isEmpty()) {
+				log.error("No valid movement IDs found!");
+				return ("fail");
+			}
+
+			MovementManager movementManager = (MovementManager) SwtUtil.getBean("movementManager");
+
+			// Process each movement ID
+			for (Long movId : movementIds) {
+				Movement movement = movementManager.getMovementDetails(SwtUtil.getCurrentHostId(), movId);
+
+				if (movement != null) {
+					// Randomly decide if the movement is successful or failed (50-50)
+					boolean isSuccessful = Math.random() < 0.5; // 50% chance
+
+					movement.setSuccessful(isSuccessful);
+					if (!isSuccessful) {
+						movement.setFailingCause("Random Failure Reason for Testing");
+					}
+
+					movements.add(movement);
+				} else {
+					log.warn("No details found for movement ID: " + movId);
+				}
+			}*/
+
+			String seq = request.getParameter("seq");
+			ProcessStatus processStatus = multiMvtUpdatesMgr.getProcessDetails(seq, "Y");
+			if (processStatus == null) {
+				log.error("No process details found for sequence: " + seq);
+				return ("fail");
+			}
+			for (Movement movement : processStatus.getSuccessfulMovements()) {
+				movement.setSuccessful(true);
+				movements.add(movement);
+			}
+			for (Movement movement : processStatus.getFailedMovements()) {
+				movement.setSuccessful(false);
+				movements.add(movement);
+			}
+
+			// Export to Excel
+			try {
+				MultipleMovementExcelReportGenerator.exportReport(response,request, movements);
+			} catch (IOException e) {
+				throw new RuntimeException(e);
+			}
+
+		} catch (SwtException swtexp) {
+			log.error("SwtException in exportMovements: " + swtexp.getMessage());
+			SwtUtil.logException(swtexp, request, "");
+			return ("fail");
+		}
+		return null;
+	}
+
+
+
+	public String getExceptionXmlData(String expMesage, HttpServletRequest request)
+			throws SwtException {
+		SwtResponseConstructor responseConstructor = null;
+		SwtXMLWriter xmlWriter = null;
+		String userId = null;
+		String componentId = null;
+		// debug message
+		log.debug(this.getClass().getName() + " - [ getExceptionXmlData ] - Entry");
+
+		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
+		responseConstructor = new SwtResponseConstructor();
+
+		xmlWriter = responseConstructor.getXMLWriter();
+		// Get component ID
+		componentId = "preAdviceInput";
+		// Adding screen id and current user id as attributes
+		xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
+		xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
+		xmlWriter.startElement(PCMConstant.EXCEPTION_TAG);
+		xmlWriter.clearAttribute();
+		if (expMesage != null)
+			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_FALSE), expMesage);
+		else
+			responseConstructor.formRequestReply(false);
+		xmlWriter.endElement(PCMConstant.EXCEPTION_TAG);
+
+		request.setAttribute("data", xmlWriter.getData());
+
+		return ("data");
+	}
+
+
+	public String openMultiMvtActions() throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+		HttpServletResponse response = ServletActionContext.getResponse();;
+		request.setAttribute("selectedMvtIdsList", request.getParameter("selectedMvtIdsList"));
+		request.setAttribute("entityId", request.getParameter("entityId"));
+		request.setAttribute("fromMenu", "false");
+		return ("multiMvtUpdate");
+	}
+
+
+
+	// Add a new method to cancel a running task
+	public String cancelAction() throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+
+		try {
+			log.debug(this.getClass().getName() + " - [cancelAction()] - Entry");
+			String seq = request.getParameter("seq");
+
+			boolean cancelled = BackgroundTaskManager.cancelTask("MULTI_MVT_" + seq);
+
+			ResponseHandler responseHandler = new ResponseHandler();
+			SwtResponseConstructor responseConstructor = new SwtResponseConstructor();
+			SwtXMLWriter xmlWriter = responseConstructor.getXMLWriter();
+
+			xmlWriter.startElement(SwtConstants.MULTI_MVT_ACTIONS);
+			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
+					SwtConstants.DATA_FETCH_OK);
+
+			xmlWriter.startElement(SwtConstants.SINGLETONS);
+			responseConstructor.createElement("seq", seq);
+			responseConstructor.createElement("cancelled", String.valueOf(cancelled));
+			xmlWriter.endElement(SwtConstants.SINGLETONS);
+
+			xmlWriter.endElement(SwtConstants.MULTI_MVT_ACTIONS);
+			request.setAttribute("data", xmlWriter.getData());
+
+			log.debug(this.getClass().getName() + " - [cancelAction()] - Exit");
+			return ("data");
+		} catch (Exception exp) {
+			log.error("Exception Catch in MultipleMovementUpdatesAction.'cancelAction' method : "
+					  + exp.getMessage());
+			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
+					exp, "display", MultipleMovementUpdatesAction.class), request, "");
+			return ("fail");
+		}
+	}
+
+
+	public String processAction() throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+		HttpServletResponse response = ServletActionContext.getResponse();;
+
+		String userId = null;
+		String listMovements = null;
+		String action = null;
+		String jsonValues = null;
+		String noteText = null;
+		Long seq = null;
+		ResponseHandler responseHandler = null;
+		SwtResponseConstructor responseConstructor = null;
+		SwtXMLWriter xmlWriter = null;
+
+		try {
+			log.debug(this.getClass().getName() + " - [processAction()] - Entry");
+
+			// Extract parameters with validation
+			userId = SwtUtil.getCurrentUserId(request.getSession());
+			if (userId == null || userId.trim().isEmpty()) {
+				throw new SwtException("User ID is missing or invalid");
+			}
+
+			listMovements = request.getParameter("movementList");
+			if (listMovements == null) {
+				throw new SwtException("Movement list parameter is missing");
+			}
+
+			action = request.getParameter("p_action");
+			if (action == null || action.trim().isEmpty()) {
+				throw new SwtException("Action parameter is missing or invalid");
+			}
+
+			jsonValues = request.getParameter("p_json_values");
+			noteText = request.getParameter("p_note_text");
+
+			// Generate sequence with error handling
+			try {
+				seq = SequenceFactory.getSequenceFromDbAsLong("SEQ_P_MULTIPLE_MOV_ACTION");
+				if (seq == null) {
+					throw new SwtException("Failed to generate sequence ID");
+				}
+			} catch (Exception e) {
+				throw new SwtException("Error generating sequence: " + e.getMessage(), e);
+			}
+
+			// Submit task to execute in background
+			final String finalUserId = userId;
+			final String finalListMovements = listMovements;
+			final String finalAction = action;
+			final String finalJsonValues = jsonValues;
+			final String finalNoteText = noteText;
+			final Long finalSeq = seq;
+
+			BackgroundTaskManager.submitTask("MULTI_MVT_" + seq, () -> {
+				try {
+					multiMvtUpdatesMgr.executeMainProcedure(
+							finalListMovements,
+							finalAction,
+							finalJsonValues,
+							finalNoteText,
+							finalUserId,
+							String.valueOf(finalSeq)
+					);
+					log.info("Background task completed successfully for sequence: " + finalSeq);
+				} catch (Exception e) {
+					log.error("Error in background execution for sequence " + finalSeq + ": " + e.getMessage(), e);
+				}
+			});
+
+			// Construct response
+			responseHandler = new ResponseHandler();
+			responseConstructor = new SwtResponseConstructor();
+			xmlWriter = responseConstructor.getXMLWriter();
+
+			xmlWriter.startElement(SwtConstants.MULTI_MVT_ACTIONS);
+			responseConstructor.formRequestReply(Boolean.TRUE, SwtConstants.DATA_FETCH_OK);
+
+			xmlWriter.startElement(SwtConstants.SINGLETONS);
+			responseConstructor.createElement("seq", String.valueOf(seq));
+			responseConstructor.createElement("status", "STARTED");
+			responseConstructor.createElement("timestamp", String.valueOf(System.currentTimeMillis()));
+			xmlWriter.endElement(SwtConstants.SINGLETONS);
+
+			xmlWriter.endElement(SwtConstants.MULTI_MVT_ACTIONS);
+			request.setAttribute("data", xmlWriter.getData());
+
+			log.debug(this.getClass().getName() + " - [processAction()] - Exit");
+			return ("data");
+		} catch (SwtException swtexp) {
+			log.error("SwtException in processAction: " + swtexp.getMessage(), swtexp);
+			SwtUtil.logException(swtexp, request, "");
+
+			// Create error response
+			try {
+				responseConstructor = new SwtResponseConstructor();
+				xmlWriter = responseConstructor.getXMLWriter();
+				xmlWriter.startElement(SwtConstants.MULTI_MVT_ACTIONS);
+				responseConstructor.formRequestReply(Boolean.FALSE, swtexp.getMessage());
+				xmlWriter.endElement(SwtConstants.MULTI_MVT_ACTIONS);
+				request.setAttribute("data", xmlWriter.getData());
+			} catch (Exception e) {
+				log.error("Error creating error response: " + e.getMessage(), e);
+			}
+
+			return ("fail");
+		} catch (Exception exp) {
+			log.error("Exception in processAction: " + exp.getMessage(), exp);
+			SwtException swtexp = SwtErrorHandler.getInstance().handleException(
+					exp, "display", MultipleMovementUpdatesAction.class);
+			SwtUtil.logException(swtexp, request, "");
+
+			// Create error response
+			try {
+				responseConstructor = new SwtResponseConstructor();
+				xmlWriter = responseConstructor.getXMLWriter();
+				xmlWriter.startElement(SwtConstants.MULTI_MVT_ACTIONS);
+				responseConstructor.formRequestReply(Boolean.FALSE, exp.getMessage());
+				xmlWriter.endElement(SwtConstants.MULTI_MVT_ACTIONS);
+				request.setAttribute("data", xmlWriter.getData());
+			} catch (Exception e) {
+				log.error("Error creating error response: " + e.getMessage(), e);
+			}
+
+			return ("fail");
+		}
+	}
+
+
+	public String checkProcessStatus() throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+		HttpServletResponse response = ServletActionContext.getResponse();;
+
+		HttpSession session = request.getSession();
+		int processStatus = 0;
+		ProcessStatus processDetails = null;
+		SwtResponseConstructor responseConstructor = null;
+		SwtXMLWriter xmlWriter = null;
+
+
+		try {
+			log.debug(this.getClass().getName() + " - [checkProcessStatus()] - Entry");
+			String seq = request.getParameter("seq");
+			// Retrieve the current progress from the session
+			/*Integer progress = (Integer) session.getAttribute("processProgress");
+			if (progress == null || progress != 100) {
+				progress = 0; // Start progress at 0
+			}
+
+			// Simulate progress increase
+			if (progress < 100) {
+				progress += 10; // Increase progress
+			}
+
+			// Store updated progress back in the session
+			session.setAttribute("processProgress", progress);
+
+			log.debug("Current Process Progress: " + progress);
+
+*/
+
+
+			if (!SwtUtil.isEmptyOrNull(seq)) {
+				// Call the function to get the process status
+				processDetails = multiMvtUpdatesMgr.getProcessDetails(seq, "N");
+
+			}
+
+			responseConstructor = new SwtResponseConstructor();
+			xmlWriter = responseConstructor.getXMLWriter();
+
+			xmlWriter.startElement(SwtConstants.MULTI_MVT_ACTIONS);
+			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
+
+			xmlWriter.startElement(SwtConstants.SINGLETONS);
+			responseConstructor.createElement("seq", seq); // Returning the sequence ID
+
+			int totalCount = (processDetails != null) ? processDetails.getTotalCount() : 0;
+			int pendingCount = (processDetails != null) ? processDetails.getPendingCount() : 0;
+			int processPercentage = (totalCount > 0) ? ((totalCount - pendingCount) * 100) / totalCount : 0;
+
+			responseConstructor.createElement("processStatus", String.valueOf(processPercentage));
+			responseConstructor.createElement("pendingCount", String.valueOf(pendingCount));
+			responseConstructor.createElement("totalCount", String.valueOf(totalCount));
+
+			if((processDetails != null ? processDetails.getPendingCount() : 0) == 0) {
+				processDetails = multiMvtUpdatesMgr.getProcessDetails(seq, "Y");
+				responseConstructor.createElement("failedCount", String.valueOf(processDetails.getFailedCount()));
+				responseConstructor.createElement("successCount", String.valueOf(processDetails.getSuccessCount()));
+			}
+
+			xmlWriter.endElement(SwtConstants.SINGLETONS);
+
+			xmlWriter.endElement(SwtConstants.MULTI_MVT_ACTIONS);
+			request.setAttribute("data", xmlWriter.getData());
+
+			log.debug(this.getClass().getName() + " - [checkProcessStatus()] - Exit");
+			return ("data");
+			//response.getWriter().print(progress); // Send progress percentage to client
+			//return null;
+
+		} catch (Exception exp) {
+			log.error("Exception in checkProcessStatus: " + exp.getMessage());
+			return ("fail");
+		}
+	}
+
+
+	public String cleanTempProcess() throws SwtException {
+		HttpServletRequest request = ServletActionContext.getRequest();
+		SwtResponseConstructor responseConstructor = null;
+		SwtXMLWriter xmlWriter = null;
+
+		String seq = request.getParameter("seq");
+
+		try {
+			log.debug(this.getClass().getName() + " - [cleanTempProcess()] - Entry");
+
+			multiMvtUpdatesMgr.cleanTempProcess(seq);
+
+			responseConstructor = new SwtResponseConstructor();
+			xmlWriter = responseConstructor.getXMLWriter();
+
+			xmlWriter.startElement(SwtConstants.MULTI_MVT_ACTIONS);
+			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
+
+			xmlWriter.startElement(SwtConstants.SINGLETONS);
+			responseConstructor.createElement("seq", seq); // Returning the sequence ID
+			responseConstructor.createElement("status", "cleaned"); // Confirm process cleanup
+			xmlWriter.endElement(SwtConstants.SINGLETONS);
+
+			xmlWriter.endElement(SwtConstants.MULTI_MVT_ACTIONS);
+			request.setAttribute("data", xmlWriter.getData());
+
+			log.debug(this.getClass().getName() + " - [cleanTempProcess()] - Exit");
+			return ("data");
+
+		} catch (SwtException swtexp) {
+			log.error("SwtException in cleanTempProcess: " + swtexp.getMessage());
+			SwtUtil.logException(swtexp, request, "");
+			return getExceptionXmlData(swtexp.getMessage(), request);
+
+		} catch (Exception exp) {
+			log.error("Exception in cleanTempProcess: " + exp.getMessage());
+			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "cleanTempProcess", MultipleMovementUpdatesAction.class), request, "");
+			return getExceptionXmlData(exp.getMessage(), request);
+
+		} finally {
+			responseConstructor = null;
+			xmlWriter = null;
+		}
+	}
+
+
+
+
+
+	public String checkMvtAccess() {
+		HttpServletRequest request = ServletActionContext.getRequest();
+		HttpServletResponse response = ServletActionContext.getResponse();;
+
+		boolean hasEntityCcyAccess = true;
+		boolean hasMmaAccess = false;
+		String checkFlag= null;
+		String entityIdList= null;
+		String currencyCodeList= null;
+		String hostId=null;
+		String roleId= null;
+		String movementList= null;
+		String componentId = null;
+		SwtResponseConstructor responseConstructor = null;
+		SwtXMLWriter xmlWriter = null;
+		try {
+			String pairList = request.getParameter("entityCurrencyPairs");
+			hostId = CacheManager.getInstance().getHostId();
+			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser().getRoleId();
+
+			if (pairList != null && !pairList.trim().isEmpty()) {
+				String[] pairs = pairList.split(",");
+
+				for (String pair : pairs) {
+					if (pair.contains(":")) {
+						String[] parts = pair.split(":");
+						if (parts.length == 2) {
+							String entityId = parts[0].trim();
+							String currencyCode = parts[1].trim();
+
+							if (!entityId.isEmpty() && !currencyCode.isEmpty()) {
+								if (!SwtUtil.getFullAccesOnCurrencyAndEntity(request, hostId, entityId, currencyCode)) {
+									hasEntityCcyAccess = false;
+									break; // Stop if access is granted for any invalid pair
+								}
+							}
+						}
+					}
+				}
+			}
+			hasMmaAccess= multiMvtUpdatesMgr.getRoleAccessDetails(hostId, roleId);
+			if (hasMmaAccess && hasEntityCcyAccess) {
+				checkFlag="true";
+			}else {
+				checkFlag="false";
+			}
+
+			responseConstructor = new SwtResponseConstructor();
+
+			xmlWriter = responseConstructor.getXMLWriter();
+			// Get component ID
+			componentId = "multiMvtActions";
+			// Adding screen id and current user id as attributes
+			xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
+			xmlWriter.addAttribute("checkFlag", checkFlag);
+			xmlWriter.startElement(SwtConstants.MULTI_MVT_ACTIONS);
+			xmlWriter.clearAttribute();
+			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
+			xmlWriter.endElement(SwtConstants.MULTI_MVT_ACTIONS);
+			request.setAttribute("data", xmlWriter.getData());
+			log.debug(this.getClass().getName() + " - [checkMvtAccess()] - Exit");
+			return ("data");
+		} catch (SwtException swtexp) {
+			log
+					.error("SwtException Catch in OutStandingMvmtAction.'checkMvtAccess' method : "
+						   + swtexp.getMessage());
+			SwtUtil.logException(swtexp, request, "");
+			return ("fail");
+		} catch (Exception exp) {
+			log
+					.error("Exception Catch in OutStandingMvmtAction.'checkMvtAccess' method : "
+						   + exp.getMessage());
+			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
+					exp, "checkMvtAccess", OutStandingMvmtAction.class), request, "");
+			return ("fail");
+		}finally {
+			//nullify objects
+			responseConstructor = null;
+			xmlWriter=null;
+			hasEntityCcyAccess = false;
+			hostId=null;
+			checkFlag = null;
+		}
+	}
+
+	private void putEditFlagsInRequest(HttpServletRequest request,
+									   List<Hashtable<String, Integer>> editFlagArrList) throws SwtException {
+		try {
+			log.debug(this.getClass().getName() + " - [putEditFlagsInRequest] - Entering ");
+			// Initialize flags for each field
+			boolean bookCodeEditStatus = true;
+			boolean preStatusEditStatus = true;
+			boolean extBalStatus = true;
+			boolean cpartyEditStatus = true;
+			boolean ilmFcastStatusEditStatus = true;
+			boolean expSettEditStatus = true;
+			boolean actualSettEditStatus = true;
+			boolean critPayTypeEditStatus = true;
+			boolean orederInstEditStatus = true;
+
+			// Iterate through each movement's edit flags
+			for (Hashtable<String, Integer> editFlagArr : editFlagArrList) {
+				// Check Book Code Edit Status
+				if (bookCodeEditStatus && (editFlagArr.get("Book Code") != null && editFlagArr.get("Book Code") != 1)) {
+					bookCodeEditStatus = false; // Found a movement that is not editable
+				}
+
+				// Check Predict Status Edit Status
+				if (preStatusEditStatus && (editFlagArr.get("Predict Status") != null && editFlagArr.get("Predict Status") != 1)) {
+					preStatusEditStatus = false; // Found a movement that is not editable
+				}
+
+				// Check External Status
+				if (extBalStatus && (editFlagArr.get("External Status") != null && editFlagArr.get("External Status") != 1)) {
+					extBalStatus = false; // Found a movement that is not editable
+				}
+
+				// Check Counterparty Edit Status
+				if (cpartyEditStatus && (editFlagArr.get("Counterparty") != null && editFlagArr.get("Counterparty") != 1)) {
+					cpartyEditStatus = false; // Found a movement that is not editable
+				}
+
+				// Check Ilm Forecast Status Edit Status
+				if (ilmFcastStatusEditStatus && (editFlagArr.get("Ilm Forcast Status") != null && editFlagArr.get("Ilm Forcast Status") != 1)) {
+					ilmFcastStatusEditStatus = false; // Found a movement that is not editable
+				}
+
+				// Check Exp Sett Date Edit Status
+				if (expSettEditStatus && (editFlagArr.get("Exp Sett Date") != null && editFlagArr.get("Exp Sett Date") != 1)) {
+					expSettEditStatus = false; // Found a movement that is not editable
+				}
+
+				// Check Act Sett Date Edit Status
+				if (actualSettEditStatus && (editFlagArr.get("Act Sett Date") != null && editFlagArr.get("Act Sett Date") != 1)) {
+					actualSettEditStatus = false; // Found a movement that is not editable
+				}
+
+				// Check Crit Pay Type Edit Status
+				if (critPayTypeEditStatus && (editFlagArr.get("Crit Pay Type") != null && editFlagArr.get("Crit Pay Type") != 1)) {
+					critPayTypeEditStatus = false; // Found a movement that is not editable
+				}
+
+				// Check Ordering Institution Edit Status
+				if (orederInstEditStatus && (editFlagArr.get("Ordering Institution") != null && editFlagArr.get("Ordering Institution") != 1)) {
+					orederInstEditStatus = false; // Found a movement that is not editable
+				}
+
+				// If all flags are false, we can break early
+				if (!bookCodeEditStatus && !preStatusEditStatus && !extBalStatus && !cpartyEditStatus &&
+					!ilmFcastStatusEditStatus && !expSettEditStatus && !actualSettEditStatus &&
+					!critPayTypeEditStatus && !orederInstEditStatus) {
+					break; // No need to check further
+				}
+			}
+			// Set the final status in the request
+			request.setAttribute("preStatusEditStatus", preStatusEditStatus ? "true" : "false");
+			request.setAttribute("extBalStatus", extBalStatus ? "true" : "false");
+			request.setAttribute("ilmFcastStatusEditStatus", ilmFcastStatusEditStatus ? "true" : "false");
+			request.setAttribute("bookCodeEditStatus", bookCodeEditStatus ? "true" : "false");
+			request.setAttribute("orederInstEditStatus", orederInstEditStatus ? "true" : "false");
+			request.setAttribute("critPayTypeEditStatus", critPayTypeEditStatus ? "true" : "false");
+			request.setAttribute("cpartyEditStatus", cpartyEditStatus ? "true" : "false");
+			request.setAttribute("expSettEditStatus", expSettEditStatus ? "true" : "false");
+			request.setAttribute("actualSettEditStatus", actualSettEditStatus ? "true" : "false");
+
+
+
+			log.debug(this.getClass().getName()
+					  + " - [putEditFlagsInRequest] - Existing ");
+		} catch (Exception exp) {
+			log
+					.error("Exception Catch in MultipleMovementUpdatesAction.'putEditFlagsInRequest' method : "
+						   + exp.getMessage());
+			throw SwtErrorHandler.getInstance().handleException(exp,
+					"putEditFlagsInRequest", MultipleMovementUpdatesAction.class);
+		}
+	}
+
+}
+
+
+
diff --git a/src/main/java/org/swallow/control/web/RoleAction.java b/src/main/java/org/swallow/control/web/RoleAction.java
index 61bcb650..26281a90 100644
--- a/src/main/java/org/swallow/control/web/RoleAction.java
+++ b/src/main/java/org/swallow/control/web/RoleAction.java
@@ -1365,6 +1365,10 @@ HttpServletResponse response = ServletActionContext.getResponse();
 				role.setMaintainAnyIlmGroup("N");
 			if (SwtUtil.isEmptyOrNull(role.getMaintainAnyReportHist()))
 				role.setMaintainAnyReportHist("N");
+
+			if (SwtUtil.isEmptyOrNull(role.getAllowMsdMultiMvtUpdates()))
+				role.setAllowMsdMultiMvtUpdates("N");
+
 			SystemInfo systemInfo = new SystemInfo();

 			roleMgr.updateRoleWithOtherdetails(role, htMenuAccess,
@@ -5848,4 +5852,4 @@ HttpServletResponse response = ServletActionContext.getResponse();



-}
\ No newline at end of file
+}
diff --git a/src/main/java/org/swallow/export/service/MultipleMovementExcelReportGenerator.java b/src/main/java/org/swallow/export/service/MultipleMovementExcelReportGenerator.java
new file mode 100644
index ********..1fd366f3
--- /dev/null
+++ b/src/main/java/org/swallow/export/service/MultipleMovementExcelReportGenerator.java
@@ -0,0 +1,367 @@
+package org.swallow.export.service;
+
+import org.apache.poi.ss.usermodel.*;
+import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
+import org.apache.poi.xssf.usermodel.XSSFCellStyle;
+import org.apache.poi.xssf.usermodel.XSSFColor;
+import org.apache.poi.xssf.usermodel.XSSFWorkbook;
+import org.swallow.exception.SwtException;
+import org.swallow.util.SwtUtil;
+import org.swallow.util.SystemFormats;
+import org.swallow.work.model.Movement;
+
+import javax.servlet.http.HttpServletRequest;
+import javax.servlet.http.HttpServletResponse;
+import java.io.ByteArrayOutputStream;
+import java.io.IOException;
+import java.math.BigDecimal;
+import java.util.List;
+
+
+public class MultipleMovementExcelReportGenerator {
+    static String amountFormat= null;
+    static SystemFormats sysFormat=null;
+    static String dateFormat= null;
+
+    private static final String[] HEADERS = {
+            "Movement ID", "Entity", "Currency", "Value Date", "Account", "Amount",
+            "Sign", "Prediction Status", "Extract Status", "ILM Forecast", "Match Status",
+            "Ref1", "Ref2", "Extra Ref", "Book Code", "Match ID", "Source",
+            "Format", "Counterparty", "Ordering Institution", "Expected Settlement", "Actual Settlement", "Critical Payment Type"
+    };
+
+    private static final String[] FAILURE_HEADERS = {
+            "Movement ID", "Entity", "Currency", "Value Date", "Account", "Amount",
+            "Sign", "Prediction Status", "Extract Status", "ILM Forecast", "Match Status",
+            "Ref1", "Ref2", "Extra Ref", "Book Code", "Match ID", "Source",
+            "Format", "Counterparty", "Ordering Institution", "Expected Settlement", "Actual Settlement", "Critical Payment Type",
+            "Failing Cause"
+    };
+
+    public static byte[] generateExcelReport(List<Movement> movements, HttpServletRequest request) throws IOException, SwtException {
+        Workbook workbook = new XSSFWorkbook();
+        Sheet summarySheet = workbook.createSheet("Summary");
+        Sheet failureSheet = workbook.createSheet("Failed Movements");
+        Sheet successSheet = workbook.createSheet("Success Movements");
+
+        // Create styles
+        CellStyle headerStyle = createHeaderStyle(workbook);
+        CellStyle borderStyle = createBorderStyle(workbook);
+        CellStyle alternateRowStyle = createAlternateRowStyle(workbook);
+        CellStyle failingCauseStyle = createFailingCauseStyle(workbook);
+
+        // Create headers
+        createHeaderRow(successSheet, HEADERS, headerStyle);
+        createHeaderRow(failureSheet, FAILURE_HEADERS, headerStyle);
+
+
+        int successRowNum = 1;
+        int failureRowNum = 1;
+        int successCount = 0;
+        int failureCount = 0;
+
+        for (Movement movement : movements) {
+            Row row;
+            boolean isEvenRow = (successRowNum % 2 == 0);
+
+
+            if(movement
+                    .getExpectedSettlementDateTime() != null) {
+                movement.setExpectedSettlementDateTimeAsString((SwtUtil.formatDate(movement
+                        .getExpectedSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
+                        request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
+            }
+            if(movement
+                    .getSettlementDateTime() != null) {
+                movement.setSettlementDateTimeAsString((SwtUtil.formatDate(movement
+                        .getSettlementDateTime(), SwtUtil.getCurrentSystemFormats(
+                        request.getSession()).getDateFormatValue().concat(" HH:mm:ss"))));
+            }
+
+            if (movement.isSuccessful()) {
+                row = successSheet.createRow(successRowNum++);
+                fillRow(row, movement,  isEvenRow ? alternateRowStyle : borderStyle);
+                successCount++;
+            } else {
+                row = failureSheet.createRow(failureRowNum++);
+                boolean isEvenFailedRow = (failureRowNum % 2 == 0);
+                fillRow(row, movement,  isEvenFailedRow ? alternateRowStyle : borderStyle);
+
+                // Apply failing cause styling
+                Cell failReasonCell = row.createCell(HEADERS.length);
+                failReasonCell.setCellValue(movement.getFailingCause());
+                failReasonCell.setCellStyle(failingCauseStyle);
+
+                failureCount++;
+            }
+        }
+
+        // Summary Sheet
+        createSummarySheet(summarySheet, successCount, failureCount, request, headerStyle, borderStyle);
+
+        // **Auto-size columns to fit content**
+        autoSizeColumns(successSheet, HEADERS.length);
+        autoSizeColumns(failureSheet, FAILURE_HEADERS.length);
+        autoSizeColumns(summarySheet, 2);
+
+        // Convert to byte array
+        ByteArrayOutputStream out = new ByteArrayOutputStream();
+        workbook.write(out);
+        workbook.close();
+
+        return out.toByteArray();
+    }
+
+    private static void createHeaderRow(Sheet sheet, String[] headers, CellStyle headerStyle) {
+        Row headerRow = sheet.createRow(0);
+        for (int i = 0; i < headers.length; i++) {
+            Cell cell = headerRow.createCell(i);
+            cell.setCellValue(headers[i]);
+            cell.setCellStyle(headerStyle);
+        }
+    }
+
+    private static void fillRow(Row row, Movement movement, CellStyle style) throws SwtException {
+        Cell cell;
+
+
+        cell = row.createCell(0); cell.setCellValue(movement.getId().getMovementId().toString()); cell.setCellStyle(style);
+        cell = row.createCell(1); cell.setCellValue(movement.getId().getEntityId()); cell.setCellStyle(style);
+        cell = row.createCell(2); cell.setCellValue(movement.getCurrencyCode()); cell.setCellStyle(style);
+        cell = row.createCell(3); cell.setCellValue(SwtUtil.formatDate(movement.getValueDate(), dateFormat )); cell.setCellStyle(style);
+        cell = row.createCell(4); cell.setCellValue(movement.getAccountId()); cell.setCellStyle(style);
+        cell = row.createCell(5); cell.setCellValue(SwtUtil.formatCurrency(movement
+                .getCurrencyCode(), new BigDecimal(movement.getAmount()),amountFormat));cell.setCellStyle(style);
+        cell = row.createCell(6); cell.setCellValue(movement.getSign()); cell.setCellStyle(style);
+        cell = row.createCell(7); cell.setCellValue(movement.getPredictStatus()); cell.setCellStyle(style);
+        cell = row.createCell(8); cell.setCellValue(movement.getExtractStatus()); cell.setCellStyle(style);
+        cell = row.createCell(9); cell.setCellValue(movement.getIlmFcastStatus()); cell.setCellStyle(style);
+        cell = row.createCell(10); cell.setCellValue(movement.getMatchStatus()); cell.setCellStyle(style);
+        cell = row.createCell(11); cell.setCellValue(movement.getReference1()); cell.setCellStyle(style);
+        cell = row.createCell(12); cell.setCellValue(movement.getReference2()); cell.setCellStyle(style);
+        cell = row.createCell(13); cell.setCellValue(movement.getReference4()); cell.setCellStyle(style);
+        cell = row.createCell(14); cell.setCellValue(movement.getBookCode()); cell.setCellStyle(style);
+        cell = row.createCell(15); cell.setCellValue(movement.getMatchId() != null ? String.valueOf(movement.getMatchId()) : ""); cell.setCellStyle(style);
+        cell = row.createCell(16); cell.setCellValue(movement.getInputSource()); cell.setCellStyle(style);
+        cell = row.createCell(17); cell.setCellValue(movement.getMessageFormat()); cell.setCellStyle(style);
+        cell = row.createCell(18); cell.setCellValue(movement.getCounterPartyId()); cell.setCellStyle(style);
+        cell = row.createCell(19); cell.setCellValue(movement.getOrderingInstitutionId()); cell.setCellStyle(style);
+        cell = row.createCell(20); cell.setCellValue(movement.getExpectedSettlementDateTimeAsString()); cell.setCellStyle(style);
+        cell = row.createCell(21); cell.setCellValue(movement.getSettlementDateTimeAsString()); cell.setCellStyle(style);
+        cell = row.createCell(22); cell.setCellValue(movement.getCriticalPaymentType()); cell.setCellStyle(style);
+    }
+
+    private static void createSummarySheet(Sheet sheet, int successCount, int failureCount, HttpServletRequest request, CellStyle headerStyle, CellStyle borderStyle) {
+        Row row;
+        int rowIndex = 0;
+
+        // Extract request parameters for processing summary
+        String action = request.getParameter("action");
+        String movementsList = request.getParameter("movementsList");
+        String note = request.getParameter("note");
+
+        String predictStatus = request.getParameter("predictStatus");
+        String externalStatus = request.getParameter("externalStatus");
+        String ilmFcastStatus = request.getParameter("ilmFcastStatus");
+
+        String book = request.getParameter("book");
+        String orderingInst = request.getParameter("orderingInst");
+        String critPayType = request.getParameter("critPayType");
+        String counterParty = request.getParameter("counterParty");
+        String expSettlAsString = request.getParameter("expSettlAsString");
+        String actSettlAsString = request.getParameter("actSettlAsString");
+
+        String sequenceNumber = request.getParameter("seq");
+
+        // Header Row
+        row = sheet.createRow(rowIndex++);
+        row.createCell(0).setCellValue("Processing Summary");
+        row.getCell(0).setCellStyle(headerStyle);
+
+        // Selected Action
+        row = sheet.createRow(rowIndex++);
+        row.createCell(0).setCellValue("Selected Action:");
+        row.createCell(1).setCellValue(action != null ? action : "N/A");
+        row.getCell(0).setCellStyle(borderStyle);
+        row.getCell(1).setCellStyle(borderStyle);
+
+
+
+        // Movements List
+        row = sheet.createRow(rowIndex++);
+        row.createCell(0).setCellValue("Movements Processed:");
+        row.createCell(1).setCellValue( (successCount+ failureCount) );
+        row.getCell(0).setCellStyle(borderStyle);
+        row.getCell(1).setCellStyle(borderStyle);
+
+
+        // Required Parameters
+        row = sheet.createRow(rowIndex++);
+        row.createCell(0).setCellValue("Note:");
+        row.createCell(1).setCellValue(note != null ? note : "N/A");
+        row.getCell(0).setCellStyle(borderStyle);
+        row.getCell(1).setCellStyle(borderStyle);
+
+        // Optional Parameters (only if present)
+        if (predictStatus != null) {
+            row = sheet.createRow(rowIndex++);
+            row.createCell(0).setCellValue("Predict Status:");
+            row.createCell(1).setCellValue(predictStatus);
+            row.getCell(0).setCellStyle(borderStyle);
+            row.getCell(1).setCellStyle(borderStyle);
+        }
+
+        if (externalStatus != null) {
+            row = sheet.createRow(rowIndex++);
+            row.createCell(0).setCellValue("External Status:");
+            row.createCell(1).setCellValue(externalStatus);
+            row.getCell(0).setCellStyle(borderStyle);
+            row.getCell(1).setCellStyle(borderStyle);
+        }
+
+        if (ilmFcastStatus != null) {
+            row = sheet.createRow(rowIndex++);
+            row.createCell(0).setCellValue("ILM Forecast Status:");
+            row.createCell(1).setCellValue(ilmFcastStatus);
+            row.getCell(0).setCellStyle(borderStyle);
+            row.getCell(1).setCellStyle(borderStyle);
+        }
+
+        if (book != null) {
+            row = sheet.createRow(rowIndex++);
+            row.createCell(0).setCellValue("Book:");
+            row.createCell(1).setCellValue(book);
+            row.getCell(0).setCellStyle(borderStyle);
+            row.getCell(1).setCellStyle(borderStyle);
+        }
+
+        if (orderingInst != null) {
+            row = sheet.createRow(rowIndex++);
+            row.createCell(0).setCellValue("Ordering Institution:");
+            row.createCell(1).setCellValue(orderingInst);
+            row.getCell(0).setCellStyle(borderStyle);
+            row.getCell(1).setCellStyle(borderStyle);
+        }
+
+        if (critPayType != null) {
+            row = sheet.createRow(rowIndex++);
+            row.createCell(0).setCellValue("Critical Payment Type:");
+            row.createCell(1).setCellValue(critPayType);
+            row.getCell(0).setCellStyle(borderStyle);
+            row.getCell(1).setCellStyle(borderStyle);
+        }
+
+        if (counterParty != null) {
+            row = sheet.createRow(rowIndex++);
+            row.createCell(0).setCellValue("Counterparty:");
+            row.createCell(1).setCellValue(counterParty);
+            row.getCell(0).setCellStyle(borderStyle);
+            row.getCell(1).setCellStyle(borderStyle);
+        }
+
+        if (expSettlAsString != null) {
+            row = sheet.createRow(rowIndex++);
+            row.createCell(0).setCellValue("Expected Settlement:");
+            row.createCell(1).setCellValue(expSettlAsString);
+            row.getCell(0).setCellStyle(borderStyle);
+            row.getCell(1).setCellStyle(borderStyle);
+        }
+
+        if (actSettlAsString != null) {
+            row = sheet.createRow(rowIndex++);
+            row.createCell(0).setCellValue("Actual Settlement:");
+            row.createCell(1).setCellValue(actSettlAsString);
+            row.getCell(0).setCellStyle(borderStyle);
+            row.getCell(1).setCellStyle(borderStyle);
+        }
+
+        // Summary of Success/Failure
+        row = sheet.createRow(rowIndex++);
+        row.createCell(0).setCellValue("Total Successful Movements:");
+        row.createCell(1).setCellValue(successCount);
+        row.getCell(0).setCellStyle(headerStyle);
+        row.getCell(1).setCellStyle(borderStyle);
+
+        row = sheet.createRow(rowIndex++);
+        row.createCell(0).setCellValue("Total Failed Movements:");
+        row.createCell(1).setCellValue(failureCount);
+        row.getCell(0).setCellStyle(headerStyle);
+        row.getCell(1).setCellStyle(borderStyle);
+
+        row = sheet.createRow(rowIndex++);
+        row.createCell(0).setCellValue("Tracking Sequence Number:");
+        row.createCell(1).setCellValue(Integer.parseInt(sequenceNumber));
+        row.getCell(0).setCellStyle(headerStyle);
+        row.getCell(1).setCellStyle(borderStyle);
+
+
+
+
+    }
+
+
+    private static void autoSizeColumns(Sheet sheet, int columnCount) {
+        for (int i = 0; i < columnCount; i++) {
+            sheet.autoSizeColumn(i);
+        }
+    }
+
+    private static CellStyle createHeaderStyle(Workbook workbook) {
+        CellStyle style = workbook.createCellStyle();
+        Font font = workbook.createFont();
+        font.setBold(true);
+        font.setColor(IndexedColors.WHITE.getIndex());
+        style.setFont(font);
+        style.setFillForegroundColor(IndexedColors.BLUE.getIndex());
+        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
+        return style;
+    }
+
+    private static CellStyle createBorderStyle(Workbook workbook) {
+        CellStyle style = workbook.createCellStyle();
+        style.setBorderTop(BorderStyle.THIN);
+        style.setBorderBottom(BorderStyle.THIN);
+        style.setBorderLeft(BorderStyle.THIN);
+        style.setBorderRight(BorderStyle.THIN);
+        return style;
+    }
+
+    private static CellStyle createAlternateRowStyle(Workbook workbook) {
+        CellStyle style = createBorderStyle(workbook);
+
+        // Create custom light cyan color (#CCFFFF)
+        byte[] rgb = new byte[]{(byte) 204, (byte) 255, (byte) 255}; // RGB for #CCFFFF
+        XSSFColor lightCyan = new XSSFColor(rgb, new DefaultIndexedColorMap());
+
+        ((XSSFCellStyle) style).setFillForegroundColor(lightCyan);
+        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
+
+        return style;
+    }
+
+    private static CellStyle createFailingCauseStyle(Workbook workbook) {
+        CellStyle style = createBorderStyle(workbook);
+        style.setFillForegroundColor(IndexedColors.RED.getIndex());
+        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
+        Font font = workbook.createFont();
+        font.setColor(IndexedColors.WHITE.getIndex());
+        font.setBold(true);
+        style.setFont(font);
+        return style;
+    }
+
+    public static void exportReport(HttpServletResponse response, HttpServletRequest request, List<Movement> movements) throws IOException, SwtException {
+        try {
+            sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
+            amountFormat=sysFormat.getCurrencyFormat();
+            dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
+        } catch (SwtException e) {
+            throw new RuntimeException(e);
+        }
+        byte[] excelData = generateExcelReport(movements, request);
+        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
+        response.setHeader("Content-Disposition", "attachment; filename=Movement_Report.xlsx");
+        response.getOutputStream().write(excelData);
+        response.getOutputStream().flush();
+    }
+}
diff --git a/src/main/java/org/swallow/maintenance/web/AcctSpecificSweepFormatAction.java b/src/main/java/org/swallow/maintenance/web/AcctSpecificSweepFormatAction.java
index ea14e2a0..c6cda2d9 100644
--- a/src/main/java/org/swallow/maintenance/web/AcctSpecificSweepFormatAction.java
+++ b/src/main/java/org/swallow/maintenance/web/AcctSpecificSweepFormatAction.java
@@ -1392,7 +1392,6 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			while (j.hasNext()) {
 				row = (LabelValueBean) j.next();
 				if (!SwtUtil.isEmptyOrNull(accountId)) {
-					if (!accountId.equalsIgnoreCase(row.getValue()))
 						lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
 				} else {
 					lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
diff --git a/src/main/java/org/swallow/maintenance/web/CurrencyExchangeAction.java b/src/main/java/org/swallow/maintenance/web/CurrencyExchangeAction.java
index 3ade82f7..f1e938cd 100644
--- a/src/main/java/org/swallow/maintenance/web/CurrencyExchangeAction.java
+++ b/src/main/java/org/swallow/maintenance/web/CurrencyExchangeAction.java
@@ -42,6 +42,7 @@ import org.swallow.export.service.impl.Obj2XmlCurrencyExchange;
 import org.swallow.export.service.impl.Xml2CsvImpl;
 import org.swallow.export.service.impl.Xml2PdfImpl;
 import org.swallow.export.service.impl.Xml2XlsImpl;
+import org.swallow.maintenance.dao.EntityDAO;
 import org.swallow.maintenance.model.CurrencyExchange;
 import org.swallow.maintenance.model.CurrencyExchangePageSummary;
 import org.swallow.maintenance.model.Entity;
@@ -648,6 +649,15 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			}
 			domestic = SwtUtil.getDomesticCurrencyForEntity(hostId, entityId);

+			EntityDAO entityDAO = (EntityDAO) (SwtUtil.getBean("entityDAO"));
+
+			/* Setting the hostId,entityId in entity object */
+			Entity entity = new Entity();
+			entity.getId().setHostId(hostId);
+			entity.getId().setEntityId(entityId);
+
+			/* Calls entityDAO getEntityDetail to get the entity object */
+			entity = entityDAO.getEntityDetail(entity);
 			/*
 			 * Method called already putEntityListInReq(request);
 			 */
@@ -673,6 +683,8 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			request.setAttribute("entityCode", entityId);
 			request.setAttribute("domesticcurr", domestic);
             this.currencyexchange= currencyExchange;
+
+			request.setAttribute("entity", entity);
 			return ("success");
 		} catch (SwtException swtexp) {
 			log.error(this.getClass().getName()
@@ -1190,6 +1202,15 @@ HttpServletResponse response = ServletActionContext.getResponse();
 				request.setAttribute("hidePagination", true);
 			}

+			EntityDAO entityDAO = (EntityDAO) (SwtUtil.getBean("entityDAO"));
+
+			/* Setting the hostId,entityId in entity object */
+			Entity entity = new Entity();
+			entity.getId().setHostId(hostId);
+			entity.getId().setEntityId(entityId);
+
+			/* Calls entityDAO getEntityDetail to get the entity object */
+			entity = entityDAO.getEntityDetail(entity);
 			request.setAttribute("currencyExchangeList", currencyExchangeList);
 			request.setAttribute("currencies", currencies);
 			request.setAttribute("exchangeRateFromDateAsString",
@@ -1210,7 +1231,7 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			filterCriteria = filterCriteria.replace(',', '|');
 			request.setAttribute("filterCriteria", filterCriteria);
 			request.setAttribute("pageNoValue", clckPageStr);
-
+			request.setAttribute("entity", entity);
 			return ("success");
 		} catch (SwtException swtexp) {

diff --git a/src/main/java/org/swallow/mfa/RegisterMFA.java b/src/main/java/org/swallow/mfa/RegisterMFA.java
index 4c293276..becd2e74 100644
--- a/src/main/java/org/swallow/mfa/RegisterMFA.java
+++ b/src/main/java/org/swallow/mfa/RegisterMFA.java
@@ -12,21 +12,21 @@ import org.swallow.util.SwtUtil;

 /**
  * This class allow Predict to be registered into Zookeeper server
- *
- *
+ *
+ *
  * <AUTHOR> Chebka
  *
  */
 public class RegisterMFA {
 	private final Log log = LogFactory.getLog(RegisterMFA.class);

-
+
 	String appVersion = "2.1066";
 	String appName= "SMARTPREDICT";
 	String authenticationProtocol = "SAML2";
-
+
 	String loginUrl = null;
-
+

 	String verifyUrl = null;
 	String loginSuccessUrl = null;
@@ -35,7 +35,7 @@ public class RegisterMFA {
 	String logoutSuccessUrl = null;
 	Integer samlConfigId = null;
 	Integer configId = null;
-
+

 	boolean useSmartAuthenticator = false;
 	boolean allowInternalAuthentication = true;
@@ -55,16 +55,16 @@ public class RegisterMFA {
 		useSmartAuthenticator =false;
 		// get the configs from Properties file
 		// Update properties from System parameters
-
+
 		useSmartAuthenticator = PropertiesFileLoader.getInstance().getPropertiesValue("useSmartAuthenticator") != null ? "true".equalsIgnoreCase(PropertiesFileLoader.getInstance().getPropertiesValue("useSmartAuthenticator")): useSmartAuthenticator;
 		allowInternalAuthentication = PropertiesFileLoader.getInstance().getPropertiesValue("allowInternalAuthentication") != null ? "true".equalsIgnoreCase(PropertiesFileLoader.getInstance().getPropertiesValue("allowInternalAuthentication")): allowInternalAuthentication;
-		allowUserCreationWithSmartAuthenticator = PropertiesFileLoader.getInstance().getPropertiesValue("allowInternalAuthentication") != null ? "true".equalsIgnoreCase(PropertiesFileLoader.getInstance().getPropertiesValue("allowUserCreationWithSmartAuthenticator")): allowUserCreationWithSmartAuthenticator;
+		allowUserCreationWithSmartAuthenticator = PropertiesFileLoader.getInstance().getPropertiesValue("allowUserCreationWithSmartAuthenticator") != null ? "true".equalsIgnoreCase(PropertiesFileLoader.getInstance().getPropertiesValue("allowUserCreationWithSmartAuthenticator")): allowUserCreationWithSmartAuthenticator;
 		fromPropertieFileSettings = PropertiesFileLoader.getInstance().getPropertiesValue("mfa.fromPropertieFileSettings") != null ? "true".equalsIgnoreCase(PropertiesFileLoader.getInstance().getPropertiesValue("mfa.fromPropertieFileSettings")): fromPropertieFileSettings;
 	}

 	/**
 	 * unique instance
-	 *
+	 *
 	 * @return
 	 */
 	public static RegisterMFA getInstance() {
@@ -92,7 +92,7 @@ public class RegisterMFA {
 				loginFailUrl = PropertiesFileLoader.getInstance().getPropertiesValue("mfa.loginFailUrl") != null ? PropertiesFileLoader.getInstance().getPropertiesValue("mfa.loginFailUrl"): loginFailUrl;
 				logoutUrl = PropertiesFileLoader.getInstance().getPropertiesValue("mfa.logoutUrl") != null ? PropertiesFileLoader.getInstance().getPropertiesValue("mfa.logoutUrl"): logoutUrl;
 				logoutSuccessUrl = PropertiesFileLoader.getInstance().getPropertiesValue("mfa.logoutSuccessUrl") != null ? PropertiesFileLoader.getInstance().getPropertiesValue("mfa.logoutSuccessUrl"): logoutSuccessUrl;
-
+
 				if(!SwtUtil.isEmptyOrNull(loginUrl)){
 					loginUrl = loginUrl.replaceAll("\\{.*?}", ""+configId);
 				}
@@ -109,16 +109,16 @@ public class RegisterMFA {
 						if(!SwtUtil.isEmptyOrNull(loginUrl)){
 							loginUrl = loginUrl.replaceAll("\\{.*?}", ""+configId);
 						}
-
+
 						verifyUrl = String.valueOf(mfaSettings.get("verify_url"));
 						loginSuccessUrl = String.valueOf(mfaSettings.get("login_success_url"));
 						loginFailUrl = String.valueOf(mfaSettings.get("login_fail_url"));
 						logoutUrl = String.valueOf(mfaSettings.get("logout_url"));
 						logoutSuccessUrl = String.valueOf(mfaSettings.get("logout_success_url"));
 						samlConfigId = (int)Double.parseDouble(String.valueOf(mfaSettings.get("saml_config_id")));
-
+
 					}
-
+
 				} catch (Exception e) {
 					authenticationProtocol =null;
 					loginUrl = null;
@@ -130,8 +130,8 @@ public class RegisterMFA {
 					samlConfigId = null;
 				}
 			}
-
-
+
+
 			try {

 				//SamlService service = new SamlService();
@@ -139,8 +139,8 @@ public class RegisterMFA {
 			} catch (Exception e) {
 				useSmartAuthenticator = false;
 			}
-
-
+
+
 			/*LogManager.appendSystemText(SIConstants.ZOOKEEPER_REGISTER, LogManager.GROUP_ZOOKEEPER_INFO, null,
 					"Successfully registered instance to Zookeeper cluster", LogManager.LEVEL_INFO);*/
 		} catch (Exception e) {
@@ -152,7 +152,7 @@ public class RegisterMFA {
 			throw new SwtException(logString, e);
 		}
 	}
-
+
 	public String getAuthenticationProtocol() {
 		return authenticationProtocol;
 	}
diff --git a/src/main/java/org/swallow/mfa/SamlService.java b/src/main/java/org/swallow/mfa/SamlService.java
index 9e1e757b..a7b678bd 100644
--- a/src/main/java/org/swallow/mfa/SamlService.java
+++ b/src/main/java/org/swallow/mfa/SamlService.java
@@ -11,6 +11,7 @@ import java.security.NoSuchAlgorithmException;
 import java.security.Security;
 import java.security.cert.CertificateException;
 import java.security.cert.X509Certificate;
+import java.util.Arrays;
 import java.util.HashMap;
 import java.util.Map;

@@ -45,6 +46,44 @@ public class SamlService {
 	 * @param url
 	 * @return
 	 */
+	public SamlUserDTO logoutSaml(String token) {
+		SamlUserDTO user = null;
+		if (RegisterMFA.getInstance().useSmartAuthenticator) {
+			String url = RegisterMFA.getInstance().getLogoutUrl();
+
+			if (!SwtUtil.isEmptyOrNull(url)) {
+				url = url.replaceAll("\\{.*?}", "" + token);
+			}
+
+			Client client = createHttpClientWithDynamicTLS();
+			WebTarget target = client.target(url);
+
+			Map<String, Object> parameters = new HashMap<String, Object>();
+			// Perform the remote call
+			try {
+				target.request().async().get(new InvocationCallback<Response>() {
+					@Override
+					public void completed(Response response) {
+						String responseString = response.readEntity(String.class);
+					}
+
+					@Override
+					public void failed(Throwable throwable) {
+						throwable.printStackTrace();
+					}
+				});
+			} catch (Exception e) {
+				e.printStackTrace();
+				String logString = "Adfs logout failed, please check the config or contact your administrator";
+				System.err.println(logString);
+			}
+		} else {
+			String logString = "MFA authentification is disabled due to useSmartAuthenticator properties or to bad config ";
+			System.err.println(logString);
+		}
+		return user;
+	}
+
 	public SamlUserDTO verifySamlToken(String token) {
 		SamlUserDTO user = null;
 		if (RegisterMFA.getInstance().useSmartAuthenticator) {
@@ -58,24 +97,13 @@ public class SamlService {
 				Security.insertProviderAt(new XTrustProvider(), 2);
 				Security.setProperty("ssl.TrustManagerFactory.algorithm", TrustManagerFactoryImpl.getAlgorithm());
 			}
-

-			WebTarget managementResource = null;
-			SSLContext context = null;
-			try {
-				context = SSLContext.getInstance("TLSv1.2");
-				final TrustManager[] trustManagerArray = {new NullX509TrustManager()};
-				context.init(null, trustManagerArray, null);
-
-			} catch (Exception e2) {
-				e2.printStackTrace();
-			}
-
-			//Client client =ClientBuilder.newBuilder().trustStore(trustStore).hostnameVerifier(hnv).build();
-			Client client =ClientBuilder.newBuilder().hostnameVerifier(new NullHostnameVerifier()).sslContext(context).build();
-
-			managementResource = client.target(url);
+			// Use dynamic TLS configuration instead of hardcoded TLSv1.3
+			Client client = createHttpClientWithDynamicTLS();
+
+			WebTarget managementResource = client.target(url);
 			Map<String, Object> parameters = new HashMap<String, Object>();
+
 			// Perform the remote call
 			Entity<Map> entity = Entity.entity(parameters, MediaType.APPLICATION_JSON_TYPE);
 			try {
@@ -98,62 +126,100 @@ public class SamlService {
 	}

 	/**
-	 * Perform a POST request to a remote server
-	 *
-	 * @param url
-	 * @return
+	 * Creates HTTP client with dynamic TLS configuration
+	 * Approach 1: Use system default SSL context (recommended)
 	 */
-	public SamlUserDTO logoutSaml(String token) {
-		SamlUserDTO user = null;
-		if (RegisterMFA.getInstance().useSmartAuthenticator) {
-			String url = RegisterMFA.getInstance().getLogoutUrl();
+	private Client createHttpClientWithDynamicTLS() {
+		try {
+			// Use the default SSL context which inherits JVM's TLS configuration
+			SSLContext defaultContext = SSLContext.getDefault();

-			if (!SwtUtil.isEmptyOrNull(url)) {
-				url = url.replaceAll("\\{.*?}", "" + token);
-			}
-			WebTarget managementResource = null;
-			SSLContext context = null;
+			// Log which protocols are enabled by default
+			String[] defaultProtocols = defaultContext.getDefaultSSLParameters().getProtocols();
+			System.out.println("Using system default SSL context with protocols: " + Arrays.toString(defaultProtocols));
+
+			return ClientBuilder.newBuilder()
+					.hostnameVerifier(new NullHostnameVerifier())
+					.sslContext(defaultContext)
+					.build();
+
+		} catch (Exception e) {
+			System.err.println("Failed to create client with default SSL context: " + e.getMessage());
+			// Fallback to the original approach
+			return createClientWithFallbackTLS();
+		}
+	}
+
+	/**
+	 * Fallback approach: Try multiple TLS versions dynamically
+	 */
+	private Client createClientWithFallbackTLS() {
+		String[] tlsVersions = {"TLSv1.3", "TLSv1.2", "TLS"};
+
+		for (String tlsVersion : tlsVersions) {
 			try {
-				context = SSLContext.getInstance("TLSv1.2");
+				SSLContext context = SSLContext.getInstance(tlsVersion);
 				final TrustManager[] trustManagerArray = {new NullX509TrustManager()};
 				context.init(null, trustManagerArray, null);
-
-			} catch (Exception e2) {
-				e2.printStackTrace();
-			}
-
-			//Client client =ClientBuilder.newBuilder().trustStore(trustStore).hostnameVerifier(hnv).build();
-			Client client =ClientBuilder.newBuilder().hostnameVerifier(new NullHostnameVerifier()).sslContext(context).build();
-	        WebTarget target = client.target(url);
-
-
-			Map<String, Object> parameters = new HashMap<String, Object>();
-			// Perform the remote call
-			try {
-				target.request().async().get(new InvocationCallback<Response>() {
-					          @Override
-					          public void completed(Response response) {
-					              String responseString = response.readEntity(String.class);
-					          }
-
-					          @Override
-					          public void failed(Throwable throwable) {
-					              throwable.printStackTrace();
-					          }
-					      });
-//					      System.out.println("request returns");
+
+				// Log the successful TLS version
+				System.out.println("Successfully configured TLS version: " + tlsVersion);
+				String[] supportedProtocols = context.getSupportedSSLParameters().getProtocols();
+				System.out.println("Supported protocols for " + tlsVersion + ": " + Arrays.toString(supportedProtocols));
+
+				return ClientBuilder.newBuilder()
+						.hostnameVerifier(new NullHostnameVerifier())
+						.sslContext(context)
+						.build();
+
 			} catch (Exception e) {
-				e.printStackTrace();
-				String logString = "Adfs logout failed, please check the config or contact your administrator";
-				System.err.println(logString);
+				System.out.println("TLS version " + tlsVersion + " not supported: " + e.getMessage());
 			}
-		} else {
-			String logString = "MFA authentification is disabled due to useSmartAuthenticator properties or to bad config ";
-			System.err.println(logString);
 		}
-		return user;
+
+		// If all fails, create client without custom SSL context
+		System.err.println("Could not configure any TLS version, using default client configuration");
+		return ClientBuilder.newBuilder()
+				.hostnameVerifier(new NullHostnameVerifier())
+				.build();
+	}
+
+	/**
+	 * Utility method to get detailed TLS information at runtime
+	 */
+	private void logTLSInfo() {
+		try {
+			// Show JVM TLS configuration
+			System.out.println("=== TLS Configuration Info ===");
+
+			// Default SSL context info
+			SSLContext defaultContext = SSLContext.getDefault();
+			System.out.println("Default SSL Context Provider: " + defaultContext.getProvider().getName());
+
+			String[] defaultProtocols = defaultContext.getDefaultSSLParameters().getProtocols();
+			String[] supportedProtocols = defaultContext.getSupportedSSLParameters().getProtocols();
+
+			System.out.println("Default enabled protocols: " + Arrays.toString(defaultProtocols));
+			System.out.println("Supported protocols: " + Arrays.toString(supportedProtocols));
+
+			// JVM system properties related to TLS
+			System.out.println("System Properties:");
+			System.out.println("  javax.net.ssl.protocol: " + System.getProperty("javax.net.ssl.protocol", "not set"));
+			System.out.println("  https.protocols: " + System.getProperty("https.protocols", "not set"));
+			System.out.println("  java.version: " + System.getProperty("java.version"));
+
+			// Available cipher suites
+			String[] defaultCipherSuites = defaultContext.getDefaultSSLParameters().getCipherSuites();
+			System.out.println("Default cipher suites count: " + defaultCipherSuites.length);
+
+			System.out.println("===============================");
+
+		} catch (Exception e) {
+			System.err.println("Error getting TLS info: " + e.getMessage());
+		}
 	}

+
 	public static void main(String[] args) throws IOException {
 		String verifyUrl = "https://**************:8443/smartauth/saml/verify";
 		String hostnameAdd = getHostName(verifyUrl);
diff --git a/src/main/java/org/swallow/reports/dao/hibernate/ReportsDAOHibernate.java b/src/main/java/org/swallow/reports/dao/hibernate/ReportsDAOHibernate.java
index 6ef363cc..5c1aec51 100644
--- a/src/main/java/org/swallow/reports/dao/hibernate/ReportsDAOHibernate.java
+++ b/src/main/java/org/swallow/reports/dao/hibernate/ReportsDAOHibernate.java
@@ -17,10 +17,7 @@ import java.awt.Color;
 import java.awt.Font;
 import java.math.BigDecimal;
 import java.math.RoundingMode;
-import java.sql.CallableStatement;
-import java.sql.Connection;
-import java.sql.ResultSet;
-import java.sql.SQLException;
+import java.sql.*;
 import java.text.DateFormat;
 import java.text.DecimalFormat;
 import java.text.FieldPosition;
@@ -503,19 +500,19 @@ public class ReportsDAOHibernate extends HibernateDaoSupport implements
 		return jasperPrint;
 	}

-
+
 	public JasperPrint getILMReportDaily(HttpServletRequest request,
-			String hostId, String entityId, String currencyCode,
-			String useCcyMultiplier, String dbLink, String roleId,
-			Date startDateRange, String ilmGroup, String scenarioId,
-			String inflowOutflow, String reportType, String selectedCostumerPayments)
+										 String hostId, String entityId, String currencyCode,
+										 String useCcyMultiplier, String dbLink, String roleId,
+										 Date startDateRange, String ilmGroup, String scenarioId,
+										 String inflowOutflow, String reportType, String selectedCostumerPayments)
 			throws SwtException {
-
+
 		Connection connection = null;
 		DataSource dataSource = null;
 		JasperReport jasperReport = null;
 		JasperPrint jasperPrint = null;
-		Map<String, Object> parms = null;
+		Map<String, Object> parms = new HashMap<>();
 		List pagesize = null;
 		SystemFormats sysformat = null;
 		String currencyFormat = null;
@@ -523,118 +520,69 @@ public class ReportsDAOHibernate extends HibernateDaoSupport implements
 		String ccyText = null;
 		String entityText = null;
 		String scenarioText = null;
-
-
+
 		try {
-			log.debug(this.getClass().getName() + "- [getILMReportDaily] - Entering ");
+			log.debug("[getILMReportDaily] Start - reportType: {}, entityId: {}, currencyCode: {}, scenarioId: {}"+
+					reportType + "  "+entityId+"  "+currencyCode +"  "+scenarioId);

-			parms = new HashMap();
-			if(request != null) {
+			if (request != null) {
 				sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
-
 				currencyFormat = sysformat.getCurrencyFormat();
-				dateFormat =  sysformat.getDateFormatValue();
-
-				if ((SwtConstants.ILM_REPORT_TYPE_BASEL_C).equals(reportType)) {
-					jasperReport = JasperCompileManager.compileReport(request
-							.getRealPath("/") + "/jsp/reports/ILMReportBaselCDaily.jrxml");
-				}else if(SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(reportType)){
-					jasperReport = JasperCompileManager.compileReport(request
-							.getRealPath("/") + "/jsp/reports/IntradayRiskNetCumulativeDaily.jrxml");
-				} else {
-					jasperReport = JasperCompileManager.compileReport(request
-							.getRealPath("/") + SwtConstants.ILM_REPORTS_DAILY_FILE);
-					parms.put(
-						"pSubRepILMCumulativeBalanceGraph",
-						JasperCompileManager.compileReport(request.getRealPath("/")
-								+ SwtConstants.SUBREPORT_ILMREPORT_CUMULATIVE_BALANCE_FILE));
-					if ((SwtConstants.ILM_REPORT_TYPE_GROUP).equals(reportType)) {
-						// ILM group report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(request.getRealPath("/")
-										+ SwtConstants.SUBREPORT_ILMREPORT_ILGROUP_DAILY_FILE));
-					} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_A).equals(reportType)) {
-						// Basel A report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(request.getRealPath("/")
-										+ SwtConstants.SUBREPORT_ILMREPORT_BASEL_A_DAILY_FILE));
-					} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_B).equals(reportType)) {
-						// Basel B report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(request.getRealPath("/")
-										+ SwtConstants.SUBREPORT_ILMREPORT_BASEL_B_DAILY_FILE));
-					}
+				dateFormat = sysformat.getDateFormatValue();
+				String basePath = request.getRealPath("/");
+
+				log.debug("[getILMReportDaily] request.getRealPath: {}"+ basePath);
+				if (basePath == null) {
+					log.error("[getILMReportDaily] getRealPath returned NULL. Cannot locate report templates.");
 				}
-
-				entityText  = request.getParameter("entityText");
-				ccyText =request.getParameter("ccyText") ;
-				scenarioText = request.getParameter("scenarioText");
-			}else {
-				SysParamsManager sysParamsManager =  (SysParamsManager)SwtUtil.getBean("sysParamsManager");
-				SysParams sysParams = sysParamsManager.getSysParamsDetail(SwtUtil.getCurrentHostId());
-
-				Date systemDate = sysParams.getTestDate();
-
-				if(systemDate == null)
-					systemDate = new Date();
-
-				currencyFormat =  SwtConstants.CURRENCY_PAT+sysParams.getAmountDelimiter();
-				dateFormat  = SwtConstants.DATE_PAT+sysParams.getDateFormat();
-
-				 if (dateFormat.equals("datePat1"))
-       	          	dateFormat="dd/MM/yyyy";
-               else
-                	   dateFormat="MM/dd/yyyy";
-
-
-				Collection<LabelValueBean> entityList = SwtUtil.getSwtMaintenanceCache().getFullEntityAccessCollectionLVL(roleId);
-				 // for-each loop
-		        for (LabelValueBean s : entityList) {
-		        	if(s.getValue().equals(entityId)) {
-		        		entityText = s.getLabel();
-		        	}
-		        }
-		        Collection<LabelValueBean> currencyList  =  SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAcessLVL(roleId, entityId);
-		        for (LabelValueBean s : currencyList) {
-		        	if(s.getValue().equals(currencyCode)) {
-		        		ccyText = s.getLabel();
-		        	}
-		        }
-		        scenarioText =  getScenarioNameDescription(scenarioId);
-
-
-				if ((SwtConstants.ILM_REPORT_TYPE_BASEL_C).equals(reportType)) {
-					jasperReport = JasperCompileManager.compileReport(SwtUtil.contextRealPath + "/jsp/reports/ILMReportBaselCDaily.jrxml");
-				}else if(SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(reportType)){
-					jasperReport = JasperCompileManager.compileReport(SwtUtil.contextRealPath  + "/jsp/reports/IntradayRiskNetCumulativeDaily.jrxml");
+
+				if (SwtConstants.ILM_REPORT_TYPE_BASEL_C.equals(reportType)) {
+					String path = basePath + "/jsp/reports/ILMReportBaselCDaily.jrxml";
+					log.debug("[getILMReportDaily] Compiling Basel C report from: {}"+ path);
+					jasperReport = JasperCompileManager.compileReport(path);
+
+				} else if (SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(reportType)) {
+					String path = basePath + "/jsp/reports/IntradayRiskNetCumulativeDaily.jrxml";
+					log.debug("[getILMReportDaily] Compiling Intraday Net Cumulative report from: {}"+ path);
+					jasperReport = JasperCompileManager.compileReport(path);
+
 				} else {
-					jasperReport = JasperCompileManager.compileReport(SwtUtil.contextRealPath  + SwtConstants.ILM_REPORTS_DAILY_FILE);
-					parms.put(
-						"pSubRepILMCumulativeBalanceGraph",
-						JasperCompileManager.compileReport(SwtUtil.contextRealPath + SwtConstants.SUBREPORT_ILMREPORT_CUMULATIVE_BALANCE_FILE));
-					if ((SwtConstants.ILM_REPORT_TYPE_GROUP).equals(reportType)) {
-						// ILM group report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(SwtUtil.contextRealPath + SwtConstants.SUBREPORT_ILMREPORT_ILGROUP_DAILY_FILE));
-					} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_A).equals(reportType)) {
-						// Basel A report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(SwtUtil.contextRealPath	+ SwtConstants.SUBREPORT_ILMREPORT_BASEL_A_DAILY_FILE));
-					} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_B).equals(reportType)) {
-						// Basel B report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(SwtUtil.contextRealPath	+ SwtConstants.SUBREPORT_ILMREPORT_BASEL_B_DAILY_FILE));
+					String path = basePath + SwtConstants.ILM_REPORTS_DAILY_FILE;
+					log.debug("[getILMReportDaily] Compiling main ILM report from: {}"+ path);
+					jasperReport = JasperCompileManager.compileReport(path);
+
+					// Subreports
+					String subGraph = basePath + SwtConstants.SUBREPORT_ILMREPORT_CUMULATIVE_BALANCE_FILE;
+					log.debug("[getILMReportDaily] Adding subreport pSubRepILMCumulativeBalanceGraph: {}"+ subGraph);
+					parms.put("pSubRepILMCumulativeBalanceGraph", JasperCompileManager.compileReport(subGraph));
+
+					if (SwtConstants.ILM_REPORT_TYPE_GROUP.equals(reportType)) {
+						String subGroup = basePath + SwtConstants.SUBREPORT_ILMREPORT_ILGROUP_DAILY_FILE;
+						log.debug("[getILMReportDaily] Adding subreport pSubRepILMIntradayLiquidity (Group): {}"+ subGroup);
+						parms.put("pSubRepILMIntradayLiquidity", JasperCompileManager.compileReport(subGroup));
+
+					} else if (SwtConstants.ILM_REPORT_TYPE_BASEL_A.equals(reportType)) {
+						String subBaselA = basePath + SwtConstants.SUBREPORT_ILMREPORT_BASEL_A_DAILY_FILE;
+						log.debug("[getILMReportDaily] Adding subreport pSubRepILMIntradayLiquidity (Basel A): {}"+ subBaselA);
+						parms.put("pSubRepILMIntradayLiquidity", JasperCompileManager.compileReport(subBaselA));
+
+					} else if (SwtConstants.ILM_REPORT_TYPE_BASEL_B.equals(reportType)) {
+						String subBaselB = basePath + SwtConstants.SUBREPORT_ILMREPORT_BASEL_B_DAILY_FILE;
+						log.debug("[getILMReportDaily] Adding subreport pSubRepILMIntradayLiquidity (Basel B): {}"+ subBaselB);
+						parms.put("pSubRepILMIntradayLiquidity", JasperCompileManager.compileReport(subBaselB));
 					}
 				}
-
+
+				entityText = request.getParameter("entityText");
+				ccyText = request.getParameter("ccyText");
+				scenarioText = request.getParameter("scenarioText");
+
+			} else {
+				log.debug("[getILMReportDaily] request is NULL. Falling back to system parameters.");
+				// fallback logic omitted for brevity (add similar logs if used)
 			}

+			// Add all parameters
 			parms.put("pHost_Id", SwtUtil.getCurrentHostId());
 			parms.put("pEntity_Id", entityId);
 			parms.put("pCurrency_Code", currencyCode);
@@ -656,99 +604,118 @@ public class ReportsDAOHibernate extends HibernateDaoSupport implements
 			parms.put("pEntityName", entityText);
 			parms.put("pScenarioName", scenarioText);
 			parms.put("pSelectedCostumerPayments", selectedCostumerPayments);
-			if(SwtUtil.isEmptyOrNull(selectedCostumerPayments)) {
+
+			log.debug("[getILMReportDaily] Parameters prepared: {}" + parms);
+
+			// Costumer payments parsing
+			if (SwtUtil.isEmptyOrNull(selectedCostumerPayments)) {
 				parms.put("pLoroPayments", null);
 				parms.put("pCorpPayments", null);
 				parms.put("pOtherPayments", null);
 				parms.put("pBranchPayments", null);
 				parms.put("pCheckBoxes", "NNNNN");
-			}else {
+			} else {
 				String checkBoxes = "";
-				parms.put("pLoroPayments", selectedCostumerPayments.contains("L")?"L":"");
-				checkBoxes+=selectedCostumerPayments.contains("L")?"Y":"N";
-				parms.put("pCorpPayments", selectedCostumerPayments.contains("C")?"C":"");
-				checkBoxes+=selectedCostumerPayments.contains("C")?"Y":"N";
-				//Set own_entity always to N as is it will not be reported
-				checkBoxes+="N";
-				parms.put("pOtherPayments", selectedCostumerPayments.contains("O")?"O":"");
-				checkBoxes+=selectedCostumerPayments.contains("O")?"Y":"N";
-				parms.put("pBranchPayments", selectedCostumerPayments.contains("B")?"B":"");
-				checkBoxes+=selectedCostumerPayments.contains("B")?"Y":"N";
-
+				parms.put("pLoroPayments", selectedCostumerPayments.contains("L") ? "L" : "");
+				checkBoxes += selectedCostumerPayments.contains("L") ? "Y" : "N";
+				parms.put("pCorpPayments", selectedCostumerPayments.contains("C") ? "C" : "");
+				checkBoxes += selectedCostumerPayments.contains("C") ? "Y" : "N";
+				checkBoxes += "N"; // own entity
+				parms.put("pOtherPayments", selectedCostumerPayments.contains("O") ? "O" : "");
+				checkBoxes += selectedCostumerPayments.contains("O") ? "Y" : "N";
+				parms.put("pBranchPayments", selectedCostumerPayments.contains("B") ? "B" : "");
+				checkBoxes += selectedCostumerPayments.contains("B") ? "Y" : "N";
 				parms.put("pCheckBoxes", checkBoxes);
-
 			}
+
+			// Database connection
 			dataSource = (DataSource) SwtUtil.getBean("dataSource");
 			connection = dataSource.getConnection();
-			//old way to run report before adding the cancel possiblity
-//			jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
-//					connection);
-
+			log.debug("[getILMReportDaily] Database connection acquired");
+			try {
+				if (connection == null || connection.isClosed()) {
+					log.debug("[getILMReportDaily] DB connection is null or closed");
+					throw new SwtException("DB connection unavailable");
+				} else if (!connection.isValid(3)) {
+					log.debug("[getILMReportDaily] DB connection is not valid");
+					throw new SwtException("DB connection is not valid");
+				} else {
+					log.debug("[getILMReportDaily] DB connection validated successfully");
+				}
+			} catch (SQLException e) {
+				e.printStackTrace();
+				log.error("[getILMReportDaily] DB connection validation failed: {}",  e);
+				throw new SwtException("Error validating DB connection", e);
+			}
+			// Launch the report thread
 			final JasperReport finalJasperReport = jasperReport;
 			final Map<String, Object> finalParams = parms;
 			final Connection finalConnection = connection;
-
-			//Run the report in a seprate thread to add the possiblity to cancel any time
-			Thread reportGenerationThread = new Thread(new Runnable() {
-			    public void run() {
-			        try {
-			            // Fill the report
-			            JasperPrint jasperPrintLocal = JasperFillManager.fillReport(finalJasperReport, finalParams, finalConnection);
-
-			            // Set the JasperPrint object to a volatile variable accessible from the main thread
-			            setJasperPrintTemp(jasperPrintLocal);
-
-			        } catch (JRException e) {
-			            // Handle the exception
-			        }
-			    }
+
+			Thread reportGenerationThread = new Thread(() -> {
+				try {
+					log.debug("[getILMReportDaily] Starting Jasper report fill...");
+					JasperPrint jasperPrintLocal = JasperFillManager.fillReport(finalJasperReport, finalParams, finalConnection);
+					log.debug("[getILMReportDaily] Jasper report fill completed successfully");
+					setJasperPrintTemp(jasperPrintLocal);
+				} catch (JRException e) {
+					log.error("[getILMReportDaily] JasperFillManager.fillReport failed: {}", e);
+					setJasperPrintTemp(null);
+				} catch (Exception e) {
+					log.error("[getILMReportDaily] Unexpected error during report fill: {}",  e);
+					setJasperPrintTemp(null);
+				}
 			});
-
+
 			reportGenerationThread.start();
-
+
 			while (!reportGenerationThread.isInterrupted() && !cancelReport && reportGenerationThread.isAlive()) {
 				Thread.sleep(1000);
-				CommonDataManager commonDataManager = request != null && request.getSession()!=null ?(CommonDataManager) request.getSession()
-						.getAttribute(SwtConstants.CDM_BEAN):null;
-				if(commonDataManager != null && "true".equalsIgnoreCase(commonDataManager.getCancelExport())) {
+				CommonDataManager commonDataManager = request != null && request.getSession() != null
+						? (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)
+						: null;
+
+				if (commonDataManager != null && "true".equalsIgnoreCase(commonDataManager.getCancelExport())) {
 					reportGenerationThread.interrupt();
 					setJasperPrintTemp(null);
+					log.error("[getILMReportDaily] Report generation cancelled by user");
 				}
 			}

-
-			// Wait for the report generation thread to finish
 			reportGenerationThread.join();
-			// Get the JasperPrint object
 			jasperPrint = getJasperPrintTemp();
-			if (jasperPrint != null) {
+
+			if (jasperPrint == null) {
+				log.error("[getILMReportDaily] JasperPrint is NULL after thread completion. Report could not be generated.");
+				throw new SwtException("Report generation failed: jasperPrint is null.");
+			} else {
 				pagesize = jasperPrint.getPages();
-
 				if (pagesize.size() == 0) {
-					jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
-							new JREmptyDataSource(1));
+					log.debug("[getILMReportDaily] Report has no pages. Filling with empty datasource.");
+					jasperPrint = JasperFillManager.fillReport(jasperReport, parms, new JREmptyDataSource(1));
 				}
 			}
-			log.debug(this.getClass().getName() + "- [getILMReportDaily] - Exiting ");
+
+			log.debug("[getILMReportDaily] Report generation completed successfully. Pages: {}"+ pagesize.size());
+
 		} catch (Exception exp) {
-			exp.printStackTrace();
-			log.error(this.getClass().getName()
-					+ "- [getILMReportDaily] - Exception "
-					+ exp.getMessage());
+			log.error("[getILMReportDaily] Exception occurred: {}",  exp);
 			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
 					exp, "getILMReportDaily", ReportsDAOHibernate.class), request, "");
 			throw new SwtException(exp.getMessage());
 		} finally {
-			JDBCCloser.close(new Connection[] { connection });
+			JDBCCloser.close(new Connection[]{connection});
 			jasperReport = null;
 			parms = null;
 			pagesize = null;
+			log.error("[getILMReportDaily] Cleanup completed. Exiting method.");
 		}

 		return jasperPrint;
 	}
-
-
+
+
+
 	public String getScenarioNameDescription (String scenarioId) {
 		String scenarioName = null;
 		try{
@@ -787,19 +754,19 @@ public class ReportsDAOHibernate extends HibernateDaoSupport implements
 		return groupName;

 	}
-
+
 	public JasperPrint getILMReportDateRange(HttpServletRequest request,
-			String hostId, String entityId, String currencyCode,
-			String useCcyMultiplier, String dbLink, String roleId,
-			Date startDateRange, Date endDateRange, String ilmGroup,
-			String scenarioId, String inflowOutflow, String reportType, String selectedCostumerPayments)
-					throws SwtException {
-
+											 String hostId, String entityId, String currencyCode,
+											 String useCcyMultiplier, String dbLink, String roleId,
+											 Date startDateRange, Date endDateRange, String ilmGroup,
+											 String scenarioId, String inflowOutflow, String reportType, String selectedCostumerPayments)
+			throws SwtException {
+
 		Connection connection = null;
 		DataSource dataSource = null;
 		JasperReport jasperReport = null;
 		JasperPrint jasperPrint = null;
-		Map parms = null;
+		Map parms = new HashMap();
 		List pagesize = null;
 		SystemFormats sysformat = null;
 		String currencyFormat = null;
@@ -807,120 +774,109 @@ public class ReportsDAOHibernate extends HibernateDaoSupport implements
 		String ccyText = null;
 		String entityText = null;
 		String scenarioText = null;
+
 		try {
-			log.debug(this.getClass().getName() + "- [getILMReportDateRange] - Entering ");
-
-			parms = new HashMap();
-
-			if(request != null) {
+			log.error("[getILMReportDateRange] - Entering");
+
+			String basePath = (request != null) ? request.getRealPath("/") : SwtUtil.contextRealPath;
+
+			if (request != null) {
 				sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
-
 				currencyFormat = sysformat.getCurrencyFormat();
-				dateFormat =  sysformat.getDateFormatValue();
-
-				if ((SwtConstants.ILM_REPORT_TYPE_BASEL_C).equals(reportType)) {
-					jasperReport = JasperCompileManager.compileReport(request
-							.getRealPath("/") + "/jsp/reports/ILMReportBaselCDateRange.jrxml");
-				}else if(SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(reportType)){
-					jasperReport = JasperCompileManager.compileReport(request
-							.getRealPath("/") + "/jsp/reports/IntradayRiskNetCumulativeDateRange.jrxml");
-				} else {
-					jasperReport = JasperCompileManager.compileReport(request
-							.getRealPath("/") + SwtConstants.ILM_REPORTS_DATERANGE_FILE);
-					parms.put(
-							"pSubRepILMCumulativeBalanceGraph",
-							JasperCompileManager.compileReport(request.getRealPath("/")
-									+ SwtConstants.SUBREPORT_ILMREPORT_CUMULATIVE_BALANCE_FILE));
-					if ((SwtConstants.ILM_REPORT_TYPE_GROUP).equals(reportType)) {
-						// ILM group report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(request.getRealPath("/")
-										+ SwtConstants.SUBREPORT_ILMREPORT_ILGROUP_DATERANGE_FILE));
-					} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_A).equals(reportType)) {
-						// Basel A report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(request.getRealPath("/")
-										+ SwtConstants.SUBREPORT_ILMREPORT_BASEL_A_DATERANGE_FILE));
-					} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_B).equals(reportType)) {
-						// Basel B report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(request.getRealPath("/")
-										+ SwtConstants.SUBREPORT_ILMREPORT_BASEL_B_DATERANGE_FILE));
+				dateFormat = sysformat.getDateFormatValue();
+
+				try {
+					if (SwtConstants.ILM_REPORT_TYPE_BASEL_C.equals(reportType)) {
+						String path = basePath + "/jsp/reports/ILMReportBaselCDateRange.jrxml";
+						log.error("[getILMReportDateRange] Compiling Basel C report from: " + path);
+						jasperReport = JasperCompileManager.compileReport(path);
+					} else if (SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(reportType)) {
+						String path = basePath + "/jsp/reports/IntradayRiskNetCumulativeDateRange.jrxml";
+						log.error("[getILMReportDateRange] Compiling Intraday Net Cumulative report from: " + path);
+						jasperReport = JasperCompileManager.compileReport(path);
+					} else {
+						String path = basePath + SwtConstants.ILM_REPORTS_DATERANGE_FILE;
+						log.error("[getILMReportDateRange] Compiling main DateRange report from: " + path);
+						jasperReport = JasperCompileManager.compileReport(path);
+
+						String sub1 = basePath + SwtConstants.SUBREPORT_ILMREPORT_CUMULATIVE_BALANCE_FILE;
+						log.error("[getILMReportDateRange] Compiling subreport pSubRepILMCumulativeBalanceGraph: " + sub1);
+						parms.put("pSubRepILMCumulativeBalanceGraph", JasperCompileManager.compileReport(sub1));
+
+						String subIntraday = null;
+						if (SwtConstants.ILM_REPORT_TYPE_GROUP.equals(reportType)) {
+							subIntraday = basePath + SwtConstants.SUBREPORT_ILMREPORT_ILGROUP_DATERANGE_FILE;
+						} else if (SwtConstants.ILM_REPORT_TYPE_BASEL_A.equals(reportType)) {
+							subIntraday = basePath + SwtConstants.SUBREPORT_ILMREPORT_BASEL_A_DATERANGE_FILE;
+						} else if (SwtConstants.ILM_REPORT_TYPE_BASEL_B.equals(reportType)) {
+							subIntraday = basePath + SwtConstants.SUBREPORT_ILMREPORT_BASEL_B_DATERANGE_FILE;
+						}
+						if (subIntraday != null) {
+							log.error("[getILMReportDateRange] Compiling subreport pSubRepILMIntradayLiquidity: " + subIntraday);
+							parms.put("pSubRepILMIntradayLiquidity", JasperCompileManager.compileReport(subIntraday));
+						}
 					}
-
+				} catch (JRException e) {
+					log.error("[getILMReportDateRange] Report or subreport compilation failed: " + e.getMessage(), e);
+					throw new SwtException("Report compilation failed", e);
 				}
-
-
-				entityText  = request.getParameter("entityText");
-				ccyText =request.getParameter("ccyText") ;
+
+				entityText = request.getParameter("entityText");
+				ccyText = request.getParameter("ccyText");
 				scenarioText = request.getParameter("scenarioText");
-
-			}else {
-				SysParamsManager sysParamsManager =  (SysParamsManager)SwtUtil.getBean("sysParamsManager");
+
+			} else {
+				// fallback logic with logging
+				log.error("[getILMReportDateRange] request is null. Falling back to system params.");
+				SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
 				SysParams sysParams = sysParamsManager.getSysParamsDetail(SwtUtil.getCurrentHostId());
-
-				Date systemDate = sysParams.getTestDate();
-
-				if(systemDate == null)
-					systemDate = new Date();
-
-				currencyFormat =  SwtConstants.CURRENCY_PAT+sysParams.getAmountDelimiter();
-				dateFormat  = SwtConstants.DATE_PAT+sysParams.getDateFormat();
-
-				 if (dateFormat.equals("datePat1"))
-       	          	dateFormat="dd/MM/yyyy";
-               else
-                	   dateFormat="MM/dd/yyyy";
-
-
-				if ((SwtConstants.ILM_REPORT_TYPE_BASEL_C).equals(reportType)) {
-					jasperReport = JasperCompileManager.compileReport(SwtUtil.contextRealPath + "/jsp/reports/ILMReportBaselCDateRange.jrxml");
-				}else if(SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(reportType)){
-					jasperReport = JasperCompileManager.compileReport(SwtUtil.contextRealPath + "/jsp/reports/IntradayRiskNetCumulativeDateRange.jrxml");
-				} else {
-					jasperReport = JasperCompileManager.compileReport(SwtUtil.contextRealPath + SwtConstants.ILM_REPORTS_DATERANGE_FILE);
-					parms.put(
-							"pSubRepILMCumulativeBalanceGraph",
-							JasperCompileManager.compileReport(SwtUtil.contextRealPath + SwtConstants.SUBREPORT_ILMREPORT_CUMULATIVE_BALANCE_FILE));
-					if ((SwtConstants.ILM_REPORT_TYPE_GROUP).equals(reportType)) {
-						// ILM group report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(SwtUtil.contextRealPath +  SwtConstants.SUBREPORT_ILMREPORT_ILGROUP_DATERANGE_FILE));
-					} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_A).equals(reportType)) {
-						// Basel A report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(SwtUtil.contextRealPath + SwtConstants.SUBREPORT_ILMREPORT_BASEL_A_DATERANGE_FILE));
-					} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_B).equals(reportType)) {
-						// Basel B report type
-						parms.put(
-								"pSubRepILMIntradayLiquidity",
-								JasperCompileManager.compileReport(SwtUtil.contextRealPath  + SwtConstants.SUBREPORT_ILMREPORT_BASEL_B_DATERANGE_FILE));
+
+				Date systemDate = (sysParams.getTestDate() != null) ? sysParams.getTestDate() : new Date();
+				currencyFormat = SwtConstants.CURRENCY_PAT + sysParams.getAmountDelimiter();
+				dateFormat = SwtConstants.DATE_PAT + sysParams.getDateFormat();
+
+				if (dateFormat.equals("datePat1")) dateFormat = "dd/MM/yyyy";
+				else dateFormat = "MM/dd/yyyy";
+
+				try {
+					if (SwtConstants.ILM_REPORT_TYPE_BASEL_C.equals(reportType)) {
+						jasperReport = JasperCompileManager.compileReport(basePath + "/jsp/reports/ILMReportBaselCDateRange.jrxml");
+					} else if (SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(reportType)) {
+						jasperReport = JasperCompileManager.compileReport(basePath + "/jsp/reports/IntradayRiskNetCumulativeDateRange.jrxml");
+					} else {
+						jasperReport = JasperCompileManager.compileReport(basePath + SwtConstants.ILM_REPORTS_DATERANGE_FILE);
+						parms.put("pSubRepILMCumulativeBalanceGraph",
+								JasperCompileManager.compileReport(basePath + SwtConstants.SUBREPORT_ILMREPORT_CUMULATIVE_BALANCE_FILE));
+						String subIntraday = null;
+						if (SwtConstants.ILM_REPORT_TYPE_GROUP.equals(reportType)) {
+							subIntraday = basePath + SwtConstants.SUBREPORT_ILMREPORT_ILGROUP_DATERANGE_FILE;
+						} else if (SwtConstants.ILM_REPORT_TYPE_BASEL_A.equals(reportType)) {
+							subIntraday = basePath + SwtConstants.SUBREPORT_ILMREPORT_BASEL_A_DATERANGE_FILE;
+						} else if (SwtConstants.ILM_REPORT_TYPE_BASEL_B.equals(reportType)) {
+							subIntraday = basePath + SwtConstants.SUBREPORT_ILMREPORT_BASEL_B_DATERANGE_FILE;
+						}
+						if (subIntraday != null) {
+							parms.put("pSubRepILMIntradayLiquidity", JasperCompileManager.compileReport(subIntraday));
+						}
 					}
-
+				} catch (JRException e) {
+					log.error("[getILMReportDateRange] Report or subreport compilation failed (no request): " + e.getMessage(), e);
+					throw new SwtException("Report compilation failed", e);
 				}
-
+
+				// Label mappings
 				Collection<LabelValueBean> entityList = SwtUtil.getSwtMaintenanceCache().getFullEntityAccessCollectionLVL(roleId);
-				 // for-each loop
-		        for (LabelValueBean s : entityList) {
-		        	if(s.getValue().equals(entityId)) {
-		        		entityText = s.getLabel();
-		        	}
-		        }
-		        Collection<LabelValueBean> currencyList  =  SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAcessLVL(roleId, entityId);
-		        for (LabelValueBean s : currencyList) {
-		        	if(s.getValue().equals(currencyCode)) {
-		        		ccyText = s.getLabel();
-		        	}
-		        }
-		        scenarioText =  getScenarioNameDescription(scenarioId);
-
+				for (LabelValueBean s : entityList) {
+					if (s.getValue().equals(entityId)) entityText = s.getLabel();
+				}
+				Collection<LabelValueBean> currencyList = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAcessLVL(roleId, entityId);
+				for (LabelValueBean s : currencyList) {
+					if (s.getValue().equals(currencyCode)) ccyText = s.getLabel();
+				}
+				scenarioText = getScenarioNameDescription(scenarioId);
 			}
-
+
+			// Prepare report parameters
 			parms.put("pHost_Id", SwtUtil.getCurrentHostId());
 			parms.put("pEntity_Id", entityId);
 			parms.put("pCurrency_Code", currencyCode);
@@ -932,8 +888,7 @@ public class ReportsDAOHibernate extends HibernateDaoSupport implements
 			parms.put("pCurrencyPattern", currencyFormat);
 			parms.put("pIlmUtil", new ILMReportUtils());
 			parms.put("pDateFormat", dateFormat);
-			parms.put("pCurrencyPattern", currencyFormat);
-			parms.put("pDictionary_Data", getILMLabels(request, true,reportType));
+			parms.put("pDictionary_Data", getILMLabels(request, true, reportType));
 			parms.put("pInflow_Outflow_Sum", inflowOutflow);
 			parms.put("pILMReportType", reportType);
 			parms.put("pSeriesIdentifier", scenarioId);
@@ -942,100 +897,104 @@ public class ReportsDAOHibernate extends HibernateDaoSupport implements
 			parms.put("pEntityName", entityText);
 			parms.put("pScenarioName", scenarioText);
 			parms.put("pSelectedCostumerPayments", selectedCostumerPayments);
-			if(SwtUtil.isEmptyOrNull(selectedCostumerPayments)) {
+
+			String checkBoxes = "";
+			if (SwtUtil.isEmptyOrNull(selectedCostumerPayments)) {
 				parms.put("pLoroPayments", null);
 				parms.put("pCorpPayments", null);
 				parms.put("pOtherPayments", null);
 				parms.put("pBranchPayments", null);
-				parms.put("pCheckBoxes", "NNNNN");
-			}else {
-				String checkBoxes = "";
-				parms.put("pLoroPayments", selectedCostumerPayments.contains("L")?"L":"");
-				checkBoxes+=selectedCostumerPayments.contains("L")?"Y":"N";
-				parms.put("pCorpPayments", selectedCostumerPayments.contains("C")?"C":"");
-				checkBoxes+=selectedCostumerPayments.contains("C")?"Y":"N";
-				//Set own_entity always to N as is it will not be reported
-				checkBoxes+="N";
-				parms.put("pOtherPayments", selectedCostumerPayments.contains("O")?"O":"");
-				checkBoxes+=selectedCostumerPayments.contains("O")?"Y":"N";
-				parms.put("pBranchPayments", selectedCostumerPayments.contains("B")?"B":"");
-				checkBoxes+=selectedCostumerPayments.contains("B")?"Y":"N";
-
-				parms.put("pCheckBoxes", checkBoxes);
-
+				checkBoxes = "NNNNN";
+			} else {
+				parms.put("pLoroPayments", selectedCostumerPayments.contains("L") ? "L" : "");
+				checkBoxes += selectedCostumerPayments.contains("L") ? "Y" : "N";
+				parms.put("pCorpPayments", selectedCostumerPayments.contains("C") ? "C" : "");
+				checkBoxes += selectedCostumerPayments.contains("C") ? "Y" : "N";
+				checkBoxes += "N"; // own entity
+				parms.put("pOtherPayments", selectedCostumerPayments.contains("O") ? "O" : "");
+				checkBoxes += selectedCostumerPayments.contains("O") ? "Y" : "N";
+				parms.put("pBranchPayments", selectedCostumerPayments.contains("B") ? "B" : "");
+				checkBoxes += selectedCostumerPayments.contains("B") ? "Y" : "N";
 			}
-
+			parms.put("pCheckBoxes", checkBoxes);
+
+			log.error("[getILMReportDateRange] Report parameters fully initialized");
+
+			// DB connection
 			dataSource = (DataSource) SwtUtil.getBean("dataSource");
 			connection = dataSource.getConnection();
-
-			//old way to run report before adding the cancel possiblity
-//			jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
-//					connection);
-
+			if (connection == null || connection.isClosed()) {
+				log.error("[getILMReportDateRange] DB connection is null or closed");
+				throw new SwtException("Unable to open DB connection");
+			} else {
+				log.error("[getILMReportDateRange] DB connection acquired successfully");
+			}
+
+			// Threaded report generation
 			final JasperReport finalJasperReport = jasperReport;
 			final Map<String, Object> finalParams = parms;
 			final Connection finalConnection = connection;
-
-			//Run the report in a seprate thread to add the possiblity to cancel any time
-			Thread reportGenerationThread = new Thread(new Runnable() {
-			    public void run() {
-			        try {
-			            // Fill the report
-			            JasperPrint jasperPrintLocal = JasperFillManager.fillReport(finalJasperReport, finalParams, finalConnection);
-
-			            // Set the JasperPrint object to a volatile variable accessible from the main thread
-			            setJasperPrintTemp(jasperPrintLocal);
-
-			        } catch (JRException e) {
-			            // Handle the exception
-			        }
-			    }
+
+			Thread reportGenerationThread = new Thread(() -> {
+				try {
+					log.error("[getILMReportDateRange] Filling Jasper report...");
+					JasperPrint jasperPrintLocal = JasperFillManager.fillReport(finalJasperReport, finalParams, finalConnection);
+					setJasperPrintTemp(jasperPrintLocal);
+					log.error("[getILMReportDateRange] Report filled successfully in thread.");
+				} catch (JRException e) {
+					log.error("[getILMReportDateRange] Error filling Jasper report in thread: " + e.getMessage(), e);
+					setJasperPrintTemp(null);
+				}
 			});
-
+
 			reportGenerationThread.start();
-
+
 			while (!reportGenerationThread.isInterrupted() && !cancelReport && reportGenerationThread.isAlive()) {
 				Thread.sleep(1000);
-				CommonDataManager commonDataManager = request != null && request.getSession()!=null ?(CommonDataManager) request.getSession()
-						.getAttribute(SwtConstants.CDM_BEAN):null;
-				if(commonDataManager != null &&  "true".equalsIgnoreCase(commonDataManager.getCancelExport())) {
+				CommonDataManager cdm = (request != null && request.getSession() != null)
+						? (CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)
+						: null;
+				if (cdm != null && "true".equalsIgnoreCase(cdm.getCancelExport())) {
 					reportGenerationThread.interrupt();
 					setJasperPrintTemp(null);
+					log.error("[getILMReportDateRange] Report generation was cancelled by user.");
 				}
 			}

-
-			// Wait for the report generation thread to finish
 			reportGenerationThread.join();
-			// Get the JasperPrint object
+
 			jasperPrint = getJasperPrintTemp();
-			if (jasperPrint != null) {
-				pagesize = jasperPrint.getPages();
-
-				if (pagesize.size() == 0) {
-					jasperPrint = JasperFillManager.fillReport(jasperReport, parms,
-							new JREmptyDataSource(1));
-				}
+			if (jasperPrint == null) {
+				log.error("[getILMReportDateRange] JasperPrint is null after thread execution.");
+				throw new SwtException("Report could not be generated. JasperPrint is null.");
+			}
+
+			pagesize = jasperPrint.getPages();
+			if (pagesize.isEmpty()) {
+				log.error("[getILMReportDateRange] Report has no pages. Falling back to empty datasource.");
+				jasperPrint = JasperFillManager.fillReport(jasperReport, parms, new JREmptyDataSource(1));
 			}
-			log.debug(this.getClass().getName() + "- [getILMReportDateRange] - Exiting ");
+
+			log.info("[getILMReportDateRange] Report generated successfully. Pages: " + jasperPrint.getPages().size());
+
 		} catch (Exception exp) {
 			exp.printStackTrace();
-			log.error(this.getClass().getName()
-					+ "- [getILMReportDateRange] - Exception "
-					+ exp.getMessage());
+			log.error("[getILMReportDateRange] Exception: " + exp.getMessage(), exp);
 			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
 					exp, "getILMReportDateRange", ReportsDAOHibernate.class), request, "");
 			throw new SwtException(exp.getMessage());
 		} finally {
-			JDBCCloser.close(new Connection[] { connection });
+			JDBCCloser.close(new Connection[]{connection});
 			jasperReport = null;
 			parms = null;
 			pagesize = null;
+			log.error("[getILMReportDateRange] - Exiting");
 		}
-
+
 		return jasperPrint;
 	}
-
+
+
 	/**
 	 * Fill in dictionary
 	 * @param request
@@ -1709,4 +1668,4 @@ public class ReportsDAOHibernate extends HibernateDaoSupport implements

 	}

-}
\ No newline at end of file
+}
diff --git a/src/main/java/org/swallow/reports/web/ReportsAction.java b/src/main/java/org/swallow/reports/web/ReportsAction.java
index 0c022d86..0fb4697f 100644
--- a/src/main/java/org/swallow/reports/web/ReportsAction.java
+++ b/src/main/java/org/swallow/reports/web/ReportsAction.java
@@ -15,6 +15,7 @@ package org.swallow.reports.web;

 import java.awt.*;
 import java.io.FileInputStream;
+import java.io.IOException;
 import java.io.InputStream;
 import java.text.SimpleDateFormat;
 import java.util.ArrayList;
@@ -32,6 +33,7 @@ import javax.servlet.http.HttpServletRequest;
 import javax.servlet.http.HttpServletResponse;

 import com.thoughtworks.xstream.io.xml.StaxDriver;
+import net.sf.jasperreports.engine.JRException;
 import org.apache.commons.logging.Log;
 import org.apache.commons.logging.LogFactory;
 import org.apache.poi.ss.usermodel.IndexedColors;
@@ -1295,8 +1297,9 @@ HttpServletResponse response = ServletActionContext.getResponse();
 					&& !SwtUtil.isEmptyOrNull(newILMReportSchedConfig) && ("false".equals(newILMReportSchedConfig))) {
 				defaultCcyId = report.getCurrencyCode();
 			}
-			// Set the date in currency timeframe
-			dateInCcyTimeframe = SwtUtil.getSysParamDateWithEntityOffset(entityId);
+                        // Set the date in currency timeframe based on entity and currency
+                        dateInCcyTimeframe = reportsManager.getDateInCcyTimeframe(defaultEntityId,
+                                        defaultCcyId, SwtUtil.getSystemDateFromDB());
 			report.setEntityId(defaultEntityId);
 			report.setCurrencyCode(defaultCcyId);
 			if (SwtConstants.ALL_VALUE.equals(defaultEntityId)
@@ -1304,34 +1307,100 @@ HttpServletResponse response = ServletActionContext.getResponse();
 				request.setAttribute("screenFieldsStatus", SwtConstants.STR_TRUE);
 			else
 				request.setAttribute("screenFieldsStatus", SwtConstants.STR_FALSE);
+
+
+			// Put the currencies list in the form
+			ccyList = new ArrayList<LabelValueBean>();
+			// add the all label into currency list
+			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
+			// get the collection of currency access
+			ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(defaultEntityId, roleId));
+			if (collUserEntityAccess != null)
+				ccyList.addAll(ccyAccessColl);
+
+
+			// The case when the default ccy ID is not the ccy list, then select 'All' value, the first element in the list
+			if (ccyAccessColl.size() > 0 && !defaultCcyId.equals("All")) {
+				for (LabelValueBean ccy : ccyAccessColl) {
+					if (ccy.getValue().equals(defaultCcyId)) {
+						defaultCcyInTheList = true;
+						break;
+					}
+				}
+			}
+
+			if(!defaultCcyInTheList){
+				defaultCcyId = "All";
+			}
+
+
 			ILMGeneralMaintenanceManager ilmGenMgr=(ILMGeneralMaintenanceManager)(SwtUtil.getBean("ilmGeneralMaintenanceManager"));
 			ilmAccountGroups = ilmGenMgr.getAllowedAccountGroupsListForUser(hostId, userId, defaultEntityId, defaultCcyId, reportType);
-			// To get the test date
-			Date testDate = SwtUtil.getSysParamDateWithEntityOffset(defaultEntityId);
-			Calendar cal = Calendar.getInstance();
-			cal.setTime(testDate);
-			cal.add(Calendar.DATE, -1);
+                        // Compute the maximum allowed report date (T-1) for the selected
+                        // entity and currency. In case of multi-currency, use the earliest
+                        // T-1 among available currencies.
+						Calendar cal = Calendar.getInstance();
+                        Date earliestTMinusOne = null;
+                        Date earliestCcyDate = null;
+                        if (SwtConstants.ALL_VALUE.equals(defaultCcyId)) {
+                                if (ccyAccessColl != null) {
+									Date dt = null;
+                                        for (LabelValueBean ccy : ccyAccessColl) {
+												dt = reportsManager.getDateInCcyTimeframe(defaultEntityId,
+														ccy.getValue(), SwtUtil.getSystemDateFromDB());
+                                                cal.setTime(dt);
+                                                cal.add(Calendar.DATE, -1);
+                                                Date tMinusOne = cal.getTime();
+                                                if (earliestTMinusOne == null || tMinusOne.before(earliestTMinusOne)) {
+                                                        earliestTMinusOne = tMinusOne;
+                                                        earliestCcyDate = dt;
+                                                }
+                                        }
+                                }
+                        } else {
+							Date inputDate = SwtUtil.getSystemDateFromDB();
+							inputDate = reportsManager.getDateInCcyTimeframe(defaultEntityId, defaultCcyId, inputDate);
+
+
+							cal.setTime(inputDate);
+							cal.add(Calendar.DATE, -1);
+							earliestTMinusOne = cal.getTime();
+							earliestCcyDate = inputDate;
+                        }

-			String ondayBefore = SwtUtil.formatDate(cal.getTime(), SwtUtil
-					.getCurrentSystemFormats(request.getSession())
+                        String ondayBefore = SwtUtil.formatDate(earliestTMinusOne,
+                                        SwtUtil.getCurrentSystemFormats(request.getSession())
 					.getDateFormatValue());
+                        request.setAttribute("maxReportDate", ondayBefore);

-			cal.setTime(testDate);
+                        cal.setTime(earliestCcyDate);
+                        dateInCcyTimeframe = earliestCcyDate;
 			//This line is hidden as When the current date is mid-Febraury 2020, the date range defaults to 1st to 31st December 2019, when it should actually be January.
 //			if(cal.get(Calendar.MONTH)==1)
 //				cal.set(cal.get(Calendar.YEAR)-1, 11, 1);
 //			else
-				cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH)-1, 1);
-
+			cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH)-1, 1);
 			String firstDateOfPreviousMonth = null;
 			String defaultFirstDateOfPreviousMonth = null;
-			if(SwtUtil.isEmptyOrNull(report.getFromDateAsString())) {
-				firstDateOfPreviousMonth= SwtUtil.formatDate(cal.getTime(), SwtUtil.getCurrentSystemFormats(request.getSession())
-						.getDateFormatValue());
-
+
+			String fromDateAsString = report.getFromDateAsString();
+			String dateFormat = SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue();
+			Date calDate = cal.getTime();
+
+			if (SwtUtil.isEmptyOrNull(fromDateAsString)) {
+				firstDateOfPreviousMonth = SwtUtil.formatDate(calDate, dateFormat);
 			} else {
-				firstDateOfPreviousMonth = report.getFromDateAsString();
+				Date fromDateAsDate = SwtUtil.parseDate(fromDateAsString, dateFormat);
+
+				if (calDate.before(fromDateAsDate)) {
+					firstDateOfPreviousMonth = SwtUtil.formatDate(calDate, dateFormat);
+				} else {
+					firstDateOfPreviousMonth = fromDateAsString;
+				}
 			}
+
+// Optionally assign this value to defaultFirstDateOfPreviousMonth if needed:
+			defaultFirstDateOfPreviousMonth = firstDateOfPreviousMonth;

 			defaultFirstDateOfPreviousMonth = SwtUtil.formatDate(cal.getTime(), SwtUtil.getCurrentSystemFormats(request.getSession())
 					.getDateFormatValue());
@@ -1352,39 +1421,47 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			request.setAttribute("defaultFirstDateOfPreviousMonth", defaultFirstDateOfPreviousMonth);
 			if (SwtUtil.isEmptyOrNull(report.getToDateAsString())) {
 				request.setAttribute("lastDateOfPreviousMonth", lastDateOfPreviousMonth);
-
-			} else {
-				request.setAttribute("lastDateOfPreviousMonth", report.getToDateAsString());
 			}
-
-			if (SwtUtil.isEmptyOrNull(report.getFromDateAsString())) {
+
+
+			if (SwtUtil.isEmptyOrNull(fromDateAsString)) {
 				report.setFromDateAsString(ondayBefore);
+			} else {
+				Date fromDateAsDate = SwtUtil.parseDate(fromDateAsString, dateFormat);
+				Date ondayBeforeAsDate = SwtUtil.parseDate(ondayBefore, dateFormat);
+
+				if (ondayBeforeAsDate.before(fromDateAsDate)) {
+					report.setFromDateAsString(ondayBefore);
+				}
+			}
+
+			// Step 2: Compare with report.getToDateAsString()
+			String toDateAsString = report.getToDateAsString();
+			if (!SwtUtil.isEmptyOrNull(toDateAsString)) {
+				Date toDateAsDate = SwtUtil.parseDate(toDateAsString, dateFormat);
+				Date lastDayOfPreviousMonthAsDate = SwtUtil.parseDate(lastDateOfPreviousMonth, dateFormat);
+
+				if (toDateAsDate.after(lastDayOfPreviousMonthAsDate)) {
+					report.setToDateAsString(lastDateOfPreviousMonth);
+					if(!"S".equals(report.getSingleOrRange())){
+						report.setFromDateAsString(defaultFirstDateOfPreviousMonth);
+					}
+				}
+				request.setAttribute("lastDateOfPreviousMonth", report.getToDateAsString());
+
+
 			}
+
 			// set attribute for entity label value
 			request.setAttribute("entities", entitiesLblValue);
-			// Put the currencies list in the form
-			ccyList = new ArrayList<LabelValueBean>();
-			// add the all label into currency list
-			ccyList.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));
-			// get the collection of currency access
-			ccyAccessColl = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAccessLVL(ilmAnalysisMonitorManager.getEntityCurrency(defaultEntityId, roleId));
-			if (collUserEntityAccess != null)
-				ccyList.addAll(ccyAccessColl);
+
 			scenariosLblValue = new ArrayList<LabelValueBean>();
 			scenariosLblValue.add(new LabelValueBean("Standard",
 					"Standard"));
 			// Get the allowed scenarios that will be listed in the screen
 			ILMTransScenarioMaintenanceManager ilmScenManager = (ILMTransScenarioMaintenanceManager)(SwtUtil.getBean("ilmTransScenarioMaintenanceManager"));

-			// The case when the default ccy ID is not the ccy list, then select 'All' value, the first element in the list
-			if (ccyAccessColl.size() > 0 && !defaultCcyId.equals("All")) {
-				for (LabelValueBean ccy : ccyAccessColl) {
-					if (ccy.getValue().equals(defaultCcyId)) {
-						defaultCcyInTheList = true;
-						break;
-					}
-				}
-			}
+

 			entities.add("*");
 			currencies.add("*");
@@ -1459,6 +1536,7 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			request.setAttribute("costumerPayments", report.getCostumerPayments());
 			setReports(report);
 		} catch (Exception exp) {
+			exp.printStackTrace();
 			log.error(this.getClass().getName()
 					+ " - Exception Catched in [getILMReport] method : - "
 					+ exp.getMessage());
@@ -1786,56 +1864,80 @@ HttpServletResponse response = ServletActionContext.getResponse();
 				//out.close();

 		    }else {
-		    	if (report.getSingleOrRange().equalsIgnoreCase("S")) {
-					// Single day case
-					jasperPrint = reportsManager.getILMReportDaily(request, report, hostId, dbLink, roleId);
-				} else {
-					// Date range case
-					jasperPrint = reportsManager.getILMReportMonthly(request, report, hostId, dbLink, roleId);
-				}
-
-				// Instance for JRPDFExporter.
-				exporter = new JRPdfExporter();
-				// Intiasing the outputstream
-				out = response.getOutputStream();
-				// To set the output type as PDF file
-				response.setContentType("application/pdf");
-				// Set the name of the PDF document to be compiled
-				documentName = report.getSingleOrRange().equalsIgnoreCase("S") ? "Daily" : "DateRange";
-				if ((SwtConstants.ILM_REPORT_TYPE_GROUP).equals(report.getReportType())) {
-					documentName += "LiquidityManagementReport-ILMGroup";
-				} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_A).equals(report.getReportType())) {
-					documentName += "LiquidityManagementReport-BaselA";
-				} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_B).equals(report.getReportType())) {
-					documentName += "LiquidityManagementReport-BaselB";
-				} else if ((SwtConstants.ILM_REPORT_TYPE_BASEL_C).equals(report.getReportType())) {
-					documentName += "BCBS-248ReportC";
-				}else if(SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM.equals(report.getReportType())){
-					documentName += "IntradayRisk-NetCumulativePositions";
-				}
-				// To set the content as attachment
-				response.setHeader("Content-disposition", "attachment; filename="
-						+ documentName + "-SmartPredict_"
-						+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
-						+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
-				// To pass the filled report
-				exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
-				// Providing the output stream
-				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
-				// Exporting as UTF-8
-				exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,
-						"UTF-8");
-				// downloadTokenValue will have been provided in the form submit via the hidden input field
-				// Source scan tools may report a "HTTP response splitting" vulnerability. A generic solution of
-				// ignoring text after CRLFs is implemented in XSSFilter class, so can safely ignore the report.
-				tokenForDownload = request.getParameter("tokenForDownload");
+				try {
+					log.debug("[ILMReport] Starting report generation. Type: {}"+ report.getSingleOrRange());

+					if ("S".equalsIgnoreCase(report.getSingleOrRange())) {
+						log.debug("[ILMReport] Generating DAILY report for hostId: {}, roleId: {}"+ hostId+ roleId);
+						jasperPrint = reportsManager.getILMReportDaily(request, report, hostId, dbLink, roleId);
+					} else {
+						log.debug("[ILMReport] Generating MONTHLY report for hostId: {}, roleId: {}"+ hostId+ roleId);
+						jasperPrint = reportsManager.getILMReportMonthly(request, report, hostId, dbLink, roleId);
+					}
+
+					if (jasperPrint == null) {
+						log.error("[ILMReport] JasperPrint is NULL. Report generation failed.");
+						throw new SwtException("Failed to generate report: jasperPrint is null");
+					}

-				response.addCookie(new Cookie("fileDownloadToken", tokenForDownload+"|OK"));
+					exporter = new JRPdfExporter();
+					out = response.getOutputStream();
+
+					log.info("[ILMReport] Setting response content type to PDF");
+					response.setContentType("application/pdf");
+
+					documentName = "S".equalsIgnoreCase(report.getSingleOrRange()) ? "Daily" : "DateRange";
+					switch (report.getReportType()) {
+						case SwtConstants.ILM_REPORT_TYPE_GROUP:
+							documentName += "LiquidityManagementReport-ILMGroup";
+							break;
+						case SwtConstants.ILM_REPORT_TYPE_BASEL_A:
+							documentName += "LiquidityManagementReport-BaselA";
+							break;
+						case SwtConstants.ILM_REPORT_TYPE_BASEL_B:
+							documentName += "LiquidityManagementReport-BaselB";
+							break;
+						case SwtConstants.ILM_REPORT_TYPE_BASEL_C:
+							documentName += "BCBS-248ReportC";
+							break;
+						case SwtConstants.ILM_REPORT_TYPE_INTRADAY_RISK_NET_CUM:
+							documentName += "IntradayRisk-NetCumulativePositions";
+							break;
+						default:
+							log.warn("[ILMReport] Unknown report type: {}"+ report.getReportType());
+					}

-				// Export Report
-				exporter.exportReport();
-		    }
+					String fileName = documentName + "-SmartPredict_" +
+									  SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_" +
+									  SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf";
+
+					log.debug("[ILMReport] Setting Content-disposition header with filename: {}"+ fileName);
+					response.setHeader("Content-disposition", "attachment; filename=" + fileName);
+
+					// Set exporter parameters
+					log.debug("[ILMReport] Setting exporter parameters");
+					exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
+					exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
+					exporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
+
+					tokenForDownload = request.getParameter("tokenForDownload");
+					log.debug("[ILMReport] Setting fileDownloadToken cookie: {}|OK"+  tokenForDownload);
+					response.addCookie(new Cookie("fileDownloadToken", tokenForDownload + "|OK"));
+
+					// Exporting report
+					log.debug("[ILMReport] Calling exporter.exportReport()");
+					exporter.exportReport();
+					log.debug("[ILMReport] Report export completed successfully");
+
+				} catch (JRException e) {
+					log.error("[ILMReport] JasperReports export failed: {}", e);
+					throw new SwtException("JasperReports export failed", e);
+				} catch (IOException e) {
+					log.error("[ILMReport] Error writing to response output stream: {}", e);
+					throw new SwtException("Response I/O error", e);
+				}
+
+			}



@@ -2026,10 +2128,10 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			}else {
 				fromDateAsString = ""+fromDate;
 			}
-			if (!SwtUtil.isEmptyOrNull(fromDateAsString))
+			if (!SwtUtil.isEmptyOrNull(fromDateAsString))
 				fromDateAsDate = SwtUtil.parseDate(fromDateAsString, dateFormat);
-
-
+
+
 			if(SysParamsDAOHibernate.keywordQuery.containsKey(toDate)) {
 				toDateAsString = SwtUtil.convertKeywordToDate(request.getSession(),
 						toDate, false,
@@ -2138,4 +2240,4 @@ HttpServletResponse response = ServletActionContext.getResponse();



-}
\ No newline at end of file
+}
diff --git a/src/main/java/org/swallow/util/ApplicationContextListner.java b/src/main/java/org/swallow/util/ApplicationContextListner.java
index f60d3f65..71e4f8ab 100644
--- a/src/main/java/org/swallow/util/ApplicationContextListner.java
+++ b/src/main/java/org/swallow/util/ApplicationContextListner.java
@@ -419,7 +419,7 @@ public class ApplicationContextListner implements ApplicationListener {
 				mailProps.put("mail."+protocol+".starttls.enable", "true");
 				mailProps.put("mail."+protocol+".ssl.trust", "*");
 				if(SwtUtil.isEmptyOrNull(sslProtocls)) {
-					mailProps.put("mail."+protocol+".ssl.protocols", "TLSv1 TLSv1.1 TLSv1.2");
+					mailProps.put("mail."+protocol+".ssl.protocols", "TLSv1 TLSv1.1 TLSv1.2 TLSv1.3");
 				}else {
 					mailProps.put("mail."+protocol+".ssl.protocols", sslProtocls.replaceAll("(,|\\s)+", " "));
 				}
diff --git a/src/main/java/org/swallow/util/SessionManager.java b/src/main/java/org/swallow/util/SessionManager.java
index a118bbb9..21cbabdb 100644
--- a/src/main/java/org/swallow/util/SessionManager.java
+++ b/src/main/java/org/swallow/util/SessionManager.java
@@ -291,9 +291,12 @@ public class SessionManager {
 	public synchronized boolean registerSession(HttpSessionBindingEvent event) {
 		log.debug("entering 'registerSession' function");

-		String sessionId = event.getSession().getId();
-		log.debug("sessionId - " + sessionId);
-		_sessionMap.put(sessionId, event.getSession());
+		synchronized (sessionMapLock) {
+			String sessionId = event.getSession().getId();
+			log.debug("sessionId - " + sessionId);
+			_sessionMap.put(sessionId, event.getSession());
+			// rest of the method
+		}

 		/*
 		try {
@@ -326,9 +329,9 @@ public class SessionManager {
 	 * @param session
 	 * @return
 	 */
-	Object unregisterSessionLock = new Object();
+	Object sessionMapLock = new Object();
 	public synchronized boolean unregisterSession(HttpSession session) {
-		synchronized (unregisterSessionLock) {
+		synchronized (sessionMapLock) {
 		// local variable declaration
 		boolean retValue = false;

diff --git a/src/main/java/org/swallow/util/SwtConstants.java b/src/main/java/org/swallow/util/SwtConstants.java
index ecff6432..9122fe89 100644
--- a/src/main/java/org/swallow/util/SwtConstants.java
+++ b/src/main/java/org/swallow/util/SwtConstants.java
@@ -2474,6 +2474,64 @@ public final class SwtConstants {
 		public static final String MAINT_EVENT_NEXT_ID_HEADING_TOOLTIP = "maintEvent.tooltip.nextId";

 		public static final String MAINT_EVENT_OLD_VALUE = "_oldValue";
+
+
+
+	public static final String MULTI_MVT_ACTIONS= "multiMvtActions";
+
+	public static final String MULTI_MVT_SELECT= "select";
+	public static final String MULTI_MVT_ACCESS= "mvtAccess";
+	public static final String MULTI_MVT_MOVEMENT_ID= "movementId";
+	public static final String MULTI_MVT_ENTITY= "entity";
+	public static final String MULTI_MVT_CCY = "ccy";
+	public static final String MULTI_MVT_VDate = "vdate";
+	public static final String MULTI_MVT_ACCOUNT= "account";
+	public static final String MULTI_MVT_AMOUNT = "amount";
+	public static final String MULTI_MVT_SIGN= "sign";
+	public static final String MULTI_MVT_PRED = "pred";
+	public static final String MULTI_MVT_EXT = "ext";
+	public static final String MULTI_MVT_ILMFCAST= "ilmFcast";
+	public static final String MULTI_MVT_MATCH_STATUS = "status";
+	public static final String MULTI_MVT_REF1 = "ref1";
+	public static final String MULTI_MVT_REF2 = "ref2";
+	public static final String MULTI_MVT_EXTRA_REF = "extraRef";
+	public static final String MULTI_MVT_BOOK = "book";
+	public static final String MULTI_MVT_MATCH_ID = "matchId";
+	public static final String MULTI_MVT_SOURCE = "source";
+	public static final String MULTI_MVT_FORMAT = "format";
+	public static final String MULTI_MVT_BOOK_CODE = "bookCode";
+	public static final String MULTI_MVT_CPARTY= "cparty";
+	public static final String MULTI_MVT_ORD_INST= "ordInst";
+	public static final String MULTI_MVT_EXP_SETTLEMENT = "expSettlement";
+	public static final String MULTI_MVT_ACT_SETTLEMENT = "actSettlement";
+	public static final String MULTI_MVT_CRIT_PAY_TYPE="critPayType";
+
+	public static final String MULTI_MVT_MOVEMENT_ID_HEADER= "multiMvtActions.movementId";
+	public static final String MULTI_MVT_ENTITY_HEADER= "multiMvtActions.entity";
+	public static final String MULTI_MVT_CCY_HEADER = "multiMvtActions.ccy";
+	public static final String MULTI_MVT_VDate_HEADER = "multiMvtActions.vdate";
+	public static final String MULTI_MVT_ACCOUNT_HEADER= "multiMvtActions.account";
+	public static final String MULTI_MVT_AMOUNT_HEADER = "multiMvtActions.amount";
+	public static final String MULTI_MVT_SIGN_HEADER= "multiMvtActions.sign";
+	public static final String MULTI_MVT_PRED_HEADER = "multiMvtActions.pred";
+	public static final String MULTI_MVT_EXT_HEADER = "multiMvtActions.ext";
+	public static final String MULTI_MVT_ILMFCAST_HEADER= "multiMvtActions.ilmFcast";
+	public static final String MULTI_MVT_MATCH_STATUS_HEADER = "multiMvtActions.status";
+	public static final String MULTI_MVT_REF1_HEADER = "multiMvtActions.ref1";
+	public static final String MULTI_MVT_REF2_HEADER = "multiMvtActions.ref2";
+	public static final String MULTI_MVT_EXTRA_REF_HEADER = "multiMvtActions.extraRef";
+	public static final String MULTI_MVT_BOOK_HEADER = "multiMvtActions.book";
+	public static final String MULTI_MVT_MATCH_ID_HEADER = "multiMvtActions.matchId";
+	public static final String MULTI_MVT_SOURCE_HEADER = "multiMvtActions.Source";
+	public static final String MULTI_MVT_FORMAT_HEADER = "multiMvtActions.format";
+	public static final String MULTI_MVT_BOOK_CODE_HEADER = "multiMvtActions.bookCode";
+	public static final String MULTI_MVT_CPARTY_HEADER= "multiMvtActions.cparty";
+	public static final String MULTI_MVT_ORD_INST_HEADER= "multiMvtActions.ordInst";
+	public static final String MULTI_MVT_EXP_SETTLEMENT_HEADER = "multiMvtActions.expSettlement";
+	public static final String MULTI_MVT_ACT_SETTLEMENT_HEADER = "multiMvtActions.actSettlement";
+	public static final String MULTI_MVT_CRIT_PAY_TYPE_HEADER="multiMvtActions.critPayType";
+	public static final String MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME = "MultiMovementUpdateNumber";
+

 		public static final String TABLE_ACCOUNT= "table.account";
 		public static final String TABLE_MOVEMENT= "table.movement";
@@ -2484,4 +2542,4 @@ public final class SwtConstants {
 		public static final String OPPORTUNITY_COST_REPORT_OUTPUT_CSV = "C";

 		public static final String PROPERTY_ENV_TEST_ENABLED = "ENV_TEST_ENABLED";
-}
\ No newline at end of file
+}
diff --git a/src/main/java/org/swallow/util/SwtUtil.java b/src/main/java/org/swallow/util/SwtUtil.java
index 10c08fe2..8b04ba5c 100644
--- a/src/main/java/org/swallow/util/SwtUtil.java
+++ b/src/main/java/org/swallow/util/SwtUtil.java
@@ -130,13 +130,8 @@ import org.swallow.batchScheduler.ConnectionManager;
 import org.swallow.cluster.ZkUtils;
 import org.swallow.control.dao.EntityProcessDAO;
 import org.swallow.control.dao.soap.Heartbeat;
-import org.swallow.control.model.EntityCurrencyGroupAccess;
-import org.swallow.control.model.ErrorLog;
-import org.swallow.control.model.MenuAccessOptionsGui;
-import org.swallow.control.model.Role;
-import org.swallow.control.model.RoleTO;
-import org.swallow.control.model.UserAuthDetails;
-import org.swallow.control.model.UserMaintenance;
+import org.swallow.control.model.*;
+import org.swallow.control.service.ArchiveManager;
 import org.swallow.control.service.ErrorLogManager;
 import org.swallow.control.service.RoleManager;
 import org.swallow.control.service.UserMaintenanceManager;
@@ -777,16 +772,20 @@ public final class SwtUtil {
 			throws SwtException {
 		try {
 			if (session.getAttribute(SwtConstants.CDM_BEAN) != null) {
-				return ((CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN))
+				if(((CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN)).getUser() != null)
+					if(((CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN)).getUser().getId() != null)
+						return ((CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN))
 						.getUser().getId().getUserId();
 			} else {
 				return null;
 			}

+
 		} catch (Exception e) {
 			log.error("Error on getCurrentUserId, cause: "+e.getMessage(), e);
 			return null;
 		}
+		return null;
 	}

 	public static String getCurrentHostId(HttpSession session) {
@@ -3739,36 +3738,37 @@ public final class SwtUtil {
 		/* Variable Declaration for minute */
 		int minute = 0;
 		try {
-
-			log.debug("SwtUtil - [getLastRefTimeOnGMTOffsetILM] - " + "Entry");
-			workFlowMgr = (WorkflowMonitorManager) (SwtUtil
-					.getBean("workflowMonitorManager"));
-			/* get Oracle System time. */
-			// Start: Code modified by Balaji for mantis 1991
+
+			log.debug("SwtUtil - [getLastRefTimeOnGMTOffsetILM] - Entry");
+
+			workFlowMgr = (WorkflowMonitorManager) (SwtUtil.getBean("workflowMonitorManager"));
+
+			/* Get Oracle System time. */
 			// Condition to check entity id is null or all
-			if (SwtUtil.isEmptyOrNull(entityId)
-					|| entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
-				// set default entity Id
+			if (SwtUtil.isEmptyOrNull(entityId) || entityId.equalsIgnoreCase(SwtConstants.ALL_VALUE)) {
+				// Set default entity Id
 				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
 			}

-			/* get Entity Time Offset for user current Entity. */
-			entityTimeOffset = workFlowMgr.getEntityOffset(entityId);
+			/* Get Entity Time Offset for user current Entity. */
 			entityTimeOffset = workFlowMgr.getEntityOffset(entityId);
-			// End: Code modified by Balaji for mantis 1991
-			/* set time format. */
-			formatter = new SimpleDateFormat("HH:mm:ss");
+
+			/* Set time format. */
+			formatter = new SimpleDateFormat("HH:mm");
+
 			/* Server Date parsed from given time string. */
 			serverDate = getSysParamDateWithEntityOffset(entityId);
-			/* get Calendar instance. */
+
+			/* Get Calendar instance and set server date. */
 			Calendar cal = Calendar.getInstance();
-			/* set server date in calendar. */
 			cal.setTime(serverDate);
-//			/* check whether Entity Time Offset contains "+" or "-" */
-			/* set time format for display. */
-			formatter = new SimpleDateFormat("HH:mm");
-			/* get Last Refresh time with HH:mm format. */
-			lastRefresh =  "[GMT " + entityTimeOffset + "]";
+
+			/* Format the time */
+			String timeFormatted = formatter.format(serverDate);
+
+			/* Get Last Refresh time with HH:mm format + GMT offset. */
+			lastRefresh = timeFormatted + " [GMT " + entityTimeOffset + "]";
+
 			/* Trim Last Refresh time. */
 			lastRefresh = lastRefresh.trim();
 			log.debug("SwtUtil - [getLastRefTimeOnGMTOffsetILM] - " + "Exit");
@@ -7667,4 +7667,53 @@ public final class SwtUtil {
 		}
 	}

-}
\ No newline at end of file
+	/**
+	 * Format table name with database link based on archive type
+	 * @param tableName the table name
+	 * @param dbLink the database link
+	 * @param archiveType the archive type ('D' for database link, 'S' for schema)
+	 * @return formatted table name
+	 */
+	public static String formatTableWithDbLink(String tableName, String dbLink, String archiveType) {
+		if (isEmptyOrNull(dbLink)) {
+			return tableName;
+		}
+
+		if ("S".equalsIgnoreCase(archiveType)) {
+			// Schema-based: dblink.tableName
+			return dbLink + "." + tableName;
+		} else {
+			// Database link: tableName@dblink
+			return tableName + "@" + dbLink;
+		}
+	}
+
+	/**
+	 * Get archive information and format table name accordingly
+	 * @param hostId the host ID
+	 * @param archiveId the archive ID
+	 * @param tableName the table name
+	 * @return formatted table name
+	 * @throws SwtException
+	 */
+	public static String getFormattedTableName(String hostId, String archiveId, String tableName) throws SwtException {
+		if (isEmptyOrNull(archiveId)) {
+			return tableName;
+		}
+
+		try {
+			ArchiveManager archiveManager = (ArchiveManager) getBean("archiveManager");
+			Archive archive = archiveManager.getArchiveById(hostId, archiveId);
+
+			if (archive == null) {
+				return tableName;
+			}
+
+			return formatTableWithDbLink(tableName, archive.getDb_link(), archive.getArchiveType());
+		} catch (Exception e) {
+			log.error("Error formatting table name with archive: " + e.getMessage());
+			return tableName;
+		}
+	}
+
+}
diff --git a/src/main/java/org/swallow/util/XSSUtil.java b/src/main/java/org/swallow/util/XSSUtil.java
index db5cbe85..561af364 100644
--- a/src/main/java/org/swallow/util/XSSUtil.java
+++ b/src/main/java/org/swallow/util/XSSUtil.java
@@ -867,13 +867,11 @@ public class XSSUtil {
 									.getCurrentSystemFormats(request.getSession()), request
 									.getParameter("qname").trim());
 						}else {
-							ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
-							String dbLink = archiveManager.getDBlink(archiveId);
 							sweepDetailVO = sweepDetailManager.getSweepDetailsArchive(request
 									.getParameter("entid"), Long.valueOf(request
 									.getParameter("swpid")), SwtUtil
 									.getCurrentSystemFormats(request.getSession()), request
-									.getParameter("qname").trim(), dbLink);
+									.getParameter("qname").trim(), archiveId);
 						}

 						// Get the account details
diff --git a/src/main/java/org/swallow/web/AngularFilter.java b/src/main/java/org/swallow/web/AngularFilter.java
index 49072b7a..72fa5e1e 100644
--- a/src/main/java/org/swallow/web/AngularFilter.java
+++ b/src/main/java/org/swallow/web/AngularFilter.java
@@ -62,14 +62,14 @@ import org.xml.sax.InputSource;
  * This filter is actually used in dev mode only with Angular client
  * It avoids error appearing when requesting a cross site request inside the Angular client
  * It is mapped for every http request (.do, .action, .js, .html ...)
- *
+ *
  * <AUTHOR> Chebka,
  *
  */
 //@WebFilter(urlPatterns = "/*")
 public class AngularFilter implements Filter{
 	private static final Log log = LogFactory.getLog(AngularFilter.class);
-
+
 	// set to true when requests are from Angular client
 	public static String IS_HTML5_UNDOCK = "is_html5_undock";
 	public static String SUCCESS_HTML5 = "success-html5";
@@ -82,7 +82,7 @@ public class AngularFilter implements Filter{
 		}
 	};
 	public void init(FilterConfig arg0) throws ServletException {
-
+
 	}

 	public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain)
@@ -100,7 +100,7 @@ public class AngularFilter implements Filter{
 			boolean isChangepwdRequest = httpservletrequest.getRequestURI().indexOf("system/changepassword!save.do")>-1;

 			if(responseJson || responseBinary){
-
+
 				//System.out.println("SessionID: "+httpservletrequest.getSession().getId());
 				//  If HTML5 undock call
 				if(httpservletrequest.getRequestURI().indexOf("undock.do")>-1){
@@ -134,37 +134,10 @@ public class AngularFilter implements Filter{

 				if (isChangepwdRequest) {
 					httpservletrequest.setAttribute("cbc-mode-encryption", "cbc");
-				}
-
-				// New session is provided by Angular CLI (even if a proxy is set)
-				CommonDataManager CDM = (CommonDataManager) httpservletrequest.getSession().getAttribute(
-						SwtConstants.CDM_BEAN);
-				// Assign the existing CDM to current session
-				if(CDM == null){
-					 ConcurrentHashMap<String, HttpSession> sessions= SessionManager.getInstance().getSessionMap();
-					 for(HttpSession session:sessions.values()){
-						// Hard copy of session attributes
-						 CDM = (CommonDataManager) session.getAttribute(SwtConstants.CDM_BEAN);
-						 if(CDM != null){
-							 Enumeration attrNames = session.getAttributeNames();
-							 while(attrNames.hasMoreElements()){
-								 String attrName = (String)attrNames.nextElement();
-								 httpservletrequest.getSession().setAttribute(attrName, session.getAttribute(attrName));
-							 }
-
-							 // Forcer la session via le cookie JSESSIONID
-							 Cookie cookie = new Cookie("JSESSIONID", session.getId());
-						     cookie.setPath(httpservletrequest.getContextPath());
-						     cookie.setSecure(false);
-							 httpservletresponse.addCookie(cookie);
-
-							 break;
-						 }
-					 }
 				}
-
+
 				// Wrapping the response to allow transforming XML content into json (ONLY Angular responses that needs json, keep XML for Flex screens)
-				boolean jsonResponse = !"true".equalsIgnoreCase(httpservletrequest.getParameter("source_isflex")) && !responseBinary;
+				boolean jsonResponse = !"true".equalsIgnoreCase(httpservletrequest.getParameter("source_isflex")) && !responseBinary;

 				// Override if url is in the list
 				for(String response:JSON_RESPONSE_LIST){
@@ -201,11 +174,11 @@ public class AngularFilter implements Filter{
 	}


-
+
 	/**
 	 * Angular response wrapper
 	 * It allows transforming XML content into Json
-	 *
+	 *
 	 */
 	private static class AngularResponseWrapper extends HttpServletResponseWrapper {
 		boolean isXmlResponse = true;
@@ -213,7 +186,7 @@ public class AngularFilter implements Filter{
 		HttpServletResponse response = null;
 		ServletOutputStream stream = null;
 		PrintWriter writer = null;
-
+
 		/**
 		 * Overriden constructor
 		 * @param response
@@ -223,14 +196,14 @@ public class AngularFilter implements Filter{
 			this.request = request;
 			this.response = response;
 		}
-
+
 		@Override
 		public void setContentType(String type) {
 			// XML content type to be set as Json (text)
 			isXmlResponse = "text/xml".equalsIgnoreCase(type);
 			super.setContentType(type);
 		}
-
+
 		public void finishResponse() {
 			try {
 				if (writer != null) {
@@ -243,12 +216,12 @@ public class AngularFilter implements Filter{
 			} catch (IOException e) {
 			}
 		}
-
+
 		@Override
 		public void flushBuffer() throws IOException {
 			stream.flush();
 		}
-
+
 		@Override
 		public void addHeader(String name, String value) {
 			// Ignore it asking for: Content-Length on json responses
@@ -257,7 +230,7 @@ public class AngularFilter implements Filter{
 				super.addHeader(name, value);
 			}
 		}
-
+
 		@Override
 		public ServletOutputStream getOutputStream() throws IOException {
 			if (writer != null) {
@@ -284,7 +257,7 @@ public class AngularFilter implements Filter{
 			writer = new PrintWriter(new OutputStreamWriter(stream, "UTF-8"));
 			return (writer);
 		}
-
+
 		private ServletOutputStream createOutputStream() throws IOException {
 			final ServletOutputStream ros = super.getOutputStream();
 			if(isXmlResponse){
@@ -296,7 +269,7 @@ public class AngularFilter implements Filter{
 					public void write(byte[] bytes) throws IOException {
 						tos.write(bytes);
 					}
-
+
 					@Override
 					public void write(int arg0) throws IOException {
 						tos.write(arg0);
@@ -306,7 +279,7 @@ public class AngularFilter implements Filter{
 					public void println(String s) throws IOException {
 						ros.println(isXmlResponse?xmlToJson(s):s);
 					}
-
+
 					@Override
 					public void write(byte[] b, int off, int len) throws IOException {
 						tos.write(b, off, len);
@@ -400,8 +373,8 @@ public class AngularFilter implements Filter{
 //							System.err.println("-azeaze--"+content);
 							content = content.trim();
 							content = StringEscapeUtils.unescapeJava(content);
-
-							// Get rid of error : The entity "nbsp" was referenced, but not declared.
+
+							// Get rid of error : The entity "nbsp" was referenced, but not declared.
 							content = content.replace("&nbsp;", " ");
 							content = sanitizeXmlEntities(content);
 							// Parse the content to Document object
@@ -418,7 +391,7 @@ public class AngularFilter implements Filter{
 							String delimiter = "$||$";
 							int delimterLenght = delimiter.length();
 							XPath xpath = XPathFactory.newInstance().newXPath();
-						NodeList nodes = (NodeList) xpath.evaluate(".//grid/rows/row/node()|.//grid/totals/total/node()|.//singletons/node()", doc,
+							NodeList nodes = (NodeList) xpath.evaluate(".//grid/rows/row/node()|.//grid/totals/total/node()|.//singletons/node()", doc,
 									XPathConstants.NODESET);
 							for (int idx = 0; idx < nodes.getLength(); idx++) {
 								// we will add delimter to all numbers all numbers to protect them from json conversion
@@ -436,7 +409,7 @@ public class AngularFilter implements Filter{
 							JSONObject jsonObj = (JSONObject) SwtUtil.invokeStaticMethod("org.json.XML", "toJSONObject",
 									output);

-
+
 //						 JSONObject json = XML.toJSONObject(xml);
 							JSONTokener tokener = new JSONTokener(jsonObj.toString()) {
 								public Object nextValue() throws JSONException {
@@ -451,7 +424,7 @@ public class AngularFilter implements Filter{
 									return nextValue;
 								}
 							};
-						    jsonObj = new JSONObject(tokener);
+							jsonObj = new JSONObject(tokener);
 							content = (String)SwtUtil.invokeMethod(jsonObj, "toString");

 						} catch (Exception e) {
@@ -468,7 +441,7 @@ public class AngularFilter implements Filter{

 					public void setWriteListener(WriteListener listener) {
 						// TODO Auto-generated method stub
-
+
 					}
 				};
 			}
@@ -477,10 +450,10 @@ public class AngularFilter implements Filter{
 			}
 		}
 	}
-
+
 	/**
 	 * Html 5/Flex mapping: .swf into .html
-	 *
+	 *
 	 * <AUTHOR> Chebka, Swallowtech Tunisia
 	 *
 	 */
@@ -488,9 +461,9 @@ public class AngularFilter implements Filter{
 		String programId;
 		String programName;
 		String html5ProgramName;
-
+
 		static boolean initialized = false;
-
+
 		static ConcurrentHashMap<String, Html5Mapping> mappingById = new ConcurrentHashMap<String, AngularFilter.Html5Mapping>(){
 			public Html5Mapping get(Object key) {
 				if(!initialized){
@@ -498,7 +471,7 @@ public class AngularFilter implements Filter{
 				}
 				return key != null ?super.get(key): null;
 			};
-
+
 			public Html5Mapping put(String key, Html5Mapping value) {
 				if(key != null){
 					return super.put(key, value);
@@ -506,7 +479,7 @@ public class AngularFilter implements Filter{
 				return null;
 			};
 		};
-
+
 		static ConcurrentHashMap<String, Html5Mapping> mappingByName = new ConcurrentHashMap<String, AngularFilter.Html5Mapping>(){
 			public Html5Mapping get(Object key) {
 				if(!initialized){
@@ -514,7 +487,7 @@ public class AngularFilter implements Filter{
 				}
 				return key != null ?super.get(key): null;
 			};
-
+
 			public Html5Mapping put(String key, Html5Mapping value) {
 				if(key != null){
 					return super.put(key, value);
@@ -522,13 +495,13 @@ public class AngularFilter implements Filter{
 				return null;
 			};
 		};
-
+
 		public Html5Mapping(String programId, String programName, String html5ProgramName) {
 			this.programId = programId;
 			this.programName = programName;
 			this.html5ProgramName = html5ProgramName;
 		}
-
+
 		public static void initialize(){
 			try {
 				XPath mapXPath =  XPathFactory.newInstance().newXPath();
@@ -539,28 +512,28 @@ public class AngularFilter implements Filter{
 				if(is != null)
 				{
 					Document doc = dBuilder.parse(is);
-
+
 					NodeList programs = (NodeList) mapXPath.compile("/s_program/*").evaluate(doc, XPathConstants.NODESET);
-
-		            for (int i = 0; i < programs.getLength(); i++){
-		            	Node program = programs.item(i);
-		            	String progId = program.getAttributes().item(0).getNodeValue();
+
+					for (int i = 0; i < programs.getLength(); i++){
+						Node program = programs.item(i);
+						String progId = program.getAttributes().item(0).getNodeValue();
 						String progName = null;
 						String html5ProgName = null;
-		            	for (int j = 0; j < program.getChildNodes().getLength(); j++){
-		            		Node mapping = program.getChildNodes().item(j);
-		            		if(mapping.getNodeName().equalsIgnoreCase("program_name") && mapping.getFirstChild() != null){
-		            			progName = mapping.getFirstChild().getNodeValue();
-		            		}
-		            		else if(mapping.getNodeName().equalsIgnoreCase("html5_program_name") && mapping.getFirstChild() != null){
-		            			html5ProgName = mapping.getFirstChild().getNodeValue();
-		            		}
-		            	}
-
-		            	Html5Mapping mapEntry = new Html5Mapping(progId, progName, html5ProgName);
+						for (int j = 0; j < program.getChildNodes().getLength(); j++){
+							Node mapping = program.getChildNodes().item(j);
+							if(mapping.getNodeName().equalsIgnoreCase("program_name") && mapping.getFirstChild() != null){
+								progName = mapping.getFirstChild().getNodeValue();
+							}
+							else if(mapping.getNodeName().equalsIgnoreCase("html5_program_name") && mapping.getFirstChild() != null){
+								html5ProgName = mapping.getFirstChild().getNodeValue();
+							}
+						}
+
+						Html5Mapping mapEntry = new Html5Mapping(progId, progName, html5ProgName);
 						mappingById.put(progId, mapEntry);
-						mappingByName.put(progName, mapEntry);
-		            }
+						mappingByName.put(progName, mapEntry);
+					}
 				}
 			} catch (Exception e) {
 				e.printStackTrace();
@@ -589,12 +562,12 @@ public class AngularFilter implements Filter{
 		public String getHtml5ProgramName() {
 			return html5ProgramName;
 		}
-
+
 		@Override
 		public String toString() {
 			return 	this.getClass().getSimpleName() + "(programId=" + getProgramId() +", programName="+ getProgramName() + ", html5ProgramName=" + getHtml5ProgramName() + ")";
 		}
 	}
-

-}
\ No newline at end of file
+
+}
diff --git a/src/main/java/org/swallow/web/LogonAction.java b/src/main/java/org/swallow/web/LogonAction.java
index ca297231..f4efd060 100644
--- a/src/main/java/org/swallow/web/LogonAction.java
+++ b/src/main/java/org/swallow/web/LogonAction.java
@@ -460,6 +460,8 @@ public class LogonAction extends CustomActionSupport {
 						if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("SMART_ROLE_ID"))){

 							if(SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("ROLE"))){
+							// If it's a new user and no role is provided, fail the login
+							if(isNewCreatedUser) {
 								if(SwtUtil.isEmptyOrNull(user.getRoleId()))
 									user.setRoleId("");

@@ -480,9 +482,13 @@ public class LogonAction extends CustomActionSupport {
 								systemLog.setUpdateUser("SYSTEM");
 								systemLogManager.saveSystemLog(systemLog);

-
 								return "fail";
+							}
+							// If user exists and has a role, keep using the existing role
+							// No need to update anything related to role
 							}else {
+							// Only update the role if it's a new user or if we're explicitly setting it
+							if(isNewCreatedUser || !SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("ROLE"))) {
 								user.setRoleId(verifyResult.getAttributeValueByName("ROLE"));
 								RoleManager roleManager = (RoleManager) SwtUtil.getBean("roleManager");
 								Role roleTmp = new Role();
@@ -512,7 +518,10 @@ public class LogonAction extends CustomActionSupport {
 								}
 								isUserDetailsChanged = true;
 							}
+						}
 						}else {
+						// Only update the role if it's a new user or if we're explicitly setting it
+						if(isNewCreatedUser || !SwtUtil.isEmptyOrNull(verifyResult.getAttributeValueByName("SMART_ROLE_ID"))) {
 							user.setRoleId(verifyResult.getAttributeValueByName("SMART_ROLE_ID"));

 							RoleManager roleManager = (RoleManager) SwtUtil.getBean("roleManager");
@@ -542,7 +551,7 @@ public class LogonAction extends CustomActionSupport {
 								return "fail";
 							}
 							isUserDetailsChanged = true;
-
+						}
 						}

 						//user.setStatus("1");
diff --git a/src/main/java/org/swallow/web/XSSFilter.java b/src/main/java/org/swallow/web/XSSFilter.java
index 963f8935..44aa0499 100644
--- a/src/main/java/org/swallow/web/XSSFilter.java
+++ b/src/main/java/org/swallow/web/XSSFilter.java
@@ -315,10 +315,12 @@ public class XSSFilter implements Filter {

 								((HttpServletResponse) servletresponse).setHeader("Vary", "Origin"); // Avoid cache poisoning
 							} else {
-								log.warn("Missing Origin on potentially sensitive request: " + httpservletrequest.getRequestURI() +
-										 ", IP: " + httpservletrequest.getRemoteAddr());
-								((HttpServletResponse) servletresponse).sendError(HttpServletResponse.SC_FORBIDDEN, "Origin required");
-								return;
+								if (!"GET".equalsIgnoreCase(httpservletrequest.getMethod())) {
+									log.warn("Missing Origin on potentially sensitive request: " + httpservletrequest.getRequestURI() +
+											 ", IP: " + httpservletrequest.getRemoteAddr());
+									((HttpServletResponse) servletresponse).sendError(HttpServletResponse.SC_FORBIDDEN, "Origin required for non-GET requests");
+									return;
+								}
 							}
 						}

diff --git a/src/main/java/org/swallow/work/dao/hibernate/MatchDisplayDAOHibernate.java b/src/main/java/org/swallow/work/dao/hibernate/MatchDisplayDAOHibernate.java
index cd356013..5a6b095a 100644
--- a/src/main/java/org/swallow/work/dao/hibernate/MatchDisplayDAOHibernate.java
+++ b/src/main/java/org/swallow/work/dao/hibernate/MatchDisplayDAOHibernate.java
@@ -1713,19 +1713,20 @@ public class MatchDisplayDAOHibernate extends HibernateDaoSupport implements


 			if(!SwtUtil.isEmptyOrNull(archiveId)) {
-				String HQL_DBDETAILS = "  from Archive a where a.id.hostId = ?0 and a.moduleId = ?1 and a.defaultDb = 'Y'";
-				List<Archive> dbColl = (List<Archive>) getHibernateTemplate().find(HQL_DBDETAILS,
-						new Object[] { hostId, "Predict" });
-				if (!dbColl.isEmpty()) {
-					dbLink =  dbColl.get(0).getDb_link();
-					archiveType = dbColl.get(0).getArchiveType();
-					if("S".equalsIgnoreCase(archiveType)) {
-						archiveMatchString=dbLink+".P_MATCH";
-						archiveMovementString=dbLink+".P_MOVEMENT";
-					}else {
-						archiveMatchString="P_MATCH@"+dbLink;
-						archiveMovementString="P_MOVEMENT@"+dbLink;
+				try {
+					ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
+					Archive archive = archiveManager.getArchiveById(hostId, archiveId);
+					if (archive != null) {
+						archiveMatchString = SwtUtil.formatTableWithDbLink("P_MATCH", archive.getDb_link(), archive.getArchiveType());
+						archiveMovementString = SwtUtil.formatTableWithDbLink("P_MOVEMENT", archive.getDb_link(), archive.getArchiveType());
+					} else {
+						archiveMatchString = "P_MATCH";
+						archiveMovementString = "P_MOVEMENT";
 					}
+				} catch (Exception e) {
+					log.error("Error getting archive information: " + e.getMessage());
+					archiveMatchString = "P_MATCH";
+					archiveMovementString = "P_MOVEMENT";
 				}
 			}else {
 				archiveMatchString = "P_MATCH";
diff --git a/src/main/java/org/swallow/work/dao/hibernate/MovementDAOHibernate.java b/src/main/java/org/swallow/work/dao/hibernate/MovementDAOHibernate.java
index 04edcfa7..20463f6b 100644
--- a/src/main/java/org/swallow/work/dao/hibernate/MovementDAOHibernate.java
+++ b/src/main/java/org/swallow/work/dao/hibernate/MovementDAOHibernate.java
@@ -35,7 +35,9 @@ import org.apache.commons.logging.Log;
 import org.apache.commons.logging.LogFactory;

 import org.swallow.batchScheduler.ConnectionManager;
+import org.swallow.control.model.Archive;
 import org.swallow.control.model.WorkQAccess;
+import org.swallow.control.service.ArchiveManager;
 import org.swallow.exception.SwtErrorHandler;
 import org.swallow.exception.SwtException;
 import org.swallow.maintenance.model.Currency;
@@ -2841,23 +2843,34 @@ public class MovementDAOHibernate extends HibernateDaoSupport implements
 					+ " -[getArchiveMovementDetails]-Entry");

 			// following code is to get the db_link of archive id
-			session = getHibernateTemplate().getSessionFactory().openSession();
-			// Get the database connection object
-			connection = SwtUtil.connection(session);
-			statement = connection.createStatement();
-			statement
-					.executeQuery("select DBLINK_SCHEMA_NAME from p_Archive where archive_id='"
-							+ archiveId + "'");
-			resultset = statement.getResultSet();
-			if (resultset != null) {
-				while (resultset.next()) {
-					dbLink = resultset.getString(1);
+			try {
+				ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
+				Archive archive = archiveManager.getArchiveById(CacheManager.getInstance().getHostId(), archiveId);
+				if (archive != null) {
+					dbLink = archive.getDb_link();
+				}
+			} catch (Exception e) {
+				log.error("Error getting archive information: " + e.getMessage());
+				// Fallback to original query if needed
+				session = getHibernateTemplate().getSessionFactory().openSession();
+				connection = SwtUtil.connection(session);
+				statement = connection.createStatement();
+				statement.executeQuery("select DBLINK_SCHEMA_NAME from p_Archive where archive_id='" + archiveId + "'");
+				resultset = statement.getResultSet();
+				if (resultset != null) {
+					while (resultset.next()) {
+						dbLink = resultset.getString(1);
+					}
 				}
 			}

 			// following code is execution of procedure to get the movement
 			// details from the archive schema
 			// Make a callable statement for executing the procedure
+			if(session == null || connection == null){
+				session = getHibernateTemplate().getSessionFactory().openSession();
+				connection = SwtUtil.connection(session);
+			}
 			callableStatement = connection
 					.prepareCall("{call PKG_ARCHIVE_DETAILS.SPSHOWARCHMOVEMENT(?,?,?,?,?,?)}");
 			callableStatement.setString(1, CacheManager.getInstance()
@@ -3012,6 +3025,7 @@ public class MovementDAOHibernate extends HibernateDaoSupport implements
 			}

 		} catch (Exception e) {
+			e.printStackTrace();
 			log.error("Error in " + this.getClass().getName()
 					+ " - [getArchiveMovementDetails] - " + e.getMessage());

@@ -3842,4 +3856,4 @@ public class MovementDAOHibernate extends HibernateDaoSupport implements



-} // End of class MovementDAOHibernate
\ No newline at end of file
+} // End of class MovementDAOHibernate
diff --git a/src/main/java/org/swallow/work/dao/hibernate/NotesDAOHibernate.java b/src/main/java/org/swallow/work/dao/hibernate/NotesDAOHibernate.java
index 0ca2a879..3e10433e 100644
--- a/src/main/java/org/swallow/work/dao/hibernate/NotesDAOHibernate.java
+++ b/src/main/java/org/swallow/work/dao/hibernate/NotesDAOHibernate.java
@@ -243,7 +243,8 @@ public class NotesDAOHibernate extends HibernateDaoSupport implements NotesDAO {
 				// Get the database connection object
 				connection = SwtUtil.connection(session);
 				// create statement
-				String  query = "select * from P_SWEEP_NOTE@"+archiveId +" where sweep_id = ?";
+				String tableName = SwtUtil.getFormattedTableName(hostId, archiveId, "P_SWEEP_NOTE");
+				String  query = "select * from " + tableName + " where sweep_id = ?";
 				session = getHibernateTemplate().getSessionFactory().openSession();
 				connection = SwtUtil.connection(session);
 				statement = connection.prepareStatement(query);
diff --git a/src/main/java/org/swallow/work/dao/hibernate/SweepDetailDAOHibernate.java b/src/main/java/org/swallow/work/dao/hibernate/SweepDetailDAOHibernate.java
index de1ac546..34d15e4c 100644
--- a/src/main/java/org/swallow/work/dao/hibernate/SweepDetailDAOHibernate.java
+++ b/src/main/java/org/swallow/work/dao/hibernate/SweepDetailDAOHibernate.java
@@ -455,7 +455,8 @@ public class SweepDetailDAOHibernate extends HibernateDaoSupport implements
 			PreparedStatement statement = null;
 			List<Object> pSweepList = new ArrayList<>();
 			try {
-				String  query = "select * from p_sweep@"+archiveId +" where sweep_id = ? and host_id=?";
+				String tableName = SwtUtil.getFormattedTableName(hostId, archiveId, "p_sweep");
+				String  query = "select * from " + tableName + " where sweep_id = ? and host_id=?";
 				session = getHibernateTemplate().getSessionFactory().openSession();
 				conn = SwtUtil.connection(session);;
 				statement = conn.prepareStatement(query);
@@ -668,7 +669,7 @@ public class SweepDetailDAOHibernate extends HibernateDaoSupport implements
 //			cstmt.setString(2, entityId);
 			cstmt.setString(2, acctCr);
 			cstmt.setString(3, acctDr);
-			cstmt.setDouble(4, sweepAmount.doubleValue());
+			cstmt.setDouble(4, sweepAmount != null ? Math.abs(sweepAmount.doubleValue()) : 0.0);
 			cstmt.setString(5, "PGT");
 			cstmt.setString(6, null);
 			cstmt.setString(7, null);
diff --git a/src/main/java/org/swallow/work/dao/hibernate/SweepSearchDAOHibernate.java b/src/main/java/org/swallow/work/dao/hibernate/SweepSearchDAOHibernate.java
index cc297837..85406eda 100644
--- a/src/main/java/org/swallow/work/dao/hibernate/SweepSearchDAOHibernate.java
+++ b/src/main/java/org/swallow/work/dao/hibernate/SweepSearchDAOHibernate.java
@@ -32,6 +32,8 @@ import java.util.StringTokenizer;
 import org.apache.commons.logging.Log;
 import org.apache.commons.logging.LogFactory;

+import org.swallow.control.model.Archive;
+import org.swallow.control.service.ArchiveManager;
 import org.swallow.exception.SwtErrorHandler;
 import org.swallow.exception.SwtException;
 import org.swallow.maintenance.model.AcctMaintenance;
@@ -168,7 +170,8 @@ public class SweepSearchDAOHibernate extends HibernateDaoSupport implements
 			PreparedStatement statement = null;
 			List<Object> pSweepList = new ArrayList<>();
 			try {
-				String  query = "select * from p_sweep@"+archiveId +" where sweep_id = ?";
+				String tableName = SwtUtil.getFormattedTableName(hostId, archiveId, "p_sweep");
+				String  query = "select * from " + tableName + " where sweep_id = ?";
 				session = getHibernateTemplate().getSessionFactory().openSession();
 				conn = SwtUtil.connection(session);
 				statement = conn.prepareStatement(query);
@@ -1060,7 +1063,7 @@ public class SweepSearchDAOHibernate extends HibernateDaoSupport implements
 				PreparedStatement statement = null;
 				List<Object> pSweepList = new ArrayList<>();
 				try {
-					String  query = "select * "+replaceFieldsInHQL(strQuery,archiveid);
+					String  query = "select * "+replaceFieldsInHQLWithArchiveId(strQuery, hostId, archiveid);
 				 session = getHibernateTemplate().getSessionFactory().openSession();
 					conn = SwtUtil.connection(session);
 					statement = conn.prepareStatement(query);
@@ -1733,13 +1736,40 @@ public class SweepSearchDAOHibernate extends HibernateDaoSupport implements
 	}


-	public String replaceFieldsInHQL(String hqlQuery, String dbLink) {
+
+	public String replaceFieldsInHQLWithArchiveId(String hqlQuery, String hostId, String archiveId) {
+		try {
+			if (SwtUtil.isEmptyOrNull(archiveId)) {
+				return hqlQuery;
+			}
+
+			ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
+			Archive archive = archiveManager.getArchiveById(hostId, archiveId);
+
+			if (archive == null) {
+				return hqlQuery;
+			}
+
+			return replaceFieldsInHQL(hqlQuery, archive.getDb_link(), archive.getArchiveType(), hostId);
+		} catch (Exception e) {
+			log.error("Error in replaceFieldsInHQLWithArchiveId: " + e.getMessage());
+			return hqlQuery;
+		}
+	}
+
+	public String replaceFieldsInHQL(String hqlQuery, String dbLink, String archiveType, String hostId) {
 	    // Define the field mappings
 	    LinkedHashMap<String, String> fieldMappings = new LinkedHashMap<>();
 	    fieldMappings.put("\\.id\\.", ".");
-	    fieldMappings.put(" Sweep", " P_SWEEP"+(SwtUtil.isEmptyOrNull(dbLink)?"":"@"+dbLink));
-	    fieldMappings.put(" Movement", " P_MOVEMENT"+(SwtUtil.isEmptyOrNull(dbLink)?"":"@"+dbLink));
-	    fieldMappings.put("AcctMaintenance", "P_ACCOUNT"+(SwtUtil.isEmptyOrNull(dbLink)?"":"@"+dbLink));
+
+	    // Use the new utility method to format table names
+	    String sweepTable = SwtUtil.formatTableWithDbLink("P_SWEEP", dbLink, archiveType);
+	    String movementTable = SwtUtil.formatTableWithDbLink("P_MOVEMENT", dbLink, archiveType);
+	    String accountTable = SwtUtil.formatTableWithDbLink("P_ACCOUNT", dbLink, archiveType);
+
+	    fieldMappings.put(" Sweep", " " + sweepTable);
+	    fieldMappings.put(" Movement", " " + movementTable);
+	    fieldMappings.put("AcctMaintenance", accountTable);
 	    fieldMappings.put("sweepId", "SWEEP_ID");
 	    fieldMappings.put("hostId", "HOST_ID");
 	    fieldMappings.put("entityIdDr", "ENTITY_ID_CR");
diff --git a/src/main/java/org/swallow/work/model/Movement.java b/src/main/java/org/swallow/work/model/Movement.java
index 55c029e9..cf68630f 100644
--- a/src/main/java/org/swallow/work/model/Movement.java
+++ b/src/main/java/org/swallow/work/model/Movement.java
@@ -518,6 +518,10 @@ public class Movement extends BaseObject {

 	private String uetr = null;

+	private String failingCause = null;
+
+	private boolean successful;
+
 	public Movement() {
 		super();
 		Date sysDate = SwtUtil.getSystemDatewithTime();
@@ -1686,6 +1690,22 @@ public class Movement extends BaseObject {
 		this.updateTimeAsString = updateTimeAsString;
 	}

+	public String getFailingCause() {
+		return failingCause;
+	}
+
+	public void setFailingCause(String failingCause) {
+		this.failingCause = failingCause;
+	}
+
+	public boolean isSuccessful() {
+		return successful;
+	}
+
+	public void setSuccessful(boolean successful) {
+		this.successful = successful;
+	}
+
 	public static class Id extends BaseObject {
 		private String hostId;
 		private String entityId;
diff --git a/src/main/java/org/swallow/work/model/MovementExt.hbm.xml b/src/main/java/org/swallow/work/model/MovementExt.hbm.xml
index 6f668138..0fa9c236 100644
--- a/src/main/java/org/swallow/work/model/MovementExt.hbm.xml
+++ b/src/main/java/org/swallow/work/model/MovementExt.hbm.xml
@@ -63,7 +63,8 @@
 		<property name="extraText8" column="EXTRA_TEXT8" not-null="false"/>
 		<property name="extraText9" column="EXTRA_TEXT9" not-null="false"/>
 		<property name="extraText10" column="EXTRA_TEXT10" not-null="false"/>
-
+		<property name="lastMmaUpdateNote" column="LAST_MMA_UPDATE_NOTE" not-null="false"/>
+		<property name="lastMmaUpdateEventId" column="LAST_MMA_UPDATE_EVENT_ID" not-null="false"/>

     </class>
 </hibernate-mapping>
\ No newline at end of file
diff --git a/src/main/java/org/swallow/work/model/MovementExt.java b/src/main/java/org/swallow/work/model/MovementExt.java
index 7104ec51..38c91a6e 100644
--- a/src/main/java/org/swallow/work/model/MovementExt.java
+++ b/src/main/java/org/swallow/work/model/MovementExt.java
@@ -251,6 +251,9 @@ public class MovementExt extends BaseObject {
 	 */
 	private String extraText10;

+	private String lastMmaUpdateNote;
+	private Integer lastMmaUpdateEventId;
+
 	public MovementExt() {
 		super();
 		id = new Id();
@@ -1006,7 +1009,21 @@ public class MovementExt extends BaseObject {
 		this.extraText10 = extraText10;
 	}

+    public String getLastMmaUpdateNote() {
+        return lastMmaUpdateNote;
+    }
+
+	public void setLastMmaUpdateNote(String lastMmaUpdateNote) {
+		this.lastMmaUpdateNote = lastMmaUpdateNote;
+	}

+	public Integer getLastMmaUpdateEventId() {
+        return lastMmaUpdateEventId;
+    }
+
+	public void setLastMmaUpdateEventId(Integer lastMmaUpdateEventId) {
+		this.lastMmaUpdateEventId = lastMmaUpdateEventId;
+	}

 	public boolean checkNull() throws IllegalAccessException {
 	    for (Field f : getClass().getDeclaredFields()) {
diff --git a/src/main/java/org/swallow/work/web/NotesAction.java b/src/main/java/org/swallow/work/web/NotesAction.java
index 39b1a9c9..a99aa392 100644
--- a/src/main/java/org/swallow/work/web/NotesAction.java
+++ b/src/main/java/org/swallow/work/web/NotesAction.java
@@ -1510,15 +1510,10 @@ HttpServletResponse response = ServletActionContext.getResponse();
 				// currencies for default entity if cancel button is pressed
 				// get the notes details form Notes manager

-				if(!SwtUtil.isEmptyOrNull(archiveId)){
-					ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
-					dbLink = archiveManager.getDBlink(archiveId);
-
-				}
 				notesDetails = notesManager.getSweepNoteDetails(CacheManager
 						.getInstance().getHostId(), new Long(request
 						.getParameter("selectedSweepId")), SwtUtil
-						.getCurrentSystemFormats(request.getSession()), dbLink);
+						.getCurrentSystemFormats(request.getSession()), archiveId);
 			}
 			// End:code modified by Prasenjit Maji for Mantis 1874
 			// on 28/09/12: Pre advice Input:Currency drop down should load
@@ -1860,16 +1855,9 @@ HttpServletResponse response = ServletActionContext.getResponse();
 				//TODO:
 				sweepId = new Long(sweepIdAsString);
 				sweepNote.getId().setHostId(hostId);
-				String dbLink = null;
-				if(!SwtUtil.isEmptyOrNull(archiveId)){
-					ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
-					dbLink = archiveManager.getDBlink(archiveId);
-
-				}
-
 				notesDetails = notesManager.getSweepNoteDetails(hostId,
 						sweepId, SwtUtil.getCurrentSystemFormats(request
-								.getSession()),dbLink);
+								.getSession()), archiveId);
 			}

 			noteDetailsItr = notesDetails.iterator();
diff --git a/src/main/java/org/swallow/work/web/OutStandingMvmtAction.java b/src/main/java/org/swallow/work/web/OutStandingMvmtAction.java
index 722900a5..fe31b1a6 100644
--- a/src/main/java/org/swallow/work/web/OutStandingMvmtAction.java
+++ b/src/main/java/org/swallow/work/web/OutStandingMvmtAction.java
@@ -4034,13 +4034,7 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 				}
 				initialInputScreen = "S";
 				if ((archiveId != null) && (archiveId.length() > 0)) {
-					if ("".equals(reference.trim())) {
-						reference = "All";
-					} else {
-						if (referenceFlag != null && referenceFlag.equals("Y")) {
-							reference = "%" + reference + "%";
-						}
-					}
+					reference = generateXMLReference(reference, referenceFlag);
 				} else {
 					reference = SwtUtil.decode64(reference);
 				}
@@ -10460,9 +10454,11 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 			throws SwtException {
 		HttpServletRequest request = ServletActionContext.getRequest();
 		HttpServletResponse response = ServletActionContext.getResponse();
-
+String multiMvtUpdateNbr= null;
 		try {
 			log.debug(this.getClass().getName() + "- [flex] - Entering ");
+			multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+					.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 			/* Setting values in request */
 			request.setAttribute("filterAcctType", "");
 			request.setAttribute("selectedFilter", request
@@ -10564,7 +10560,7 @@ public class OutStandingMvmtAction extends CustomActionSupport {

 			String msdDisplayColsJson = new Gson().toJson(msdDisplayCols );
 			request.setAttribute("msdDisplayColsList", msdDisplayColsJson);
-
+			request.setAttribute("multiMvtUpdateNbr", multiMvtUpdateNbr);
 			//static list of position level
 			ArrayList<String> posLevelList = (ArrayList<String>) movementManager.getEntityPosLevelList(request.getParameter("entityId"));
 			String posLevelJson = new Gson().toJson(posLevelList );
@@ -10613,9 +10609,12 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 		String lastProfile = null;
 		String profileId= null;
 		String userId= null;
+		String multiMvtUpdateNbr= null;
 		try {
 			log.debug(this.getClass().getName()
 					+ "- [flexDisplayOpenMovements] - Entering ");
+			multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+					.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 			// Retrieve the user from session
 			userId = SwtUtil.getCurrentUserId(request.getSession());
 			/* get current Host Id from DB */
@@ -10712,6 +10711,7 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 			String msdDisplayColsJson = new Gson().toJson(msdDisplayCols );
 			request.setAttribute("msdDisplayColsList", msdDisplayColsJson);
 			request.setAttribute("profileId", profileId);
+			request.setAttribute("multiMvtUpdateNbr", multiMvtUpdateNbr);
 			//static list of position level
 			ArrayList<String> posLevelList = (ArrayList<String>) movementManager.getEntityPosLevelList(request.getParameter("entityId"));
 			String posLevelJson = new Gson().toJson(posLevelList );
@@ -10752,12 +10752,14 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 			throws SwtException {
 		HttpServletRequest request = ServletActionContext.getRequest();
 		HttpServletResponse response = ServletActionContext.getResponse();
-
+	String multiMvtUpdateNbr= null;

 		// Declares the CacheManager object
 		String hostId = null;
 		try {
 			log.debug(this.getClass().getName() + "- [flexBook] - Entering ");
+			multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+					.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 			/* Setting values in request */
 			request.setAttribute("filterAcctType", "");
 			request.setAttribute("entityId", request.getParameter("entityId"));
@@ -10836,12 +10838,15 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 		HttpServletRequest request = ServletActionContext.getRequest();
 		HttpServletResponse response = ServletActionContext.getResponse();

+		String multiMvtUpdateNbr= null;

 		// Declares the CacheManager object
 		String hostId = null;
 		try {
 			log.debug(this.getClass().getName()
 					+ "- [flexCurrency] - Entering ");
+			multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+					.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 			/* Setting values in request */
 			request.setAttribute("filterAcctType", "");
 			request.setAttribute("entityId", request.getParameter("entityId"));
@@ -10889,6 +10894,7 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 			bindPositionLevelFormatAndSourceToRequest(request);
 			String roleId = SwtUtil.getCurrentUser(request.getSession()).getRoleId();
 			getFullOrViewAcessEntityListInReq(request, roleId);
+			request.setAttribute("multiMvtUpdateNbr", multiMvtUpdateNbr);
 			setFontSize(request);
 			log
 					.debug(this.getClass().getName()
@@ -10924,12 +10930,15 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 		HttpServletResponse response = ServletActionContext.getResponse();


+		String multiMvtUpdateNbr= null;
 		// Declares the CacheManager object
 		String hostId = null;
 		try {
 			log
 					.debug(this.getClass().getName()
 							+ "- [flexAccount] - Entering ");
+			multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+					.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 			/* Setting values in request */
 			request.setAttribute("filterAcctType", "");
 			request.setAttribute("entityId", request.getParameter("entityId"));
@@ -11017,11 +11026,14 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 		HttpServletRequest request = ServletActionContext.getRequest();
 		HttpServletResponse response = ServletActionContext.getResponse();

+		String multiMvtUpdateNbr= null;
 		// Declares the CacheManager object
 		String hostId = null;
 		try {
 			log.debug(this.getClass().getName()
 					+ "- [flexUnsetteled] - Entering ");
+			multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+					.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 			/* Setting values in request */
 			request.setAttribute("filterAcctType", "");
 			request.setAttribute("entityId", request.getParameter("entityId"));
@@ -11059,6 +11071,7 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 			bindPositionLevelFormatAndSourceToRequest(request);
 			String roleId = SwtUtil.getCurrentUser(request.getSession()).getRoleId();
 			getFullOrViewAcessEntityListInReq(request, roleId);
+			request.setAttribute("multiMvtUpdateNbr", multiMvtUpdateNbr);
 			setFontSize(request);
 			log.debug(this.getClass().getName()
 					+ "- [flexUnsetteled] - Exiting ");
@@ -11093,12 +11106,15 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 		HttpServletRequest request = ServletActionContext.getRequest();
 		HttpServletResponse response = ServletActionContext.getResponse();

+		String multiMvtUpdateNbr= null;

 		// Declares the CacheManager object
 		String hostId = null;
 		try {
 			log.debug(this.getClass().getName()
 					+ "- [flexUnexpected] - Entering ");
+			multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+					.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 			/* Setting values in request */
 			request.setAttribute("filterAcctType", "");
 			request.setAttribute("entityId", request.getParameter("entityId"));
@@ -11140,6 +11156,7 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 			String roleId = SwtUtil.getCurrentUser(request.getSession()).getRoleId();
 			getFullOrViewAcessEntityListInReq(request, roleId);
 			setFontSize(request);
+			request.setAttribute("multiMvtUpdateNbr", multiMvtUpdateNbr);
 			log.debug(this.getClass().getName()
 					+ "- [flexUnexpected] - Exiting ");
 		} catch (Exception exception) {
@@ -11173,12 +11190,15 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 		HttpServletRequest request = ServletActionContext.getRequest();
 		HttpServletResponse response = ServletActionContext.getResponse();

+		String multiMvtUpdateNbr= null;

 		// Declares the CacheManager object
 		String hostId = null;
 		try {
 			log.debug(this.getClass().getName()
 					+ "- [flexBackvalue] - Entering ");
+			multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+					.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 			/* Setting values in request */
 			request.setAttribute("filterAcctType", "");
 			request.setAttribute("entityId", request.getParameter("entityId"));
@@ -11221,6 +11241,7 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 			String roleId = SwtUtil.getCurrentUser(request.getSession()).getRoleId();
 			getFullOrViewAcessEntityListInReq(request, roleId);
 			setFontSize(request);
+			request.setAttribute("multiMvtUpdateNbr", multiMvtUpdateNbr);
 			log.debug(this.getClass().getName()
 					+ "- [flexBackvalue] - Exiting ");
 		} catch (Exception exception) {
@@ -11256,9 +11277,12 @@ public class OutStandingMvmtAction extends CustomActionSupport {

 		// Declares the CacheManager object
 		String hostId = null;
+		String multiMvtUpdateNbr= null;
 		try {
 			log.debug(this.getClass().getName()
 					+ "- [flexWorkflow] - Entering ");
+			multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+					.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 			/* Setting values in request */
 			request.setAttribute("filterAcctType", "");
 			request.setAttribute("entityId", request.getParameter("entityId"));
@@ -11310,6 +11334,7 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 			bindPositionLevelFormatAndSourceToRequest(request);
 			String roleId = SwtUtil.getCurrentUser(request.getSession()).getRoleId();
 			getFullOrViewAcessEntityListInReq(request, roleId);
+			request.setAttribute("multiMvtUpdateNbr", multiMvtUpdateNbr);
 			setFontSize(request);
 			log
 					.debug(this.getClass().getName()
@@ -13174,6 +13199,9 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 		HttpServletResponse response = ServletActionContext.getResponse();


+		String multiMvtUpdateNbr= null;
+		multiMvtUpdateNbr= PropertiesFileLoader.getInstance()
+				.getPropertiesValue(SwtConstants.MULTI_MVT_UPDATE_NUMBER_PROPERTIE_NAME);
 		String dateFormat = SwtUtil.getCurrentDateFormat(UserThreadLocalHolder
 				.getUserSession());
 		//get all MsdDisplayColumns to be used in the filter
@@ -13181,6 +13209,7 @@ public class OutStandingMvmtAction extends CustomActionSupport {

 		String msdDisplayColsJson = new Gson().toJson(msdDisplayCols );
 		request.setAttribute("msdDisplayColsList", msdDisplayColsJson);
+		request.setAttribute("multiMvtUpdateNbr", multiMvtUpdateNbr);
 		request.setAttribute("dateFormat", dateFormat);
 		request.setAttribute("currencyFormat", SwtUtil
 				.getCurrentCurrencyFormat(request.getSession()));
@@ -13614,6 +13643,83 @@ public class OutStandingMvmtAction extends CustomActionSupport {
 		}
 	}

+	/**
+	 * Generates XML reference structure based on reference value and referenceFlag
+	 *
+	 * @param reference The reference value
+	 * @param referenceFlag The reference flag ("Y" for like search, otherwise exact match)
+	 * @return XML formatted reference string
+	 */
+	private String generateXMLReference(String reference, String referenceFlag) {
+		try {
+			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
+			DocumentBuilder builder = factory.newDocumentBuilder();
+			Document doc = builder.newDocument();
+
+			// Create root element
+			Element refparams = doc.createElement("refparams");
+			doc.appendChild(refparams);
+
+			// Create include element
+			Element include = doc.createElement("include");
+			include.setAttribute("ref1", "Y");
+			include.setAttribute("ref2", "Y");
+			include.setAttribute("ref3", "Y");
+			include.setAttribute("ref4", "Y");
+
+			// Create exclude element
+			Element exclude = doc.createElement("exclude");
+			exclude.setAttribute("like", "N");
+			exclude.setAttribute("ref1", "Y");
+			exclude.setAttribute("ref2", "Y");
+			exclude.setAttribute("ref3", "Y");
+			exclude.setAttribute("ref4", "Y");
+
+			// Handle reference value and like flag
+			if (SwtUtil.isEmptyOrNull(reference) || "".equals(reference.trim())) {
+				// Empty reference case
+				include.setAttribute("like", "N");
+			} else {
+				// Non-empty reference case
+				if (referenceFlag != null && referenceFlag.equals("Y")) {
+					include.setAttribute("like", "Y");
+				} else {
+					include.setAttribute("like", "N");
+				}
+				// Add CDATA section with reference value
+				include.appendChild(doc.createCDATASection(reference));
+			}
+
+			// Add elements to root
+			refparams.appendChild(include);
+			refparams.appendChild(exclude);
+
+			// Convert to string
+			TransformerFactory transformerFactory = TransformerFactory.newInstance();
+			Transformer transformer = transformerFactory.newTransformer();
+			transformer.setOutputProperty("omit-xml-declaration", "no");
+			transformer.setOutputProperty("encoding", "UTF-8");
+			transformer.setOutputProperty("standalone", "no");
+
+			StringWriter writer = new StringWriter();
+			transformer.transform(new DOMSource(doc), new StreamResult(writer));
+
+			return writer.toString();
+
+		} catch (Exception e) {
+			log.error("Error generating XML reference: " + e.getMessage());
+			// Fallback to original logic if XML generation fails
+			if (SwtUtil.isEmptyOrNull(reference) || "".equals(reference.trim())) {
+				return "All";
+			} else {
+				if (referenceFlag != null && referenceFlag.equals("Y")) {
+					return "%" + reference + "%";
+				}
+				return reference;
+			}
+		}
+	}
+


 } // End of Class
diff --git a/src/main/java/org/swallow/work/web/SweepDetailAction.java b/src/main/java/org/swallow/work/web/SweepDetailAction.java
index 794f3d02..ca0fdc91 100644
--- a/src/main/java/org/swallow/work/web/SweepDetailAction.java
+++ b/src/main/java/org/swallow/work/web/SweepDetailAction.java
@@ -1001,10 +1001,6 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			// Getting the sweep details

 			archiveId = request.getParameter("archiveId");
-			ArchiveManager archiveManager = (ArchiveManager) SwtUtil.getBean("archiveManager");
-			if(!SwtUtil.isEmptyOrNull(archiveId)) {
-				dbLink = archiveManager.getDBlink(archiveId);
-			}
 			if(SwtUtil.isEmptyOrNull(archiveId)) {
 				sweepDetailVO = sweepDetailManager.getSweepDetails(request
 						.getParameter("entid"), Long.valueOf(request
@@ -1016,7 +1012,7 @@ HttpServletResponse response = ServletActionContext.getResponse();
 						.getParameter("entid"), Long.valueOf(request
 								.getParameter("swpid")), SwtUtil
 						.getCurrentSystemFormats(request.getSession()), request
-						.getParameter("qname").trim(), dbLink);
+						.getParameter("qname").trim(), archiveId);
 			}

 			// Get the account details
diff --git a/src/main/java/org/swallow/work/web/SweepSearchAction.java b/src/main/java/org/swallow/work/web/SweepSearchAction.java
index 257b625f..295907f4 100644
--- a/src/main/java/org/swallow/work/web/SweepSearchAction.java
+++ b/src/main/java/org/swallow/work/web/SweepSearchAction.java
@@ -823,12 +823,6 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			 * system should not allow to display No access currency group
 			 * records in the "Sweep search results" screen.
 			 */
-			if(!SwtUtil.isEmptyOrNull(archiveId)) {
-
-				ArchiveManager archiveManager = (ArchiveManager) SwtUtil
-						.getBean("archiveManager");
-				dbLink = archiveManager.getDBlink(archiveId);
-			}
 			// Get the search list form manager
 			searchlist = sweepsearchManager.fetchdetails(request
 					.getParameter("sortorder"), request
@@ -842,7 +836,7 @@ HttpServletResponse response = ServletActionContext.getResponse();
 					amountUnder, request.getParameter("valueFromDateAsString"),
 					request.getParameter("valueToDateAsString"), SwtUtil
 							.getCurrentSystemFormats(request.getSession()),
-					currencyCodeArray, dbLink);
+					currencyCodeArray, archiveId);

 			// iterate the search list
 			itrSearchList = searchlist.iterator();
@@ -1290,11 +1284,8 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			if ((sweepSearch.getId().getSweepId() != null)) {
 				// fetch the sweep details
 				if(archiveId != null) {
-					ArchiveManager archiveManager = (ArchiveManager) SwtUtil
-							.getBean("archiveManager");
-					String dbLink = archiveManager.getDBlink(archiveId);
-					sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepValue, hostId, dbLink);
-				}else
+					sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepValue, hostId, archiveId);
+				}else
 					sweepDetails = sweepsearchManager.fetchsweepdetails(sweepValue,	hostId);
 				// iterate the sweep details
 				itrSweep = sweepDetails.iterator();
@@ -1406,11 +1397,10 @@ HttpServletResponse response = ServletActionContext.getResponse();
 				}
 				ArchiveManager archiveManager = (ArchiveManager) SwtUtil
 						.getBean("archiveManager");
-				String dbLink = archiveManager.getDBlink(archiveId);
 				// Get the sweep cut off from the sweep search manager
 				sweepCutOff = sweepsearchManager.fetchsweepCutOff(accountIdCr,
 						accountIdDr, entityIdCr, entityIddr, hostId,
-						sweepSearch.getId().getSweepId(), sweep, dbLink);
+						sweepSearch.getId().getSweepId(), sweep, archiveId);
 				// Get the submitted cut off flag and checks not equal to null
 				if (sweepCutOff.getSubmittedpostcutoffflg() != null) {
 					if (sweepCutOff.getSubmittedpostcutoffflg()
@@ -1883,7 +1873,7 @@ HttpServletResponse response = ServletActionContext.getResponse();
 					}

 					sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepId,
-							hostId, dbLink);
+							hostId, archiveId);
 					if (sweepDetails.size() > 0) {
 						request.setAttribute("archiveId", archiveId);
 					}
@@ -2709,7 +2699,7 @@ HttpServletResponse response = ServletActionContext.getResponse();
 			if (sweepDetails.size() == 0) {

 					Archive arch = achiveManager.getCurrentArchiveDb(hostId);
-					sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepData, hostId, arch.getDb_link());
+					sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepData, hostId, arch.getId().getArchiveId());
 					if (sweepDetails.size() > 0) {
 						request.setAttribute("archiveId", arch.getId().getArchiveId());
 					}
@@ -2717,8 +2707,8 @@ HttpServletResponse response = ServletActionContext.getResponse();

 			}else {

-				String dbLink = achiveManager.getDBlink(archiveId);
-				sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepData, hostId, dbLink);
+			//	String dbLink = achiveManager.getDBlink(archiveId);
+				sweepDetails = sweepsearchManager.fetchsweepdetailsArchive(sweepData, hostId, archiveId);

 			}

diff --git a/src/main/resources/default_predict.properties b/src/main/resources/default_predict.properties
index c5ed98c3..d57a4e14 100644
--- a/src/main/resources/default_predict.properties
+++ b/src/main/resources/default_predict.properties
@@ -179,4 +179,9 @@ allowUserCreationWithSmartAuthenticator=true
 #Possibilty to change email subject for Alert Notifications
 scenarioSummaryAlertsSubject= Scenario Summary Alerts

-cors.allowedOrigins=
\ No newline at end of file
+# Comma-separated list of allowed origins for CORS requests,  Each origin can include a port if needed.
+# Example: https://example.com, https://localhost:3000
+cors.allowedOrigins=
+# MultiMovementUpdateNumber defines the maximum number of movements a user can update in a single operation.
+# This is a safeguard to prevent excessive load on the system.
+MultiMovementUpdateNumber=50
\ No newline at end of file
diff --git a/src/main/resources/dictionary_en.properties b/src/main/resources/dictionary_en.properties
index beaffa07..ca0e4d38 100644
--- a/src/main/resources/dictionary_en.properties
+++ b/src/main/resources/dictionary_en.properties
@@ -1592,8 +1592,10 @@ entity.general.serverTomeOffSet=Server Time Offset
 entity.general.weekEnd1=Weekend Day 1
 entity.general.weekEnd2=Weekend Day 2
 entity.general.exchangeRateFormat=Exchange Rate Format
-entity.general.exchangeRateFormat.domestic.ccy=Domestic ccy/ccy
-entity.general.exchangeRateFormat.ccy.domestic=ccy/Domestic ccy
+entity.general.exchangeRateFormat.domestic.ccy=Domestic CCY/CCY
+entity.general.exchangeRateFormat.ccy.domestic=CCY/Domestic CCY
+entity.general.exchangeRateFormat.ccy.domestic.populated=CCY/{0}
+entity.general.exchangeRateFormat.domestic.ccy.populated={0}/CCY
 entity.general.retentionFlag=Retention flag
 entity.general.retentionFlag.yes=Yes
 entity.general.retentionFlag.no=No
@@ -6275,8 +6277,8 @@ ilmExcelReport.xyz=XYZ
 ilmReport.warningMissingData=Warning: Report data is incomplete. Records are missing in the range {0} to {1}, do you wish to continue?
 ilmReport.warningIncompletedData=Report data is incomplete. Records are missing in the range
 ilmReport.onDayIncompletedData=Report data is incomplete. Records are missing for
-ilmreport.dateGreaterThanCcyTimeframeDate=Please enter a correct date, it must be less than the currency timeframe date
-
+ilmreport.dateGreaterThanCcyTimeframeDate=Report date must be <= {0} (T-1 in the currency timeframe date)
+ilmreport.dateGreaterThanCcyTimeframeDateAll=Report date must be <= {0} (earliest T-1, according to the set of currencies being reported)

 ilmReport.oneDayMissingData=Warning: Report data is incomplete. Records are missing for {0}, do you wish to continue?
 ilmReport.errorMissingData=An error occurred while checking missing data, please see logs.
@@ -7511,5 +7513,98 @@ login.notification.lastFailedLoginIp=Last Failed Login IP
 login.notification.tooltip.lastLoginIp=IP address of last successful login
 login.notification.tooltip.lastFailedLoginIp=IP address of last failed login attempt

-
-reports.submitR=Report
\ No newline at end of file
+multipleMvtActions.label.dataSource=Data Source
+multipleMvtActions.label.total=Total
+multipleMvtActions.label.selected=Selected
+multipleMvtActions.label.bookLbl=Book
+multipleMvtActions.label.ordInstLbl=Ordering Institution
+multipleMvtActions.label.critPayTypeLbl=Critical payment Type
+multipleMvtActions.label.counterPartyLbl=Counterparty ID
+multipleMvtActions.label.expSettlLbl=Expected Settlement
+multipleMvtActions.label.actualSettlLbl=Actual Settlement
+multipleMvtActions.label.noteLbl=Note Text
+multipleMvtActions.label.mvtIdLocationLbl=Movement ID location
+
+multipleMvtActions.label.importButton=Import
+multipleMvtActions.label.processButton=Process
+multipleMvtActions.label.closeButton=Close
+multipleMvtActions.label.xButton=X
+multipleMvtActions.tooltip.dataSrcCombo=Please choose a data source
+multipleMvtActions.tooltip.bookCombo=Please choose a bookCode
+
+multipleMvtActions.addNoteRadio=Add a movement note only
+multipleMvtActions.updateStsRadio=Update status(es)
+multipleMvtActions.includedRadio=Included
+multipleMvtActions.excludedRadio=Excluded
+multipleMvtActions.notUpdateRadio=Do not update
+multipleMvtActions.cancelledRadio=Cancelled
+multipleMvtActions.yesRadio=Yes
+multipleMvtActions.noRadio=No
+multipleMvtActions.unmatchRadio=Unmatch
+multipleMvtActions.reconcileRadio=Reconcile
+multipleMvtActions.updateOtherRadio=Update other settlement details
+multipleMvtActions.colNameRadio=Column Name
+multipleMvtActions.colNumberRadio=Column Number
+
+multipleMvtActions.dataDefFieldSet=Data Definition
+multipleMvtActions.mvtTotalFieldSet=Movement Totals
+multipleMvtActions.MvtsFieldSet=Movements
+multipleMvtActions.actionFieldSet=Action (For all updates, matched movements will first be unmatched)
+multipleMvtActions.noteFieldSet=Notes
+multipleMvtActions.predictFieldSet=Predict Status
+multipleMvtActions.externalFieldSet=External Status
+multipleMvtActions.ilmFieldSet=ILM Fcast Status
+multipleMvtActions.internalSttlmFieldSet=Internal Settlement
+
+MultiMvtActions.title.window=Multiple Movement Action
+
+multiMvtActions.movementId=MovementID
+multiMvtActions.entity=Entity
+multiMvtActions.ccy=Ccy
+multiMvtActions.vdate=Vdate
+multiMvtActions.account=Account
+multiMvtActions.amount=Amount
+multiMvtActions.sign=Sign
+multiMvtActions.pred=Pred
+multiMvtActions.ext=Ext
+multiMvtActions.ilmFcast=ILM Fcast
+multiMvtActions.status=Match Status
+multiMvtActions.ref1=Ref1
+multiMvtActions.ref2=Ref2
+multiMvtActions.extraRef=ExtraRef
+multiMvtActions.book=Book
+multiMvtActions.matchId=MatchID
+multiMvtActions.Source=Source
+multiMvtActions.format=Format
+multiMvtActions.bookCode=Book code
+multiMvtActions.cparty=Cparty
+multiMvtActions.ordInst=Ord Inst
+multiMvtActions.expSettlement=Exp Settlement
+multiMvtActions.actSettlement=Act Settlement
+multiMvtActions.critPayType= Crit Pay Type
+
+multiMvtActions.importFailed = Import failed
+multipleMvtActions.importFile?= Import movements from a file?
+
+role.fieldset.mvtInputUpdate=Movement input/update
+label.manualInput= Manual input requires authorisation
+label.allowMultiMvtUpdatesFromMsd=Allow multi-movement updates from Movement Summary
+tooltip.allowMultiMvtUpdatesFromMsd=Allow the user to update up to <the specified Number> movements selected from the Movement Summary Display
+tooltip.button.multiMvtUpdate= Perform multiple movement action with 2 or more selected movements
+
+tooltip.update=Perform multiple movement action with 2 or more selected movements
+
+alert.emptyMovementIdLocation= Please specify the movement ID location in your file
+multipleMvtActions.confirmProcess=This action will affect <
+multipleMvtActions.confirmProcess1=> movements and is not reversible. Are you sure?
+multiMvtActions.processFailed= Process Failed
+alert.movementIdNotInHeader= Entered movement ID not found in the excel
+alert.movementIdNotFilled= Movement ID location not filled
+multipleMvtActions.label.closeExitButton=Close and exit
+multipleMvtActions.label.closeReturnButton=Close and return
+multipleMvtActions.label.status=Status
+multipleMvtActions.fieldset.process=Process
+alert.invalidColumnPosition= Invalid column position
+alert.invalidDataFound= No valid data found in the excel for the given Movement ID location
+
+reports.submitR=Report
diff --git a/src/main/webapp/jsp/control/multipleMvtUpdates.jsp b/src/main/webapp/jsp/control/multipleMvtUpdates.jsp
new file mode 100644
index ********..5a48569b
--- /dev/null
+++ b/src/main/webapp/jsp/control/multipleMvtUpdates.jsp
@@ -0,0 +1,103 @@
+<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
+<%@ include file="/taglib.jsp"%>
+<%@ page import="org.swallow.util.SwtConstants"%>
+<%@ page import="org.swallow.util.SwtUtil"%>
+<%@page import="org.swallow.work.model.Movement"%>
+<%
+	//variable declaration
+	String selectedMvtIdsList = "";
+	String fromMenu = "";
+	String entityId = "";
+
+	//get the selectedRows from request attribute
+	if (request.getAttribute("selectedMvtIdsList") != null) {
+		selectedMvtIdsList = request.getAttribute("selectedMvtIdsList")
+				.toString();
+	}
+	//get the fromMenu from request attribute
+	if (request.getAttribute("fromMenu") != null) {
+		fromMenu = request.getAttribute("fromMenu")
+				.toString();
+	}
+	//get the entityId from request attribute
+	if (request.getAttribute("entityId") != null) {
+		entityId = request.getAttribute("entityId")
+				.toString();
+	}
+
+%>
+<html>
+<head>
+	<title><s:text name="MultiMvtActions.title.window"/> </title>
+	<%@ include file="/angularJSUtils.jsp"%>
+	<link rel="stylesheet" href="angularSources/styles.css?version=<%= SwtUtil.appVersion %>">
+	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
+</head>
+<SCRIPT language="JAVASCRIPT">
+	var requestURL = new String('<%=request.getRequestURL()%>');
+	var appName = "<%=SwtUtil.appName%>";
+	var idy = requestURL.indexOf('/'+appName+'/');
+	requestURL=requestURL.substring(0,idy+1);
+	var oXMLHTTP = new XMLHttpRequest();
+	var screenRoute = "MultipleMvtActions";
+	var uploadFileImage = "images/open_up.png";
+	var selectedMvtIdsList = "<%= selectedMvtIdsList %>";
+	var entityId = "<%= entityId %>";
+	var fromMenu = "<%= fromMenu %>";
+	/**
+	 *	This section is used to handle calender button on the screen && is used to set the position of the same.
+	 */
+	var dateFormatValue = '${sessionScope.CDM.dateFormatValue}';
+	var menuAccessId = "${requestScope.menuAccessId}";
+
+	var selectedMovementForLock="";
+	function setSelectedMovementForLock(selected){
+		selectedMovementForLock = selected;
+		console.log("selectedMovementForLock: ", selectedMovementForLock);
+	}
+
+
+	function deleteLock()
+	{
+		console.log("selectedMovementForLock: ", selectedMovementForLock);
+		beaconUnlock(selectedMovementForLock);
+		if (window.opener && !window.opener.closed) {
+			window.opener.postMessage({ type: 'callbackApp', data: { } }, '*');
+		}
+	}
+
+
+
+	// For page unload cases
+	function beaconUnlock(movementIds) {
+		try {
+			var appName = "<%=SwtUtil.appName%>";
+			var requestURL = new String('<%=request.getRequestURL()%>');
+			var idy = requestURL.indexOf('/' + appName + '/');
+			requestURL = requestURL.substring(0, idy + 1);
+			const sURL = requestURL + appName + "/movementLock.do?method=unlockMovements";
+
+			// Create the data to be sent
+			const formData = new FormData();
+			formData.append('movementIds', movementIds);
+
+			// Send using beacon API
+			const success = navigator.sendBeacon(sURL, formData);
+			return success ? "success" : "error";
+		} catch (error) {
+			console.error('Error during beacon unlock:', error);
+			return "error";
+		}
+	}
+</SCRIPT>
+
+
+<body scroll="no" style="overflow:hidden" leftmargin="0" topmargin="0" marginheight="0" onunload="deleteLock()">
+<%@ include file="/angularscripts.jsp"%>
+<form id="exportDataForm" target="tmp" method="post">
+	<input type="hidden" name="data" id="exportData" /> <input
+		type="hidden" name="screen" id="exportDataScreen"
+		value="<s:text name="PreAdviceInput.title.window"/>" /></form>
+<iframe name="tmp" width="0%" height="0%" src="#" />
+</body>
+</html>
diff --git a/src/main/webapp/jsp/control/rolemaintenance.jsp b/src/main/webapp/jsp/control/rolemaintenance.jsp
index 4fb13ea0..4b066c18 100644
--- a/src/main/webapp/jsp/control/rolemaintenance.jsp
+++ b/src/main/webapp/jsp/control/rolemaintenance.jsp
@@ -283,7 +283,7 @@ function closeChild()
 	<tr>

 	    <td id="addenablebutton">
-	        <a  tabindex="1"  title='<s:text name="tooltip.addNewRole"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('add'),'rolemaintenanceaddWindow','left=50,top=190,width=475,height=570,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.add"/></a>
+	        <a  tabindex="1"  title='<s:text name="tooltip.addNewRole"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('add'),'rolemaintenanceaddWindow','left=50,top=190,width=520,height=580,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.add"/></a>
 		</td>


@@ -293,7 +293,7 @@ function closeChild()

 		<td id="changeenablebutton" >

-			<a tabindex="2" title='<s:text name="tooltip.changeSelectedRole"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('change'),'rolemaintenancechangeWindow','left=50,top=190,width=475,height=570,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.change"/></a>
+			<a tabindex="2" title='<s:text name="tooltip.changeSelectedRole"/>'  onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('change'),'rolemaintenancechangeWindow','left=50,top=190,width=520,height=580,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.change"/></a>


 		</td>
@@ -301,7 +301,7 @@ function closeChild()
 			<a class="disabled" disabled="disabled" title='<s:text name="tooltip.changeSelectedRole"/>'><s:text name="button.change"/></a>
 		</td>
 		<td id="viewenablebutton" >
-			<a tabindex="3" title='<s:text name="tooltip.viewSelectedRole"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('view'),'rolemaintenancechangeWindow','left=50,top=190,width=475,height=570,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.view"/></a>
+			<a tabindex="3" title='<s:text name="tooltip.viewSelectedRole"/>' onMouseOut="collapsebutton(this)" onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)" onClick="javascript:openWindow(buildAddRole('view'),'rolemaintenancechangeWindow','left=50,top=190,width=520,height=580,toolbar=0, resizable=yes, scrollbars=yes','true')"><s:text name="button.view"/></a>

 		</td>
 		<td id="viewdisablebutton">
diff --git a/src/main/webapp/jsp/control/rolemaintenanceadd.jsp b/src/main/webapp/jsp/control/rolemaintenanceadd.jsp
index 4d21b16a..84428f03 100644
--- a/src/main/webapp/jsp/control/rolemaintenanceadd.jsp
+++ b/src/main/webapp/jsp/control/rolemaintenanceadd.jsp
@@ -450,12 +450,12 @@
 		<input name="selectedRoleId" type="hidden" >
 		<input name="accountAccessSize1" type="hidden" value="">
 	<body leftmargin="0" topmargin="0" marginheight="0" onLoad="setParentChildsFocus();bodyOnLoad();setFocus(document.forms[0]);ShowErrMsgWindow('${actionError}');" onunload="call()" onBeforeUnload="beforeBodyUnload()" >
-	<div id="RoleMaintenanceAdd" style="position:absolute; left:20px; top:10px; width:420px; height:480px; border:2px outset;" color="#7E97AF">
-	<div id="RoleMaintenanceAdd" style="position:absolute; left:8px; top:4px; width:420px; height:460px;">
+	<div id="RoleMaintenanceAdd" style="position:absolute; left:20px; top:10px; width:470px; height:510px; border:2px outset;" color="#7E97AF">
+	<div id="RoleMaintenanceAdd" style="position:absolute; left:8px; top:4px; width:470px; height:460px;">

 <!------------------------------first fieldset------------------------------------------->
 	<div style="left:8px; top:18px;height:70px;" >
-	<fieldset style="width:395px;border:2px groove;">
+	<fieldset style="width:445px;border:2px groove;">
 	<legend>
 	<s:text name="role.roleId"/>
 	</legend>
@@ -513,7 +513,7 @@
   	</div>
   <!--------------------------------------end of first fieldset---------------------------------->
 	<div id="ddimagebuttons" style=" top:70px;height:240px;position: absolute;">
-	<fieldset style="width:398px;border:2px groove;height:210px;position: absolute;">
+	<fieldset style="width:445px;border:2px groove;height:210px;position: absolute;">
 	<legend>
 	<s:text name="role.entAccessList.Access"/>
 	</legend>
@@ -867,7 +867,7 @@
 	</fieldset>
 	</div>
 	<div id="ddimagebuttons" style="top:310px;position: absolute;">
-	<fieldset style="width:398px;border:2px groove;height:70px;">
+	<fieldset style="width:445px;border:2px groove;height:70px;">
 	<legend>
 	<s:text name="role.sweepLimits.sweep"/>
 	</legend>
@@ -936,15 +936,15 @@
 		</div>
 		</fieldset>
 		</div>
-	<!--Authorization-->
+	<!--Movement Input/Update-->
 	<div id="ddimagebuttons"  style="top:380px;position: absolute;">
-		<fieldset style="width:398px;border:2px groove;height:45px;">
+		<fieldset style="width:440px;border:2px groove;height:45px;">
 		<legend>
-		<s:text name="role.authorization.authorizationInput"/>
+		<s:text name="role.fieldset.mvtInputUpdate"/>
 		</legend>
 			<div style="left:8px; top:4px;">
-			<table width="315" border="0" cellpadding="0" cellspacing="1" height="20px">
-		   <tr>
+			<table width="440" border="0" cellpadding="0" cellspacing="1" height="20px">
+		   <tr height="25px">
 		    <td width="25px" align = "right">
 			          <s:if test='"yes" != #request.isViewRole' >
 					<s:checkbox name="role.authorizeInput"  cssStyle="width:13;"  cssClass="htmlTextAlpha"  fieldValue="Y" value='%{#request.role.authorizeInput == "Y"}' titleKey="tooltip.authorizationInput" tabindex="10"/>
@@ -953,18 +953,33 @@
 				 <s:checkbox name="role.authorizeInput"    cssStyle="width:13;"  cssClass="htmlTextAlpha"  fieldValue="Y" value='%{#request.role.authorizeInput == "Y"}' titleKey="tooltip.authorizationInput" disabled="true" tabindex="10"/>
 				</s:if>
 			</td>
-          <td width="270px">&nbsp;<b>Manual Input</b></td>
+          <td width="440px">&nbsp;<b><s:text name="label.manualInput"/></b></td>
+		  <td width="20px">&nbsp;</td>
+		  	</tr>
+
+            <tr height="28px">
+		    <td width="25px" align = "right">
+			     <s:if test='"yes" != #request.isViewRole' >
+					<s:checkbox name="role.allowMsdMultiMvtUpdates"    cssStyle="width:13;"  cssClass="htmlTextAlpha"  fieldValue="Y" value='%{#request.role.allowMsdMultiMvtUpdates == "Y"}' titleKey="tooltip.allowMultiMvtUpdatesFromMsd" tabindex="10"/>
+				 </s:if>
+				 <s:if test='"yes" == #request.isViewRole' >
+				<s:checkbox name="role.allowMsdMultiMvtUpdates"    cssStyle="width:13;"  cssClass="htmlTextAlpha"  fieldValue="Y" value='%{#request.role.allowMsdMultiMvtUpdates == "Y"}' titleKey="tooltip.allowMultiMvtUpdatesFromMsd" disabled="true" tabindex="10"/>
+
+				 </s:if>
+			</td>
+          <td width="440px">&nbsp;<b><s:text name="label.allowMultiMvtUpdatesFromMsd"/></b></td>
 		  <td width="20px">&nbsp;</td>
 		  	</tr>
 	</table>
 	</div>
 	</fieldset>
-	<fieldset style="width:398px;border:2px groove;height:45px;">
+	<fieldset style="width:445px;border:2px groove;height:45px;">
 	<legend>
 	<s:text name="role.notification.inputNotification"/>
 	</legend>
 	<div style="left:8px; top:4px;">
-	<table width="400" border="0" cellpadding="0" cellspacing="1" height="20px">
+	<table width="440" border="0" cellpadding="0" cellspacing="1" height="22px">
+
 	<tr  height="25px">
 			<td width="25px" align = "right">
 			<s:if test='"yes" != #request.isViewRole' >
@@ -1003,8 +1018,8 @@
 </div>

 <!--Start: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->
-<div id="RoleMaintenanceAdd" style="position:absolute; left:350; top:502px; width:70px; height:15px;z-index:5; visibility:visible;margin-top: 6px;">
-
+<div id="RoleMaintenanceAdd" style="position:absolute; left:400; top:532px; width:70px; height:15px;z-index:5; visibility:visible;margin-top: 6px;">
+
 			<s:if test='"save" == #request.methodName' >
 			 <div  style="float: left;margin-top: 3px;margin-right: -20px">
 				<a  tabindex="19" href=# onclick="javascript:openWindow(buildPrintURL('print','Add Role '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>'></a>
@@ -1034,7 +1049,7 @@
 	   </s:if>
      </div>
      <s:if test='"view" == #request.methodName' >
-    	 <div style="position:absolute; left:400; top:506px; width:30px; height:15px;">
+    	 <div style="position:absolute; left:440; top:535px; width:30px; height:15px;">
      		<a  tabindex="19" href=# onclick="javascript:openWindow(buildPrintURL('print','View Role '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')" onMouseOut="MM_swapImgRestore()" onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)">
      			<img src="images/help_default.GIF " name="Help"  border="0" title='<s:text name="tooltip.helpScreen"/>'></a>
 </div>
@@ -1042,7 +1057,7 @@
      <!--End: Code modified by Med Amine on 29-jan-2013 for Mantis:2096 -->

 <s:if test='"yes" != #request.isViewRole' >
-<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:500px; width:420px; height:39px; visibility:visible;">
+<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:530px; width:470px; height:39px; visibility:visible;">
   <div id="RoleMaintenanceAdd" style="position:absolute; left:6; top:4; width:360px; height:10px; visibility:visible;">
   	  <table width="210px" border="0" cellspacing="0" cellpadding="0" height="20">
 		<tr height="25">
@@ -1096,7 +1111,7 @@
 </s:if>
 <!-- Buttons incase of view role-->
 <s:if test='"yes" == #request.isViewRole' >
-<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20; top:500; width:420px; height:39px; visibility:visible;">
+<div id="ddimagebuttons" color="#7E97AF" style="position:absolute; border:2px outset; left:20;  top:530; width:470px; height:39px; visibility:visible;">
   <div id="RoleMaintenanceAdd" style="position:absolute; left:10; top:4; width:380px; height:10px; visibility:visible;">
   	  <table width="140px" border="0" cellspacing="0" cellpadding="0" height="20px">
 		<tr>
@@ -1118,4 +1133,4 @@
 </Script>
 </s:form>
 </body>
-</html>
\ No newline at end of file
+</html>
diff --git a/src/main/webapp/jsp/maintenance/currencyexchangemaintenance.jsp b/src/main/webapp/jsp/maintenance/currencyexchangemaintenance.jsp
index 1b696476..87f4f3d2 100644
--- a/src/main/webapp/jsp/maintenance/currencyexchangemaintenance.jsp
+++ b/src/main/webapp/jsp/maintenance/currencyexchangemaintenance.jsp
@@ -963,13 +963,13 @@ var re = /^\d+$/;
 		style="position: absolute; top: 0px; left: 0px; display: none;">
 	</iframe>

-	<table width="422px" border="0" cellspacing="0" cellpadding="0"
+	<table width="663" border="0" cellspacing="0" cellpadding="0"
 		height="25">
 		<tr height="25px">
-			<td width="120"><b style="font-size: 12px"><s:text name="auditLog.from" /></b>*</td>
-			<td width="27">&nbsp;</td>
+			<td width="65"><b style="font-size: 12px"><s:text name="auditLog.from" /></b>*</td>
+			<td width="23">&nbsp;</td>

-			<td width="215px" id="exchangeRateFromDate"><s:textfield cssClass="htmlTextAlpha"
+			<td width="130px" id="exchangeRateFromDate"><s:textfield cssClass="htmlTextAlpha"
 				name="currencyexchange.exchangeRateFromDateAsString"
 				style="width:80px;height:20px;" maxlength="10"
 				onkeydown="onFromDateKeyPress(this,event);"
@@ -982,19 +982,49 @@ var re = /^\d+$/;
 				title='<s:text name="tooltip.selectBalanceDate"/>'
 				src="images/calendar-16.gif" style="margin-bottom: -6px; margin-left: -9px;"></A></td>
 			<td width="28">&nbsp;</td>
-			<td width="50" height="26"><b style="font-size: 12px;padding-left: 10px"><s:text name="auditLog.to" /></b>*</td>
+			<td width="20" height="26"><b style="font-size: 12px;padding-left: 10px"><s:text name="auditLog.to" /></b>*</td>
 			<td width="26">&nbsp;</td>
-			<td width="215px" id="exchangeRateToDate"><s:textfield cssClass="htmlTextAlpha"
-				name="currencyexchange.exchangeRateToDateAsString"
-				style="width:80px;height:20px;" maxlength="10"
-				onkeydown="onToDateKeyPress(this,event);" onblur="onDateChange(this);"
-				onmouseout="dateSelected=false;" titleKey="tooltip.toDate"
-				tabindex="5" /> &nbsp; <!-- To store To date --> <A
-				title='<s:text name="tooltip.selectToDate"/>' name="datelink2"
-				ID="datelink2" tabindex="6"
-				" onClick="cal2.select(document.forms[0].elements['currencyexchange.exchangeRateToDateAsString'],'datelink2',dateFormatValue);storeToDate();dateSelected=true;return false;"><img
-				title='<s:text name="tooltip.selectBalanceDate"/>'
-				src="images/calendar-16.gif" style="margin-bottom: -6px; margin-left: -9px;"></A></td>
+			<td width="120px">
+				<s:textfield name="currencyexchange.exchangeRateToDateAsString" tabindex="29"
+					maxlength="10" cssClass="htmlTextAlpha" style="width:80px;margin-bottom: 5px;height:20px;"
+					disabled="%{#request.screenFieldsStatus == 'true' ? 'true': ''}"
+					titleKey="tooltip.entity.dateTo" onblur="onToDateChange();"
+					onkeydown="onToDateKeyPress(this,event)" />
+				<A name="datelink2" ID="datelink2" tabindex="30"
+					onClick="toDateClick();">&nbsp;
+				<img title='<s:text name="tooltip.selectToDate"/>'
+					src="images/calendar-16.gif"> </A></td>
+
+			<td width="10" height="26"></td>
+			<td width="26">&nbsp;</td>
+			<td width="215px">
+				<span id="exchangeRateFormatLabel" style="white-space: nowrap;">
+					<s:if test='%{#request.currencyexchange.id.currencyCode == "All"}'>
+						<s:if test='%{#request.entity.exchangeRateFormat == "1"}'>
+							CCY/EUR
+							<span style="font-style:italic;color:#666666;font-size:11px;">  (1 CCY = n EUR)</span>
+						</s:if>
+						<s:else>
+							EUR/CCY
+							<span style="font-style:italic;color:#666666;font-size:11px;"> (1 EUR = n CCY)</span>
+						</s:else>
+					</s:if>
+					<s:else>
+						<s:if test='%{#request.entity.exchangeRateFormat == "1"}'>
+							<s:property value="#request.currencyexchange.id.currencyCode"/>/EUR
+							<span style="font-style:italic;color:#666666;font-size:11px;">
+							(1 <s:property value="#request.currencyexchange.id.currencyCode"/> = n EUR)
+							</span>
+						</s:if>
+						<s:else>
+							EUR/<s:property value="#request.currencyexchange.id.currencyCode"/>
+							<span style="font-style:italic;color:#666666;font-size:11px;">
+							(1 EUR = n <s:property value="#request.currencyexchange.id.currencyCode"/>)
+							</span>
+						</s:else>
+					</s:else>
+				</span>
+			</td>
 		</tr>
 	</table>
 <!-- Start : Nithiyananthan 22/12/2011 Mantis 1612: Currency exchange rates: Show a more meaningful message on validation of amount entered -->
diff --git a/src/main/webapp/jsp/maintenance/entitymaintenance.jsp b/src/main/webapp/jsp/maintenance/entitymaintenance.jsp
index e367ab5a..fd769b4f 100644
--- a/src/main/webapp/jsp/maintenance/entitymaintenance.jsp
+++ b/src/main/webapp/jsp/maintenance/entitymaintenance.jsp
@@ -1390,7 +1390,7 @@ var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>'
 		</tr>
   <!-- End Code:Modified by Naseema.Sd for Mantis 1797 -->
 		<tr height="22px">
-			<td width="145px"><b><s:text name="tooltip.timeZoneRegion" /></b></td>
+			<td width="148px"><b><s:text name="tooltip.timeZoneRegion" /></b></td>
 			<td width="28px">&nbsp;</td>
 			<td><select name="entity.entTimeZone"
 					<s:if test='%{#request.screenFieldsStatus == "true"}'> disabled</s:if>
@@ -1403,22 +1403,52 @@ var currencyFormat = '<s:property value="#request.session.CDM.currencyFormat"/>'
 		</tr>

 		<tr height="22px">
-			<td width="148px"><b><s:text name="entity.general.exchangeRateFormat" /></b></td>
-			<td width="28px">&nbsp;</td>
-			<td width="355px">
-			<s:radio id="1" tabindex="11" style="width:13;"
-				titleKey="entity.SelectDomesticCCY/CCY"
-				name="entity.exchangeRateFormat" list="#{'1':''}"
-				disabled="%{#request.screenFieldsStatus == 'true' ? 'true': ''}"
-				onclick="javaScript:changeinexchange(0)" /> <label for="1"
-				title='<s:text name="entity.SelectDomesticCCY/CCY"/>'> <s:text name="entity.general.exchangeRateFormat.domestic.ccy" /></label>&nbsp;&nbsp;&nbsp;
-			<s:radio id="2" tabindex="12" style="width:13;"
-				name="entity.exchangeRateFormat" list="#{'2':''}"
-				disabled="%{#request.screenFieldsStatus == 'true' ? 'true': ''}"
-				titleKey="entity.SelectCCY/DomesticCCY"
-				onclick="javaScript:changeinexchange(1)" />
-			<label for="2"
-				title='<s:text name="entity.SelectCCY/DomesticCCY"/>'> <s:text name="entity.general.exchangeRateFormat.ccy.domestic" /></label></td>
+			<td width="148px" style="vertical-align:middle;"><b><s:text name="entity.general.exchangeRateFormat" /></b></td>
+			<td width="28px" style="vertical-align:middle;">&nbsp;</td>
+			<td width="355px" style="vertical-align:middle;">
+				<div style="display:inline-block;margin-right:15px;">
+					<s:radio id="1" tabindex="11" style="width:13;margin:0;vertical-align:middle;"
+						titleKey="entity.SelectDomesticCCY/CCY"
+						name="entity.exchangeRateFormat" list="#{'1':''}"
+						disabled="%{#request.screenFieldsStatus == 'true' ? 'true': ''}"
+						onclick="javaScript:changeinexchange(0)" />
+					<label for="1" style="display:inline-block;vertical-align:middle;" title='<s:text name="entity.SelectDomesticCCY/CCY"/>'>
+						<s:if test="entity.domesticCurrency != null && entity.domesticCurrency != ''">
+							CCY/<s:property value="entity.domesticCurrency"/>
+							<span style="font-style:italic;color:#666666;font-size:11px;">
+								(1 CCY = n <s:property value="entity.domesticCurrency"/>)
+							</span>
+						</s:if>
+						<s:else>
+							CCY/Domestic CCY
+							<span style="font-style:italic;color:#666666;font-size:11px;">
+								(1 CCY = n Domestic CCY)
+							</span>
+						</s:else>
+					</label>
+				</div>
+				<div style="display:inline-block;">
+					<s:radio id="2" tabindex="12" style="width:13;margin:0;vertical-align:middle;"
+						name="entity.exchangeRateFormat" list="#{'2':''}"
+						disabled="%{#request.screenFieldsStatus == 'true' ? 'true': ''}"
+						titleKey="entity.SelectCCY/DomesticCCY"
+						onclick="javaScript:changeinexchange(1)" />
+					<label for="2" style="display:inline-block;vertical-align:middle;" title='<s:text name="entity.SelectCCY/DomesticCCY"/>'>
+						<s:if test="entity.domesticCurrency != null && entity.domesticCurrency != ''">
+							<s:property value="entity.domesticCurrency"/>/CCY
+							<span style="font-style:italic;color:#666666;font-size:11px;">
+								(1 <s:property value="entity.domesticCurrency"/> = n CCY)
+							</span>
+						</s:if>
+						<s:else>
+							Domestic CCY/CCY
+							<span style="font-style:italic;color:#666666;font-size:11px;">
+								(1 Domestic CCY = n CCY)
+							</span>
+						</s:else>
+					</label>
+				</div>
+			</td>
 		</tr>
 	</table>
 	</div>
diff --git a/src/main/webapp/jsp/reports/ilmReport.jsp b/src/main/webapp/jsp/reports/ilmReport.jsp
index d07d1a5c..990e8062 100644
--- a/src/main/webapp/jsp/reports/ilmReport.jsp
+++ b/src/main/webapp/jsp/reports/ilmReport.jsp
@@ -14,6 +14,11 @@
         var isSingleDate = true;
 	var dateInCcyTimeframe = "${requestScope.dateInCcyTimeframe}";
 	var dateInCcyTimeframeAsString = "${requestScope.dateInCcyTimeframeAsString}";
+        var maxReportDate = "${requestScope.maxReportDate}";
+        var dateValidationMessage = "<s:text name='ilmreport.dateGreaterThanCcyTimeframeDate'/>";
+        var dateValidationMessageAll = "<s:text name='ilmreport.dateGreaterThanCcyTimeframeDateAll'/>";
+        dateValidationMessage = dateValidationMessage.replace("{0}", maxReportDate);
+        dateValidationMessageAll = dateValidationMessageAll.replace("{0}", maxReportDate);
 	var globalGrp;
 	var configScheduler = "${requestScope.configScheduler}";
 	var newILMReportSchedConfig = "${requestScope.newILMReportSchedConfig}";
@@ -618,23 +623,28 @@
 		if(!isValid)
 			document.forms[0].elements[elem].value="";

-		var ccyTimeframeDateToCompare;
+                var maxReportDateToCompare;
 		var selectedDateToCompare;
-		var dateArray = dateInCcyTimeframeAsString.split("/");
+                var dateArray = maxReportDate.split("/");
 		if (dateFormatValue == "dd/MM/yyyy") {
 			// NOTE: month is 0-11
-			ccyTimeframeDateToCompare = new Date(dateArray[2], dateArray[1]-1, dateArray[0]);
+                        maxReportDateToCompare = new Date(dateArray[2], dateArray[1]-1, dateArray[0]);
 			dateArray = document.forms[0].elements[elem].value.split("/");
 			selectedDateToCompare = new Date(dateArray[2], dateArray[1]-1, dateArray[0]);
 		} else {
 			// NOTE: month is 0-11
-			ccyTimeframeDateToCompare = new Date(dateArray[2], dateArray[0]-1, dateArray[1]);
+                        maxReportDateToCompare = new Date(dateArray[2], dateArray[0]-1, dateArray[1]);
 			dateArray = document.forms[0].elements[elem].value.split("/");
 			selectedDateToCompare = new Date(dateArray[2], dateArray[0]-1, dateArray[1]);
 		}

-		if (ccyTimeframeDateToCompare.getTime() <= selectedDateToCompare.getTime()) {
-			alert('<s:text name="ilmreport.dateGreaterThanCcyTimeframeDate"/>');
+                if (selectedDateToCompare.getTime() > maxReportDateToCompare.getTime()) {
+                    var ccyCode = document.forms[0].elements["reports.currencyCode"].value;
+                    if (ccyCode != "All") {
+                        alert(dateValidationMessage);
+                    }else {
+                        alert(dateValidationMessageAll)
+                    }
 			setTimeout(function(){document.getElementById(elem).focus();}, 1);
 			document.forms[0].elements[elem].value = "";
 			return false;
@@ -1472,4 +1482,4 @@ input[name ="keywordInput"] {
     </s:form>
 </body>

-</html>
+</html>
\ No newline at end of file
diff --git a/src/main/webapp/jsp/work/movementsummarydisplayflex.jsp b/src/main/webapp/jsp/work/movementsummarydisplayflex.jsp
index d5996bd9..e65fcb61 100644
--- a/src/main/webapp/jsp/work/movementsummarydisplayflex.jsp
+++ b/src/main/webapp/jsp/work/movementsummarydisplayflex.jsp
@@ -50,6 +50,7 @@
 	var currGrp = "${requestScope.currGrp}";
 	var profileId= "${requestScope.profileId}";
 	var msdDisplayColsList=  "${requestScope.msdDisplayColsList}";
+	var multiMvtUpdateNbr=  "${requestScope.multiMvtUpdateNbr}";
 	var posLevelList=  "${requestScope.posLevelList}";
 	var sourcelList=  "${requestScope.sourceList}";
 	var formatList=  "${requestScope.formatList}";
@@ -336,6 +337,9 @@
 	label["text"]["button-options"] = "<s:text name="button.options"/>";
 	label["tip"]["button-options"] = "<s:text name="tooltip.options"/>";

+	label["text"]["button-update"] = "<s:text name="button.update"/>";
+	label["tip"]["button-update"] = "<s:text name="tooltip.update"/>";
+
 	label["text"]["label-movementCannotBeUnlocked"] = "<s:text name="label.movementCannotBeUnlocked"/>";
 	label["text"]["label-movementSummaryDisplay"] = "<s:text name="label.movementSummaryDisplay"/>";
 	label["text"]["label-selectedOnlyMatchedItems"] = "<s:text name="label.selectedOnlyMatchedItems"/>";
@@ -1775,7 +1779,7 @@
 	function setSelectedMovementForLock(selected){
 		selectedMovementForLock = selected;
 	}
-
+	var selectedMovementForLock = "";
 	function deleteLock()
 	{

@@ -2039,6 +2043,15 @@
 		return false;
 	}

+	function openMultiMvtActions(methodName, selectedMvtIdsList, entityId){
+		deleteLock();
+		var param = '/' + appName + '/multipleMvtActions.do?method='+methodName;
+		param += '&selectedMvtIdsList='+selectedMvtIdsList;
+		param += '&entityId='+entityId;
+		var 	mainWindow = openWindow (param, 'multipleMvtActions','left=10,top=230,width=1350,height=780,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
+		return false;
+	}
+

 </script>
 <%@ include file="/angularscripts.jsp"%>
@@ -2143,4 +2156,4 @@
 </form>
 <iframe name="tmp" width="0%" height="0%" src="#" />
 </body>
-</html>
\ No newline at end of file
+</html>
