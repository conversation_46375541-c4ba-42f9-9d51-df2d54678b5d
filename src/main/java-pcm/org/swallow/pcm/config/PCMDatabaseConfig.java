package org.swallow.pcm.config;

import org.apache.commons.dbcp.BasicDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.*;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.orm.hibernate5.HibernateExceptionTranslator;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.swallow.config.PcmEnabledCondition;
import org.swallow.security.SwtSecureBasicDataSource;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import javax.sql.DataSource;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Properties;

/**
 * Created by allan on 23/08/16.
 */
@Conditional(PcmEnabledCondition.class)
@Configuration
@ComponentScan("org.swallow")
@EnableTransactionManagement
@Order(1)
public class PCMDatabaseConfig {

	@Value("classpath:connection-pcm.properties")
	private Resource resource;

	@Bean
	@Order(1)
	@Qualifier(value = "dataSource-pc")
    public DataSource PCMdataSource() {
    	Properties props = new Properties();
		try {
			props.load(resource.getInputStream());
		} catch (IOException e) {
			throw new RuntimeException("Error while loading file: connection.properties, cause: "+e.getMessage(), e);
		}

        SwtSecureBasicDataSource dataSource = new SwtSecureBasicDataSource();
        dataSource.setConnectionModule("PCM");
        dataSource.setDriverClassName(props.getProperty("pcm.datasource.driver"));
        dataSource.setUrl(props.getProperty("pcm.datasource.url"));
        dataSource.setUsername(props.getProperty("pcm.datasource.username"));
        dataSource.setPassword(props.getProperty("pcm.datasource.password"));
//        dataSource.setDefaultAutoCommit(false);
        return dataSource;
    }


    @Bean
    @Qualifier(value = "dbaEntityManagerPC")
    public EntityManager dbaEntityManagerPC(EntityManagerFactory entityManagerFactory) {
        return entityManagerFactory.createEntityManager();
    }

    @Bean
    public PlatformTransactionManager transactionManagerPC() throws SQLException {
        JpaTransactionManager txManager = new JpaTransactionManager();
        txManager.setEntityManagerFactory(entityManagerFactoryPC());
        txManager.setDataSource(PCMdataSource());
        return txManager;
    }
    
    @Bean
    public EntityManagerFactory entityManagerFactoryPC() throws SQLException {

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setGenerateDdl(false);

        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
        factory.setJpaVendorAdapter(vendorAdapter);
        factory.setPackagesToScan("org.swallow.pcm");
        factory.setDataSource(PCMdataSource());
        factory.afterPropertiesSet();

        return factory.getObject();
    }

    
    // @Bean
    public Properties hibernateProperties() {
        Properties properties = new Properties();
        //properties.put("hibernate.dialect", "org.hibernate.dialect.Oracle10gDialect");
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.format_sql", "false");
        properties.put("hbm2ddl.auto", "true");
        properties.put("hibernate.connection.autocommit", "false");
        properties.put("hibernate.allow_update_outside_transaction", "true");
        properties.put("hibernate.module", "PCM");
        // Add more Hibernate properties as needed
        return properties;
    }
    
    @Bean
    @Qualifier(value = "sessionFactoryPC")
    public LocalSessionFactoryBean sessionFactoryPC() {
        LocalSessionFactoryBean sessionFactory = new LocalSessionFactoryBean();
        sessionFactory.setDataSource(PCMdataSource());
        sessionFactory.setPackagesToScan("org.swallow.model", "org.swallow.pcm.maintenance.model",
                "org.swallow.pcm.control.model", "org.swallow.pcm.work.model",
                "org.swallow.work.model"); // Added missing package
        sessionFactory.setHibernateProperties(hibernateProperties());



        
        // Add ALL hbm.xml files
        //sessionFactory.setMappingResources("org/swallow/maintenance/model/*.hbm.xml");
        try {
            Resource[] resources = new PathMatchingResourcePatternResolver()
                    .getResources("classpath:org/swallow/**/*.hbm.xml");

            sessionFactory.setMappingLocations(resources);
        } catch (IOException e) {
            throw new RuntimeException("I/O Error occurred while mapping HBM.XML resources, cause: "+ e.getMessage(), e);
        }
        
        return sessionFactory;
    }
}