(window.webpackJsonp=window.webpackJsonp||[]).push([[104],{FuMI:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),l=i("mrSG"),a=i("447K"),r=i("ZYCi"),o=i("uPAI"),s=i("6blF"),u=(i("0GgQ"),i("ik3b")),h=i("EVdn"),c=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.inputData=new a.G(n.commonService),n.alertingData=new a.G(n.commonService),n.refreshTabsData=new a.G(n.commonService),n.iLMConfData=new a.G(n.commonService),n.baseURL=a.Wb.getBaseURL(),n.actionMethod="",n.actionPath="",n.requestParams=[],n.jsonReader=new a.L,n.jsonReaderTabs=new a.L,n.lastTabToOpenSeriesStyle=null,n.lastTabToOpenSeriesStyleIsGlobal=null,n.menuAccess=2,n.tabsConfig=[],n.tabsName=[],n.tabsOrder=[],n.refreshRate=10,n.refValueChanged=!1,n.tooltipEntityId=null,n.tooltipCurrencyCode=null,n.tooltipFacilityId=null,n.tooltipSelectedDate=null,n.tooltipOtherParams=[],n.selectedNodeId=null,n.treeLevelValue=null,n.lastSelectedTooltipParams=null,n.eventsCreated=!1,n.customTooltip=null,n.seriesStyleComboboxValues=null,n.seriesStyleSizeOfAllCombo=null,n.seriesStyleIsGlobal=null,n.arrayOfTabs=[],n.nameOfTabs=[],n.swtAlert=new a.bb(e),n}return l.d(e,t),e.ngOnDestroy=function(){window.Main=instanceElement=null},e.prototype.ngOnDestroy=function(){},e.prototype.ngOnInit=function(){window.Main=instanceElement=this,this.entityLabel.text=a.Wb.getPredictMessage("ilm.entityFilter",null),this.entityCombo.toolTip=a.x.call("getBundle","tip","entity","Select an entity ID"),this.ccyLabel.text=a.Wb.getPredictMessage("ilm.ccyFilter",null),this.currencyCombo.toolTip=a.x.call("getBundle","tip","currency","Select currency code"),this.refreshCB.label=a.x.call("getBundle","text","refreshevery","Refresh Every"),this.refreshText.toolTip=a.x.call("getBundle","tip","refreshevery","Enter Refresh Every"),this.refreshButton.label=a.x.call("getBundle","text","label-refresh","Refresh"),this.ccyMuliplierCB.label=a.x.call("getBundle","text","ccymultiplier","Use Currency Multiplier"),this.optionButton.label=a.Wb.getPredictMessage("button.option",null),this.closeButton.label=a.Wb.getPredictMessage("button.close",null)},e.prototype.onLoad=function(){var t=this;this.iLMConfData.cbResult=this.saveResult.bind(this),this.iLMConfData.cbFault=this.saveFault.bind(this),this.iLMConfData.encodeURL=!1,this.currencyGrid=this.summaryCanvas.addChild(a.Vb),this.currencyGrid.listenHorizontalScrollEvent=!0,this.currencyGrid.hideHorizontalScrollBar=!0,this.currencyGrid.lockedColumnCount=2,this.currencyGrid.treeMaxLevel=3,this.totalsGrid=this.totalsContainer.addChild(a.Ub),this.totalsGrid.selectable=!1,this.totalsGrid.fireHorizontalScrollEvent=!0,this.totalsGrid.lockedColumnCount=2,this.currencyGrid.uniqueColumn="seqN",this.currencyGrid.colWidthURL(this.baseURL+"ilmAnalysisMonitor.do?screenName=ilmSummaryGrid"),this.currencyGrid.colOrderURL(this.baseURL+"ilmAnalysisMonitor.do?screenName=ilmSummaryGrid"),this.currencyGrid.saveWidths=!0,this.currencyGrid.saveColumnOrder=!0,this.currencyGrid.ITEM_CLICK.subscribe(function(e){t.itemClickFunction(e),t.cellLogic(e)}),this.currencyGrid.columnWidthChanged.subscribe(function(e){t.resizeGrids(e)}),this.currencyGrid.columnOrderChanged.subscribe(function(e){t.resizeGrids(e)}),s.a.fromEvent(document.body,"click").subscribe(function(e){t.positionX=e.clientX,t.positionY=e.clientY}),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.requestParams=[],this.requestParams.firstLoad=!0,this.actionPath="ilmAnalysisMonitor.do?",this.actionMethod="method=ilmSummaryGridDisplay",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),a.b.subscribe(function(e){e.id==t.exportContainer.id&&t.report(e.type)}),a.Zb.subscribe(function(e){t.tabNavigator.selectedIndex>=t.tabNavigator.tabChildrenArray.length&&(t.tabNavigator.selectedIndex=0),t.updateILMOptionConfig(e)})},e.prototype.resizeGrids=function(t){try{this.totalsGrid.setRefreshColumnWidths(this.currencyGrid.gridObj.getColumns())}catch(e){console.log("resizeGrids",e)}},e.prototype.getParamsFromParent=function(){return{sqlParams:this.lastSelectedTooltipParams,facilityId:this.tooltipFacilityId,selectedNodeId:this.selectedNodeId,treeLevelValue:this.treeLevelValue,tooltipCurrencyCode:this.tooltipCurrencyCode,tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate,tooltipSelectedAccount:null,tooltipOtherParams:this.tooltipOtherParams}},e.prototype.createTooltip=function(){var t=this;this.customTooltip&&this.customTooltip.close&&this.removeTooltip();try{this.customTooltip=a.Eb.createPopUp(parent,a.u,{}),this.positionY>360&&(this.positionY=360),this.customTooltip.setWindowXY(this.positionX,this.positionY),this.customTooltip.enableResize=!1,this.customTooltip.width="410",this.customTooltip.height="450",this.customTooltip.enableResize=!1,this.customTooltip.title="Alert Summary Tooltip",this.customTooltip.showControls=!0,this.customTooltip.showHeader=!1,this.customTooltip.parentDocument=this,this.customTooltip.processBox=this,this.customTooltip.display(),setTimeout(function(){t.eventsCreated||t.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe(function(e){t.lastSelectedTooltipParams=e.noode.data,a.x.call("openAlertInstanceSummary","openAlertInstSummary")})},0),setTimeout(function(){t.eventsCreated||t.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe(function(e){t.getScenarioFacility(e.noode.data.scenario_id),t.lastSelectedTooltipParams=e.noode.data,t.hostId=e.hostId,t.entityId=t.lastSelectedTooltipParams.ENTITY,t.currencyId=t.lastSelectedTooltipParams.CCY})},0),setTimeout(function(){t.eventsCreated||t.customTooltip.getChild().ITEM_CLICK.subscribe(function(e){t.selectedNodeId=e.noode.data.id,t.treeLevelValue=e.noode.data.treeLevelValue,t.customTooltip.getChild().linkToSpecificButton.enabled=!1,1==e.noode.data.count&&0==e.noode.isBranch&&(t.customTooltip.getChild().linkToSpecificButton.enabled=!0)})},0)}catch(e){console.log("SwtCommonGrid -> createTooltip -> error",e)}},e.prototype.getScenarioFacility=function(t){var e=this;this.requestParams=[],this.alertingData.cbStart=this.startOfComms.bind(this),this.alertingData.cbStop=this.endOfComms.bind(this),this.alertingData.cbFault=this.inputDataFault.bind(this),this.alertingData.encodeURL=!1,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getScenarioFacility",this.requestParams.scenarioId=t,this.alertingData.url=this.baseURL+this.actionPath+this.actionMethod,this.alertingData.cbResult=function(t){e.openGoToScreen(t)},this.alertingData.send(this.requestParams)},e.prototype.openGoToScreen=function(t){if(t&&t.ScenarioSummary&&t.ScenarioSummary.scenarioFacility){var e=t.ScenarioSummary.scenarioFacility,i=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.entity_id?this.lastSelectedTooltipParams.entity_id:this.tooltipEntityId,n=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.currency_code?this.lastSelectedTooltipParams.currency_code:this.tooltipCurrencyCode,l=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.match_id?this.lastSelectedTooltipParams.match_id:null,r=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.movement_id?this.lastSelectedTooltipParams.movement_id:null,o=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.sweep_id?this.lastSelectedTooltipParams.sweep_id:null;a.x.call("goTo",e,this.hostId,i,l,n,r,o,"")}},e.prototype.removeTooltip=function(){null!=this.customTooltip&&this.customTooltip.close()},e.prototype.itemClickFunction=function(t){null!=t.target&&null!=t.target.field&&"alerting"==t.target.field&&(this.tooltipCurrencyCode=0==t.target.data.__treeLevel?t.target.data.currencyEntityGroupAccount:t.target.data.ccyCode,this.tooltipEntityId=t.target.data.__treeLevel>0?t.target.data.entityId:null,this.tooltipFacilityId=this.getFacilityName(t.target.data.__treeLevel),this.tooltipOtherParams.ilmAccountGroup=t.target.data.__treeLevel>1?t.target.data.accountGroupId:null,this.tooltipSelectedDate=this.jsonReader.getScreenAttributes().valueDate,this.createTooltip())},e.prototype.getFacilityName=function(t){var e=null;switch(t){case 0:e="ILM_MONITOR_SUMMARY_CCY_ROW";break;case 1:e="ILM_MONITOR_SUMMARY_ENTITY_ROW";break;case 2:e="ILM_MONITOR_SUMMARY_GROUP_ROW"}return e},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.enableDisableFields(!1)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableDisableFields(!0)},e.prototype.inputDataFault=function(){this.swtAlert.error("Generic exception error")},e.prototype.inputDataResult=function(t){var e=this,i=null;if(this.inputData.isBusy())this.inputData.cbStop();else{this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.entityCombo.setComboData(this.jsonReader.getSelects()),this.currencyCombo.setComboData(this.jsonReader.getSelects()),this.currencyPattern=this.jsonReader.getSingletons().currencyPattern,this.menuAccess=Number(this.jsonReader.getScreenAttributes().menuaccess),this.refreshCB.selected="Y"==this.jsonReader.getScreenAttributes().withrefresh,this.ccyMuliplierCB.selected="Y"==this.jsonReader.getScreenAttributes().currencymultiplier,this.hideNonSumAccount.selected="Y"==this.jsonReader.getScreenAttributes().hideNonSumAccount,this.includecreditLine.selected="Y"==this.jsonReader.getScreenAttributes().includecreditLine,this.sumCutOff.selected="Y"==this.jsonReader.getScreenAttributes().sumCutOff,this.includeSod.selected="Y"==this.jsonReader.getScreenAttributes().includeSod,this.includeOpenMvmt.selected="Y"==this.jsonReader.getScreenAttributes().includeOpenMvmt,i=this.jsonReader.getColumnData();for(var n=JSON.parse(JSON.stringify(i)),l=0;l<n.column.length;l++)if("currencyEntityGroupAccount"==n.column[l].dataelement){n.column[l].type="str";break}var r={columns:i};null!==this.currencyGrid&&void 0!==this.currencyGrid||(this.currencyGrid.componentID=this.jsonReader.getSingletons().screenid),this.currencyGrid.doubleClickEnabled=!0,this.currencyGrid.currencyFormat=this.currencyPattern,this.currencyGrid.CustomGrid(r);for(l=0;l<this.currencyGrid.columnDefinitions.length;l++){var o=this.currencyGrid.columnDefinitions[l];if("alerting"==o.field){var s="./"+a.x.call("eval","alertOrangeImage"),c="./"+a.x.call("eval","alertRedImage");o.properties={enabled:!1,columnName:"alerting",imageEnabled:s,imageCritEnabled:c,imageDisabled:"",_toolTipFlag:!0,style:" display: block; margin-left: auto; margin-right: auto;"},this.currencyGrid.columnDefinitions[l].editor=null,this.currencyGrid.columnDefinitions[l].formatter=u.a}}this.jsonReader.getGridData().size>0?(this.currencyGrid.dataProvider=null,this.currencyGrid.gridData=this.jsonReader.getGridData(),this.currencyGrid.setRowSize=this.jsonReader.getRowSize(),this.currencyGrid.doubleClickEnabled=!0,this.currencyGrid.customContentFunction=this.gridsContentItemRender.bind(this),this.currencyGrid.rowColorFunction=function(t,i,n,l){return e.drawRowBackground(t,i,n,l)}):(this.currencyGrid.dataProvider=null,this.currencyGrid.gridData={size:0,row:{}},this.currencyGrid.refresh(),this.currencyGrid.selectedIndex=-1);var d=[];if(this.tabsName=[],this.tabsOrder=[],this.lastRecievedJSON.ilmSummary.tabs.row){this.tabsConfig=h.extend(!0,[],this.lastRecievedJSON.ilmSummary.tabs.row),0==this.tabsConfig.length&&(this.tabsConfig[0]=this.tabsConfig);for(l=0;l<this.tabsConfig.length;l++)d.push(this.tabsConfig[l].id),this.tabsName[this.tabsConfig[l].id]=this.tabsConfig[l].name,this.tabsOrder[this.tabsConfig[l].id]=this.tabsConfig[l].tabOrder}this.createTabs(d),this.totalsGrid.CustomGrid({columns:n}),this.totalsGrid.gridData=this.jsonReader.getTotalsData(),this.autoRefresh||(this.refreshText.text=""+this.jsonReader.getRefreshRate(),this.refreshRate=parseInt(this.jsonReader.getRefreshRate()),this.autoRefresh=new a.cc(1e3*this.refreshRate*60,0),this.autoRefresh.addEventListener("timer",this.updateData.bind(this)))}null!=this.autoRefresh&&this.refreshCB.selected&&(this.autoRefresh.running||this.autoRefresh.start())},e.prototype.saveChangesForILMStyles=function(t,e,i){this.seriesStyleComboboxValues=e,this.seriesStyleSizeOfAllCombo=i,this.seriesStyleIsGlobal=this.lastTabToOpenSeriesStyleIsGlobal,a.bc.emit("updateSeriesStyle#"+this.lastTabToOpenSeriesStyle)},e.prototype.drawRowBackground=function(t,e,i,n){var l;a.Z.isTrue(t.cutOffExceeded)&&(l="#DDDDDD");try{if("predicted"==n||"external"==n||"confD"==n||"minBalT"==n)(r=t[n+"Color"])&&(l=r);else if("currencyEntityGroupAccount"==n){var r;(r=t.rowColor)&&(l=r)}}catch(o){}return l},e.prototype.gridsContentItemRender=function(t,e,i,n){try{"cutOff"===e&&"0"==t.indent&&(t.__collapsed||(i=""))}catch(l){console.log(l)}return i},e.prototype.tabChanged=function(){try{a.bc.emit(this.tabNavigator.getSelectedTab().id)}catch(t){}},e.prototype.updateILMConf=function(t,e){if(this.iLMConfData.url=this.baseURL+this.actionPath+"method=saveLiquidityMonitorConfig&",this.ilmConfParams=[],null==t);else{if("refreshText"==t||"refreshCB"==t){if(this.autoRefresh.stop(),""==this.refreshText.text||0==parseInt(this.refreshText.text))return void this.swtAlert.show(a.Wb.getPredictMessage("alert.ilmanalysis.nonValidValue",null),"Error");this.refreshRate=parseInt(this.refreshText.text),this.autoRefresh.stop(),this.autoRefresh=new a.cc(1e3*this.refreshRate*60,0),this.autoRefresh.addEventListener("timer",this.updateData.bind(this)),null!=this.autoRefresh&&this.refreshCB.selected&&(this.autoRefresh.running||this.autoRefresh.start()),a.bc.emit("changeRefreshRate#"+this.refreshRate)}"refreshCB"==t?(this.ilmConfParams.paramName="withRefresh",this.ilmConfParams.paramValue=this.refreshCB.selected?"Y":"N"):"refreshText"==t&&this.refValueChanged?(this.ilmConfParams.paramName="refreshRate",this.ilmConfParams.paramValue=this.refreshText.text):"ccyMuliplierCB"==t?(this.ilmConfParams.paramName="useCurrencyMultiplier",this.ilmConfParams.paramValue=this.ccyMuliplierCB.selected?"Y":"N"):"includeSod"==t?(this.ilmConfParams.paramName="includeSodSummary",this.ilmConfParams.paramValue=this.includeSod.selected?"Y":"N"):"includecreditLine"==t?(this.ilmConfParams.paramName="includecreditLineSummary",this.ilmConfParams.paramValue=this.includecreditLine.selected?"Y":"N"):"includeOpenMvmt"==t?(this.ilmConfParams.paramName="includeOpenMvmtSummary",this.ilmConfParams.paramValue=this.includeOpenMvmt.selected?"Y":"N"):"hideNonSumAccount"==t?(this.ilmConfParams.paramName="hideNonSumAccountSummary",this.ilmConfParams.paramValue=this.hideNonSumAccount.selected?"Y":"N"):"sumCutOff"==t?(this.ilmConfParams.paramName="sumCutOffSummary",this.ilmConfParams.paramValue=this.sumCutOff.selected?"Y":"N"):"lastSelectedEntity"==t?(this.ilmConfParams.paramName="lastSelectedEntity",this.ilmConfParams.paramValue=this.entityCombo.selectedItem.content):"lastSelectedCcy"==t&&(this.ilmConfParams.paramName="lastSelectedCcy",this.ilmConfParams.paramValue=this.currencyCombo.selectedItem.content),this.ilmConfParams.isGeneral="true"}this.iLMConfData.send(this.ilmConfParams)},e.prototype.saveResult=function(t){var e=new a.L;e.setInputJSON(t),e.getRequestReplyStatus()||this.swtAlert.error(a.x.call("getBundle","text","label-contactAdmin","Error occurred, Please contact your System Administrator: \n")+e.getRequestReplyMessage(),a.x.call("getBundle","text","alert-error","Error"))},e.prototype.saveFault=function(t){},e.prototype.updateValuesUsingCcyMultiplier=function(t){a.bc.emit("useCurrencyMutliper#"+this.ccyMuliplierCB.selected)},e.prototype.optionHandler=function(){a.x.call("openOptions")},e.prototype.cellLogic=function(t){var e=t.target.field;if("confD"==e){var i=t.target.data,n=i.accountGroupId,l=i.ccyCode,r=i.entityId;(i.slickgrid_rowcontent[e]?i.slickgrid_rowcontent[e].clickable:null)&&a.x.call("openILMThroughPutRatioMonitor",r,l,n)}},e.prototype.throughPutRatioMonitorDrillDownHandler=function(){var t=window.open("/ILMThroughPutRatioMonitor","Stop Rule Add","height=700,width=960,left=100,top=100,resizable=yes,scrollbars=yes,toolbar=yes,menubar=no,location=no,directories=no, status=yes");window.focus&&t.focus()},e.prototype.closeHandler=function(){a.x.call("close")},e.prototype.createTabs=function(t){try{for(var e=[],i=this.arrayOfTabs.length;i--;)-1==t.indexOf(this.arrayOfTabs[i].id)?(this.tabNavigator.selectedIndex==this.tabNavigator.tabChildrenArray.indexOf(this.arrayOfTabs[i])&&(this.tabNavigator.selectedIndex=0),this.tabNavigator.removeChild(this.arrayOfTabs[i]),this.arrayOfTabs.splice(i,1)):(e.push(this.arrayOfTabs[i].id),this.arrayOfTabs[i].label=this.tabsName[this.arrayOfTabs[i].id],this.arrayOfTabs[i].order=this.tabsOrder[this.arrayOfTabs[i].id],a.bc.emit("updateTabs#"+this.arrayOfTabs[i].id));for(i=0;i<t.length;i++)if(-1==e.indexOf(t[i])){var n=this.tabNavigator.addChildPushStategy(a.ac);n.height="100%",n.closable=!0,n.label=this.tabsName[t[i]],n.order=this.tabsOrder[t[i]],n.id=t[i],n.enabled=!1;var l=n.addChild(o.a);l.height="100%",l.entityTabName=n.id.split("/")[0],l.ccyTabName=n.id.split("/")[1],l.parentDocument=this,this.nameOfTabs.push(t[i]),this.arrayOfTabs.push(n)}}catch(r){}},e.prototype.enableDisableTab=function(t,e){for(var i=this.arrayOfTabs.length;i--;)t==this.arrayOfTabs[i].id&&(this.arrayOfTabs[i].enabled=!0)},e.prototype.refreshTabList=function(){var t=this;this.requestParams=[],this.requestParams.entityId=this.entityCombo.selectedItem.content,this.requestParams.currencyId=this.currencyCombo.selectedItem.content,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.refreshTabListResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="ilmAnalysisMonitor.do?",this.actionMethod="method=ilmGetSummaryTabList",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.resetParamsWhenBusy(this.inputData,this.requestParams)},e.prototype.updateILMOptionConfig=function(t){var e=this;this.requestParams=[],this.requestParams.tabId=t,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.refreshTabListResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="ilmAnalysisMonitor.do?",this.actionMethod="method=updateILMOptions",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.resetParamsWhenBusy(this.inputData,this.requestParams)},e.prototype.refreshTabListResult=function(t){if(this.refreshTabsData.isBusy())this.refreshTabsData.cbStop();else{var e=[];if(this.tabsName=[],this.tabsOrder=[],t.ilmSummary.tabs&&t.ilmSummary.tabs.row){this.tabsConfig=h.extend(!0,[],t.ilmSummary.tabs.row),0==this.tabsConfig.length&&(this.tabsConfig[0]=this.tabsConfig);for(var i=0;i<this.tabsConfig.length;i++)e.push(this.tabsConfig[i].id),this.tabsName[this.tabsConfig[i].id]=this.tabsConfig[i].name,this.tabsOrder[this.tabsConfig[i].id]=this.tabsConfig[i].tabOrder}this.createTabs(e)}},e.prototype.resetParamsWhenBusy=function(t,e){var i=this;t.isBusy()?setTimeout(function(){i.resetParamsWhenBusy(t,e)},0):t.send(e)},e.prototype.updateData=function(t){this.requestParams=[],"entityCombo"==t?this.requestParams.entityChanged="None"!=this.entityCombo.selectedItem.content?"true":"false":"ccyCombo"==t&&(this.requestParams.currencyChanged="None"!=this.entityCombo.selectedItem.content?"true":"false"),this.requestParams.entityId=this.entityCombo.selectedItem.content,this.requestParams.currencyId=this.currencyCombo.selectedItem.content,this.requestParams.includeSod=this.includeSod.selected?"Y":"N",this.requestParams.includecreditLine=this.includecreditLine.selected?"Y":"N",this.requestParams.sumCutOff=this.sumCutOff.selected?"Y":"N",this.requestParams.includeOpenMvmt=this.includeOpenMvmt.selected?"Y":"N",this.requestParams.hideNonSumAccount=this.hideNonSumAccount.selected?"Y":"N",this.requestParams.applyCcyMultiplier=this.ccyMuliplierCB.selected?"Y":"N",this.actionMethod="method=ilmSummaryGridDisplay",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.resetParamsWhenBusy(this.inputData,this.requestParams)},e.prototype.report=function(t){var e=[];e.push("Include SOD ="+(this.includeSod.selected?"Y":"N")),e.push("Include Credit Line ="+(this.includecreditLine.selected?"Y":"N")),e.push("Sum by Cut-Off ="+(this.sumCutOff.selected?"Y":"N")),e.push("Include Open Movements ="+(this.includeOpenMvmt.selected?"Y":"N")),e.push("Hide Non-sum Accounts ="+(this.hideNonSumAccount.selected?"Y":"N")),this.exportContainer.convertData(this.lastRecievedJSON.ilmSummary.grid.metadata.columns,this.currencyGrid,this.totalsGrid.gridData,e,t,!0,!0)},e.prototype.enableDisableFields=function(t){this.exportContainer.enabled=t,this.includeSod.enabled=t,this.includecreditLine.enabled=t,this.sumCutOff.enabled=t,this.includeOpenMvmt.enabled=t,this.hideNonSumAccount.enabled=t,this.refreshButton.enabled=t,this.refreshCB.enabled=t,this.refreshText.enabled=t,this.ccyMuliplierCB.enabled=t},e.prototype.arrayRemove=function(t,e){return t.filter(function(t){return t!=e})},e}(a.yb),d=[{path:"",component:c}],b=(r.l.forChild(d),function(){return function(){}}()),m=i("pMnS"),p=i("RChO"),g=i("t6HQ"),f=i("WFGK"),y=i("5FqG"),C=i("Ip0R"),S=i("gIcY"),w=i("t/Na"),R=i("sE5F"),I=i("OzfB"),T=i("T7CS"),v=i("S7LP"),O=i("6aHO"),L=i("WzUx"),D=i("A7o+"),M=i("zCE2"),P=i("Jg5P"),x=i("3R0m"),G=i("hhbb"),B=i("5rxC"),N=i("Fzqc"),A=i("21Lb"),_=i("hUWP"),k=i("3pJQ"),J=i("V9q+"),F=i("VDKW"),W=i("kXfT"),z=i("BGbe");i.d(e,"ILMMainScreenModuleNgFactory",function(){return E}),i.d(e,"RenderType_ILMMainScreen",function(){return Y}),i.d(e,"View_ILMMainScreen_0",function(){return q}),i.d(e,"View_ILMMainScreen_Host_0",function(){return U}),i.d(e,"ILMMainScreenNgFactory",function(){return j});var E=n.Gb(b,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[m.a,p.a,g.a,f.a,y.Cb,y.Pb,y.r,y.rc,y.s,y.Ab,y.Bb,y.Db,y.qd,y.Hb,y.k,y.Ib,y.Nb,y.Ub,y.yb,y.Jb,y.v,y.A,y.e,y.c,y.g,y.d,y.Kb,y.f,y.ec,y.Wb,y.bc,y.ac,y.sc,y.fc,y.lc,y.jc,y.Eb,y.Fb,y.mc,y.Lb,y.nc,y.Mb,y.dc,y.Rb,y.b,y.ic,y.Yb,y.Sb,y.kc,y.y,y.Qb,y.cc,y.hc,y.pc,y.oc,y.xb,y.p,y.q,y.o,y.h,y.j,y.w,y.Zb,y.i,y.m,y.Vb,y.Ob,y.Gb,y.Xb,y.t,y.tc,y.zb,y.n,y.qc,y.a,y.z,y.rd,y.sd,y.x,y.td,y.gc,y.l,y.u,y.ud,y.Tb,j]],[3,n.n],n.J]),n.Rb(4608,C.m,C.l,[n.F,[2,C.u]]),n.Rb(4608,S.c,S.c,[]),n.Rb(4608,S.p,S.p,[]),n.Rb(4608,w.j,w.p,[C.c,n.O,w.n]),n.Rb(4608,w.q,w.q,[w.j,w.o]),n.Rb(5120,w.a,function(t){return[t,new a.tb]},[w.q]),n.Rb(4608,w.m,w.m,[]),n.Rb(6144,w.k,null,[w.m]),n.Rb(4608,w.i,w.i,[w.k]),n.Rb(6144,w.b,null,[w.i]),n.Rb(4608,w.f,w.l,[w.b,n.B]),n.Rb(4608,w.c,w.c,[w.f]),n.Rb(4608,R.c,R.c,[]),n.Rb(4608,R.g,R.b,[]),n.Rb(5120,R.i,R.j,[]),n.Rb(4608,R.h,R.h,[R.c,R.g,R.i]),n.Rb(4608,R.f,R.a,[]),n.Rb(5120,R.d,R.k,[R.h,R.f]),n.Rb(5120,n.b,function(t,e){return[I.j(t,e)]},[C.c,n.O]),n.Rb(4608,T.a,T.a,[]),n.Rb(4608,v.a,v.a,[]),n.Rb(4608,O.a,O.a,[n.n,n.L,n.B,v.a,n.g]),n.Rb(4608,L.c,L.c,[n.n,n.g,n.B]),n.Rb(4608,L.e,L.e,[L.c]),n.Rb(4608,D.l,D.l,[]),n.Rb(4608,D.h,D.g,[]),n.Rb(4608,D.c,D.f,[]),n.Rb(4608,D.j,D.d,[]),n.Rb(4608,D.b,D.a,[]),n.Rb(4608,D.k,D.k,[D.l,D.h,D.c,D.j,D.b,D.m,D.n]),n.Rb(4608,L.i,L.i,[[2,D.k]]),n.Rb(4608,L.r,L.r,[L.L,[2,D.k],L.i]),n.Rb(4608,L.t,L.t,[]),n.Rb(4608,L.w,L.w,[]),n.Rb(1073742336,r.l,r.l,[[2,r.r],[2,r.k]]),n.Rb(1073742336,C.b,C.b,[]),n.Rb(1073742336,S.n,S.n,[]),n.Rb(1073742336,S.l,S.l,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,S.e,S.e,[]),n.Rb(1073742336,x.a,x.a,[]),n.Rb(1073742336,D.i,D.i,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,w.d,w.d,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,G.b,G.b,[]),n.Rb(1073742336,B.b,B.b,[]),n.Rb(1073742336,I.c,I.c,[]),n.Rb(1073742336,N.a,N.a,[]),n.Rb(1073742336,A.d,A.d,[]),n.Rb(1073742336,_.c,_.c,[]),n.Rb(1073742336,k.a,k.a,[]),n.Rb(1073742336,J.a,J.a,[[2,I.g],n.O]),n.Rb(1073742336,F.b,F.b,[]),n.Rb(1073742336,W.a,W.a,[]),n.Rb(1073742336,z.b,z.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,b,b,[]),n.Rb(256,w.n,"XSRF-TOKEN",[]),n.Rb(256,w.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,D.m,void 0,[]),n.Rb(256,D.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,r.i,function(){return[[{path:"",component:c}]]},[])])}),V=[[""]],Y=n.Hb({encapsulation:0,styles:V,data:{}});function q(t){return n.dc(0,[n.Zb(*********,1,{_container:0}),n.Zb(*********,2,{entityLabel:0}),n.Zb(*********,3,{ccyLabel:0}),n.Zb(*********,4,{ccyMultiplierLabel:0}),n.Zb(*********,5,{entityCombo:0}),n.Zb(*********,6,{currencyCombo:0}),n.Zb(*********,7,{refreshCB:0}),n.Zb(*********,8,{ccyMuliplierCB:0}),n.Zb(*********,9,{includeSod:0}),n.Zb(*********,10,{includecreditLine:0}),n.Zb(*********,11,{sumCutOff:0}),n.Zb(*********,12,{includeOpenMvmt:0}),n.Zb(*********,13,{hideNonSumAccount:0}),n.Zb(*********,14,{refreshText:0}),n.Zb(*********,15,{refreshButton:0}),n.Zb(*********,16,{optionButton:0}),n.Zb(*********,17,{closeButton:0}),n.Zb(*********,18,{loadingImage:0}),n.Zb(*********,19,{exportContainer:0}),n.Zb(*********,20,{tabNavigator:0}),n.Zb(*********,21,{summaryTab:0}),n.Zb(*********,22,{summaryCanvas:0}),n.Zb(*********,23,{totalsContainer:0}),(t()(),n.Jb(23,0,null,null,103,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,l=t.component;"creationComplete"===e&&(n=!1!==l.onLoad()&&n);return n},y.ad,y.hb)),n.Ib(24,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(25,0,null,0,101,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,y.od,y.vb)),n.Ib(26,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(27,0,null,0,51,"SwtCanvas",[["height","35"],["minWidth","1000"],["width","100%"]],null,null,null,y.Nc,y.U)),n.Ib(28,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),n.Jb(29,0,null,0,49,"Grid",[["height","100%"],["width","100%"]],null,null,null,y.Cc,y.H)),n.Ib(30,4440064,null,0,a.z,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(31,0,null,0,47,"GridRow",[["height","100%"],["width","100%"]],null,null,null,y.Bc,y.J)),n.Ib(32,4440064,null,0,a.B,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(33,0,null,0,13,"GridItem",[["paddingLeft","5"],["width","40%"]],null,null,null,y.Ac,y.I)),n.Ib(34,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(35,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,y.Ac,y.I)),n.Ib(36,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(37,0,null,0,1,"SwtLabel",[["width","100"]],null,null,null,y.Yc,y.fb)),n.Ib(38,4440064,[[2,4],["entityLabel",4]],0,a.vb,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(39,0,null,0,1,"SwtComboBox",[["dataLabel","entityList"],["width","120"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var l=!0,a=t.component;"window:mousewheel"===e&&(l=!1!==n.Tb(t,40).mouseWeelEventHandler(i.target)&&l);"change"===e&&(a.updateData("entityCombo"),l=!1!==a.updateILMConf("lastSelectedEntity",null)&&l);return l},y.Pc,y.W)),n.Ib(40,4440064,[[5,4],["entityCombo",4]],0,a.gb,[n.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"]},{change_:"change"}),(t()(),n.Jb(41,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,y.Ac,y.I)),n.Ib(42,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(43,0,null,0,1,"SwtLabel",[["width","100"]],null,null,null,y.Yc,y.fb)),n.Ib(44,4440064,[[3,4],["ccyLabel",4]],0,a.vb,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(45,0,null,0,1,"SwtComboBox",[["dataLabel","currencyList"],["width","120"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var l=!0,a=t.component;"window:mousewheel"===e&&(l=!1!==n.Tb(t,46).mouseWeelEventHandler(i.target)&&l);"change"===e&&(a.updateData("ccyCombo"),l=!1!==a.updateILMConf("lastSelectedCcy",null)&&l);return l},y.Pc,y.W)),n.Ib(46,4440064,[[6,4],["currencyCombo",4]],0,a.gb,[n.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"]},{change_:"change"}),(t()(),n.Jb(47,0,null,0,31,"GridItem",[["width","60%"]],null,null,null,y.Ac,y.I)),n.Ib(48,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(49,0,null,0,29,"VBox",[["width","100%"]],null,null,null,y.od,y.vb)),n.Ib(50,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"]},null),(t()(),n.Jb(51,0,null,0,25,"HBox",[["horizontalAlign","right"],["horizontalGap","20"]],null,null,null,y.Dc,y.K)),n.Ib(52,4440064,null,0,a.C,[n.r,a.i],{horizontalGap:[0,"horizontalGap"],horizontalAlign:[1,"horizontalAlign"]},null),(t()(),n.Jb(53,0,null,0,5,"GridItem",[],null,null,null,y.Ac,y.I)),n.Ib(54,4440064,null,0,a.A,[n.r,a.i],null,null),(t()(),n.Jb(55,0,null,0,1,"SwtCheckBox",[["class","withoutMargin"],["id","ccyMuliplierCB"],["paddingTop","1"],["selected","true"]],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(l.updateILMConf("ccyMuliplierCB",null),l.updateValuesUsingCcyMultiplier(i),n=!1!==l.updateData("refreshButton")&&n);return n},y.Oc,y.V)),n.Ib(56,4440064,[[8,4],["ccyMuliplierCB",4]],0,a.eb,[n.r,a.i],{id:[0,"id"],paddingTop:[1,"paddingTop"],selected:[2,"selected"]},{change_:"change"}),(t()(),n.Jb(57,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["paddingTop","1"]],null,null,null,y.Yc,y.fb)),n.Ib(58,4440064,[[4,4],["ccyMultiplierLabel",4]],0,a.vb,[n.r,a.i],{paddingTop:[0,"paddingTop"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(59,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),n.Ib(60,4440064,null,0,a.A,[n.r,a.i],null,null),(t()(),n.Jb(61,0,null,0,1,"SwtCheckBox",[["class","withoutMargin"],["id","refreshCB"],["selected","true"]],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(n=!1!==l.updateILMConf("refreshCB",null)&&n);return n},y.Oc,y.V)),n.Ib(62,4440064,[[7,4],["refreshCB",4]],0,a.eb,[n.r,a.i],{id:[0,"id"],selected:[1,"selected"]},{change_:"change"}),(t()(),n.Jb(63,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),n.Ib(64,4440064,null,0,a.A,[n.r,a.i],null,null),(t()(),n.Jb(65,0,null,0,1,"SwtTextInput",[["editable","true"],["height","20"],["id","refreshText"],["maxChars","3"],["restrict","0-9"],["width","35"]],null,[[null,"change"],[null,"focusOut"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(n=!1!=(l.refValueChanged=!0)&&n);"focusOut"===e&&(n=!1!==l.updateILMConf("refreshText",null)&&n);return n},y.kd,y.sb)),n.Ib(66,4440064,[[14,4],["refreshText",4]],0,a.Rb,[n.r,a.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],width:[3,"width"],height:[4,"height"],enabled:[5,"enabled"],editable:[6,"editable"]},{onFocusOut_:"focusOut",change_:"change"}),(t()(),n.Jb(67,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),n.Ib(68,4440064,null,0,a.A,[n.r,a.i],null,null),(t()(),n.Jb(69,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["text","mins"],["width","30"]],null,null,null,y.Yc,y.fb)),n.Ib(70,4440064,null,0,a.vb,[n.r,a.i],{width:[0,"width"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(71,0,null,0,5,"GridItem",[],null,null,null,y.Ac,y.I)),n.Ib(72,4440064,null,0,a.A,[n.r,a.i],null,null),(t()(),n.Jb(73,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","refreshButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.updateData("refreshButton")&&n);return n},y.Mc,y.T)),n.Ib(74,4440064,[[15,4],["refreshButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(75,0,null,0,1,"SwtLoadingImage",[],null,null,null,y.Zc,y.gb)),n.Ib(76,114688,[[18,4],["loadingImage",4]],0,a.xb,[n.r],null,null),(t()(),n.Jb(77,0,null,0,1,"HBox",[["horizontalAlign","right"]],null,null,null,y.Dc,y.K)),n.Ib(78,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"]},null),(t()(),n.Jb(79,0,null,0,47,"SwtTabNavigator",[["applyOrder","true"],["borderBottom","false"],["height","100%"],["id","tabs"],["minWidth","1000"],["showDropDown","true"],["width","100%"]],null,[[null,"onChange"]],function(t,e,i){var n=!0,l=t.component;"onChange"===e&&(n=!1!==l.tabChanged()&&n);return n},y.id,y.pb)),n.Ib(80,4440064,[[20,4],["tabs",4]],1,a.Ob,[n.r,a.i,n.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"],borderBottom:[4,"borderBottom"],showDropDown:[5,"showDropDown"],applyOrder:[6,"applyOrder"]},{onChange_:"onChange"}),n.Zb(603979776,24,{tabChildren:1}),(t()(),n.Jb(82,0,null,0,44,"SwtTabPushStrategy",[["height","100%"],["id","summaryTab"],["label","Summary"],["order","1"],["width","100%"]],null,null,null,y.md,y.ub)),n.Ib(83,4440064,[[21,4],["summaryTab",4]],0,a.ac,[n.r,a.i,n.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],label:[3,"label"],order:[4,"order"]},null),n.Yb(2048,[[24,4]],a.Xb,null,[a.ac]),(t()(),n.Jb(85,0,null,0,41,"VBox",[["height","100%"],["minWidth","1000"],["width","100%"]],null,null,null,y.od,y.vb)),n.Ib(86,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),n.Jb(87,0,null,0,21,"HBox",[["height","40"],["width","100%"]],null,null,null,y.Dc,y.K)),n.Ib(88,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(89,0,null,0,3,"HBox",[["height","40"],["horizontalAlign","left"],["paddingLeft","20"],["width","50%"]],null,null,null,y.Dc,y.K)),n.Ib(90,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),n.Jb(91,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["textDictionaryId","ilmSummary.label.currentDateSummary"],["width","200"]],null,null,null,y.Yc,y.fb)),n.Ib(92,4440064,null,0,a.vb,[n.r,a.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(93,0,null,0,15,"HBox",[["height","40"],["horizontalAlign","right"],["horizontalGap","20"],["paddingRight","20"],["width","50%"]],null,null,null,y.Dc,y.K)),n.Ib(94,4440064,null,0,a.C,[n.r,a.i],{horizontalGap:[0,"horizontalGap"],horizontalAlign:[1,"horizontalAlign"],width:[2,"width"],height:[3,"height"],paddingRight:[4,"paddingRight"]},null),(t()(),n.Jb(95,0,null,0,1,"SwtCheckBox",[["id","includeSod"],["label","Include SOD"]],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(l.updateData(i),n=!1!==l.updateILMConf("includeSod",null)&&n);return n},y.Oc,y.V)),n.Ib(96,4440064,[[9,4],["includeSod",4]],0,a.eb,[n.r,a.i],{id:[0,"id"],label:[1,"label"]},{change_:"change"}),(t()(),n.Jb(97,0,null,0,5,"VBox",[["height","100%"]],null,null,null,y.od,y.vb)),n.Ib(98,4440064,null,0,a.ec,[n.r,a.i,n.T],{height:[0,"height"]},null),(t()(),n.Jb(99,0,null,0,1,"SwtCheckBox",[["height","50%"],["id","includecreditLine"],["label","Include Credit Line"]],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(l.updateData(i),n=!1!==l.updateILMConf("includecreditLine",null)&&n);return n},y.Oc,y.V)),n.Ib(100,4440064,[[10,4],["includecreditLine",4]],0,a.eb,[n.r,a.i],{id:[0,"id"],height:[1,"height"],label:[2,"label"]},{change_:"change"}),(t()(),n.Jb(101,0,null,0,1,"SwtCheckBox",[["height","50%"],["id","includeOpenMvmt"],["label","Include Open Movements"]],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(l.updateData(i),n=!1!==l.updateILMConf("includeOpenMvmt",null)&&n);return n},y.Oc,y.V)),n.Ib(102,4440064,[[12,4],["includeOpenMvmt",4]],0,a.eb,[n.r,a.i],{id:[0,"id"],height:[1,"height"],label:[2,"label"]},{change_:"change"}),(t()(),n.Jb(103,0,null,0,5,"VBox",[["height","100%"]],null,null,null,y.od,y.vb)),n.Ib(104,4440064,null,0,a.ec,[n.r,a.i,n.T],{height:[0,"height"]},null),(t()(),n.Jb(105,0,null,0,1,"SwtCheckBox",[["height","50%"],["id","sumCutOff"],["label","Sum by Cut-off"]],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(l.updateData(i),n=!1!==l.updateILMConf("sumCutOff",null)&&n);return n},y.Oc,y.V)),n.Ib(106,4440064,[[11,4],["sumCutOff",4]],0,a.eb,[n.r,a.i],{id:[0,"id"],height:[1,"height"],label:[2,"label"]},{change_:"change"}),(t()(),n.Jb(107,0,null,0,1,"SwtCheckBox",[["height","50%"],["id","hideNonSumAccount"],["label","Hide Non-sum Accounts"]],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(l.updateData(i),n=!1!==l.updateILMConf("hideNonSumAccount",null)&&n);return n},y.Oc,y.V)),n.Ib(108,4440064,[[13,4],["hideNonSumAccount",4]],0,a.eb,[n.r,a.i],{id:[0,"id"],height:[1,"height"],label:[2,"label"]},{change_:"change"}),(t()(),n.Jb(109,0,null,0,5,"VBox",[["height","100%"],["minWidth","1000"],["verticalGap","0"],["width","100%"]],null,null,null,y.od,y.vb)),n.Ib(110,4440064,null,0,a.ec,[n.r,a.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"]},null),(t()(),n.Jb(111,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["width","100%"]],null,null,null,y.Nc,y.U)),n.Ib(112,4440064,[[22,4],["summaryCanvas",4]],0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"],border:[2,"border"]},null),(t()(),n.Jb(113,0,null,0,1,"SwtCanvas",[["border","false"],["height","40"],["id","totalsContainer"],["width","100%"]],null,null,null,y.Nc,y.U)),n.Ib(114,4440064,[[23,4],["totalsContainer",4]],0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(t()(),n.Jb(115,0,null,0,11,"SwtCanvas",[["height","40"],["width","100%"]],null,null,null,y.Nc,y.U)),n.Ib(116,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(117,0,null,0,9,"HBox",[["height","100%"],["width","100%"]],null,null,null,y.Dc,y.K)),n.Ib(118,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(119,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.optionHandler()&&n);return n},y.Mc,y.T)),n.Ib(120,4440064,[[16,4],["optionButton",4]],0,a.cb,[n.r,a.i],null,{onClick_:"click"}),(t()(),n.Jb(121,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,y.Dc,y.K)),n.Ib(122,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),n.Jb(123,0,null,0,1,"DataExport",[["id","exportContainerSummary"]],null,null,null,y.Sc,y.Z)),n.Ib(124,4440064,[[19,4],["exportContainer",4]],0,a.kb,[a.i,n.r],{id:[0,"id"]},null),(t()(),n.Jb(125,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.closeHandler()&&n);return n},y.Mc,y.T)),n.Ib(126,4440064,[[17,4],["closeButton",4]],0,a.cb,[n.r,a.i],null,{onClick_:"click"})],function(t,e){t(e,24,0,"100%","100%");t(e,26,0,"100%","100%","5","5","5","5");t(e,28,0,"100%","35","1000");t(e,30,0,"100%","100%");t(e,32,0,"100%","100%");t(e,34,0,"40%","5");t(e,36,0,"50%");t(e,38,0,"100");t(e,40,0,"entityList","120");t(e,42,0,"50%");t(e,44,0,"100");t(e,46,0,"currencyList","120");t(e,48,0,"60%");t(e,50,0,"100%");t(e,52,0,"20","right"),t(e,54,0);t(e,56,0,"ccyMuliplierCB","1","true");t(e,58,0,"1","normal"),t(e,60,0);t(e,62,0,"refreshCB","true"),t(e,64,0);t(e,66,0,"3","0-9","refreshText","35","20",n.Tb(e,62).selected,"true"),t(e,68,0);t(e,70,0,"30","mins","normal"),t(e,72,0);t(e,74,0,"refreshButton","true"),t(e,76,0);t(e,78,0,"right");t(e,80,0,"tabs","100%","100%","1000","false","true","true");t(e,83,0,"summaryTab","100%","100%","Summary","1");t(e,86,0,"100%","100%","1000");t(e,88,0,"100%","40");t(e,90,0,"left","50%","40","20");t(e,92,0,"ilmSummary.label.currentDateSummary","200","bold");t(e,94,0,"20","right","50%","40","20");t(e,96,0,"includeSod","Include SOD");t(e,98,0,"100%");t(e,100,0,"includecreditLine","50%","Include Credit Line");t(e,102,0,"includeOpenMvmt","50%","Include Open Movements");t(e,104,0,"100%");t(e,106,0,"sumCutOff","50%","Sum by Cut-off");t(e,108,0,"hideNonSumAccount","50%","Hide Non-sum Accounts");t(e,110,0,"0","100%","100%","1000");t(e,112,0,"100%","100%","false");t(e,114,0,"totalsContainer","100%","40","false");t(e,116,0,"100%","40");t(e,118,0,"100%","100%"),t(e,120,0);t(e,122,0,"right","100%");t(e,124,0,"exportContainerSummary"),t(e,126,0)},null)}function U(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-ilm",[],null,null,null,q,Y)),n.Ib(1,4440064,null,0,c,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var j=n.Fb("app-ilm",c,U,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);