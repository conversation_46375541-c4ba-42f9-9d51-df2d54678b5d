(window.webpackJsonp=window.webpackJsonp||[]).push([[60],{knsY:function(t,e,l){"use strict";l.r(e);var n=l("CcnG"),i=l("mrSG"),o=l("447K"),a=l("ZYCi"),u=function(t){function e(e,l){var n=t.call(this,l,e)||this;return n.commonService=e,n.element=l,n.inputData=new o.G(n.commonService),n.baseURL=o.Wb.getBaseURL(),n.errorLocation=0,n.jsonReader=new o.L,n.actionMethod="",n.actionPath="",n.requestParams=[],n.swtAlert=new o.bb(e),n}return i.d(e,t),e.prototype.ngOnDestroy=function(){},e.prototype.ngOnInit=function(){this.killButton.label=o.Wb.getPredictMessage("button.kill",null),this.cancelButton.label=o.Wb.getPredictMessage("button.close",null)},e.prototype.onLoad=function(){var t=[];try{window.opener&&window.opener.instanceElement&&(t=window.opener.instanceElement.getParamsFromParent())&&(this.menuAccessId=t[0].menuAccessId,this.moduleTextInput.text=this.moduleId=t[0].moduleId,this.connectionIdTextInput.text=this.connectionIdAsString=t[0].connectionId,this.statusTextInput.text=this.status=t[0].status,this.durationTextInput.text=this.duration=t[0].duration,this.lastActionTimeTextInput.text=this.lastActionTime=t[0].lastActionTime,this.sqlStatusTextInput.text=this.sqlStatus=t[0].sqlStatus,this.sidTextInput.text=this.sid=t[0].ssid,this.audsidTextInput.text=this.audsid=t[0].audSid,this.sqlIdTextInput.text=this.sqlId=t[0].sqlId,this.sqlExecStartInput.text=this.sqlExecStart=t[0].sqlExecStart,this.stackTraceTextArea.text=this.stackTrace=t[0].stackTrace,this.sqlStatementTextArea.text=this.sqlStatement=t[0].sqlStatement)}catch(e){console.log("e",e)}},e.prototype.doKillConnectionEventHandler=function(t){var e=o.Wb.getPredictMessage("connectionPool.alertKillingConsequences",null);this.swtAlert.confirm(e,null,o.c.YES|o.c.NO,null,this.checkConnectionStatusChanged.bind(this))},e.prototype.checkConnectionStatusChanged=function(t){var e=this;t.detail===o.c.YES&&(this.killButton.enabled=!1,this.requestParams=[],this.actionMethod="method=checkConnectionChanged",this.actionPath="connectionPool.do?",this.requestParams.connectionId=this.connectionIdAsString,this.requestParams.moduleId=this.moduleId,this.requestParams.connectionId=this.connectionIdAsString,this.requestParams.sqlId=this.sqlId,this.requestParams.duration=this.duration,this.requestParams.sqlStatement=this.sqlStatement,this.requestParams.sqlStatus=this.sqlStatus,this.requestParams.moduleId=this.moduleId,this.requestParams.audSid=this.audsid,this.requestParams.stackTrace=this.stackTrace,this.requestParams.ssid=this.sid,this.requestParams.lastActionTime=this.lastActionTime,this.requestParams.status=this.status,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.cbResult=function(t){e.checkConnectionChangedDataResult(t)},this.inputData.send(this.requestParams))},e.prototype.checkConnectionChangedDataResult=function(t){try{if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus())this.killConnection();else if("CONNECTION_NOT_EXIST"==this.jsonReader.getRequestReplyMessage()){var e=o.Wb.getPredictMessage("connectionPool.alertConnectionKilled",null);this.swtAlert.confirm(e,null,o.c.YES,null,this.popupClosed.bind(this))}else if("CONNECTION_DETAILS_CHANGED"==this.jsonReader.getRequestReplyMessage()){e=o.Wb.getPredictMessage("connectionPool.alertDetailsChanged",null);this.swtAlert.confirm(e,null,o.c.YES|o.c.NO,null,this.connectionChangeAlertHandler.bind(this))}}catch(l){this.swtAlert.show(o.x.call("eval","label['alert']['server_error']"))}},e.prototype.connectionChangeAlertHandler=function(t){t.detail===o.c.YES&&this.killConnection()},e.prototype.killConnection=function(){var t=this;try{this.requestParams=[],this.requestParams.connectionIds=this.connectionIdAsString,this.requestParams.moduleId=this.moduleId,this.actionMethod="method=killConnectionPool",this.actionPath="connectionPool.do?",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.send(this.requestParams)}catch(e){o.Wb.logError(e,this.moduleId,"killConnection","doHelp",10)}},e.prototype.inputDataResult=function(t){try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyMessage()&&this.popupClosed())}catch(e){console.log("error:   ",e),o.Wb.logError(e,this.moduleId,"className","inputDataResult",this.errorLocation)}this.killButton.enabled=!0},e.prototype.startOfComms=function(){},e.prototype.endOfComms=function(){},e.prototype.keyDownEventHandler=function(t){try{var e=Object(o.ic.getFocus()).name;t.keyCode===o.N.ENTER&&("killButton"===e?this.killConnection():"cancelButton"===e&&this.popupClosed())}catch(l){o.Wb.logError(l,this.moduleId,"CategoryMaintenance","keyDownEventHandler",this.errorLocation)}},e.prototype.popupClosed=function(){window.close()},e.prototype.inputDataFault=function(t){this.swtAlert.error(t.fault.faultstring+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail)},e}(o.yb),h=[{path:"",component:u}],d=(a.l.forChild(h),function(){return function(){}}()),s=l("pMnS"),c=l("RChO"),b=l("t6HQ"),r=l("WFGK"),g=l("5FqG"),w=l("Ip0R"),p=l("gIcY"),I=l("t/Na"),m=l("sE5F"),f=l("OzfB"),x=l("T7CS"),R=l("S7LP"),y=l("6aHO"),S=l("WzUx"),D=l("A7o+"),C=l("zCE2"),k=l("Jg5P"),T=l("3R0m"),q=l("hhbb"),P=l("5rxC"),v=l("Fzqc"),J=l("21Lb"),B=l("hUWP"),L=l("3pJQ"),A=l("V9q+"),E=l("VDKW"),_=l("kXfT"),H=l("BGbe");l.d(e,"ConnectionPoolDetailsModuleNgFactory",function(){return O}),l.d(e,"RenderType_ConnectionPoolDetails",function(){return K}),l.d(e,"View_ConnectionPoolDetails_0",function(){return M}),l.d(e,"View_ConnectionPoolDetails_Host_0",function(){return Y}),l.d(e,"ConnectionPoolDetailsNgFactory",function(){return j});var O=n.Gb(d,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[s.a,c.a,b.a,r.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,j]],[3,n.n],n.J]),n.Rb(4608,w.m,w.l,[n.F,[2,w.u]]),n.Rb(4608,p.c,p.c,[]),n.Rb(4608,p.p,p.p,[]),n.Rb(4608,I.j,I.p,[w.c,n.O,I.n]),n.Rb(4608,I.q,I.q,[I.j,I.o]),n.Rb(5120,I.a,function(t){return[t,new o.tb]},[I.q]),n.Rb(4608,I.m,I.m,[]),n.Rb(6144,I.k,null,[I.m]),n.Rb(4608,I.i,I.i,[I.k]),n.Rb(6144,I.b,null,[I.i]),n.Rb(4608,I.f,I.l,[I.b,n.B]),n.Rb(4608,I.c,I.c,[I.f]),n.Rb(4608,m.c,m.c,[]),n.Rb(4608,m.g,m.b,[]),n.Rb(5120,m.i,m.j,[]),n.Rb(4608,m.h,m.h,[m.c,m.g,m.i]),n.Rb(4608,m.f,m.a,[]),n.Rb(5120,m.d,m.k,[m.h,m.f]),n.Rb(5120,n.b,function(t,e){return[f.j(t,e)]},[w.c,n.O]),n.Rb(4608,x.a,x.a,[]),n.Rb(4608,R.a,R.a,[]),n.Rb(4608,y.a,y.a,[n.n,n.L,n.B,R.a,n.g]),n.Rb(4608,S.c,S.c,[n.n,n.g,n.B]),n.Rb(4608,S.e,S.e,[S.c]),n.Rb(4608,D.l,D.l,[]),n.Rb(4608,D.h,D.g,[]),n.Rb(4608,D.c,D.f,[]),n.Rb(4608,D.j,D.d,[]),n.Rb(4608,D.b,D.a,[]),n.Rb(4608,D.k,D.k,[D.l,D.h,D.c,D.j,D.b,D.m,D.n]),n.Rb(4608,S.i,S.i,[[2,D.k]]),n.Rb(4608,S.r,S.r,[S.L,[2,D.k],S.i]),n.Rb(4608,S.t,S.t,[]),n.Rb(4608,S.w,S.w,[]),n.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),n.Rb(1073742336,w.b,w.b,[]),n.Rb(1073742336,p.n,p.n,[]),n.Rb(1073742336,p.l,p.l,[]),n.Rb(1073742336,C.a,C.a,[]),n.Rb(1073742336,k.a,k.a,[]),n.Rb(1073742336,p.e,p.e,[]),n.Rb(1073742336,T.a,T.a,[]),n.Rb(1073742336,D.i,D.i,[]),n.Rb(1073742336,S.b,S.b,[]),n.Rb(1073742336,I.e,I.e,[]),n.Rb(1073742336,I.d,I.d,[]),n.Rb(1073742336,m.e,m.e,[]),n.Rb(1073742336,q.b,q.b,[]),n.Rb(1073742336,P.b,P.b,[]),n.Rb(1073742336,f.c,f.c,[]),n.Rb(1073742336,v.a,v.a,[]),n.Rb(1073742336,J.d,J.d,[]),n.Rb(1073742336,B.c,B.c,[]),n.Rb(1073742336,L.a,L.a,[]),n.Rb(1073742336,A.a,A.a,[[2,f.g],n.O]),n.Rb(1073742336,E.b,E.b,[]),n.Rb(1073742336,_.a,_.a,[]),n.Rb(1073742336,H.b,H.b,[]),n.Rb(1073742336,o.Tb,o.Tb,[]),n.Rb(1073742336,d,d,[]),n.Rb(256,I.n,"XSRF-TOKEN",[]),n.Rb(256,I.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,D.m,void 0,[]),n.Rb(256,D.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,a.i,function(){return[[{path:"",component:u}]]},[])])}),N=[[""]],K=n.Hb({encapsulation:0,styles:N,data:{}});function M(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{moduleTextInput:0}),n.Zb(402653184,3,{connectionIdTextInput:0}),n.Zb(402653184,4,{statusTextInput:0}),n.Zb(402653184,5,{durationTextInput:0}),n.Zb(402653184,6,{lastActionTimeTextInput:0}),n.Zb(402653184,7,{sqlStatusTextInput:0}),n.Zb(402653184,8,{sidTextInput:0}),n.Zb(402653184,9,{audsidTextInput:0}),n.Zb(402653184,10,{sqlIdTextInput:0}),n.Zb(402653184,11,{sqlExecStartInput:0}),n.Zb(402653184,12,{stackTraceTextArea:0}),n.Zb(402653184,13,{sqlStatementTextArea:0}),n.Zb(402653184,14,{killButton:0}),n.Zb(402653184,15,{cancelButton:0}),(t()(),n.Jb(15,0,null,null,89,"SwtModule",[["height","620"],["paddingLeft","10"],["paddingTop","10"],["width","100%"]],[[8,"title",0]],[[null,"creationComplete"]],function(t,e,l){var n=!0,i=t.component;"creationComplete"===e&&(n=!1!==i.onLoad()&&n);return n},g.ad,g.hb)),n.Ib(16,4440064,null,0,o.yb,[n.r,o.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(17,0,null,0,87,"VBox",[["height","100%"]],null,null,null,g.od,g.vb)),n.Ib(18,4440064,null,0,o.ec,[n.r,o.i,n.T],{height:[0,"height"]},null),(t()(),n.Jb(19,0,null,0,77,"SwtCanvas",[["height","90%"],["width","98%"]],null,null,null,g.Nc,g.U)),n.Ib(20,4440064,null,0,o.db,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(21,0,null,0,75,"HBox",[["height","185"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(22,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(23,0,null,0,73,"VBox",[["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(24,4440064,null,0,o.ec,[n.r,o.i,n.T],{width:[0,"width"]},null),(t()(),n.Jb(25,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(26,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(27,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.module"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(28,4440064,[["categoryRuleNameLabel",4]],0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(29,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","module"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(30,4440064,[[2,4],["module",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(31,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(32,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(33,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.connectionId"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(34,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(35,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","connectionId"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(36,4440064,[[3,4],["connectionId",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(37,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(38,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(39,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.status"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(40,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(41,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","status"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(42,4440064,[[4,4],["status",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(43,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(44,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(45,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.lastActionTime"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(46,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(47,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","lastActionTime"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(48,4440064,[[6,4],["lastActionTime",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(49,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(50,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(51,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.duration"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(52,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(53,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","duration"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(54,4440064,[[5,4],["duration",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(55,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(56,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(57,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.sqlStatus"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(58,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(59,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","sqlStatus"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(60,4440064,[[7,4],["sqlStatus",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(61,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(62,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(63,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.sid"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(64,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(65,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","sid"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(66,4440064,[[8,4],["sid",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(67,0,null,0,5,"HBox",[["height","110"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(68,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(69,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.sqlStatement"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(70,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(71,0,null,0,1,"SwtTextArea",[["enabled","false"],["height","90%"],["id","sqlStatement"],["required","true"],["width","550"]],null,null,null,g.jd,g.rb)),n.Ib(72,4440064,[[13,4],["sqlStatement",4]],0,o.Qb,[n.r,o.i,n.L],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],required:[4,"required"],editable:[5,"editable"]},null),(t()(),n.Jb(73,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(74,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(75,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.sqlId"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(76,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(77,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","sqlId"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(78,4440064,[[10,4],["sqlId",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(79,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(80,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(81,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.sqlExecStartTime"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(82,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(83,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","sqlExecStart"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(84,4440064,[[11,4],["sqlExecStart",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(85,0,null,0,5,"HBox",[["height","30"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(86,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(87,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.audsid"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(88,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(89,0,null,0,1,"SwtTextInput",[["editable","false"],["enabled","false"],["height","22"],["id","audsid"],["width","270"]],null,null,null,g.kd,g.sb)),n.Ib(90,4440064,[[9,4],["audsid",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],editable:[4,"editable"]},null),(t()(),n.Jb(91,0,null,0,5,"HBox",[["height","110"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(92,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(93,0,null,0,1,"SwtLabel",[["height","19"],["textDictionaryId","connectionPool.stackTrace"],["width","160"]],null,null,null,g.Yc,g.fb)),n.Ib(94,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(95,0,null,0,1,"SwtTextArea",[["enabled","false"],["height","90%"],["id","stackTrace"],["required","true"],["width","550"]],null,null,null,g.jd,g.rb)),n.Ib(96,4440064,[[12,4],["stackTrace",4]],0,o.Qb,[n.r,o.i,n.L],{id:[0,"id"],width:[1,"width"],height:[2,"height"],enabled:[3,"enabled"],required:[4,"required"],editable:[5,"editable"]},null),(t()(),n.Jb(97,0,null,0,7,"SwtCanvas",[["height","7%"],["id","canvasContainer"],["width","98%"]],null,null,null,g.Nc,g.U)),n.Ib(98,4440064,null,0,o.db,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(99,0,null,0,5,"HBox",[],null,null,null,g.Dc,g.K)),n.Ib(100,4440064,null,0,o.C,[n.r,o.i],null,null),(t()(),n.Jb(101,0,null,0,1,"SwtButton",[["id","killButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.doKillConnectionEventHandler(l)&&n);"keyDown"===e&&(n=!1!==i.keyDownEventHandler(l)&&n);return n},g.Mc,g.T)),n.Ib(102,4440064,[[14,4],["killButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(103,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.popupClosed()&&n);"keyDown"===e&&(n=!1!==i.keyDownEventHandler(l)&&n);return n},g.Mc,g.T)),n.Ib(104,4440064,[[15,4],["cancelButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"})],function(t,e){t(e,16,0,"100%","620","10","10");t(e,18,0,"100%");t(e,20,0,"98%","90%");t(e,22,0,"100%","185");t(e,24,0,"100%");t(e,26,0,"100%","30");t(e,28,0,"connectionPool.module","160","19");t(e,30,0,"module","270","22","false","false");t(e,32,0,"100%","30");t(e,34,0,"connectionPool.connectionId","160","19");t(e,36,0,"connectionId","270","22","false","false");t(e,38,0,"100%","30");t(e,40,0,"connectionPool.status","160","19");t(e,42,0,"status","270","22","false","false");t(e,44,0,"100%","30");t(e,46,0,"connectionPool.lastActionTime","160","19");t(e,48,0,"lastActionTime","270","22","false","false");t(e,50,0,"100%","30");t(e,52,0,"connectionPool.duration","160","19");t(e,54,0,"duration","270","22","false","false");t(e,56,0,"100%","30");t(e,58,0,"connectionPool.sqlStatus","160","19");t(e,60,0,"sqlStatus","270","22","false","false");t(e,62,0,"100%","30");t(e,64,0,"connectionPool.sid","160","19");t(e,66,0,"sid","270","22","false","false");t(e,68,0,"100%","110");t(e,70,0,"connectionPool.sqlStatement","160","19");t(e,72,0,"sqlStatement","550","90%","false","true",!1);t(e,74,0,"100%","30");t(e,76,0,"connectionPool.sqlId","160","19");t(e,78,0,"sqlId","270","22","false","false");t(e,80,0,"100%","30");t(e,82,0,"connectionPool.sqlExecStartTime","160","19");t(e,84,0,"sqlExecStart","270","22","false","false");t(e,86,0,"100%","30");t(e,88,0,"connectionPool.audsid","160","19");t(e,90,0,"audsid","270","22","false","false");t(e,92,0,"100%","110");t(e,94,0,"connectionPool.stackTrace","160","19");t(e,96,0,"stackTrace","550","90%","false","true",!1);t(e,98,0,"canvasContainer","98%","7%"),t(e,100,0);t(e,102,0,"killButton");t(e,104,0,"cancelButton","true")},function(t,e){var l=e.component;t(e,15,0,n.Lb(1,"",l.title,""))})}function Y(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-connection-pool-details",[],null,null,null,M,K)),n.Ib(1,4440064,null,0,u,[o.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var j=n.Fb("app-connection-pool-details",u,Y,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);