<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@ include file="/taglib.jsp" %>
<%@ page import="org.swallow.util.SwtConstants" %>
<%@ page import="org.swallow.util.SwtUtil" %>
<%@page import="org.swallow.work.model.Movement" %>
<%@ page import="org.swallow.util.struts.TokenHelper" %>
<%@ page import="static org.swallow.util.SwtConstants.*" %>

<%
    //Initialize screenIdentifier,valueDateEditStatus,amountEditStatus,bookCodeEditStatus
    //posLevelEditStatus  with empty value
    String screenIdentifier = "";
    String methodName = "";
    String valueDateEditStatus = "";
    String amountEditStatus = "";
    String accountEditStatus = "";
    String bookCodeEditStatus = "";
    String posLevelEditStatus = "";
    //get the methodName from request attribute
    if (request.getAttribute("methodName") != null) {
        methodName = request.getAttribute("methodName")
                .toString();
    }
    //get the screenIdentifier from request attribute
    if (request.getAttribute("screenIdentifier") != null) {
        screenIdentifier = request.getAttribute("screenIdentifier")
                .toString();
    }
    //get the valueDateEditStatus from request attribute
    if ((request.getAttribute("valueDateEditStatus") != null)) {
        valueDateEditStatus = request.getAttribute(
                "valueDateEditStatus").toString();
    }
    //get the amountEditStatus from request attribute
    if ((request.getAttribute("amountEditStatus") != null)) {
        amountEditStatus = request.getAttribute("amountEditStatus")
                .toString();
    }
    //get the accountEditStatus from request attribute
    if ((request.getAttribute("accountEditStatus") != null)) {
        accountEditStatus = request.getAttribute("accountEditStatus")
                .toString();
    }

    //get the bookCodeEditStatus from request attribute
    if ((request.getAttribute("bookCodeEditStatus") != null)) {
        bookCodeEditStatus = request.getAttribute("bookCodeEditStatus")
                .toString();
    }
    //get the posLevelEditStatus from request attribute
    if ((request.getAttribute("posLevelEditStatus") != null)) {
        posLevelEditStatus = request.getAttribute("posLevelEditStatus")
                .toString();
    }
    //variable declaration
    //Initialize currencyCode,currencyName,accountId,bookCodeId,accountName
    //bookCodeName with empty value
    String currencyCode = "";
    String currencyName = "";
    String accountId = "";
    String bookCodeId = "";
    String accountName = "";
    String bookCodeName = "";
    Movement movement = null;
    //get the movement from request
    if (request.getAttribute("movement") != null) {
        //set the Movement bean
        movement = (Movement) request.getAttribute("movement");
        //get the currency code
        currencyCode = movement.getCurrencyCode();
        //check currency name not null
        if (request.getAttribute("currencyName") != null)
            //get the country name from request
            currencyName = request.getAttribute("currencyName")
                    .toString();
        //get the account id
        accountId = movement.getAccountId();
        //check account name not null
        if (request.getAttribute("accountName") != null)
            //get the account name from request
            accountName = request.getAttribute("accountName")
                    .toString();
        //get the book code id
        bookCodeId = movement.getBookCode();
        //check account name not null
        if (request.getAttribute("bookCodeName") != null)
            //get the account name from request
            bookCodeName = request.getAttribute("bookCodeName")
                    .toString();

    }
%>
<html>
<head>

    <c:choose>
        <c:when test="${requestScope.screenIdentifier == 'movementChange'}">
            <title><fmt:message key="mvmDisplay.title.changewindow"/></title>
        </c:when>
        <c:otherwise>
            <c:choose>
                <c:when test="${requestScope.showMovementId == 'yes'}">
                    <title><fmt:message key="mvmDisplay.title.window"/></title>
                </c:when>
                <c:otherwise>
                    <title><fmt:message key="manualInput.title.window"/></title>
                </c:otherwise>
            </c:choose>
        </c:otherwise>
    </c:choose>


    <meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
    <SCRIPT language="JAVASCRIPT">

        //Variable for changeFlag
        var changeFlag = false;

        //Array to hold the cancel & close button id
        var cancelcloseElements = new Array(2);
        cancelcloseElements[0] = "cancelbutton";
        cancelcloseElements[1] = "closebutton";
        //mandatoryFieldsArray =["*"];
        //Following variables assignment are used for make ajax request.
        var appName = "<%=SwtUtil.appName%>";
        var requestURL = new String('<%=request.getRequestURL()%>');
        var appIndex = requestURL.indexOf('/' + appName + '/');
        requestURL = requestURL.substring(0, appIndex + 1);
        var oXMLHTTP = new XMLHttpRequest();

        //Following variable's value assigned from request attribute which are used in the javascipt function.
        var initialscreen = window.opener.initialscreen;
        var parentScreen = "${requestScope.parentScreen}";
        var parent = '${requestScope.parentScreen}';
        var authorizeStatus = '${requestScope.authorizeStatus}';
        var screenIdentifier = "<%= screenIdentifier %>";
        var openUnopenFlag = '${requestScope.openUnopenFlag}';
        var currencyCode = "<%= currencyCode %>";
        var currencyName = "<%= currencyName %>";
        var accountId = "<%= accountId %>";
        var accountName = "<%= accountName %>";
        var bookCodeId = "<%= bookCodeId %>";
        var bookCodeName = "<%= bookCodeName %>";
        var valueDateEditStatus = "<%= valueDateEditStatus %>";
        var amountEditStatus = "<%= amountEditStatus %>";
        var refEditStatus = '${requestScope.refEditStatus}';
        var cpartyEditStatus = '${requestScope.cpartyEditStatus}';
        var benEditStatus = '${requestScope.benEditStatus}';
        var custEditStatus = '${requestScope.custEditStatus}';
        var MatchPartyEditStatus = '${requestScope.MatchPartyEditStatus}';
        var PrdTypeEditStatus = '${requestScope.PrdTypeEditStatus}';
        var PostDateEditStatus = '${requestScope.PostDateEditStatus}';
        var preStatusEditStatus = '${requestScope.preStatusEditStatus}';
        var extBalStatus = '${requestScope.extBalStatus}';
        var ilmFcastStatusEditStatus = '${requestScope.ilmFcastStatusEditStatus}';
        var extStatusEditStatus = '${requestScope.extStatusEditStatus}';
        var critPayTypeEditStatus = '${requestScope.CritPayTypeEditStatus}';
        var expSettEditStatus = '${requestScope.ExpSettEditStatus}';
        var actualSettEditStatus = '${requestScope.ActualSettEditStatus}';
        var bookCodeEditStatus = '<%= bookCodeEditStatus%>';
        var accountEditStatus = '<%= accountEditStatus%>';

        var OrederCustEditStatus = '${requestScope.OrederCustEditStatus}';
        var OrederInstEditStatus = '${requestScope.OrederInstEditStatus}';
        var SendCorresEditStatus = '${requestScope.SendCorresEditStatus}';
        var UETREditStatus = '${requestScope.UETREditStatus}';
        var ReceivCorrespEditStatus = '${requestScope.ReceivCorrespEditStatus}';
        var IntermInstitEditStatus = '${requestScope.IntermInstitEditStatus}';
        var ActWithInstitEditStatus = '${requestScope.ActWithInstitEditStatus}';
        var BenfCustomEditStatus = '${requestScope.BenfCustomEditStatus}';
        var SendToReceivInfEditStatus = '${requestScope.SendToReceivInfEditStatus}';

        var enteredMovId = null;
        var currencyAccessInd = "${requestScope.currGrpAccess}";
        //Following variables are used to populate the collection while clicking on account/bookcode select box
        var accountClk = true;
        var acctSelectElement = null;
        var acctSwSelectBox = null;
        var bookCodeClk = true;
        var bookCodeSelElement = null;
        var bookCodeSwSelBox = null;
        //Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing
        var initialinputscreen = "${requestScope.initialinputscreen}";
        //Following variables are used to show the calender
        var valueDtCal = new CalendarPopup("caldiv", false, "calFrame");
        valueDtCal.showNavigationDropdowns();
        valueDtCal.setCssPrefix("CAL");
        valueDtCal.offsetX = 20;
        valueDtCal.offsetY = 2;

        var postingDtCal = new CalendarPopup("caldiv", false, "calFrame");
        postingDtCal.showNavigationDropdowns();
        postingDtCal.setCssPrefix("CAL");
        postingDtCal.offsetX = -40;
        postingDtCal.offsetY = -40;

        var expectedDtCal = new CalendarPopup("caldiv", false, "calFrame");
        expectedDtCal.showNavigationDropdowns();
        expectedDtCal.setCssPrefix("CAL");
        expectedDtCal.offsetX = 10;
        expectedDtCal.offsetY = -140;
        expectedDtCal.withTime = true;

        var actualDtCal = new CalendarPopup("caldiv", false, "calFrame");
        actualDtCal.showNavigationDropdowns();
        actualDtCal.setCssPrefix("CAL");
        actualDtCal.offsetX = -100;
        actualDtCal.offsetY = -140;
        actualDtCal.withTime = true;


        /**
         * submitForm
         *
         * This method is used to submit the form while perform button operation on full input/movement display screen.
         **/

        function submitForm(methodName) {
            if (methodName == "change" || methodName == "update") {
                if (parentScreen == "movementSummaryDisplay") {
                    document.forms[0].parentScreen.value = "movementSummaryDisplay";
                    enableFields();
                    document.forms[0].movementId.value = document.forms[0].elements["movement.id.movementIdAsString"].value;
                }
                // Set the parentscreen name
                if (parentScreen == "authorise") {
                    document.forms[0].parentScreen.value = "authorise";
                }
            }
            //validate the field in full input and save the movement details.
            if (methodName == "save") {
                if (validateField(document.forms[0].elements['movement.valueDateAsString'], 'valueDateAsString', dateFormat)
                    && validateDateWithTimeField(document.forms[0].elements['movement.expectedSettlementDateTimeAsString'], 'expectedSettlement', dateFormat)
                    && validateDateWithTimeField(document.forms[0].elements['movement.settlementDateTimeAsString'], 'actualSettlment', dateFormat)) {
                    if (validateField(document.forms[0].elements['movement.postingDateAsString'], 'postingDateAsString', dateFormat)) {
                        if (document.forms[0].elements['movement.uetr'].value) {
                            if (!validateUETR(document.forms[0].elements['movement.uetr'].value))
                                return;
                        }
                        var amount = validateCurrency(document.forms[0].elements['movement.amountAsString'], 'movement.amountAsString', currencyFormat, document.forms[0].elements['movement.currencyCode'].value);
                        if (amount) {
                            if (validateForm(document.forms[0])) {

                                elementTrim(document.forms[0]);
                                document.forms[0].elements["movement.counterPartyId"].disabled = "";
                                document.forms[0].elements["movement.beneficiaryId"].disabled = "";
                                document.forms[0].elements["movement.custodianId"].disabled = "";
                                var counterPartyId = document.forms[0].elements["movement.counterPartyId"].value;
                                var beneficiaryId = document.forms[0].elements["movement.beneficiaryId"].value;
                                var custodianId = document.forms[0].elements["movement.custodianId"].value;
                                document.forms[0].elements["movement.counterPartyId"].value = counterPartyId;
                                document.forms[0].elements["movement.beneficiaryId"].value = beneficiaryId;
                                document.forms[0].elements["movement.custodianId"].value = custodianId;
                                document.forms[0].method.value = methodName;
                                if (accountAccess()) {
                                    document.getElementById("saveButton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.save"/></a>';
                                    document.forms[0].submit();
                                }
                            }
                        } else {
                            document.forms[0].elements['movement.amountAsString'].focus();
                        }
                    } else {
                        document.forms[0].elements['movement.postingDateAsString'].focus();
                    }
                } else {
                    document.forms[0].elements['movement.valueDateAsString'].focus();
                }
            } else {
                if (methodName == 'copyFrom') {
                    var copyFromURL = 'movement.do?method=' + methodName + '&selectedEntityId=';
                    copyFromURL += document.forms[0].elements["movement.id.entityId"].value;
                    return copyFromURL;
                } else if (methodName == 'change' || methodName == 'displayMovement') {
                    enableFields();
                    if (validateFormDisplay(document.forms[0])) {
                        document.forms[0].method.value = methodName;
                        document.forms[0].submit();
                    }
                } else if (methodName == 'update' || methodName == 'copy') {
                    enableFields();
                    document.forms[0].showMovementId.value = '${requestScope.showMovementId}';
                    document.forms[0].method.value = methodName;
                    document.forms[0].submit();
                } else if (methodName != 'checkExternalPositionLevel') {
                    document.forms[0].method.value = methodName;
                    document.forms[0].submit();
                }
            }
        }

        /**
         * submitChangeForm
         *
         * This method is used to submit the form when click the save button on change movement screen.
         **/
        function submitChangeForm(methodName) {
            //to trim the values in the entire form
            elementTrim(document.forms[0]);
            //validation for date
            if (validateField(document.forms[0].elements['movement.valueDateAsString'], 'valueDateAsString', dateFormat)) {
                if (validateField(document.forms[0].elements['movement.postingDateAsString'], 'postingDateAsString', dateFormat)) {
                    if (parentScreen == "authorise") {
                        document.forms[0].parentScreen.value = "authorise";
                    }
                    if (methodName == "update") {
                        if (parentScreen == "movementSummaryDisplay") {
                            document.forms[0].parentScreen.value = "movementSummaryDisplay";
                            document.forms[0].movementId.value = document.forms[0].elements["movement.id.movementIdAsString"].value;
                        }
                        document.forms[0].screenIdentifier.value = screenIdentifier;
                        //remove movement lock
                        releaseLock();
                        //validating form
                        if (validateChangeForm(document.forms[0])) {
                            var amount = document.forms[0].elements['movement.amountAsString'].value;
                            if (!validateCurrency(document.forms[0].elements['movement.amountAsString'], 'movement.amountAsString', currencyFormat, document.forms[0].elements['movement.currencyCode'].value)) {
                            } else {
                                if ((amount.indexOf("m") != -1) || (amount.indexOf("b") != -1) || (amount.indexOf("t") != -1) || (amount.indexOf("M") != -1) || (amount.indexOf("B") != -1) || (amount.indexOf("T") != -1)) {
                                    document.forms[0].elements['movement.amountAsString'].value = expandCurrencyAmount_movementdisplay(document.forms[0].elements['movement.amountAsString'].value, currencyFormat, document.forms[0].elements['movement.currencyCode'].value);
                                } else {
                                    // if authorization in the role is yes, alert message appears when update the movement
                                    if (authorizeStatus == 'Y') alert('<fmt:message key="preadvice.movetoauthorise"/>');
                                    document.forms[0].method.value = methodName;
                                    enableFields();
                                    //Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing
                                    document.forms[0].initialInputScreen.value = initialinputscreen;
                                    document.forms[0].submit();
                                    // reload the parent screen if parent is movement Summary Display
                                    if (parentScreen == "movementSummaryDisplay") {
                                        window.opener.authRefreshFlag = true;
                                        if (window.opener.CallBackApp)
                                            window.opener.CallBackApp();
                                    }
                                    if (parentScreen == "authorise") {
                                        window.opener.authRefreshFlag = true;
                                        // Refresh the MSD screen(From Movement Search screen), when save the data.
                                        if (window.opener.CallBackApp)
                                            window.opener.CallBackApp();
                                    }
                                }
                            }
                        }
                    }
                } else {
                    document.forms[0].elements['movement.postingDateAsString'].focus();
                }
            } else {
                document.forms[0].elements['movement.valueDateAsString'].focus();
            }
        }


        /**
         * openMvmntMessage
         *
         * This method is used to open the Movement message screen when click on message button.
         **/
        function openMvmntMessage() {
            //checking for valid movement Id
            if (validateMovementId()) {
                //Framing URL for request
                var mvmtMsgURL = 'movement.do?method=mvmntMessageDisplay';
                mvmtMsgURL += '&movmentId=' + removeLeadingZeros(document.forms[0].elements['movement.id.movementIdAsString'].value);
                openWindow(mvmtMsgURL, 'movMessageWindow', 'left=50,top=190,width=560,height=445,toolbar=0, resizable=yes, scrollbars=yes', 'true');
            }
        }

        /**
         * buildMovement
         *
         * This method is used to open the Movement notes screen when click on notes button.
         **/
        function buildMovement(methodName, disBtnLock) {
            <c:if test="${requestScope.showMovementId == 'yes'}">
            document.forms[0].selectedMovementId.value = document.forms[0].elements["movement.id.movementIdAsString"].value;
            var entityId = document.forms[0].elements["movement.id.entityId"].value;
            </c:if>
            var unLock = "false";
            if (document.forms[0].elements["movement.id.movementIdAsString"] != null && !document.forms[0].elements["movement.id.movementIdAsString"].disabled) {
                unLock = "true";
            }
            var notesURL = 'notes.do?method=' + methodName + '&movId=';
            notesURL += removeLeadingZeros(document.forms[0].selectedMovementId.value);
            notesURL += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;
            notesURL += '&screenName=' + 'manualInputScreenViewMode';
            notesURL += '&archiveId=${archiveId}';
            notesURL += '&currencyAccess=' + currencyAccessInd;
            notesURL += '&unLock=' + unLock;
            notesURL += '&disBtnLock=' + disBtnLock;
            return notesURL;
        }

        /**
         * clickOpenButton
         *
         * This method is used to set the open status for movement while clicking open button on movement display
         **/
        function clickOpenButton(methodName) {
            if (validateMovementId()) {
                var lockVal = checkLock();
                if (lockVal == "true") {
                    acquireLock();
                    var confirmState = window.confirm('<fmt:message key="confirm.open"/>');
                    if (confirmState == true) {
                        if (parentScreen == "movementSummaryDisplay") {
                            document.forms[0].parentScreen.value = "movementSummaryDisplay";
                            enableFields();
                            document.forms[0].movementId.value = document.forms[0].elements["movement.id.movementIdAsString"].value
                        }
                        document.forms[0].initialInputScreen.value = initialinputscreen;
                        document.forms[0].method.value = methodName;
                        document.forms[0].submit();
                    }
                    releaseLock();
                } else {
                    disableButtonsForLock(lockVal);
                }
            }
        }

        /**
         * clickUnopenButton
         *
         * This method is used to set the unopen status for movement while clicking unopen button on movement display
         **/
        function clickUnopenButton(methodName) {
            if (validateMovementId()) {
                var lockVal = checkLock();
                if (lockVal == "true") {
                    acquireLock();
                    var confirmState = window.confirm('<fmt:message key="confirm.unopen"/>');
                    if (confirmState == true) {
                        if (parentScreen == "movementSummaryDisplay") {
                            document.forms[0].parentScreen.value = "movementSummaryDisplay";
                            enableFields();
                            document.forms[0].movementId.value = document.forms[0].elements["movement.id.movementIdAsString"].value
                        }
                        document.forms[0].initialInputScreen.value = initialinputscreen;
                        document.forms[0].method.value = methodName;
                        document.forms[0].submit();
                    }
                    releaseLock();
                } else {
                    disableButtonsForLock(lockVal);
                }
            }
        }

        /**
         * validateForm
         *
         * This method is used to validate the form elements while saving the new movement on full input screen.
         **/
        function validateForm(objForm) {
            var elementsRef = new Array(6);
            elementsRef[0] = objForm.elements["movement.id.entityId"];
            elementsRef[1] = objForm.elements["movement.valueDateAsString"];
            elementsRef[2] = objForm.elements["movement.amountAsString"];
            elementsRef[3] = objForm.elements["movement.sign"];
            elementsRef[4] = objForm.elements["movement.currencyCode"];
            elementsRef[5] = objForm.elements["movement.positionLevelAsString"];
            var otherFieldStatusBlank = false; // This variable is false till all other mandatory fields are filled
            for (var i = 0; i < 6; i++) {
                if ((new String(elementsRef[i].value)).trim() == "") {
                    otherFieldStatusBlank = true;
                }
            }
            if (document.forms[0].elements["movement.accountId"].value == "" && !otherFieldStatusBlank) {
                alert("<fmt:message key="positionLevel.alert.mandatoryFields"/>");
                document.getElementById("dropdownbutton_2").focus();
                return false;
            }
            return validate(elementsRef);
        }


        /**
         * validateFormDisplay
         *
         * This method is used to validate the movement id which is entered in the movement display screen.
         * Once validation return true then only it will get the movement details
         *
         **/
        function validateFormDisplay(objForm) {
            var elementsRef = new Array(1);
            elementsRef[0] = objForm.elements["movement.id.movementIdAsString"];
            return validate(elementsRef);
        }

        /**
         * enableFields
         *
         * This method is used to enable the form elements which was in disable previously.
         * If elements are in enable state then only we can get the value for that element
         * so that this function will be called before form getting submit
         *
         **/
        function enableFields() {
            document.forms[0].elements["movement.id.entityId"].disabled = "";
            document.forms[0].elements["movement.id.movementIdAsString"].disabled = "";
            if (screenIdentifier == "movementChange") {
                document.forms[0].elements['movement.currencyCode'].disabled = "";
                document.forms[0].elements["movement.movementType"][0].disabled = "";
                document.forms[0].elements["movement.movementType"][1].disabled = "";
                document.forms[0].elements["movement.predictStatus"][0].disabled = "";
                document.forms[0].elements["movement.predictStatus"][1].disabled = "";
                document.forms[0].elements["movement.predictStatus"][2].disabled = "";
                document.forms[0].elements["movement.extBalStatus"][0].disabled = "";
                document.forms[0].elements["movement.extBalStatus"][1].disabled = "";
                document.forms[0].elements["movement.accountId"].disabled = "";
                document.forms[0].elements["movement.bookCode"].disabled = "";

            }
        }


        var valueDateInForm = ""; //This variable stores current system date which is set in the form from the back

        /**
         * populateDropDowns
         *
         * This method is used to add account/bookcode in the select box while displaying manual input/change movement screen.
         */
        function populateDropDowns() {
            if (screenIdentifier == "manualInput" || accountEditStatus == "false" || bookCodeEditStatus == "false") {
                //set the accountId and accountName

                var acctElement = document.getElementById("accountId")
                if (accountId != "" && accountId != "null" && acctElement != null) {
                    acctSelectElement = document.forms[0].elements["movement.accountId"];
                    var optionElement = document.createElement("option");
                    optionElement.text = accountName;
                    optionElement.value = accountId;
                    acctSelectElement.options.add(optionElement);
                    acctSelectElement.selectedIndex = 0;
                    acctElement.value = accountId;
                    if (accountName != "" && accountName != "null")
                        document.getElementById("accountDesc").value = accountName;
                }
                //set the bookCodeId and bookCodeName
                var bookElement = document.getElementById("bookCode")
                if (bookCodeId != "" && bookCodeId != "null" && bookElement != null) {
                    bookSelectElement = document.forms[0].elements["movement.bookCode"];
                    var optionElement = document.createElement("option");
                    optionElement.text = bookCodeName;
                    optionElement.value = bookCodeId;
                    bookSelectElement.options.add(optionElement);
                    bookSelectElement.selectedIndex = 0;
                    bookElement.value = bookCodeId;
                    if (bookCodeName != "" && bookCodeName != "null")
                        document.getElementById("bookCodeDesc").value = bookCodeName;

                }
            }
        }


        /**
         * onLoadStatus
         *
         * This method is used to decide the components properties based on which screen it is calling from
         */
        function onLoadStatus() {
            //set the components properties for movement display screen.
            if (screenIdentifier == "movementDisplay") {
                // document.getElementById("divMovementDisplay1").style.top = '266px';
                document.getElementById("accounttooltip").title = accountId;
                document.getElementById("booktooltip").title = bookCodeId;
                document.getElementById("entityIdtooltip").title = document.forms[0].elements['movement.id.entityId'].value;
                if (parentScreen == "movementSummaryDisplay") {
                    document.forms[0].elements["movement.id.movementIdAsString"].disabled = "true";
                } else {
                    document.forms[0].elements["movement.id.movementIdAsString"].disabled = "";
                }
                document.getElementById("movement.valueDateAsString").styleClass = 'htmlTextAlpha';
                //set disable property for element when screen displaying the movement display
                setStyleElement(["movement.valueDateAsString", "movement.amountAsString", "movement.reference1", "movement.reference2", "movement.reference3", "movement.reference4", "movement.counterPartyId"]);
                document.getElementById("counterPartyLink").style.visibility = 'hidden';
                setStyleElement(["movement.counterPartyText1", "movement.counterPartyText2", "movement.counterPartyText3", "movement.counterPartyText4", "movement.counterPartyText5", "movement.beneficiaryId"]);
                document.getElementById("beneificiaryPartyLink").style.visibility = 'hidden';
                setStyleElement(["movement.beneficiaryText1", "movement.beneficiaryText2", "movement.beneficiaryText3", "movement.beneficiaryText4", "movement.custodianId", "movement.beneficiaryText5"]);
                document.getElementById("custodianPartyLink").style.visibility = 'hidden';
                setStyleElement(["movement.custodianText1", "movement.custodianText2", "movement.custodianText3", "movement.custodianText4", "movement.custodianText5", "movement.matchingParty"]);
                document.getElementById("matchingPartyLink").style.visibility = 'hidden';
                setStyleElement(["movement.productType", "movement.postingDateAsString", "movement.criticalPaymentType", "movement.settlementDateTimeAsString", "movement.expectedSettlementDateTimeAsString", "movement.uetr"]);
                document.getElementById("movement.postingDateAsString").styleClass = 'htmlTextAlpha';
                document.getElementById("postingDateLink").style.display = 'none';
                document.getElementById("expectedDateLink").style.display = 'none';
                document.getElementById("actualDateLink").style.display = 'none';

                setStyleElement(["movement.orderingCustomerId", "movement.movementExt.orderingCustomer1", "movement.movementExt.orderingCustomer2", "movement.movementExt.orderingCustomer3", "movement.movementExt.orderingCustomer4", "movement.movementExt.orderingCustomer5"]);
                setStyleElement(["movement.orderingInstitutionId", "movement.movementExt.orderingInstitution1", "movement.movementExt.orderingInstitution2", "movement.movementExt.orderingInstitution3", "movement.movementExt.orderingInstitution4", "movement.movementExt.orderingInstitution5"]);
                setStyleElement(["movement.senderCorrespondentId", "movement.movementExt.senderCorrespondent1", "movement.movementExt.senderCorrespondent2", "movement.movementExt.senderCorrespondent3", "movement.movementExt.senderCorrespondent4", "movement.movementExt.senderCorrespondent5"]);
                setStyleElement(["movement.receiverCorrespondentId", "movement.movementExt.receiverCorrespondent1", "movement.movementExt.receiverCorrespondent2", "movement.movementExt.receiverCorrespondent3", "movement.movementExt.receiverCorrespondent4", "movement.movementExt.receiverCorrespondent5"]);
                setStyleElement(["movement.intermediaryInstitutionId", "movement.movementExt.intermediaryInstitution1", "movement.movementExt.intermediaryInstitution2", "movement.movementExt.intermediaryInstitution3", "movement.movementExt.intermediaryInstitution4", "movement.movementExt.intermediaryInstitution5"]);
                setStyleElement(["movement.accountWithInstitutionId", "movement.movementExt.accountWithInstitution1", "movement.movementExt.accountWithInstitution2", "movement.movementExt.accountWithInstitution3", "movement.movementExt.accountWithInstitution4", "movement.movementExt.accountWithInstitution5"]);
                setStyleElement(["movement.beneficiaryCustomerId", "movement.movementExt.beneficiaryCustomer1", "movement.movementExt.beneficiaryCustomer2", "movement.movementExt.beneficiaryCustomer3", "movement.movementExt.beneficiaryCustomer4", "movement.movementExt.beneficiaryCustomer5"]);
                setStyleElement(["movement.movementExt.senderToReceiverInfo1", "movement.movementExt.senderToReceiverInfo2", "movement.movementExt.senderToReceiverInfo3", "movement.movementExt.senderToReceiverInfo4", "movement.movementExt.senderToReceiverInfo5", "movement.movementExt.senderToReceiverInfo6"]);


                document.getElementById("orderingCustomerLink").style.visibility = 'hidden';
                document.getElementById("orderingInstLink").style.visibility = 'hidden';
                document.getElementById("senderCorresLink").style.visibility = 'hidden';
                document.getElementById("receiverCorresLink").style.visibility = 'hidden';
                document.getElementById("intermediaryInstitutionLink").style.visibility = 'hidden';
                document.getElementById("accountwithInstitutionLink").style.visibility = 'hidden';
                document.getElementById("beneficiaryCustomerLink").style.visibility = 'hidden';

            } else if (screenIdentifier == "movementChange") {
                document.getElementById("entityIdtooltip").title = document.forms[0].elements['movement.id.entityId'].value;
// 	        document.getElementById("divMovementDisplay").style.height = '720px';
//            	document.getElementById("divMovementDisplay2").style.top = '80px';
// 	        document.getElementById("dropdowndiv_2").style.top = '199px';
// 	        document.getElementById("dropdowndiv_3").style.top = '248px';


                //set disable property for element when screen displaying the movement
                setStyleElement(["movement.id.movementIdAsString"]);
                document.forms[0].elements["movement.id.movementIdAsString"].onkeydown = "";
                if (valueDateEditStatus == "true") {
                    setStyleElement(["movement.valueDateAsString"]);
                }
                if (amountEditStatus == "true") {
                    setStyleElement(["movement.amountAsString"]);
                }
                if (refEditStatus == "true") {
                    setStyleElement(["movement.reference1", "movement.reference2", "movement.reference3", "movement.reference4"]);
                }

                if (cpartyEditStatus == "true") {
                    document.getElementById("counterPartyLink").disabled = "true";
                    document.getElementById("counterPartyLink").style.visibility = 'hidden';
                    setStyleElement(["movement.counterPartyId", "movement.counterPartyText1", "movement.counterPartyText2", "movement.counterPartyText3", "movement.counterPartyText4", "movement.counterPartyText5"]);
                }
                if (benEditStatus == "true") {
                    document.getElementById("beneificiaryPartyLink").disabled = "true";
                    document.getElementById("beneificiaryPartyLink").style.visibility = 'hidden';
                    setStyleElement(["movement.beneficiaryId", "movement.beneficiaryText1", "movement.beneficiaryText2", "movement.beneficiaryText3", "movement.beneficiaryText4", "movement.beneficiaryText5"]);
                }
                if (custEditStatus == "true") {
                    document.getElementById("custodianPartyLink").disabled = "true";
                    document.getElementById("custodianPartyLink").style.visibility = 'hidden';
                    setStyleElement(["movement.custodianId", "movement.custodianText1", "movement.custodianText2", "movement.custodianText3", "movement.custodianText4", "movement.custodianText5"]);
                }
                if (MatchPartyEditStatus == "true") {
                    setStyleElement(["movement.matchingParty"]);
                    document.getElementById("matchingPartyLink").disabled = "true";
                    document.getElementById("matchingPartyLink").style.visibility = 'hidden';
                }
                if (PrdTypeEditStatus == "true") setStyleElement(["movement.productType"]);
                if (PostDateEditStatus == "true") {
                    setStyleElement(["movement.postingDateAsString", "movement.postingDateAsString"]);
                    document.getElementById("movement.postingDateAsString").styleClass = 'htmlTextAlpha';
                    document.getElementById("postingDateLink").style.display = 'none';
                }

                if (expSettEditStatus == "true") {
                    setStyleElement(["movement.expectedSettlementDateTimeAsString"]);
                    document.getElementById("movement.expectedSettlementDateTimeAsString").styleClass = 'htmlTextAlpha';
                    document.getElementById("expectedDateLink").style.display = 'none';
                }
                if (actualSettEditStatus == "true") {
                    setStyleElement(["movement.settlementDateTimeAsString"]);
                    document.getElementById("movement.settlementDateTimeAsString").styleClass = 'htmlTextAlpha';
                    document.getElementById("actualDateLink").style.display = 'none';
                }
                if (critPayTypeEditStatus == "true") {
                    setStyleElement(["movement.criticalPaymentType"]);
                }


                if (OrederCustEditStatus == "true") {
                    document.getElementById("orderingCustomerLink").disabled = "true";
                    document.getElementById("orderingCustomerLink").style.visibility = 'hidden';
                    setStyleElement(["movement.orderingCustomerId", "movement.movementExt.orderingCustomer1", "movement.movementExt.orderingCustomer2", "movement.movementExt.orderingCustomer3", "movement.movementExt.orderingCustomer4", "movement.movementExt.orderingCustomer5"]);
                }
                if (OrederInstEditStatus == "true") {
                    document.getElementById("orderingInstLink").disabled = "true";
                    document.getElementById("orderingInstLink").style.visibility = 'hidden';
                    setStyleElement(["movement.orderingInstitutionId", "movement.movementExt.orderingInstitution1", "movement.movementExt.orderingInstitution2", "movement.movementExt.orderingInstitution3", "movement.movementExt.orderingInstitution4", "movement.movementExt.orderingInstitution5"]);
                }

                if (SendCorresEditStatus == "true") {
                    document.getElementById("senderCorresLink").disabled = "true";
                    document.getElementById("senderCorresLink").style.visibility = 'hidden';
                    setStyleElement(["movement.senderCorrespondentId", "movement.movementExt.senderCorrespondent1", "movement.movementExt.senderCorrespondent2", "movement.movementExt.senderCorrespondent3", "movement.movementExt.senderCorrespondent4", "movement.movementExt.senderCorrespondent5"]);
                }

                if (UETREditStatus == "true") {
                    setStyleElement(["movement.uetr"]);
                }


                if (ReceivCorrespEditStatus == "true") {
                    document.getElementById("receiverCorresLink").disabled = "true";
                    document.getElementById("receiverCorresLink").style.visibility = 'hidden';
                    setStyleElement(["movement.receiverCorrespondentId", "movement.movementExt.receiverCorrespondent1", "movement.movementExt.receiverCorrespondent2", "movement.movementExt.receiverCorrespondent3", "movement.movementExt.receiverCorrespondent4", "movement.movementExt.receiverCorrespondent5"]);
                }
                if (IntermInstitEditStatus == "true") {
                    document.getElementById("intermediaryInstitutionLink").disabled = "true";
                    document.getElementById("intermediaryInstitutionLink").style.visibility = 'hidden';
                    setStyleElement(["movement.intermediaryInstitutionId", "movement.movementExt.intermediaryInstitution1", "movement.movementExt.intermediaryInstitution2", "movement.movementExt.intermediaryInstitution3", "movement.movementExt.intermediaryInstitution4", "movement.movementExt.intermediaryInstitution5"]);
                }
                if (OrederCustEditStatus == "true") {
                    document.getElementById("accountwithInstitutionLink").disabled = "true";
                    document.getElementById("accountwithInstitutionLink").style.visibility = 'hidden';
                    setStyleElement(["movement.accountWithInstitutionId", "movement.movementExt.accountWithInstitution1", "movement.movementExt.accountWithInstitution2", "movement.movementExt.accountWithInstitution3", "movement.movementExt.accountWithInstitution4", "movement.movementExt.accountWithInstitution5"]);
                }
                if (BenfCustomEditStatus == "true") {
                    document.getElementById("beneficiaryCustomerLink").disabled = "true";
                    document.getElementById("beneficiaryCustomerLink").style.visibility = 'hidden';
                    setStyleElement(["movement.beneficiaryCustomerId", "movement.movementExt.beneficiaryCustomer1", "movement.movementExt.beneficiaryCustomer2", "movement.movementExt.beneficiaryCustomer3", "movement.movementExt.beneficiaryCustomer4", "movement.movementExt.beneficiaryCustomer5"]);
                }


                if (SendToReceivInfEditStatus == "true") {
                    setStyleElement(["movement.movementExt.senderToReceiverInfo1", "movement.movementExt.senderToReceiverInfo2", "movement.movementExt.senderToReceiverInfo3", "movement.movementExt.senderToReceiverInfo4", "movement.movementExt.senderToReceiverInfo5", "movement.movementExt.senderToReceiverInfo6"]);
                }


                document.forms[0].elements["movement.movementType"][0].disabled = "true";
                document.forms[0].elements["movement.movementType"][1].disabled = "true";
                //set disable property for predictStatus radio button
                var radioPrdStatus = document.forms[0].elements["movement.predictStatus"];
                radioPrdStatus[0].disabled = (preStatusEditStatus == "false" ? "" : "true");
                radioPrdStatus[1].disabled = (preStatusEditStatus == "false" ? "" : "true");
                radioPrdStatus[2].disabled = (preStatusEditStatus == "false" ? "" : "true");

                //set disable property for extBalStatus radio button
                var radioExtBalStatus = document.forms[0].elements["movement.extBalStatus"];
                radioExtBalStatus[0].disabled = (extBalStatus == "false" ? "" : "true");
                radioExtBalStatus[1].disabled = (extBalStatus == "false" ? "" : "true");

                //set disable property for ilmFcastStatus radio button
                var radioIlmFcastStatus = document.forms[0].elements["movement.ilmFcastStatus"];
                radioIlmFcastStatus[0].disabled = (ilmFcastStatusEditStatus == "false" ? "" : "true");
                radioIlmFcastStatus[1].disabled = (ilmFcastStatusEditStatus == "false" ? "" : "true");


            }
            //set mandatory symbol for value date field
            if ((screenIdentifier == "manualInput") || (valueDateEditStatus == "false")) {
                document.getElementById("valueMan").innerText = "*";
            }
            //set mandatory symbol for amount field
            if ((screenIdentifier == "manualInput") || (amountEditStatus == "false")) {
                document.getElementById("amountMan").innerText = "*";
            }
            //set mandatory symbol for account field
            if ((screenIdentifier == "manualInput") || (accountEditStatus == "false")) {
                document.getElementById("acctMan").innerText = "*";
            }
// 	    //to set status panal
// 	    if(screenIdentifier != "manualInput"){
// 	    	document.getElementById("divMovementDisplay").style.height = '735px';
// 	    }

            if (document.getElementById("movement.id.movementIdAsString")) {
                document.getElementById("movement.id.movementIdAsString").style.paddingTop = "2px";

            }

            if (screenIdentifier == "manualInput") {
                document.forms[0].elements["movement.ilmFcastStatus"][0].disabled = "true";
                document.forms[0].elements["movement.ilmFcastStatus"][1].disabled = "true";
            }
        }

        /**
         * setStyleElement
         *
         * This method is used to set disable property for given element.
         */
        function setStyleElement(elementArr) {
            for (var i = 0; i < elementArr.length; i++) {
                var element = document.getElementById(elementArr[i]);
                if (element != null) {
                    element.style.background = 'transparent';
                    element.style.border = "thin";
                    element.style.paddingTop = "6";
                    element.readOnly = true;
                    element.disabled = '';
                    element.title = '';
                    element.tabIndex = -1;
                    element.style.textAlign = "left";
                }
            }
        }

        /**
         * bodyOnLoad
         *
         * This method is called when window onloading and used to set form
         * values in the page and enable/diable the button
         */
        function bodyOnLoad() {


            document.forms[0].menuAccessId.value = "${requestScope.menuAccessId}";
            document.getElementById("entityDesc").value = "${entityDesc}";
            document.getElementById("accountDesc").value = "${accountDesc}";
            document.getElementById("bookCodeDesc").value = "${bookCodeDesc}";
            document.getElementById("positionName").value = "${posLevelDesc}";
            <c:if test="${requestScope.showMovementId == 'yes'}">
            var reconButtonStatus = '${requestScope.reconButtonStatus}';
            var matchStatus;
            if (document.forms[0].elements['movement.matchStatus'] != null)
                matchStatus = document.forms[0].elements['movement.matchStatus'].value;
            if ((reconButtonStatus == 'Y') && (matchStatus == "<%=SwtConstants.OUTSTANDING_STATUS%>")) {
                if (document.getElementById("reconbutton") != null) {
                    document.getElementById("reconbutton").innerHTML = document.getElementById("reconenablebutton").innerHTML;
                }
            } else {
                if (document.getElementById("reconbutton") != null) {
                    document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;
                }
            }
            document.getElementById("bookCodeDesc").value = "${bookCodeDesc}";
            <c:if test="${requestScope.hasNotes == 'Y'}">
            document.getElementById("notesIndicator").src = "images/notes.gif";
            document.getElementById("notesIndicator").title = '<fmt:message key="msg.title.notesAvailable"/>';
            </c:if>
            var isManyMessagesFlag;
            if (document.forms[0].elements["movement.isManyMessages"] != null)
                isManyMessagesFlag = document.forms[0].elements["movement.isManyMessages"].value;
            bringMyChildOnTop(window.name);
            //Set the focus to the movement of text field in loading of screen
            this.window.focus();
            if (document.forms[0].elements["movement.id.movementIdAsString"].disabled == "false")
                document.forms[0].elements["movement.id.movementIdAsString"].focus();
            </c:if>
            <c:if test="${requestScope.showMovementId != 'yes'}">
            var entityBox = new SwSelectBox(document.forms[0].elements['movement.id.entityId'], document.getElementById("entityDesc"));
            </c:if>

            var dropBox1 = new SwSelectBox(document.forms[0].elements["movement.positionLevelAsString"], document.getElementById("positionName"));

            document.getElementById("beneficiaryName").innerText = "${beneficiaryName}";
            document.getElementById("custodianName").innerText = "${custodianName}";
            document.getElementById("counterPartyDesc").innerText = "${counterPartyDesc}";
            document.getElementById("matchingPartyDesc").innerText = "${matchingPartyDesc}";

            document.getElementById("orderingCustomerName").innerText = "${orderingCustName}";
            document.getElementById("orderInstName").innerText = "${orderingInstName}";
            document.getElementById("senderCorrName").innerText = "${senderCorresName}";
            document.getElementById("receiverCorrName").innerText = "${receivCorresName}";
            document.getElementById("intermediaryInstitutionName").innerText = "${intermInstName}";
            document.getElementById("accountWithInstitutionName").innerText = "${acctWithInstName}";
            document.getElementById("beneficiaryCustomerName").innerText = "${benefCustName}";


            <c:if test="${requestScope.parentFormRefresh == 'yes'}">
            alert('<fmt:message key="manualInput.alert.mvmSaved"/>' + ${requestScope.savedMovementId});
            <c:if test="${requestScope.matchStatus == 'A'}">
            alert('<fmt:message key="manualInput.alert.chkStatus"/>');
            </c:if>
            </c:if>
            <c:if test="${requestScope.movementAvailable == 'no'}">
            document.forms[0].elements["movement.id.movementIdAsString"].value = "";
            alert('<fmt:message key="manualInput.alert.mvmNotonFile"/>');
            </c:if>
            <c:if test="${requestScope.movementAccessAvailable == 'no'}">
            alert('<fmt:message key="movementDisplay.alert.noAccess"/>');
            </c:if>
            if (screenIdentifier != "movementChange") {
                if (document.forms[0].elements["movement.id.movementIdAsString"] != null && document.forms[0].elements["movement.id.movementIdAsString"].value != "") {
                    var chkLockURL = requestURL + appName + "/movementLock.do?method=checkLock";
                    var oXMLHTTP = new XMLHttpRequest();
                    chkLockURL = chkLockURL + "&movementId=" + document.forms[0].elements["movement.id.movementIdAsString"].value;
                    oXMLHTTP.open("POST", chkLockURL, false);
                    oXMLHTTP.send();
                    var lockVal = new String(oXMLHTTP.responseText);
                    <c:if test="${requestScope.parentScreen == 'movementSummaryDisplay'}">
                    /* for msd called from excluded outstandings */
                    if (initialscreen == "E" || initialscreen == "X") {
                        lockVal = "true";
                    }
                    </c:if>
                    if (initialscreen == "movementmatchsummarydisplay") {                /* for mmsd */
                        lockVal = "true";
                    }
                    if (lockVal != "true") {
                        disableButtonsForLock(lockVal);
                    }
                    enteredMovId = document.forms[0].elements["movement.id.movementIdAsString"].value;
                }
                changeBorderColor();
                $(document).on('change', '#accountDropDown', function () {
                    changeBorderColor();
                });
            }
            valueDateInForm = document.forms[0].elements['movement.valueDateAsString'].value;
            if (document.getElementById("openUnopenButton") != null) {
                var matchStatus = document.forms[0].elements['movement.matchStatus'].value;
                var valueDateInd = '${requestScope.valueDateInd}';
                if (matchStatus == "<%=SwtConstants.AUTHORISE_STATUS%>" || matchStatus == "<%=SwtConstants.REFERRED_STATUS%>" || matchStatus == "<%=SwtConstants.RECONCILE_STATUS%>") {
                    <%if ( SwtConstants.NO.equals(request.getAttribute(SwtConstants.OPEN_UNOPEN_FLAG))	) {	%>
                    document.getElementById("openUnopenButton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.Open"/></a>';
                    <%}%>
                }
            }
            var archive_id = '${archiveId}';
            if (archive_id != null && archive_id != "") {
                if (document.getElementById("reconbutton") != null) {
                    document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;
                }

                if (document.getElementById("xrefsbutton") != null) {
                    document.getElementById("xrefsbutton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.crossReference"/></a>';
                }


                if (document.getElementById("openUnopenButton") != null) {
                    <%if ( SwtConstants.YES.equals(request.getAttribute(SwtConstants.OPEN_UNOPEN_FLAG))	) {	%>
                    document.getElementById("openUnopenButton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.Unopen"/></a>';
                    <%}%>
                    <%if ( SwtConstants.NO.equals(request.getAttribute(SwtConstants.OPEN_UNOPEN_FLAG))	) {	%>
                    document.getElementById("openUnopenButton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.Open"/></a>';
                    <%}%>
                }
                if (document.getElementById("changebutton") != null) {
                    document.getElementById("changebutton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>';
                }
            }
            tooltipDate();
            document.forms[0].openUnopenFlag.value = "${requestScope.openUnopenFlag}";
            if (window.opener != null && window.opener.authRefreshFlag != 'undefined' &&
                window.opener.authRefreshFlag == true) {
                if (parentScreen == "movementSummaryDisplay") {
                    if (window.opener.CallBackApp)
                        window.opener.CallBackApp();
                }
                if (parentScreen == "authorise") {
                    window.opener.refreshScreen('display');
                }
                window.opener.authRefreshFlag = false;
            }
        }

        /**
         * accountAccess
         *
         * This method is called when select the account id on the account drop down and
         * used to get the input access for selected account and based on the access it will
         * allow to select the account otherwise display the alert meessage to user
         */
        function accountAccess() {
            var accountId = document.forms[0].elements["movement.accountId"].value;
            if (accountId.trim() != "") {
                var entity = document.forms[0].elements["movement.id.entityId"].value;
                var accessFlag = accountAccessConfirm(accountId, entity, "Input");
                if (accessFlag == "false") {
                    ShowErrMsgWindowWithBtn("", '<fmt:message key="message.alert.acctAccessInput"/>', null);
                    return false;
                } else {
                    return true
                }
            } else {
                return false;
            }
        }

        /**
         * accountAccessConfirm
         *
         * This method is used to get the input access for given account id from database
         */
        function accountAccessConfirm(accountId, entity, status) {
            var oXMLHTTP = new XMLHttpRequest();
            var acctAccessURL = requestURL + appName + "/accountAccess.do?method=acctAccessConfirm";
            acctAccessURL = acctAccessURL + "&accountId=" + accountId;
            acctAccessURL = acctAccessURL + "&entityId=" + entity;
            acctAccessURL = acctAccessURL + "&status=" + status;
            oXMLHTTP.open("POST", acctAccessURL, false);
            oXMLHTTP.send();
            var acctAccessVal = oXMLHTTP.responseText;
            return acctAccessVal;
        }

        /**
         *
         * This method is invoked while pressing enter key on movement textbox and validate the entered movement id
         * and doing form submit to get the movement details
         *
         * @param element
         */
        function checkKey(element, e) {
            var event = (window.event || e);
            //get the movement id from the form
            var tempVal = element.value;
            if (tempVal.length > 0 && (event.keyCode == 9 || event.keyCode == 13)) {
                var strPat = 'numberPat'
                var thePat = PatternsDict[strPat];
                var gotIt = thePat.exec(element.value);
                if (gotIt != null) {
                    submitForm('displayMovement');
                } else {
                    if (window.event) {
                        window.event.returnValue = false;
                    } else {
                        event.preventDefault();
                    }
                    alert("Please enter a valid " + PatternsMetaData[strPat]);
                    //calling the emptyMovementDetails method to reload the movement display screen
                    emptyMovementDetails('display');
                    element.focus();
                    return false;
                }
            }
        }

        /**
         * populateDropBoxes
         *
         * This method is invoked on onload event of window
         */
        function populateDropBoxes() {
            populateDropDowns();
            bodyOnLoad();
        }

        /**
         * set the function which are to be invoked when window onload event is fired.
         */
        window.onload = function () {
            onLoadStatus();
            setParentChildsFocus();
            if ((parentScreen != "movementSummaryDisplay") ||
                (document.forms[0].elements["movement.id.movementIdAsString"] != null && document.forms[0].elements["movement.id.movementIdAsString"].disabled != true)) {
                setFocus(document.forms[0]);
            }
            populateDropBoxes();
            ShowErrMsgWindow('${actionError}');
        };

        /**
         * showMatch
         *
         * This method is used to show the match for that movement Id
         **/
        function showMatch(methodName) {
            //checking for valid movemenId
            if (validateMovementId()) {
                var archiveId = "${archiveId}";
                var oXMLHTTP = new XMLHttpRequest();
                //url for AJAx request
                var chkMatchURL = requestURL + appName + "/movementmatchdisplay.do?method=checkMatchId";
                chkMatchURL = chkMatchURL + "&matchId=" + document.forms[0].elements["movement.matchId"].value;
                chkMatchURL = chkMatchURL + "&movId=" + removeLeadingZeros(document.forms[0].elements["movement.id.movementIdAsString"].value);
                chkMatchURL = chkMatchURL + "&archiveId=" + archiveId;
                chkMatchURL = chkMatchURL + "&entityId=" + document.forms[0].elements['movement.id.entityId'].value;
                //sending AJAX request
                oXMLHTTP.open("POST", chkMatchURL, false);
                oXMLHTTP.send();
                //gettin response in AJAX request
                var matchVal = new String(oXMLHTTP.responseText);
                if (matchVal != "false" && matchVal != "null") {
                    alert("<fmt:message key='movement.matchChanged'/>");
                } else {
                    var showMatchURL = 'movementmatchdisplay.do?method=' + methodName;
                    showMatchURL = showMatchURL + "&entityId=" + document.forms[0].elements['movement.id.entityId'].value;
                    showMatchURL += '&movId=';
                    if (archiveId != "" && archiveId != "null") showMatchURL += removeLeadingZeros(document.forms[0].elements["movement.id.movementIdAsString"].value) + "&archiveId=${archiveId}";
                    else showMatchURL += removeLeadingZeros(document.forms[0].elements["movement.id.movementIdAsString"].value);
                    showMatchURL += '&parentScreen=' + parentScreen;
                    showMatchURL = showMatchURL + "&menuAccessId=" + document.forms[0].menuAccessId.value;
                    openWindow(showMatchURL, 'movementmatchdisplayWindow', 'left=0,top=55,width=1350,height=570,toolbar=0, resizable=yes, scrollbars=yes,status=yes');

                }
            }
        }

        /**
         * movementLog
         *
         * This method is used to open the movement log screen which having all log details for given movement id
         */
        function movementLog(methodName) {
            var auditLogURL = 'auditlog.do?method=' + methodName;
            auditLogURL += '&entityCode=';
            auditLogURL += document.forms[0].elements['movement.id.entityId'].value;
            auditLogURL += '&movId=';
            auditLogURL += removeLeadingZeros(document.forms[0].elements['movement.id.movementIdAsString'].value);
            auditLogURL += '&archiveId=${archiveId}';
            return auditLogURL;
        }


        /**
         * checkMovementId
         *
         * This method is called while clicking the log button in the movement display screen to open the movement log window
         */
        function checkMovementId() {
            if ('<%=request.getParameter("archiveId")%>' != "" && '<%=request.getParameter("archiveId")%>' != "null") {
                openWindow(movementLog('movementLog'), 'movementLogWindow', 'left=50,top=190,width=1240,height=555,toolbar=0, resizable=yes, scrollbars=yes', 'true');
            } else if (validateMovementId()) {
                openWindow(movementLog('movementLog'), 'movementLogWindow', 'left=50,top=190,width=1240,height=555,toolbar=0, resizable=yes, scrollbars=yes', 'true');
            }
        }


        /**
         * bodyUnLoad()
         * @param methodName
         *
         * Method called when a screen is closed
         **/
        function bodyUnLoad(methodName) {
            //It calls a javascript method to refresh the window
            call();
            if (screenIdentifier == "movementChange") {
                releaseLock();
            } else {
                if (parentScreen == "movementSummaryDisplay") {
                    //it refresh the parent screen
                    parentScreenRefresh();
                    if (changeFlag == false)
                        if (window.opener.CallBackApp)
                            window.opener.CallBackApp();
                }
            }
        }

        /**
         * parentScreenRefresh()
         *
         * Method is called when close the moveement display which has opened
         * from movement summary display screen ans used to set the form value
         **/
        function parentScreenRefresh() {
            var parentWindow = window.opener;
            var notesValue = document.forms[0].isNotesPresent.value;
            var notesScreenOpened = document.forms[0].notesScreenOpened.value;
            if (notesScreenOpened != "Y") notesValue = '${requestScope.isNotesPresent}';
            if (notesValue != "Y") {
                notesValue = "";
            }
            var isNotesPresent = document.forms[0].isNotesPresent.value;
            if (isNotesPresent == 'Y') {
                notesValue = document.forms[0].noOfNotes.value;
            }
            enableFields();
            var movId = removeLeadingZeros(document.forms[0].elements["movement.id.movementIdAsString"].value);
        }

        /**
         * refreshMovmentNoteImage
         *
         * This method is used to update the notes image if there any notes available
         * for movement and it will be invoked from notes window
         **/
        function refreshMovmentNoteImage() {

            <c:if test="${requestScope.showMovementId == 'yes'}">
            var isNotesPresent = document.forms[0].isNotesPresent.value;
            var notesIndicator = document.getElementById("notesIndicator");
            if (isNotesPresent == 'Y') {
                notesIndicator.src = "images/notes.gif"
                notesIndicator.title = ' <fmt:message key="msg.title.notesAvailable"/>';

            }
            if (isNotesPresent != 'Y') {
                notesIndicator.src = "images/blank.png"
                notesIndicator.title = "";
            }
            </c:if>
        }

        /**
         * party
         *
         * This method is used to open the party search screen when click on the
         * counterparty/beneficiary/custodian/matching party drop down button
         **/
        function party(flag, elementId, elementName) {
            var entityId = document.forms[0].elements['movement.id.entityId'].value;
            var partyURL = 'party.do?method=preSearchParties&entityId=' + entityId;
            partyURL += '&custodianFlag=' + flag;
            partyURL += '&idElementName=' + elementId;
            partyURL += '&descElementName=' + elementName;
            openWindow(partyURL, 'SearchParty', 'left=50,top=190,width=509,height=579,toolbar=0,resizable=yes,scrollbars=yes', 'true');
        }

        /**
         * clearCounterPartyDesc
         *
         * This method is called on change of the counter party and used to clear the
         * description of the counterparty
         **/

        function clearCounterPartyDesc() {
            document.getElementById("counterPartyDesc").innerText = "";
        }

        /**
         * clearBeneficiaryDesc
         *
         * This method is called on change of the beneficiary and used to clear the
         * description of the beneficiary
         **/

        function clearBeneficiaryDesc() {
            document.getElementById("beneficiaryName").innerText = "";
        }

        /**
         * clearCustodianDesc
         *
         * This method is called on change of the custodian and used to clear the
         * description of the custodian
         **/

        function clearCustodianDesc() {
            document.getElementById("custodianName").innerText = "";
        }


        function clearBeneficiaryCustomerDesc() {
            document.getElementById("beneficiaryCustomerName").innerText = "";
        }

        function clearOrderingCustomerDesc() {
            document.getElementById("orderingCustomerName").innerText = "";
        }

        function clearOrderingInstitutionDesc() {
            document.getElementById("orderInstName").innerText = "";
        }

        function clearSenderCorrespondentDesc() {
            document.getElementById("senderCorrName").innerText = "";
        }

        function clearReceiverCorrespondentDesc() {
            document.getElementById("receiverCorrName").innerText = "";
        }

        function clearIntermediaryInstituationDesc() {
            document.getElementById("intermediaryInstitutionName").innerText = "";
        }

        function clearAccountwithInstitutionDesc() {
            document.getElementById("accountWithInstitutionName").innerText = "";
        }

        function clearBeneficiaryCustomerDesc() {
            document.getElementById("beneficiaryCustomerName").innerText = "";
        }


        var dateFormat = '${sessionScope.CDM.dateFormat}';
        var currencyFormat = '${sessionScope.CDM.currencyFormat}';

        function tooltipDate() {
            if (dateFormat == 'datePat2') {
                document.getElementById("movement.valueDateAsString").titleKey = "tooltip.ValueDateMMDDYY";
            } else {
                document.getElementById("movement.valueDateAsString").titleKey = "tooltip.enterValueDate";
            }
        }

        /**
         * cancelChanges
         *
         * This method is called when click on the cancel button in the movement change screen
         * and used to cancel the updte operation for movement
         **/
        function cancelChanges() {
            document.forms[0].elements["movement.id.entityId"].disabled = "";
            document.forms[0].elements["movement.id.movementIdAsString"].disabled = "";
            //Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing
            document.forms[0].initialInputScreen.value = initialinputscreen;
            //check if parent screen is Movement summary display
            if (parentScreen == "movementSummaryDisplay") {
                document.forms[0].movementId.value = document.forms[0].elements["movement.id.movementIdAsString"].value;
                document.forms[0].method.value = "showMovementDetails";
            } else {
                document.forms[0].method.value = "displayMovement";
            }
            document.forms[0].parentScreen.value = parentScreen;
            document.forms[0].submit();
        }

        /**
         * openreconConfirmationbox
         *
         * This method is called when click on the recon button in the movement display screen
         * and used to update the movement status to reconcile
         **/
        function openreconConfirmationbox(methodName) {
            if (validateMovementId()) {
                var lockVal = checkLock();
                if (lockVal == "true") {
                    acquireLock();

                    var yourstate = window.confirm('<fmt:message key="confirm.recon"/>');
                    if (yourstate == true) {
                        <c:if test="${requestScope.parentScreen == 'movementSummaryDisplay'}">
                        document.forms[0].parentScreen.value = "movementSummaryDisplay";
                        enableFields();
                        document.forms[0].movementId.value = document.forms[0].elements["movement.id.movementIdAsString"].value;
                        </c:if>
                        document.forms[0].method.value = methodName;
                        document.forms[0].submit();
                    }
                    releaseLock();
                } else {
                    disableButtonsForLock(lockVal);
                }
            }
        }

        /**
         * openNotes
         *
         * This method is called when click on the notes button in the movement display screen
         * and used to open movement notes window to display the notes details for movement
         **/
        function openNotes() {
            if ('<%=request.getParameter("archiveId")%>' != "" && '<%=request.getParameter("archiveId")%>' != "null") {
                openWindow(buildMovement('notes', 'Y'), 'movementNotesWindow', 'left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes');
            } else if (validateMovementId()) {
                var lockVal = checkLock();
                if ((initialscreen != "E" && parentScreen == "movementSummaryDisplay") && initialscreen != "movementmatchsummarydisplay") {
                    var chkLockURL = requestURL + appName + "/movementLock.do?method=checkLock";
                    var oXMLHTTP = new XMLHttpRequest();
                    chkLockURL = chkLockURL + "&movementId=" + document.forms[0].elements["movement.id.movementIdAsString"].value;
                    oXMLHTTP.open("POST", chkLockURL, false);
                    oXMLHTTP.send();
                    lockVal = new String(oXMLHTTP.responseText);
                }
                if (lockVal == "true") {
                    lockVal = acquireLock();
                    openWindow(buildMovement('notes', 'N'), 'movementNotesWindow', 'left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes');
                } else {
                    openWindow(buildMovement('notes', 'Y'), 'movementNotesWindow', 'left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes');
                }
            }
        }

        /**
         * openCrossRef
         *
         * This function is used to open the Cross refrence display screen and
         * used to display the cross reference for corresponding movement
         **/
        function openCrossRef() {
            //Condition for checking weather the Movement id is valid or not
            if (validateMovementId()) {
                <c:if test="${requestScope.showMovementId == 'yes'}">
                document.forms[0].selectedMovementId.value = document.forms[0].elements["movement.id.movementIdAsString"].value;
                var entityId = document.forms[0].elements['movement.id.entityId'].value
                </c:if>
                //Url framing to open new screen
                var crossRefURL = 'movement.do?method=crossReference&movId=';
                crossRefURL += removeLeadingZeros(document.forms[0].selectedMovementId.value);
                crossRefURL += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;
                crossRefURL += '&screenName=' + 'manualInputScreenViewMode';
                //request to open new window
                openWindow(crossRefURL, 'movementNotesWindow', 'left=170,top=210,width=987,height=405,toolbar=0, resizable=yes, scrollbars=yes');
            }
        }

        /**
         * checkLock
         *
         * This function is used to check the lock for given movement.
         * if movement is used by some other user then return that user id otherwise return true
         **/
        function checkLock() {
            var lockVal = "true";
            //Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing
            //condition to check whether the initial screen is Excluded Outstanding/MMSD screen.if not proceed for the Lock
            if (initialinputscreen != "E" && initialscreen != "movementmatchsummarydisplay") {
                var chkLockURL = requestURL + appName + "/movementLock.do?method=checkLock";
                var oXMLHTTP = new XMLHttpRequest();
                chkLockURL = chkLockURL + "&movementId=" + document.forms[0].elements["movement.id.movementIdAsString"].value;
                oXMLHTTP.open("POST", chkLockURL, false);
                oXMLHTTP.send();
                lockVal = new String(oXMLHTTP.responseText);

            }
            return lockVal;
        }


        /**
         * acquireLock
         *
         * This function is used to acquire lock for given movement.
         * If user going to access the movement then this function provide lock for that movement with specified user
         *
         **/
        function acquireLock() {
            var lockVal = "true";
            //Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing
            //condition to check whether the initial screen is Excluded Outstanding/MMSD screen.if not proceed for the Lock
            if (initialinputscreen != "E" && initialscreen != "movementmatchsummarydisplay") {
                var lockMovURL = requestURL + appName + "/movementLock.do?method=lockMovement";
                var oXMLHTTP = new XMLHttpRequest();
                lockMovURL = lockMovURL + "&movementId=" + document.forms[0].elements["movement.id.movementIdAsString"].value;
                oXMLHTTP.open("POST", lockMovURL, false);
                oXMLHTTP.send();
                lockVal = new String(oXMLHTTP.responseText);
            }
            return lockVal;
        }

        /**
         * releaseLock
         *
         * This function is used to release lock for given movement.
         * once user done changes in the movement then it should get the release from lock
         **/
        function releaseLock() {
		//Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing
		//condition to check whether the initial screen is Excluded Outstanding/MMSD screen.if not proceed for the Lock
		if (initialinputscreen != "E" && initialscreen != "movementmatchsummarydisplay") {
			beaconUnlock(document.forms[0].elements["movement.id.movementIdAsString"].value);
		}
	}


	// For page unload cases
    function beaconUnlock(movementIds) {
        try {
   			 const sURL = requestURL + appName + "/movementLock.do?method=unlockMovements";

            // Create the data to be sent
            const formData = new FormData();
            formData.append('movementIds', movementIds);
            formData.append('method', 'unlockMovements');

            // Send using beacon API
            const success = navigator.sendBeacon(sURL, formData);
            return success ? "success" : "error";
        } catch (error) {
            console.error('Error during beacon unlock:', error);
            return "error";
        }
    }


        /**
         * disableButtonsForLock
         *
         * This function is used to disable the change,recon,open,unoopen button when entered movement is in lock
         **/
        function disableButtonsForLock(lockUser) {
            ShowErrMsgWindowWithBtn("", '<fmt:message key="alert.MovementInUse"/>' + lockUser + ' ' + '<fmt:message key="alert.Viewonlyaccess"/>', null);
            if (document.getElementById("changebutton") != null) {
                document.getElementById("changebutton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>';
            }//changebutton
            if (document.getElementById("reconbutton") != null) {
                document.getElementById("reconbutton").innerHTML = document.getElementById("recondisablebutton").innerHTML;
            }//reconbutton
            if (document.getElementById("openUnopenButton") != null) {
                <%if ( SwtConstants.YES.equals(request.getAttribute(SwtConstants.OPEN_UNOPEN_FLAG))	) {	%>
                document.getElementById("openUnopenButton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.Unopen"/></a>';
                <%}%>
                <%if ( SwtConstants.NO.equals(request.getAttribute(SwtConstants.OPEN_UNOPEN_FLAG))	) {	%>
                document.getElementById("openUnopenButton").innerHTML = '<a class="disabled" disabled="disabled"><fmt:message key="button.Open"/></a>';
                <%}%>
            }//openunopen
        }

        /**
         * onChangeButtonClick
         *
         * This function is called when click on change button in the movement display screen and
         * used to change the details for entered movement
         **/
        function onChangeButtonClick() {
            //check for valid movement id
            if (validateMovementId()) {
                if (document.forms[0].elements["movement.id.movementIdAsString"].value.trim() == "") {
                    alert('<fmt:message key="match.addMvmt.mvmtIdempty"/>');
                } else if (document.forms[0].elements["movement.matchId"].value != null && document.forms[0].elements["movement.matchId"].value != "") {
                    alert("<fmt:message key='movement.mostUnmatched'/>");
                } else {
                    //checking Lock
                    var lockVal = checkLock();
                    if (lockVal == "true") {
                        lockVal = acquireLock();
                        //set the change flag as true
                        changeFlag = true;
                        //Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing
                        document.forms[0].initialInputScreen.value = initialinputscreen;
                        submitForm('change');
                    } else {
                        disableButtonsForLock(lockVal);
                    }
                }

            }
        }

        /**
         * validateMovementId
         *
         * This function is used to validate the movement if entered movement is not
         * in the database then display the alet msg to user
         **/
        function validateMovementId() {
            if (validateField(document.forms[0].elements["movement.id.movementIdAsString"], 'movementId', 'numberPat')) {
                //Check the movement Id is available in the table
                if (document.forms[0].elements["movement.id.movementIdAsString"].value.trim() == "") {
                    alert('<fmt:message key="match.addMvmt.mvmtIdempty"/>');
                    //calling the emptyMovementDetails method to reload the movement display screen
                    emptyMovementDetails('display');
                    return false;
                } else {
                    var archiveId = "${archiveId}";
                    // Frame the Ajax request url
                    var oXMLHTTP = new XMLHttpRequest();
                    var chkMovURL = requestURL + appName + "/movement.do?method=checkMovementId";
                    chkMovURL = chkMovURL + "&movId=" + removeLeadingZeros(document.forms[0].elements["movement.id.movementIdAsString"].value);
                    chkMovURL = chkMovURL + "&archiveId=" + archiveId;
                    chkMovURL = chkMovURL + "&entityId=" + document.forms[0].elements['movement.id.entityId'].value;
                    oXMLHTTP.open("POST", chkMovURL, false);
                    oXMLHTTP.send();
                    var lockVal = new String(oXMLHTTP.responseText);
                    if (lockVal == "false") {
                        alert('<fmt:message key="manualInput.alert.mvmNotonFile"/>');
                        //calling the emptyMovementDetails method to reload the movement display screen
                        emptyMovementDetails('display');
                        return false;
                    } else if (enteredMovId != null && enteredMovId != removeLeadingZeros(document.forms[0].elements["movement.id.movementIdAsString"].value)) {
                        alert('<fmt:message key="manualInput.alert.mvmIdamended"/>');
                        submitForm('displayMovement');
                        return false;
                    } else {
                        return true;
                    }
                }
            }
            //calling the emptyMovementDetails method to reload the movement display screen
            emptyMovementDetails('display');
        }


        function removeLeadingZeros(movementId) {
            return movementId.replace(/^[0]+/g, "");
        }

        /**
         *
         * This method is used to submit empty form for reloading movement display screen when movement id is invalid
         * @param method
         */
        function emptyMovementDetails(method) {
            //empty the movemnt id in form
            document.forms[0].elements["movement.id.movementIdAsString"].value = "";
            document.forms[0].method.value = method;
            //Submitting Form to empty the movement details when moventId is invalid
            document.forms[0].submit();
        }

        /**
         *
         * This method is used to validate the movement id is numeric value
         * @param method
         */
        function onChangeMovementId() {
            if (!validateField(document.forms[0].elements["movement.id.movementIdAsString"], 'movementId', 'numberPat')) {
                emptyMovementDetails('display');
            }
        }

        /**
         * checkExternalPositionLevels
         *
         * This  method is used for check the selected position level is External balance
         **/
        function checkExternalPositionLevels() {
            var oXMLHTTP = new XMLHttpRequest();
            var extPosLvlURL = requestURL + appName + "/movement.do?method=checkExternalPositionLevels";
            // setting position level
            extPosLvlURL = extPosLvlURL + "&position=" + document.forms[0].elements["movement.positionLevelAsString"].value;
            // setting entityID
            extPosLvlURL = extPosLvlURL + "&entityId=" + document.forms[0].elements["movement.id.entityId"].value;
            oXMLHTTP.open("POST", extPosLvlURL, false);
            oXMLHTTP.send();
            //variable for the string
            var extPostionLvl = new String(oXMLHTTP.responseText);
            if (extPostionLvl != document.forms[0].elements["movement.positionLevelAsString"].value) {
                // checking the current external balance value
                for (i = 0; i < document.forms[0].elements["movement.extBalStatus"].length; i++) {
                    if (document.forms[0].elements["movement.extBalStatus"][i].value == "<%=SwtConstants.PREDICT_INC%>") {
                        document.forms[0].elements["movement.extBalStatus"][1].checked = true;
                    }
                }
            } else {
                // checking the current external balance value
                for (i = 0; i < document.forms[0].elements["movement.extBalStatus"].length; i++) {
                    if (document.forms[0].elements["movement.extBalStatus"][i].value == "<%=SwtConstants.PREDICT_EXC%>") {
                        document.forms[0].elements["movement.extBalStatus"][0].checked = true;
                    }
                }
            }
            return extPostionLvl;
        }

        /**
         * getAccountList
         *
         * This method is used to get the collection of account list while
         * clicking the account drop down button and populate the collection in the drop down box
         */
        function getAccountList(event) {
            if (accountClk) {
                //get the all elements for selectbox
                var oXMLHTTP = new XMLHttpRequest();
                var divElement = document.getElementById("dropdowndiv_2");
                var idElement = document.forms[0].elements["accountId"];
                var descElement = document.forms[0].elements["accountDesc"];
                var arrowElement = document.forms[0].elements["dropdownbutton_2"];
                acctSelectElement = document.forms[0].elements["movement.accountId"];
                var movType;
                var radioMovType = document.forms[0].elements["movement.movementType"];
                if (radioMovType[0].checked == true) {
                    movType = radioMovType[0].value;
                } else {
                    movType = radioMovType[1].value;
                }
                var accountURL = requestURL + appName + "/movement.do?method=getAccountsList";
                accountURL = accountURL + "&entityId=" + document.forms[0].elements["movement.id.entityId"].value;
                accountURL = accountURL + "&currencyCode=" + document.forms[0].elements["movement.currencyCode"].value;
                accountURL = accountURL + "&movType=" + movType;
                oXMLHTTP.open("POST", accountURL, false);
                //send the request for list
                oXMLHTTP.send();
                //get the response text
                var listValue = new String(oXMLHTTP.responseText);
                listValue = listValue.split('\n');
                if (acctSelectElement.options.length > 0) {
                    acctSelectElement.remove(0);
                }
                var index = 0;
                //add the main account into option
                for (var i = 0; i < listValue.length - 1; i++) {
                    var lblValue = listValue[i].split('~~~');
                    var optionElement = document.createElement("option");
                    optionElement.text = lblValue[0];
                    optionElement.value = lblValue[1];
                    if (optionElement.value == accountId && (idElement.value.length > 0)) index = i;
                    acctSelectElement.options.add(optionElement);
                }
                //set the selected index
                acctSelectElement.selectedIndex = index;
                //frame the selectbox component
                acctSwSelectBox = new SwMainSelectBox(divElement, acctSelectElement, idElement, descElement, arrowElement, 12, 30);
                acctSwSelectBox.setClickFlag();
                //call to populate the list in the list box
                acctSwSelectBox.arrowElementOnClick(event);
                accountClk = false;

            }
        }


        /**
         * getBookCodeList
         *
         * This method is used to get the collection of book code list while
         * clicking the book code drop down button and populate the collection in the drop down box
         */
        function getBookCodeList(event) {

            if (bookCodeClk) {
                //get the all elements for selectbox
                var oXMLHTTP = new XMLHttpRequest();
                var divElement = document.getElementById("dropdowndiv_3");
                var idElement = document.forms[0].elements["bookCode"];
                var descElement = document.forms[0].elements["bookCodeDesc"];
                var arrowElement = document.forms[0].elements["dropdownbutton_3"];
                bookCodeSelElement = document.forms[0].elements["movement.bookCode"];
                var bookCodeURL = requestURL + appName + "/movement.do?method=getBookCodeList";
                bookCodeURL = bookCodeURL + "&entityId=" + document.forms[0].elements["movement.id.entityId"].value;
                oXMLHTTP.open("POST", bookCodeURL, false);
                //send the request for list
                oXMLHTTP.send();
                //get the response text
                var listValue = new String(oXMLHTTP.responseText);
                listValue = listValue.split('\n');
                if (bookCodeSelElement.options.length > 0) {
                    bookCodeSelElement.remove(0);
                }
                var index = 0;
                //add the main account into option
                for (var i = 0; i < listValue.length - 1; i++) {
                    var lblValue = listValue[i].split('~~~');
                    var optionElement = document.createElement("option");
                    optionElement.text = lblValue[0];
                    optionElement.value = lblValue[1];
                    if (optionElement.value == bookCodeId) index = i;
                    bookCodeSelElement.options.add(optionElement);
                }
                //set the selected index
                bookCodeSelElement.selectedIndex = index;
                //frame the selectbox component
                bookCodeSwSelBox = new SwMainSelectBox(divElement, bookCodeSelElement, idElement, descElement, arrowElement, 12, 30);
                bookCodeSwSelBox.setClickFlag();
                //call to populate the list in the list box
                bookCodeSwSelBox.arrowElementOnClick(event);
                bookCodeClk = false;
            }
        }

        /**
         * onChangeCurrencyOrType
         *
         * This method is called while changing the currency code to empty the collection in list box.
         */
        function onChangeCurrencyOrType() {

            accountClk = true;
            //clear the account list and value
            if (acctSelectElement != null && acctSelectElement.options != null) acctSelectElement.options.length = 0;
            if (acctSwSelectBox != null) {
                acctSwSelectBox.arrowElement.onclick = getAccountList;
            }
            if (document.forms[0].elements['movement.amountAsString'].value != "") {

                var amount = validateCurrency(document.forms[0].elements['movement.amountAsString'], 'movement.amountAsString', currencyFormat, document.forms[0].elements['movement.currencyCode'].value);

            }
            document.forms[0].elements["accountId"].value = "";
            document.forms[0].elements["accountDesc"].value = "";
            //clear the book code list and value
            if (bookCodeSelElement != null && bookCodeSelElement.options != null) bookCodeSelElement.selectedIndex = 0;
            if (bookCodeSwSelBox != null) {
                bookCodeSwSelBox.idElement.value = "";
                bookCodeSwSelBox.descElement.value = "";
            }
        }

        /**
         * buildMovementChange
         *
         * This method is called while clicking the notes button in the movement change screen and
         * used to show the notes details for movement
         */
        function buildMovementChange(methodName) {
            document.forms[0].selectedMovementId.value = document.forms[0].elements["movement.id.movementIdAsString"].value;
            var entityId = document.forms[0].elements['movement.id.entityId'].value;
            var notesURL = 'notes.do?method=' + methodName + '&movId=';
            notesURL += removeLeadingZeros(document.forms[0].selectedMovementId.value);
            notesURL += '&entityCode=' + document.forms[0].elements['movement.id.entityId'].value;
            notesURL += '&screenName=' + 'manualInputScreenViewMode';
            notesURL += '&parentScreen=changemovement';
            return notesURL;
        }

        /**
         * validateChangeForm
         *
         * This validation method wiil be invoked while going to update the details of the movement.
         */
        function validateChangeForm(objForm) {
            if (amountEditStatus == "false") {
                if (valueDateEditStatus == "false") {
                    var elementsRef = new Array(1);
                    elementsRef[0] = objForm.elements["movement.amountAsString"];
                    elementsRef[1] = objForm.elements["movement.valueDateAsString"];
                } else if (valueDateEditStatus == "true") {
                    var elementsRef = new Array(1);
                    elementsRef[0] = objForm.elements["movement.amountAsString"];
                }
            } else if (amountEditStatus == "true") {
                if (valueDateEditStatus == "false") {
                    var elementsRef = new Array(1);
                    elementsRef[0] = objForm.elements["movement.valueDateAsString"];
                } else if (valueDateEditStatus == "true") {
                    var elementsRef = new Array(0);
                }
            }

            if (document.getElementById("accountDesc").value == "") {
                alert("<fmt:message key="positionLevel.alert.mandatoryFields"/>");
                document.getElementById("dropdownbutton_2").focus();
                return false;
            }

            if (validateCurrency(document.forms[0].elements['movement.amountAsString'], 'movement.amountAsString', currencyFormat, document.forms[0].elements['movement.currencyCode'].value))
                return validate(elementsRef);
        }

        function changeBorderColor() {

            if (screenIdentifier == "manualInput" || screenIdentifier == "movementChange" || accountEditStatus == "false") {
                var element_ = document.getElementById('accountId');
                if (element_ != null && typeof element_ != 'undefined') {
                    element_.style.border = element_.value == "" ? "red 1px solid" : "#a9a9a9 1px solid";
                }

                var elementAmount = document.forms[0].elements['movement.amountAsString'];
                if (elementAmount != null && typeof elementAmount != 'undefined')
                    elementAmount.style.borderColor = elementAmount.value == "" ? "red" : "";

                var elementDateValue = document.forms[0].elements['movement.valueDateAsString'];
                if (elementDateValue != null && typeof elementDateValue != 'undefined')
                    elementDateValue.style.borderColor = elementDateValue.value == "" ? "red" : "";
            }
        }

        var tooltipFacilityId;
        var tooltipMvtId;

        function openAlertingWindow() {
            var requestURL = "movement.do";
            requestURL += "?method=openAlerting";
            tooltipFacilityId = 'MOVEMENT_DISPLAY';
            tooltipMvtId = document.forms[0].elements["movement.id.movementIdAsString"].value;
            requestURL += "&tooltipFacilityId=" + tooltipFacilityId;
            requestURL += "&tooltipMvtId=" + tooltipMvtId;
            openWindow(requestURL, 'openAlertingJSP', 'left=10,top=230,width=450,height=480,toolbar=0, resizable=yes, //status=yes, scrollbars=yes');
        }

        function updateIlmFcastStatus(element) {
            if (screenIdentifier == "manualInput") {
                val = element.value == "<%=SwtConstants.PREDICT_CAN%>" ? "<%=SwtConstants.PREDICT_EXC%>" : element.value;
                document.forms[0].elements["movement.ilmFcastStatus"].value = val;
            }
        }


        function validateUETR() {
            var pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-(8|9|a|b)[0-9a-f]{3}-[0-9a-f]{12}$/;
            if (pattern.test(document.getElementById("movement.uetr").value)) {
                return true;
            } else {
                alert("Invalid UETR format!");
                return false;
            }
        }


    </SCRIPT>

</head>

<form action="movement.do" method="post">
    <!-- Store transaction token key in hidden field to avoid duplicate form submission. -->
    <%
        if (request.getSession().getAttribute(TokenHelper.TOKEN_NAME_FIELD) != null) {
            String tokenName = "" + request.getSession().getAttribute(TokenHelper.TOKEN_NAME_FIELD);
            String tokenValue = "" + request.getSession().getAttribute(tokenName);
    %>
    <input type="hidden" name="<%=TokenHelper.TOKEN_NAME_FIELD%>"
           value="<%=tokenName%>">
    <input type="hidden" name="<%=tokenName%>"
           value="<%=tokenValue%>">


    <%
        }
    %>
    <%
        if (!screenIdentifier.equals("movementChange")) {
    %>
    <input type="hidden" name="movement.matchId" value="${movement.matchId}"/>
    <input type="hidden" name="movement.matchStatus" value="${movement.matchStatus}"/>
    <input type="hidden" name="movement.isManyMessages" value="${movement.isManyMessages}"/>
    <%
        }
    %>
    <input name="selectedMovementId" type="hidden">
    <input name="selectedEntityId" type="hidden">
    <input name="method" type="hidden" value="display">
    <input name="buttonDiv" type="hidden" value="GBP">
    <input name="matchType" type="hidden" value="">
    <input name="showMovementId" type="hidden" value="">
    <input name="isNotesPresent" type="hidden" value="">
    <input name="notesScreenOpened" type="hidden" value="">
    <input name="noOfNotes" type="hidden" value="">
    <input name="screenIdentifier" type="hidden" value="">
    <input name="parentScreen" type="hidden" value="">
    <input name="movementId" type="hidden" value="">
    <!-- Code Modified by Chinniah for Mantis 2066 on 4-Oct-2012:Movement is not locked while changing -->
    <input name="initialInputScreen" type="hidden" value="">


    <input name="menuAccessId" type="hidden">
    <input name="openUnopenFlag" type="hidden">

    <body onmousemove="reportMove()" leftmargin="0" topmargin="0"
          marginheight="0" onunload="bodyUnLoad('clearSession')">
    <div id="caldiv"
         style="z-index: 99; position: absolute; visibility: visible; background-color: white; layer-background-color: white;"></div>
    <iframe id="calFrame" src="javascript:false;" scrolling="no"
            frameborder="0"
            style="position: absolute; top: 0px; left: 0px; display: none;">
    </iframe>
    <div id="dropdowndiv_2"
         style="position: absolute; width: 265px; left: 154px; top: 198px; visibility: hidden; z-index: 99"
         class="swdropdown"><select
            id="accountDropDown"
            name="movement.accountId"
            class="htmlTextFixed"
            size="10"
            style="width:329px;z-index:99"
            onclick="accountAccess()">
    </select></div>
    <div id="dropdowndiv_3"
         style="position: absolute; width: 265px; left: 154px; top: 237px; visibility: hidden; z-index: 99"
         class="swdropdown"><select
            class="htmlTextFixed"
            name="movement.bookCode"
            size="10"
            style="width:329px;z-index:99">
    </select></div>
    <div id="divMovementDisplay"
         style="position: absolute; left: 20px; top: 5px; width: 937px; height: 65px; border: 2px outset;"
         color="#7E97AF">
        <div id="MovementDisplay1"
             style="position: absolute; top: 1px; width: 920px; height: 180px; left: 0px">

            <table width="726" border="0" style="padding-left: 10px" cellpadding="0" cellspacing="0">
                <tr height="30px">
                    <td width="123px"><b>&nbsp;<fmt:message key="manualInput.entity.id"/></b></td>

                    <td id="entityIdtooltip" width="137px">
                        <%
                            if (screenIdentifier.equals("manualInput")) {
                        %>
                        <select
                                name="movement.id.entityId"
                                titleKey="tooltip.selectEntityId"
                                class="htmlTextAlpha"
                                onchange="submitForm('displayListByEntity');"
                                style="width:119px;"
                                tabindex="1">
                            <c:forEach var="entity" items="${requestScope.entities}">
                                <option value="${entity.value}"
                                        <c:if test="${movement.id.entityId == entity.value}">selected</c:if>>
                                        ${entity.label}
                                </option>
                            </c:forEach>
                        </select>
                        <%
                        } else {
                        %>
                        <input
                                type="text"
                                name="movement.id.entityId"
                                class="htmlTextAlpha"
                                readonly="readonly"
                                style="width:120px;background:transparent;border:thin;"
                                tabindex="-1"
                                value="${movement.id.entityId}"/>
                        <%
                            }
                        %>
                    </td>

                    <td width="460px"><span id="entityDesc" name="entityDesc"
                                            class="spantext"></td>
                </tr>
            </table>
            <%
                if (!screenIdentifier.equals("manualInput")) {
            %>
            <table width="750px" style="padding-left: 10px;" border="0" cellpadding="0" cellspacing="0"
                   height="30">
                <tr height="21px">
                    <td width="111px"><b>&nbsp;<fmt:message key="manualInput.id.movementId"/></b></td>
                    <td align="left" width="96px"><input
                            type="text"
                            name="movement.id.movementIdAsString"
                            class="htmlTextNumeric"
                            maxlength="12"
                            tabindex="1"
                            titleKey="tooltip.enterMvmId"
                            onchange="onChangeMovementId();"
                            style="width:99px;"
                            onkeydown="checkKey(this, event)"
                            value="${movement.id.movementIdAsString}"/></td>
                    <td width="20px">
                        <c:if test="${not empty  movement}">
                            <c:if test="${requestScope.movement.scenarioHighlighted == 'C'}">
                                <img onclick="setTimeout(() => { openAlertingWindow() }, 100);" style=""
                                     src="images/Alert/scenario/critical.png" align="left" name="imgLoro" border="0">
                            </c:if>
                            <c:if test="${requestScope.movement.scenarioHighlighted == 'Y'}">
                                <img onclick="setTimeout(() => { openAlertingWindow() }, 100);" style=""
                                     src="images/Alert/scenario/normal.png" align="left" name="imgLoro" border="0">
                            </c:if>
                        </c:if>
                    </td>
                    <td width="24px">&nbsp;</td>
                    <td width="416px"><b><input type="text"
                                                class="htmlTextAlpha"
                                                tabindex="-1"
                                                name="movement.matchStatusDesc"
                                                value="${movement.matchStatusDesc}"
                                                readonly="readonly"
                                                style="width:378px;background:transparent;color:green;border:thin;"/></b>
                    </td>
                    <td width="20px"><img id="notesIndicator" src='images/blank.png'
                                          border="0" title=""></td>

                </tr>
            </table>
            <%
                }
            %>

            <div id="ddimagetabs"
                 style="position: absolute;  top: 70px; width: 920px; height: 20px;">

                <a href="#" onmouseout="revertback('sc1',this);" tabindex="3"
                   onmouseover="changecontent('sc1',this)"
                   onClick="changeselected('sc1');expandcontent('sc1', this)"><b><fmt:message
                        key="entity.generalTab"/></b></a> <a href="#"
                                                             onmouseout="revertback('sc2',this);" tabindex="13"
                                                             onmouseover="changecontent('sc2',this)"
                                                             onClick="changeselected('sc2');expandcontent('sc2', this);"><b><fmt:message
                    key="movementDisplay.parties"/></b></a></div>
            <div id="Line"
                 style="position: absolute; left: 163px; top: 87px; width: 773px; height: 20px;">
                <table width="100%">
                    <tr>
                        <td><img src="images/tabline.gif" width="100%" height="1"></td>
                    </tr>
                </table>
            </div>

            <div id="tabcontentcontainer"
                 style="position: absolute; top: 90px; width: 932px; height: 620px;">
                <div id="sc1" class="tabcontent">
                    <div style="position: absolute; left: 6px; top: 5px; width: 900px;">
                        <div style="left:8px; top:4px; height: 575px;">
                            <div id="divMovStatus"
                                 style="position: absolute; left: 500px; top: 8px; width: 100px; height: 160px;">
                                <div id="MovementDisplay1"
                                     style="position: absolute; top: 0px;  width: 400px; height: 162px;">

                                    <%
                                        if (!screenIdentifier.equals("manualInput")) {
                                    %>
                                    <div
                                            style="position: absolute; left: 9px;   width: 50px;">
                                        <fieldset style="width: 105px; border: 2px groove; height: 93px;">
                                            <legend><fmt:message key="movementDisplay.initPredStatus"/></legend>
                                            <table width="100px" border="0" cellspacing="2" cellpadding="0" height="20">
                                                <tr height="22px">
                                                    <td width="100px">
                                                        <input type="radio"
                                                               id="11"
                                                               style="width:23px"
                                                               tabindex="21"
                                                               name="movement.initialPredStatus"
                                                               value="I"
                                                               <c:if test="${movement.initialPredStatus == 'I'}">checked</c:if>
                                                               disabled="true"/>
                                                        <label titleKey="tooltip.includeMvmInInitMonitors" for="11">
                                                            <fmt:message key="movementDisplay.included"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                                <tr height="25px">
                                                    <td width="100px">
                                                        <input type="radio"
                                                               id="12"
                                                               style="width:23px"
                                                               tabindex="22"
                                                               name="movement.initialPredStatus"
                                                               value="E"
                                                               <c:if test="${movement.initialPredStatus == 'E'}">checked</c:if>
                                                               disabled="true"/>
                                                        <label titleKey="tooltip.exlMvmInitMonitors" for="12">
                                                            <fmt:message key="movementDisplay.excluded"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td width="100px">
                                                        <input type="radio"
                                                               id="13"
                                                               style="width:23px"
                                                               tabindex="23"
                                                               name="movement.initialPredStatus"
                                                               value="C"
                                                               <c:if test="${movement.initialPredStatus == 'C'}">checked</c:if>
                                                               disabled="true"/>
                                                        <label titleKey="tooltip.cancelMvm" for="13">
                                                            <fmt:message key="movementDisplay.cancelled"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                            </table>

                                        </fieldset>
                                    </div>
                                    <%
                                        }
                                    %>

                                    <div
                                            style="position: absolute; left: 150px;   width: 50px;">
                                        <fieldset style="width: 105px; border: 2px groove; height: 93px;">
                                            <legend><fmt:message key="movementDisplay.predict"/></legend>


                                            <table width="100px" border="0" cellspacing="2" cellpadding="0" height="20">
                                                <tr height="22px">
                                                    <td width="100px">
                                                        <input
                                                                type="radio"
                                                                id="3"
                                                                name="movement.predictStatus"
                                                                value="I"
                                                                tabindex="14"
                                                                style="width:23px"
                                                                titleKey="tooltip.includeMvmInMonitors"
                                                        ${movement.predictStatus == 'I' ? 'checked' : ''}
                                                        ${screenFieldsStatus ? 'disabled="disabled"' : ''}
                                                                onclick="updateIlmFcastStatus(this)"
                                                        />
                                                        <label
                                                                titleKey="tooltip.includeMvmInMonitors"
                                                                for="3"
                                                        >
                                                            <fmt:message key="movementDisplay.included"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                                <tr height="25px">
                                                    <td width="100px">
                                                        <input
                                                                type="radio"
                                                                id="5"
                                                                name="movement.predictStatus"
                                                                value="E"
                                                                tabindex="15"
                                                                style="width:23px"
                                                                titleKey="tooltip.exlMvmDealerMons"
                                                        ${movement.predictStatus == 'E' ? 'checked' : ''}
                                                        ${screenFieldsStatus ? 'disabled="disabled"' : ''}
                                                                onclick="updateIlmFcastStatus(this)"
                                                        />
                                                        <label
                                                                titleKey="tooltip.exlMvmDealerMons"
                                                                for="5"
                                                        >
                                                            <fmt:message key="movementDisplay.excluded"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td width="100px">
                                                        <input
                                                                type="radio"
                                                                id="6"
                                                                name="movement.predictStatus"
                                                                value="C"
                                                                tabindex="16"
                                                                style="width:23px"
                                                                titleKey="tooltip.cancelMvm"
                                                        ${movement.predictStatus == 'C' ? 'checked' : ''}
                                                        ${screenFieldsStatus ? 'disabled="disabled"' : ''}
                                                                onclick="updateIlmFcastStatus(this)"
                                                        />
                                                        <label
                                                                titleKey="tooltip.cancelMvm"
                                                                for="6"
                                                        >
                                                            <fmt:message key="movementDisplay.cancelled"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                            </table>

                                        </fieldset>
                                    </div>


                                    <div
                                            style="position: absolute; left: 291px;   width: 50px;">
                                        <fieldset style="width: 105px; border: 2px groove; height: 64px;">
                                            <legend><fmt:message key="movement.external"/></legend>
                                            <table width="96px" border="0" cellspacing="2" cellpadding="0" height="20">
                                                <tr height="25px">
                                                    <td width="96px">
                                                        <input
                                                                type="radio"
                                                                id="7"
                                                                name="movement.extBalStatus"
                                                                value="I"
                                                                tabindex="17"
                                                                style="width:23px"
                                                                titleKey="tooltip.includeMvmInDataExternal"
                                                        ${movement.extBalStatus == 'I' ? 'checked' : ''}
                                                        ${screenFieldsStatus ? 'disabled="disabled"' : ''}
                                                        />
                                                        <label
                                                                titleKey="tooltip.includeMvmInDataExternal"
                                                                for="7"
                                                        >
                                                            <fmt:message key="movementDisplay.included"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                                <tr height="25px">
                                                    <td width="96px">
                                                        <input
                                                                type="radio"
                                                                id="8"
                                                                name="movement.extBalStatus"
                                                                value="E"
                                                                tabindex="18"
                                                                style="width:23px"
                                                                titleKey="tooltip.excMvmInDataExternal"
                                                        ${movement.extBalStatus == 'E' ? 'checked' : ''}
                                                        ${screenFieldsStatus ? 'disabled="disabled"' : ''}
                                                        />
                                                        <label
                                                                titleKey="tooltip.excMvmInDataExternal"
                                                                for="8"
                                                        >
                                                            <fmt:message key="movementDisplay.excluded"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                            </table>

                                        </fieldset>
                                    </div>


                                    <div
                                            style="position: absolute; left: 291px;  top: 87px; width:50px; ">
                                        <fieldset style="width: 105px; border: 2px groove; height: 64px;">
                                            <legend><fmt:message key="movementDisplay.ilmFcast"/></legend>
                                            <table width="96px" border="0" cellspacing="2" cellpadding="0" height="20">
                                                <tr height="25px">
                                                    <td width="96px">
                                                        <input
                                                                type="radio"
                                                                id="9"
                                                                name="movement.ilmFcastStatus"
                                                                value="I"
                                                                tabindex="19"
                                                                style="width:23px"
                                                                titleKey="tooltip.includeMvmInIlmFcast"
                                                        ${movement.ilmFcastStatus == 'I' ? 'checked' : ''}
                                                        ${screenFieldsStatus ? 'disabled="disabled"' : ''}
                                                        />
                                                        <label
                                                                titleKey="tooltip.includeMvmInIlmFcast"
                                                                for="9"
                                                        >
                                                            <fmt:message key="movementDisplay.included"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                                <tr height="25px">
                                                    <td width="96px">
                                                        <input
                                                                type="radio"
                                                                id="10"
                                                                name="movement.ilmFcastStatus"
                                                                value="E"
                                                                tabindex="20"
                                                                style="width:23px"
                                                                titleKey="tooltip.exlMvmFromIlmFcast"
                                                        ${movement.ilmFcastStatus == 'E' ? 'checked' : ''}
                                                        ${screenFieldsStatus ? 'disabled="disabled"' : ''}
                                                        />
                                                        <label
                                                                titleKey="tooltip.exlMvmFromIlmFcast"
                                                                for="10"
                                                        >
                                                            <fmt:message key="movementDisplay.excluded"/>
                                                        </label>
                                                    </td>
                                                </tr>
                                            </table>

                                        </fieldset>
                                    </div>
                                </div>
                            </div>
                            <fieldset style="width: 920px; border:2px groove; height: 237px;">
                                <legend><fmt:message key="movementDisplay.movementparam"/></legend>
                                <table id="" width="513" border="0" cellpadding="0" cellspacing="0" height="10">
                                    <tr height="25px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.positionLevel"/></b></td>

                                        <% if (screenIdentifier.equals("manualInput") || posLevelEditStatus.equals("false")) { %>
                                        <td width="50px">
                                            <select
                                                    class="htmlTextAlpha"
                                                    tabindex="2"
                                                    titleKey="tooltip.selectPosLevel"
                                                    name="movement.positionLevelAsString"
                                                    style="width:37px;"
                                                    onchange="javaScript:checkExternalPositionLevels();"
                                            ${screenFieldsStatus ? 'disabled="disabled"' : ''}>
                                                <c:forEach var="posLevel" items="${requestScope.positionLevelList}">
                                                    <option value="${posLevel.value}" ${movement.positionLevelAsString == posLevel.value ? 'selected' : ''}>
                                                            ${posLevel.label}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                        </td>
                                        <% } else { %>
                                        <td width="50px" align="left">
                                            <input
                                                    type="text"
                                                    name="movement.positionLevelAsString"
                                                    class="htmlTextAlpha"
                                                    readonly="readonly"
                                                    style="width:12px;background:transparent;border:thin;"
                                                    value="${movement.positionLevelAsString}"/>
                                        </td>
                                        <% } %>

                                        <td width="297">
                                            <span id="positionName" name="positionName" class="spantext"></span>
                                        </td>
                                    </tr>
                                </table>


                                <table width="264px" border="0" cellpadding="0" cellspacing="1">
                                    <tr height="25px">
                                        <td width="168px">
                                            <b>&nbsp;<fmt:message key="movement.valueDate"/><span id="valueMan"/></b>
                                        </td>
                                        <td width="81px">
                                            <input
                                                    type="text"
                                                    name="movement.valueDateAsString"
                                                    class="htmlTextNumeric"
                                                    maxlength="10"
                                                    tabindex="3"
                                                    style="width:80px;height: 22px;"
                                                    value="${movement.valueDateAsString}"
                                            ${screenFieldsStatus ? 'disabled="disabled"' : ''}
                                                    onchange="if(validateField(this, 'valueDateAsString', dateFormat)) { return changeBorderColor(); }"/>
                                        </td>
                                        <td width="15px">
                                            <% if (screenIdentifier.equals("manualInput") || valueDateEditStatus.equals("false")) { %>
                                            <a
                                                    tabindex="4"
                                                    titleKey="tooltip.clickValueDate"
                                                    name="datelink"
                                                    id="datelink"
                                                    style="width:20px;"
                                                    onclick="valueDtCal.select(document.forms[0].elements['movement.valueDateAsString'], 'datelink', '${sessionScope.CDM.dateFormatValue}'); return false;">
                                                <img src="images/calendar-16.gif" style="top:40px; position:absolute;"/>
                                            </a>
                                            <% } %>
                                        </td>
                                        <% if (SwtConstants.YES.equals(request.getAttribute(SwtConstants.OPEN_UNOPEN_FLAG))) { %>
                                        <td width="425px"
                                            readonly="true"
                                            style="width: 425px; background: transparent; color: green; border: thin;">
                                            <fmt:message key="movementDisplay.openStatus"/>
                                        </td>
                                        <% } %>
                                    </tr>
                                </table>


                                <table width="438" border="0" cellpadding="0" cellspacing="1">
                                    <tr height="25px">
                                        <td width="168px">
                                            <b>&nbsp;<fmt:message key="manualInput.amount"/><span id="amountMan"/></b>
                                        </td>
                                        <td width="70px">
                                            <%
                                                if (screenIdentifier.equals("manualInput") || amountEditStatus.equals("false")) {
                                                    if (screenIdentifier.equals("movementChange")) {
                                            %>
                                            <select
                                                    style="width:54px;"
                                                    tabindex="5"
                                                    class="htmlTextAlpha"
                                                    titleKey="tooltip.selectCurrencyCode"
                                                    name="movement.currencyCode"
                                                    onchange="onChangeCurrencyOrType()"
                                                    disabled="disabled">
                                                <c:forEach var="currency" items="${requestScope.currencies}">
                                                    <option value="${currency.value}" ${currency.value == movement.currencyCode ? "selected" : ""}>
                                                            ${currency.label}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <%
                                            } else {
                                            %>
                                            <select
                                                    style="width:54px;"
                                                    tabindex="5"
                                                    class="htmlTextAlpha"
                                                    titleKey="tooltip.selectCurrencyCode"
                                                    name="movement.currencyCode"
                                                    onchange="onChangeCurrencyOrType()"
                                            ${screenFieldsStatus ? "disabled='disabled'" : ""}>
                                                <c:forEach var="currency" items="${requestScope.currencies}">
                                                    <option value="${currency.value}" ${currency.value == movement.currencyCode ? "selected" : ""}>
                                                            ${currency.label}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <%
                                                }
                                            } else {
                                            %>
                                            <input
                                                    type="text"
                                                    name="movement.currencyCode"
                                                    class="htmlTextAlpha"
                                                    style="width:54px;background:transparent;border:thin;"
                                                    value="${movement.currencyCode}"
                                                    readonly="readonly"/>
                                            <%
                                                }
                                            %>
                                        </td>
                                        <td width="165px">
                                            <input
                                                    type="text"
                                                    tabindex="6"
                                                    titleKey="tooltip.enterAmount"
                                                    class="htmlTextNumeric"
                                                    maxlength="28"
                                                    name="movement.amountAsString"
                                                    onchange="if(validateCurrency(this, 'movement.amountAsString', currencyFormat, document.forms[0].elements['movement.currencyCode'].value)) { return changeBorderColor(); }"
                                            ${screenFieldsStatus ? "disabled='disabled'" : ""}
                                                    style="width:160px;border-color:red;height:22px;"
                                                    value="${movement.amountAsString}"/>
                                        </td>
                                        <td width="35px">
                                            <%
                                                if (screenIdentifier.equals("manualInput") || amountEditStatus.equals("false")) {
                                            %>
                                            <select
                                                    name="movement.sign"
                                                    class="htmlTextAlpha"
                                                    tabindex="7"
                                                    titleKey="tooltip.selectAmountSign"
                                                    style="width:35px;"
                                            ${screenFieldsStatus ? "disabled='disabled'" : ""}>
                                                <c:forEach var="sign" items="${requestScope.signList}">
                                                    <option value="${sign.value}" ${sign.value == movement.sign ? "selected" : ""}>
                                                            ${sign.label}
                                                    </option>
                                                </c:forEach>
                                            </select>
                                            <%
                                            } else {
                                            %>
                                            <input
                                                    type="text"
                                                    name="movement.sign"
                                                    class="htmlTextAlpha"
                                                    style="width:35px;background:transparent;border:thin;"
                                                    value="${movement.sign}"
                                                    readonly="readonly"/>
                                            <%
                                                }
                                            %>
                                        </td>
                                    </tr>
                                </table>


                                <table width="327px" border="0" cellpadding="0" cellspacing="1">
                                    <tr height="25px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.movementType"/></b></td>
                                        <td width="160px">
                                            <input type="radio" id="1" style="width:13px" tabindex="8"
                                                   name="movement.movementType"
                                                   <c:if test="${movement.movementType == 'C'}">checked</c:if>
                                                   value="C" titleKey="tooltip.cash"
                                                   onclick="onChangeCurrencyOrType()"
                                            ${screenFieldsStatus ? "disabled='disabled'" : ""} />
                                            <label titleKey="tooltip.cash" for="1"><fmt:message
                                                    key="movementDisplay.cash"/>&nbsp;&nbsp;&nbsp;</label>

                                            <input type="radio" id="2" tabindex="9" name="movement.movementType"
                                                   <c:if test="${movement.movementType == 'U'}">checked</c:if>
                                                   value="U" titleKey="tooltip.securities"
                                                   onclick="onChangeCurrencyOrType()"
                                            ${screenFieldsStatus ? "disabled='disabled'" : ""} />
                                            <label titleKey="tooltip.securities" for="2"><fmt:message
                                                    key="movementDisplay.securities"/></label>
                                        </td>
                                    </tr>
                                </table>

                                <table width="800px" border="0" cellspacing="0" cellpadding="0">
                                    <tr height="24px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.accountId"/><span
                                                id="acctMan"/></b></td>
                                        <td id="accounttooltip" width="304px">
                                            <%
                                                if (screenIdentifier.equals("manualInput") || accountEditStatus.equals("false")) {
                                            %>
                                            <input type="text" class="textAlpha" name="accountId" id="accountId"
                                                   style="width:238px; margin-right:10px; height:22px;"
                                                   titleKey="tooltip.accountId"
                                                   value="${movement.accountId}" readonly="readonly"/>
                                            <input id="dropdownbutton_2" tabindex="10" titleKey="tooltip.movAccount"
                                                   type="button"
                                                   value="..." onclick="getAccountList(event)"/>
                                            <%
                                            } else {
                                            %>
                                            <input type="text" name="movement.accountId" class="htmlTextAlpha"
                                                   style="width:253px; background:transparent; border:thin;"
                                                   value="${movement.accountId}" readonly="readonly"/>
                                            <%
                                                }
                                            %>
                                        </td>
                                        <td width="330px">
                                            <input type="text" class="textAlpha" tabindex="-1" id="accountDesc"
                                                   style="width:330px; background:transparent; border:thin;"
                                                   value="${accountDesc}" readonly="readonly"/>
                                        </td>
                                    </tr>

                                    <tr height="30px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.bookcode"/></b></td>
                                        <td id="booktooltip" width="304px">
                                            <%
                                                if (screenIdentifier.equals("manualInput") || bookCodeEditStatus.equals("false")) {
                                            %>
                                            <input type="text" class="textAlpha" name="bookCode"
                                                   style="width:120px; margin-right:5px; height:22px;"
                                                   titleKey="tooltip.bookCode"
                                                   value="${movement.bookCode}" readonly="readonly"/>
                                            <input id="dropdownbutton_3" tabindex="12" type="button" value="..."
                                                   onclick="getBookCodeList(event)"/>
                                            <%
                                            } else {
                                            %>
                                            <input type="text" name="movement.bookCode" class="htmlTextAlpha"
                                                   style="width:140px; background:transparent; border:thin;"
                                                   value="${bookCode}" readonly="readonly"/>
                                            <%
                                                }
                                            %>
                                        </td>
                                        <td width="330px">
                                            <input type="text" class="textAlpha" tabindex="-1"
                                                   titleKey="tooltip.bookCode" id="bookCodeDesc"
                                                   style="background:transparent; border:thin;" value="${bookCodeDesc}"
                                                   readonly="readonly"/>
                                        </td>
                                    </tr>
                                </table>

                                <table width="900px" border="0" cellpadding="0" cellspacing="1">
                                    <tr height="22px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.reference1"/></b></td>
                                        <td width="321px">
                                            <input type="text" class="htmlTextAlpha" tabindex="21" maxlength="35"
                                                   titleKey="tooltip.enterMvmRef1" name="movement.reference1"
                                                   value="${movement.reference1}" ${screenFieldsStatus ? "disabled='disabled'" : ""}
                                                   style="width:321px; height:22px;"/>
                                        </td>
                                        <td width="90px"><b>&nbsp;<fmt:message key="movement.reference3"/></b></td>
                                        <td width="321px">
                                            <input type="text" class="htmlTextAlpha" tabindex="21" maxlength="35"
                                                   titleKey="tooltip.enterMvmRef3" name="movement.reference3"
                                                   value="${movement.reference3}" ${screenFieldsStatus ? "disabled='disabled'" : ""}
                                                   style="width:321px; height:22px;"/>
                                        </td>
                                    </tr>
                                </table>

                                <table width="900px" border="0" cellpadding="0" cellspacing="1">
                                    <tr height="22px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.reference2"/></b></td>
                                        <td width="321px">
                                            <input type="text" class="htmlTextAlpha" maxlength="35" tabindex="22"
                                                   titleKey="tooltip.enterMvmRef2" name="movement.reference2"
                                                   value="${movement.reference2}" ${screenFieldsStatus ? "disabled='disabled'" : ""}
                                                   style="width:321px; height:22px;"/>
                                        </td>
                                        <td width="90px"><b>&nbsp;<fmt:message key="movement.extraText"/></b></td>
                                        <td width="321px">
                                            <input type="text" class="htmlTextAlpha" maxlength="35" tabindex="24"
                                                   titleKey="tooltip.enterMvmExtraText" name="movement.reference4"
                                                   value="${movement.reference4}" ${screenFieldsStatus ? "disabled='disabled'" : ""}
                                                   style="width:321px; height:22px;"/>
                                        </td>
                                    </tr>
                                </table>


                                <table width="491px" border="0" cellpadding="0" cellspacing="0">
                                    <tr height="15px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.uetr"/></b></td>
                                        <td width="321px" colspan="3">
                                            <input type="text" name="movement.uetr" class="htmlTextAlpha" tabindex="32"
                                                   titleKey="tooltip.movement.uetr"
                                                   style="width:321px;margin-bottom: 5px;height: 22px;"
                                                   value="${movement.uetr}" ${screenFieldsStatus ? "disabled='disabled'" : ""} />
                                        </td>
                                    </tr>
                                </table>

                                <table width="910px" border="0" cellpadding="0" cellspacing="0">
                                    <tr height="15px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.productType"/></b></td>
                                        <td width="190px">
                                            <input type="text" name="movement.productType" class="htmlTextAlpha"
                                                   tabindex="25" maxlength="16"
                                                   titleKey="tooltip.enterProductType"
                                                   style="width:152px;margin-bottom: 5px;height: 22px;"
                                                   value="${movement.productType}" ${screenFieldsStatus ? "disabled='disabled'" : ""} />
                                        </td>
                                        <td width="218px">&nbsp;</td>
                                        <td width="161px"><b>&nbsp;<fmt:message key="movement.postingDate"/></b></td>
                                        <td width="175px">
                                            <input type="text" name="movement.postingDateAsString"
                                                   class="htmlTextNumeric" maxlength="10" tabindex="26"
                                                   titleKey="tooltip.enterPostingDate"
                                                   style="width:80px; margin-bottom: 5px;height: 22px;"
                                                   onchange="return validateField(this,'postingDateAsString', dateFormat);"
                                                   value="${movement.postingDateAsString}" ${screenFieldsStatus ? "disabled='disabled'" : ""} />
                                            <a tabindex="27" titleKey="tooltip.selectDate" id="postingDateLink"
                                               onclick="postingDtCal.select(document.forms[0].elements['movement.postingDateAsString'],'postingDateLink','${sessionScope.CDM.dateFormatValue}'); return false;">
                                                <img src="images/calendar-16.gif"/>
                                            </a>
                                        </td>
                                    </tr>
                                    <tr height="15px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.expectedSettlement"/></b>
                                        </td>
                                        <td width="190px">
                                            <input type="text" name="movement.expectedSettlementDateTimeAsString"
                                                   class="htmlTextAlpha" maxlength="20"
                                                   tabindex="28" titleKey="tooltip.expectedSettlement"
                                                   onchange="return validateDateWithTimeField(this,'expectedSettlement', dateFormat);"
                                                   style="width:152px;margin-bottom: 5px;height: 22px;"
                                                   value="${movement.expectedSettlementDateTimeAsString}" ${screenFieldsStatus ? "disabled='disabled'" : ""} />
                                            <a tabindex="29" titleKey="tooltip.selectDate" id="expectedDateLink"
                                               onclick="expectedDtCal.select(document.forms[0].elements['movement.expectedSettlementDateTimeAsString'],'expectedDateLink','${sessionScope.CDM.dateFormatValue} HH:mm:ss'); return false;">
                                                <img src="images/calendar-16.gif"/>
                                            </a>
                                        </td>
                                        <td width="218px">&nbsp;</td>
                                        <td width="161px"><b>&nbsp;<fmt:message key="movement.actualSettlement"/></b>
                                        </td>
                                        <td width="175px">
                                            <input type="text" name="movement.settlementDateTimeAsString"
                                                   class="htmlTextAlpha" maxlength="20"
                                                   tabindex="30" titleKey="tooltip.actualSettlment"
                                                   onchange="return validateDateWithTimeField(this,'actualSettlment', dateFormat);"
                                                   style="width:141px;margin-bottom: 5px;height: 22px;"
                                                   value="${movement.settlementDateTimeAsString}" ${screenFieldsStatus ? "disabled='disabled'" : ""} />
                                            <a tabindex="31" titleKey="tooltip.selectDate" id="actualDateLink"
                                               onclick="actualDtCal.select(document.forms[0].elements['movement.settlementDateTimeAsString'],'actualDateLink','${sessionScope.CDM.dateFormatValue} HH:mm:ss'); return false;">
                                                <img src="images/calendar-16.gif"/>
                                            </a>
                                        </td>
                                    </tr>
                                </table>

                                <table width="491px" border="0" cellpadding="0" cellspacing="0">
                                    <tr height="15px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.criticalPaymentType"/></b>
                                        </td>
                                        <td width="325px" colspan="3">
                                            <input type="text" name="movement.criticalPaymentType" class="htmlTextAlpha"
                                                   tabindex="32"
                                                   titleKey="tooltip.criticalPaymentType"
                                                   style="width:325px;margin-bottom: 5px;height: 22px;"
                                                   value="${movement.criticalPaymentType}" ${screenFieldsStatus ? "disabled='disabled'" : ""} />
                                        </td>
                                    </tr>
                                </table>

                                <table width="884px" border="0" cellspacing="0" cellpadding="0">
                                    <tr height="20px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.matchingParty"/></b></td>
                                        <td>
                                            <input type="text" name="movement.matchingParty" maxlength="35"
                                                   class="htmlTextAlpha" tabindex="33"
                                                   titleKey="tooltip.enterMatchingParty"
                                                   style="width:305px;height: 22px;"
                                                   value="${movement.matchingParty}" ${screenFieldsStatus ? "disabled='disabled'" : ""} />
                                        </td>
                                        <td width="100px" style="padding-left: 8px;">
                                            <input id="matchingPartyLink" type="button"
                                                   titleKey="tooltip.clickSelMatchingParty"
                                                   value="..." tabindex="34"
                                                   onclick="javascript:party('M','movement.matchingParty','matchingPartyDesc');"/>
                                        </td>
                                        <td width="305px">
                                            <span id="matchingPartyDesc" name="matchingPartyDesc"
                                                  class="spantext">${matchingPartyDesc}</span>
                                        </td>
                                    </tr>
                                </table>


                                <table width="903x" border="0" cellspacing="0" cellpadding="0">
                                    <tr height="20px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.counterParty"/></b></td>
                                        <td width="120px">
                                            <input type="text" class="htmlTextAlpha"
                                                   maxlength="12" name="movement.counterPartyId" tabindex="35"
                                                   onchange="clearCounterPartyDesc()" titleKey="tooltip.counterPartId"
                                                   value="${movement.counterPartyId}" style="width:120px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                        <td width="50px" style="padding-left: 3px">
                                            <input id="counterPartyLink" type="button"
                                                   titleKey="tooltip.clickSelCounterId" tabindex="36"
                                                   value="..."
                                                   onclick="javascript:party('N','movement.counterPartyId','counterPartyDesc')"/>&nbsp;&nbsp;
                                        </td>
                                        <td width="240px">
                                            <span id="counterPartyDesc" class="spantext"></span>
                                        </td>
                                        <td width="324px">
                                            <input type="text"
                                                   name="movement.counterPartyText3" maxlength="35"
                                                   class="htmlTextAlpha" tabindex="39"
                                                   titleKey="tooltip.enterCPartytext3"
                                                   value="${movement.counterPartyText3}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                    </tr>
                                </table>


                                <!-- First Table -->
                                <table width="900px" border="0" cellspacing="0" cellpadding="0">
                                    <tr height="20">
                                        <td width="170px">&nbsp;</td>
                                        <td width="324px">
                                            <input type="text"
                                                   name="movement.counterPartyText1" class="htmlTextAlpha"
                                                   maxlength="35" tabindex="37" titleKey="tooltip.enterCPartytext1"
                                                   value="${movement.counterPartyText1}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                        <td width="91px">&nbsp;</td>
                                        <td width="321px">
                                            <input type="text"
                                                   name="movement.counterPartyText4" class="htmlTextAlpha"
                                                   maxlength="35" tabindex="40" titleKey="tooltip.enterCPartytext4"
                                                   value="${movement.counterPartyText4}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                    </tr>
                                    <tr height="20">
                                        <td width="170px">&nbsp;</td>
                                        <td width="324px">
                                            <input type="text"
                                                   name="movement.counterPartyText2" class="htmlTextAlpha"
                                                   maxlength="35" tabindex="38" titleKey="tooltip.enterCPartytext2"
                                                   value="${movement.counterPartyText2}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                        <td width="91px">&nbsp;</td>
                                        <td width="321px">
                                            <input type="text"
                                                   name="movement.counterPartyText5" class="htmlTextAlpha"
                                                   maxlength="35" tabindex="41" titleKey="tooltip.enterCPartytext5"
                                                   value="${movement.counterPartyText5}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                    </tr>
                                </table>

                                <!-- Second Table -->
                                <table width="903px" border="0" cellspacing="0" cellpadding="0">
                                    <tr height="20px">
                                        <td width="166px"><b>&nbsp;<fmt:message key="movement.custodian"/></b></td>
                                        <td width="120px">
                                            <input type="text" class="htmlTextAlpha"
                                                   maxlength="12" name="movement.custodianId" tabindex="42"
                                                   onchange="clearCustodianDesc()" titleKey="tooltip.custId"
                                                   value="${movement.custodianId}"
                                                   style="width:120px;height: 22px;margin-left:1px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                        <td width="50px" style="padding-left: 3px">
                                            <input id="custodianPartyLink" type="button"
                                                   titleKey="tooltip.movCustodian" value="..."
                                                   tabindex="43"
                                                   onclick="javascript:party('Y','movement.custodianId','custodianName')">&nbsp;&nbsp;
                                        </td>
                                        <td width="240px">
                                            <span id="custodianName" class="spantext"></span>
                                        </td>
                                        <td width="324px">
                                            <input type="text"
                                                   name="movement.custodianText3" class="htmlTextAlpha"
                                                   maxlength="35" tabindex="46" titleKey="tooltip.enterCustText3"
                                                   value="${movement.custodianText3}" style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                    </tr>
                                </table>
                                <!-- First Table -->
                                <table width="900px" border="0" cellspacing="0" cellpadding="0">
                                    <tr height="20px">
                                        <td width="170px">&nbsp;&nbsp;</td>
                                        <td width="324px">
                                            <input type="text"
                                                   name="movement.custodianText1" class="htmlTextAlpha"
                                                   maxlength="35" tabindex="44" titleKey="tooltip.enterCustText1"
                                                   value="${movement.custodianText1}" style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                        <td width="91px">&nbsp;</td>
                                        <td width="321px">
                                            <input type="text"
                                                   name="movement.custodianText4" class="htmlTextAlpha"
                                                   maxlength="35" tabindex="47" titleKey="tooltip.enterCustText4"
                                                   value="${movement.custodianText4}" style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                    </tr>
                                    <tr height="20px">
                                        <td width="170px">&nbsp;&nbsp;</td>
                                        <td width="324px">
                                            <input type="text"
                                                   name="movement.custodianText2" class="htmlTextAlpha"
                                                   maxlength="35" tabindex="45" titleKey="tooltip.enterCustText2"
                                                   value="${movement.custodianText2}" style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                        <td width="91px">&nbsp;</td>
                                        <td width="321px">
                                            <input type="text"
                                                   name="movement.custodianText5" class="htmlTextAlpha"
                                                   maxlength="35" tabindex="48" titleKey="tooltip.enterCustText5"
                                                   value="${movement.custodianText5}" style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                    </tr>
                                </table>

                                <!-- Second Table -->
                                <table width="900px" border="0" cellspacing="0" cellpadding="0">
                                    <tr height="20">
                                        <td width="168px"><b>&nbsp;<fmt:message
                                                key="movement.senderToReceiverInfo"/></b></td>
                                        <td width="324px">
                                            <input type="text"
                                                   name="movement.movementExt.senderToReceiverInfo1"
                                                   class="htmlTextAlpha"
                                                   maxlength="35" tabindex="49" titleKey="tooltip.senderToReceiverText"
                                                   value="${movement.movementExt.senderToReceiverInfo1}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                        <td width="91px">&nbsp;</td>
                                        <td width="321px">
                                            <input type="text"
                                                   name="movement.movementExt.senderToReceiverInfo4"
                                                   class="htmlTextAlpha"
                                                   maxlength="35" tabindex="52" titleKey="tooltip.senderToReceiverText3"
                                                   value="${movement.movementExt.senderToReceiverInfo4}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                    </tr>
                                    <tr height="20px">
                                        <td width="168px">&nbsp;</td>
                                        <td width="324px">
                                            <input type="text"
                                                   name="movement.movementExt.senderToReceiverInfo2"
                                                   class="htmlTextAlpha"
                                                   maxlength="35" tabindex="50" titleKey="tooltip.senderToReceiverText1"
                                                   value="${movement.movementExt.senderToReceiverInfo2}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                        <td width="91px">&nbsp;</td>
                                        <td width="321px">
                                            <input type="text"
                                                   name="movement.movementExt.senderToReceiverInfo5"
                                                   class="htmlTextAlpha"
                                                   maxlength="35" tabindex="53" titleKey="tooltip.senderToReceiverText4"
                                                   value="${movement.movementExt.senderToReceiverInfo5}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                    </tr>
                                    <tr height="20px">
                                        <td width="168px">&nbsp;</td>
                                        <td width="324px">
                                            <input type="text"
                                                   name="movement.movementExt.senderToReceiverInfo3"
                                                   class="htmlTextAlpha"
                                                   maxlength="35" tabindex="51" titleKey="tooltip.senderToReceiverText2"
                                                   value="${movement.movementExt.senderToReceiverInfo3}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                        <td width="91px">&nbsp;</td>
                                        <td width="321px">
                                            <input type="text"
                                                   name="movement.movementExt.senderToReceiverInfo6"
                                                   class="htmlTextAlpha"
                                                   maxlength="35" tabindex="54" titleKey="tooltip.senderToReceiverText5"
                                                   value="${movement.movementExt.senderToReceiverInfo6}"
                                                   style="width:321px;height: 22px;"
                                                   <c:if test="${screenFieldsStatus}">disabled</c:if> />
                                        </td>
                                    </tr>
                                </table>


                        </div>
                        <%
                            if (!screenIdentifier.equals("manualInput")) {
                        %>
                        <div id="MovementDisplay" color="#7E97AF"
                             style="position: absolute;  top: 480; width: 710px; height: 0px; visibility: visible;">
                            <div id="divMovementDisplay2"
                                 style="position: absolute;  top: 77; width: 690px;  visibility: visible;">
                                <fieldset style="width: 921px; border: 2px groove; height: 30px;">
                                    <legend><fmt:message key="movementDisplay.originalMessage"/></legend>
                                    <table width="893px" border="0" cellpadding="0" cellspacing="0"
                                           height="30">
                                        <tr height="25px">
                                            <td width="90px"><b>&nbsp;<fmt:message key="movementDisplay.source"/></b>
                                            </td>
                                            <td width="30px">&nbsp;</td>
                                            <td width="142px">
                                                <input type="text" class="htmlTextAlpha"
                                                       tabindex="-1" readonly="readonly"
                                                       name="movement.inputSource"
                                                       style="width:144px;background:transparent;border:thin;"
                                                       value="${movement.inputSource}"/>
                                            </td>
                                            <td width="20px">&nbsp;</td>
                                            <td width="76px"><b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<fmt:message
                                                    key="messageFormats.id.formatId"/></b></td>
                                            <td width="28px">&nbsp;</td>
                                            <td width="120px">
                                                <input type="text" class="htmlTextAlpha" maxlength="12"
                                                       readonly="readonly" tabindex="-1"
                                                       name="movement.messageFormat"
                                                       style="width:120px;background:transparent;border:thin;"
                                                       value="${movement.messageFormat}"/>
                                            </td>
                                            <td width="70px"><b>&nbsp;<fmt:message key="movement.messageFormat"/></b>
                                            </td>
                                            <td width="28px">&nbsp;</td>
                                            <td width="260px">
                                                <input type="text" class="htmlTextAlpha" maxlength="12"
                                                       readonly="readonly" tabindex="-1"
                                                       name="movement.messageId"
                                                       style="width:120px;background:transparent;border:thin;"
                                                       value="${movement.messageId}"/>
                                            </td>
                                        </tr>

                                    </table>
                                </fieldset>
                            </div>
                        </div>
                        <%
                            }
                        %>
                    </div>
                </div>
            </div>

            <div id="sc2" class="tabcontent">
                <div style="position: absolute; left: 6px; top: 100px; width: 900px; ">


                    <!-- Ordering Customer -->
                    <table width="925px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20">
                            <td width="185px"><b>
                                <fmt:message key="movement.orderingCustomer"/>
                            </b></td>
                            <td width="120px">
                                <input type="text" class="htmlTextAlpha" maxlength="12"
                                       name="movement.orderingCustomerId"
                                       tabindex="55"
                                       onchange="clearOrderingCustomerDesc()"
                                       titleKey="tooltip.orderCus"
                                       style="width:120px;height: 22px;"
                                       value="${movement.orderingCustomerId}"/>
                            </td>
                            <td width="50px" style="padding-left: 4px">
                                <input id="orderingCustomerLink" type="button"
                                       titleKey="tooltip.movOrderingCustomer"
                                       value="..."
                                       tabindex="56"
                                       onclick="javascript:party('N','movement.orderingCustomerId','orderingCustomerName')"/>
                            </td>
                            <td width="242px">
					<span id="orderingCustomerName"
                          name="orderingCustomerName"
                          class="spantext">${orderingCustomerName}</span>
                            </td>
                            <td width="324px" style="">
                                <input type="text" name="movement.movementExt.orderingCustomer3"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="59"
                                       titleKey="tooltip.enterOrderCusText3"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingCustomer3}"/>
                            </td>
                        </tr>

                    </table>
                    <table width="925px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20px">
                            <td width="185px"></td>
                            <td width="321px">
                                <input type="text"
                                       name="movement.movementExt.orderingCustomer1"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="57"
                                       titleKey="tooltip.enterOrderCusText1"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingCustomer1}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                            <td width="95px">&nbsp;</td>
                            <td width="324px">
                                <input type="text"
                                       name="movement.movementExt.orderingCustomer4"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="60"
                                       titleKey="tooltip.enterOrderCusText4"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingCustomer4}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                        </tr>
                        <tr height="20px">
                            <td width="185px">&nbsp;</td>
                            <td width="321px">
                                <input type="text"
                                       name="movement.movementExt.orderingCustomer2"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="58"
                                       titleKey="tooltip.enterOrderCusText2"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingCustomer2}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                            <td width="91px">&nbsp;</td>
                            <td width="324px">
                                <input type="text"
                                       name="movement.movementExt.orderingCustomer5"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="61"
                                       titleKey="tooltip.enterOrderCusText5"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingCustomer5}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                        </tr>
                    </table>


                    <!-- Ordering Institution -->
                    <table width="925px" style="padding-top: 2px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20">
                            <td width="185px"><b>
                                <fmt:message key="movement.orderingInstitution"/>
                            </b></td>
                            <td width="120px">
                                <input type="text"
                                       class="htmlTextAlpha"
                                       maxlength="12"
                                       name="movement.orderingInstitutionId"
                                       tabindex="62"
                                       onchange="clearOrderingInstitutionDesc()"
                                       titleKey="tooltip.orderIns"
                                       style="width:120px;height: 22px;"
                                       value="${movement.orderingInstitutionId}"/>
                            </td>
                            <td width="50px" style="padding-left: 4px">
                                <input id="orderingInstLink"
                                       type="button"
                                       titleKey="tooltip.movOrderingInst"
                                       value="..."
                                       tabindex="63"
                                       onClick="javascript:party('N','movement.orderingInstitutionId','orderInstName')"/>&nbsp;&nbsp;
                            </td>
                            <td width="242px">
                                <span id="orderInstName" name="orderInstName" class="spantext"></span>
                            </td>
                            <td width="324px" style="">
                                <input type="text"
                                       name="movement.movementExt.orderingInstitution3"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="66"
                                       titleKey="tooltip.enterOrderInstText3"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingInstitution3}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                        </tr>

                    </table>
                    <table width="925px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20px">
                            <td width="185px"></td>
                            <td width="321px">
                                <input type="text"
                                       name="movement.movementExt.orderingInstitution1"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="64"
                                       titleKey="tooltip.enterOrderInstText1"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingInstitution1}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                            <td width="95px">&nbsp;</td>
                            <td width="324px">
                                <input type="text"
                                       name="movement.movementExt.orderingInstitution4"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="67"
                                       titleKey="tooltip.enterOrderInstText4"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingInstitution4}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                        </tr>
                        <tr height="20px">
                            <td width="185px">&nbsp;</td>
                            <td width="321px">
                                <input type="text"
                                       name="movement.movementExt.orderingInstitution2"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="65"
                                       titleKey="tooltip.enterOrderInstText2"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingInstitution2}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                            <td width="91px">&nbsp;</td>
                            <td width="324px">
                                <input type="text"
                                       name="movement.movementExt.orderingInstitution5"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="68"
                                       titleKey="tooltip.enterOrderInstText5"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.orderingInstitution5}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                        </tr>
                    </table>

                    <!-- Sender's Correspondent -->
                    <table width="925px" style="padding-top: 2px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20">
                            <td width="185px"><b><fmt:message key="movement.senderCorrespondent"/></b></td>
                            <td width="120px">
                                <input type="text"
                                       name="movement.senderCorrespondentId"
                                       maxlength="12"
                                       class="htmlTextAlpha"
                                       tabindex="69"
                                       onchange="clearSenderCorrespondentDesc()"
                                       titleKey="tooltip.sendCorrs"
                                       style="width:120px;height: 22px;"
                                       value="${movement.senderCorrespondentId}"/>
                            </td>
                            <td width="50px" style="padding-left: 4px">
                                <input id="senderCorresLink"
                                       type="button"
                                       titleKey="tooltip.movSenderCorres"
                                       value="..."
                                       tabindex="70"
                                       onClick="javascript:party('N','movement.senderCorrespondentId','senderCorrName')"/>&nbsp;&nbsp;
                            </td>
                            <td width="242px">
                                <span id="senderCorrName" name="senderCorrName" class="spantext"></span>
                            </td>
                            <td width="324px">
                                <input type="text"
                                       name="movement.movementExt.senderCorrespondent3"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="73"
                                       titleKey="tooltip.enterSenderCorrText3"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.senderCorrespondent3}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                        </tr>
                    </table>

                    <table width="925px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20px">
                            <td width="185px"></td>
                            <td width="321px">
                                <input type="text"
                                       name="movement.movementExt.senderCorrespondent1"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="71"
                                       titleKey="tooltip.enterSenderCorrText1"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.senderCorrespondent1}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                            <td width="95px">&nbsp;</td>
                            <td width="324px">
                                <input type="text"
                                       name="movement.movementExt.senderCorrespondent4"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="74"
                                       titleKey="tooltip.enterSenderCorrText4"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.senderCorrespondent4}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                        </tr>
                        <tr height="20px">
                            <td width="185px">&nbsp;</td>
                            <td width="321px">
                                <input type="text"
                                       name="movement.movementExt.senderCorrespondent2"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="72"
                                       titleKey="tooltip.enterSenderCorrText2"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.senderCorrespondent2}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                            <td width="91px">&nbsp;</td>
                            <td width="324px">
                                <input type="text"
                                       name="movement.movementExt.senderCorrespondent5"
                                       maxlength="35"
                                       class="htmlTextAlpha"
                                       tabindex="75"
                                       titleKey="tooltip.enterSenderCorrText5"
                                       style="width:321px;height: 22px;"
                                       value="${movement.movementExt.senderCorrespondent5}"
                                ${screenFieldsStatus ? 'disabled="disabled"' : ''} />
                            </td>
                        </tr>
                    </table>


                    <!-- Receiver's Correspondent -->
                    <!-- Receiver Correspondent -->
                    <table width="925px" style="padding-top: 2px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20">
                            <td width="185px"><b><fmt:message key="movement.receiverCorrespondent"/></b></td>
                            <td width="120px">
                                <input
                                        type="text"
                                        class="htmlTextAlpha"
                                        maxlength="12"
                                        name="movement.receiverCorrespondentId"
                                        tabindex="76"
                                        onchange="clearReceiverCorrespondentDesc()"
                                        titleKey="tooltip.recCorrs"
                                        style="width:120px;height:22px;"
                                        value="${movement.receiverCorrespondentId}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="50px" style="padding-left: 4px">
                                <input
                                        id="receiverCorresLink"
                                        type="button"
                                        titleKey="tooltip.movReceiverCorres"
                                        value="..."
                                        tabindex="77"
                                        onclick="javascript:party('N', 'movement.receiverCorrespondentId', 'receiverCorrName')"
                                />
                            </td>
                            <td width="242px"><span id="receiverCorrName" name="receiverCorrName"
                                                    class="spantext">${receiverCorrName}</span></td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.receiverCorrespondent3"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="80"
                                        titleKey="tooltip.enterreceiverCorrText3"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.receiverCorrespondent3}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>
                    <table width="925px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20px">
                            <td width="185px"></td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.movementExt.receiverCorrespondent1"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="78"
                                        titleKey="tooltip.enterreceiverCorrText1"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.receiverCorrespondent1}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="95px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.receiverCorrespondent4"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="81"
                                        titleKey="tooltip.enterreceiverCorrText4"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.receiverCorrespondent4}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                        <tr height="20px">
                            <td width="185px">&nbsp;</td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.movementExt.receiverCorrespondent2"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="79"
                                        titleKey="tooltip.enterreceiverCorrText2"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.receiverCorrespondent2}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="91px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.receiverCorrespondent5"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="82"
                                        titleKey="tooltip.enterreceiverCorrText5"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.receiverCorrespondent5}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>

                    <!-- Intermediary Institution -->
                    <table width="925px" style="padding-top: 2px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20">
                            <td width="185px"><b><fmt:message key="movement.intermediaryInstitution"/></b></td>
                            <td width="120px">
                                <input
                                        type="text"
                                        class="htmlTextAlpha"
                                        maxlength="12"
                                        name="movement.intermediaryInstitutionId"
                                        tabindex="83"
                                        onchange="clearIntermediaryInstituationDesc()"
                                        titleKey="tooltip.interInstit"
                                        style="width:120px;height:22px;"
                                        value="${movement.intermediaryInstitutionId}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="50px" style="padding-left: 4px">
                                <input
                                        id="intermediaryInstitutionLink"
                                        type="button"
                                        titleKey="tooltip.movintermediaryInstitution"
                                        value="..."
                                        tabindex="84"
                                        onclick="javascript:party('N', 'movement.intermediaryInstitutionId', 'intermediaryInstitutionName')"
                                />
                            </td>
                            <td width="242px"><span id="intermediaryInstitutionName" name="intermediaryInstitutionName"
                                                    class="spantext">${intermediaryInstitutionName}</span></td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.intermediaryInstitution3"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="87"
                                        titleKey="tooltip.intermediaryInstitutionText3"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.intermediaryInstitution3}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>
                    <table width="925px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20px">
                            <td width="185px"></td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.movementExt.intermediaryInstitution1"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="85"
                                        titleKey="tooltip.intermediaryInstitutionText1"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.intermediaryInstitution1}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="95px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.intermediaryInstitution4"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="88"
                                        titleKey="tooltip.intermediaryInstitutionText4"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.intermediaryInstitution4}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                        <tr height="20px">
                            <td width="185px">&nbsp;</td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.movementExt.intermediaryInstitution2"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="86"
                                        titleKey="tooltip.intermediaryInstitutionText2"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.intermediaryInstitution2}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="91px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.intermediaryInstitution5"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="89"
                                        titleKey="tooltip.intermediaryInstitutionText5"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.intermediaryInstitution5}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>


                    <!-- Account with Institution -->
                    <table width="925px" style="padding-top: 2px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20">
                            <td width="185px"><b><fmt:message key="movement.accountWithInstitution"/></b></td>
                            <td width="120px">
                                <input
                                        type="text"
                                        class="htmlTextAlpha"
                                        maxlength="12"
                                        name="movement.accountWithInstitutionId"
                                        tabindex="90"
                                        onchange="clearAccountwithInstitutionDesc()"
                                        titleKey="tooltip.accInstit"
                                        style="width:120px;height:22px;"
                                        value="${movement.accountWithInstitutionId}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="50px" style="padding-left: 4px">
                                <input
                                        id="accountwithInstitutionLink"
                                        type="button"
                                        titleKey="tooltip.moviaccountWithInstitution"
                                        value="..."
                                        tabindex="91"
                                        onclick="javascript:party('N', 'movement.accountWithInstitutionId', 'accountWithInstitutionName')"
                                />
                            </td>
                            <td width="242px"><span id="accountWithInstitutionName" name="accountWithInstitutionName"
                                                    class="spantext">${accountWithInstitutionName}</span></td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.accountWithInstitution3"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="94"
                                        titleKey="tooltip.enterAccountWithInstitutionText3"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.accountWithInstitution3}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>
                    <table width="925px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20px">
                            <td width="185px"></td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.movementExt.accountWithInstitution1"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="92"
                                        titleKey="tooltip.enterAccountWithInstitutionText1"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.accountWithInstitution1}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="95px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.accountWithInstitution4"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="95"
                                        titleKey="tooltip.enterAccountWithInstitutionText4"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.accountWithInstitution4}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                        <tr height="20px">
                            <td width="185px">&nbsp;</td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.movementExt.accountWithInstitution2"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="93"
                                        titleKey="tooltip.enterAccountWithInstitutionText2"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.accountWithInstitution2}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="91px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.accountWithInstitution5"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="96"
                                        titleKey="tooltip.enterAccountWithInstitutionText5"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.accountWithInstitution5}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>


                    <!-- Beneficiary Institution -->
                    <table width="925px" style="padding-top: 2px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20">
                            <td width="185px"><b><fmt:message key="movement.beneficiaryInstitution"/></b></td>
                            <td width="120px">
                                <input
                                        type="text"
                                        class="htmlTextAlpha"
                                        maxlength="12"
                                        name="movement.beneficiaryId"
                                        tabindex="97"
                                        onchange="clearBeneficiaryDesc()"
                                        titleKey="tooltip.benID"
                                        style="width:120px;height:22px;"
                                        value="${movement.beneficiaryId}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="50px" style="padding-left: 4px">
                                <input
                                        id="beneificiaryPartyLink"
                                        type="button"
                                        titleKey="tooltip.movBeneficiary"
                                        value="..."
                                        tabindex="98"
                                        onclick="javascript:party('N', 'movement.beneficiaryId', 'beneficiaryName')"
                                />
                            </td>
                            <td width="242px"><span id="beneficiaryName" name="beneficiaryName"
                                                    class="spantext">${beneficiaryName}</span></td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.beneficiaryText3"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="101"
                                        titleKey="tooltip.enterBenText3"
                                        style="width:321px;height:22px;"
                                        value="${movement.beneficiaryText3}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>
                    <table width="925px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20px">
                            <td width="185px"></td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.beneficiaryText1"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="99"
                                        titleKey="tooltip.enterBenText1"
                                        style="width:321px;height:22px;"
                                        value="${movement.beneficiaryText1}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="95px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.beneficiaryText4"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="102"
                                        titleKey="tooltip.enterBenText4"
                                        style="width:321px;height:22px;"
                                        value="${movement.beneficiaryText4}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                        <tr height="20px">
                            <td width="185px">&nbsp;</td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.beneficiaryText2"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="100"
                                        titleKey="tooltip.enterBenText2"
                                        style="width:321px;height:22px;"
                                        value="${movement.beneficiaryText2}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="91px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.beneficiaryText5"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="103"
                                        titleKey="tooltip.enterBenText5"
                                        style="width:321px;height:22px;"
                                        value="${movement.beneficiaryText5}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>

                    <!-- Beneficiary Customer -->
                    <table width="925px" style="padding-top: 2px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20">
                            <td width="185px"><b><fmt:message key="movement.beneficiaryCustomer"/></b></td>
                            <td width="120px">
                                <input
                                        type="text"
                                        class="htmlTextAlpha"
                                        maxlength="12"
                                        name="movement.beneficiaryCustomerId"
                                        tabindex="104"
                                        onchange="clearBeneficiaryCustomerDesc()"
                                        titleKey="tooltip.benCustomer"
                                        style="width:120px;height:22px;"
                                        value="${movement.beneficiaryCustomerId}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="50px" style="padding-left: 4px">
                                <input
                                        id="beneficiaryCustomerLink"
                                        type="button"
                                        titleKey="tooltip.movBeneficiaryCustomer"
                                        value="..."
                                        tabindex="105"
                                        onclick="javascript:party('N', 'movement.beneficiaryCustomerId', 'beneficiaryCustomerName')"
                                />
                            </td>
                            <td width="242px"><span id="beneficiaryCustomerName" name="beneficiaryCustomerName"
                                                    class="spantext">${beneficiaryCustomerName}</span></td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.beneficiaryCustomer3"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="108"
                                        titleKey="tooltip.enterBeneficiaryCustomerText3"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.beneficiaryCustomer3}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>
                    <table width="925px" border="0" cellspacing="0" cellpadding="0">
                        <tr height="20px">
                            <td width="185px"></td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.movementExt.beneficiaryCustomer1"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="106"
                                        titleKey="tooltip.enterBeneficiaryCustomerText1"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.beneficiaryCustomer1}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="95px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.beneficiaryCustomer4"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="109"
                                        titleKey="tooltip.enterBeneficiaryCustomerText4"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.beneficiaryCustomer4}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                        <tr height="20px">
                            <td width="185px">&nbsp;</td>
                            <td width="321px">
                                <input
                                        type="text"
                                        name="movement.movementExt.beneficiaryCustomer2"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="107"
                                        titleKey="tooltip.enterBeneficiaryCustomerText2"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.beneficiaryCustomer2}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                            <td width="91px">&nbsp;</td>
                            <td width="324px">
                                <input
                                        type="text"
                                        name="movement.movementExt.beneficiaryCustomer5"
                                        maxlength="35"
                                        class="htmlTextAlpha"
                                        tabindex="110"
                                        titleKey="tooltip.enterBeneficiaryCustomerText5"
                                        style="width:321px;height:22px;"
                                        value="${movement.movementExt.beneficiaryCustomer5}"
                                        disabled="${screenFieldsStatus}"
                                />
                            </td>
                        </tr>
                    </table>


                </div>
            </div>


        </div>

            <%
		if (screenIdentifier.equals("manualInput")) {
	%>
        <div id="MovementDisplay1"
             style="position: absolute; left: 120; top: 734px; width: 620px; height: 15px; visibility: visible;">
            <table width="800px" border="0" cellspacing="0" cellpadding="0"
                   height="20">
                <tr>
                    <td align="right" id="Print"><a onKeyDown="submitEnter(this,event)"
                                                    onclick="printPage();" tabindex="111"
                                                    onMouseOut="MM_swapImgRestore()"
                                                    onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
                            src="images/Print.gif " name="Print" border="0"
                            titleKey="tooltip.printScreen"></a></td>
                </tr>
            </table>
        </div>
        <div id="ddimagebuttons" color="#7E97AF"
             style="position: absolute; border: 2px outset; left:0px; top: 725; width: 937px; height: 39px; visibility: visible;">
            <div id="MovementDisplay1"
                 style="position: absolute; left: 6; top: 4; width: 290px; height: 15px; visibility: visible;">
                <table border="0" cellspacing="0" cellpadding="0" width="350px">
                    <tr>
                        <!-- Notes Button -->
                        <td><%if (request.getAttribute(SwtConstants.NOTES_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="112" titleKey="tooltip.enterNotes"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onKeyDown="submitEnter(this,event)"
                               onClick="javascript:openWindow(buildMovement('notes'),'movementNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes')"><fmt:message
                                    key="button.notes"/></a>
                            <%}%>     <%if (request.getAttribute(SwtConstants.NOTES_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.notes"/></a>
                            <%}%></td>
                        <!-- CopyFrom Button -->
                        <td><%if (request.getAttribute(SwtConstants.COPY_FROM_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="113" titleKey="tooltip.copyExMvm"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onKeyDown="submitEnter(this,event)"
                               onClick="javascript:openWindow(submitForm('copyFrom'),'copyfrommovementWindow','left=50,top=190,width=425,height=120,toolbar=0, resizable=yes, scrollbars=yes','true')"><fmt:message
                                    key="button.cpyFrom"/></a>
                            <%}%> <%if (request.getAttribute(SwtConstants.COPY_FROM_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.cpyFrom"/></a>
                            <%}%></td>
                        <!-- Save Button -->
                        <td id="saveButton"><%if (request.getAttribute(SwtConstants.SAV_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="114" titleKey="tooltip.SaveChanges"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onKeyDown="submitEnter(this,event)"
                               onclick="javascript:submitForm('save');"><fmt:message key="button.save"/></a>
                            <%}%> <%if (request.getAttribute(SwtConstants.SAV_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.save"/></a>
                            <%}%></td>
                        <!-- Cancel Button -->
                        <td id="cancelbutton"><%if (request.getAttribute(SwtConstants.CAN_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="115" titleKey="tooltip.cancel"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onKeyDown="submitEnter(this,event)"
                               onclick="javascript:submitForm('clearSession');"><fmt:message key="button.cancel"/></a>
                            <%}%> <%if (request.getAttribute(SwtConstants.CAN_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.cancel"/></a>
                            <%}%></td>
                        <!-- Close Button   -->
                        <td id="closebutton"><a tabindex="116"
                                                titleKey="tooltip.close"
                                                onMouseOut="collapsebutton(this)"
                                                onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
                                                onMouseUp="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
                                                onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>
                    </tr>
                </table>
                <table border="0" cellspacing="0" cellpadding="0" width="210px">
                </table>
            </div>
        </div>
            <%
		} else if (screenIdentifier.equals("movementDisplay")) {
	%>
        <div id="ddimagebuttons" color="#7E97AF"
             style="position: absolute; border: 2px outset;  top: 725; width: 937px; height: 39px; visibility: visible;">
            <div id="MovementDisplay"
                 style="position: absolute; left: 6; top: 4; width: 430px; height: 15px; visibility: visible;">
                <table border="0" cellspacing="0" cellpadding="0" width="600px">
                    <tr>
                        <!-- Change Button -->
                        <td id="changebutton"><%if (request.getAttribute(SwtConstants.CHG_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="112"
                               titleKey="tooltip.changeSelectMvm"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onclick="javascript:onChangeButtonClick()"><fmt:message key="button.change"/></a>
                            <%}%> <%if (request.getAttribute(SwtConstants.CHG_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.change"/></a>
                            <%}%></td>
                        <!-- Open/Unopen Button -->
                        <td id="openUnopenButton"><%if (SwtConstants.YES.equals(request.getAttribute(SwtConstants.OPEN_UNOPEN_FLAG))) { %>
                            <%if (request.getAttribute(SwtConstants.OPEN_UNOPEN_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="113"
                               titleKey="tooltip.unopenSelectMvm"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onclick="clickUnopenButton('updateOpenUnopenFlag')"><fmt:message
                                    key="button.Unopen"/></a>
                            <%}%>
                            <%if (request.getAttribute(SwtConstants.OPEN_UNOPEN_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.Unopen"/></a>
                            <%}%>
                            <%}%> <%if (SwtConstants.NO.equals(request.getAttribute(SwtConstants.OPEN_UNOPEN_FLAG))) { %>
                            <%if (request.getAttribute(SwtConstants.OPEN_UNOPEN_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="114" titleKey="tooltip.openSelectMvm"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onclick="clickOpenButton('updateOpenUnopenFlag')"><fmt:message key="button.Open"/></a>
                            <%}%>
                            <%if (request.getAttribute(SwtConstants.OPEN_UNOPEN_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.Open"/></a>
                            <%}%>
                            <%}%><%if ("".equals(request.getAttribute(SwtConstants.OPEN_UNOPEN_FLAG))) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.Open"/></a>
                            <%}%></td>
                        <!-- Recon Button -->
                        <td id="reconbutton"></td>
                        <!-- Match Button -->
                        <td id="matchbutton"><%if (request.getAttribute(SwtConstants.MATCH_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="115"
                               titleKey="tooltip.viewMatchSelMvm"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onClick="showMatch('showMatchDetails')"><fmt:message key="button.match"/></a>
                            <%}%> <%if (request.getAttribute(SwtConstants.MATCH_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.match"/></a>
                            <%}%></td>
                        <!-- Notes Button -->
                        <td id="notesbutton"><%if (request.getAttribute(SwtConstants.NOTES_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="116" titleKey="tooltip.enterNotes"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onClick="openNotes()"><fmt:message key="button.notes"/></a>
                            <%}%>     <%if (request.getAttribute(SwtConstants.NOTES_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.notes"/></a>
                            <%}%></td>
                        <!-- Log Button -->
                        <td><%if (request.getAttribute(SwtConstants.LOG_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="117" titleKey="tooltip.logSelMvm"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onClick="checkMovementId()"><fmt:message key="button.log"/></a>
                            <%}%> <%if (request.getAttribute(SwtConstants.LOG_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.log"/></a>
                            <%}%></td>
                        <!-- Message Button -->
                        <td><%if (request.getAttribute(SwtConstants.MESS_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="118" titleKey="tooltip.msgSelMvm"
                               onclick="openMvmntMessage()" onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"><fmt:message
                                    key="button.message"/></a>
                            <%}%> <%if (request.getAttribute(SwtConstants.MESS_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.message"/></a>
                            <%}%></td>

                        <td id="xrefsbutton"><%if (request.getAttribute(SwtConstants.NOTES_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="119"
                               titleKey="tooltip.viewCrossReference"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onClick="openCrossRef()"><fmt:message key="button.crossReference"/></a>
                            <%}%>     <%if (request.getAttribute(SwtConstants.NOTES_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.crossReference"/></a>
                            <%}%></td>

                        <!-- Close Button -->
                        <td id="closebutton"><a tabindex="120"
                                                titleKey="tooltip.close"
                                                onMouseOut="collapsebutton(this)" onKeyDown="submitEnter(this,event)"
                                                onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
                                                onMouseUp="highlightbutton(this)"
                                                onclick="confirmClose('P');"><fmt:message key="button.close"/></a></td>
                    </tr>
                </table>
            </div>
        </div>

            <%
		} else if (screenIdentifier.equals("movementChange")) {
	%>
        <div id="ddimagebuttons" color="#7E97AF"
             style="position: absolute; border: 2px outset; top: 725; width: 937px; height: 39px; visibility: visible;">
            <div id="MovementDisplay66"
                 style="position: absolute; left: 6; top: 4; width: 938px; height: 39px; visibility: visible;">
                <table border="0" cellspacing="0" cellpadding="0" width="210px">
                    <tr>
                        <!-- Notes Button -->
                        <td><a tabindex="112"
                               titleKey="tooltip.enterNotes"
                               onMouseOut="collapsebutton(this)"
                               onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
                               onKeyDown="submitEnter(this,event)" onMouseUp="highlightbutton(this)"
                               onClick="javascript:openWindow(buildMovementChange('notes'),'movementNotesWindow','left=170,top=210,width=588,height=272,toolbar=0, resizable=yes, scrollbars=yes')"><fmt:message
                                key="button.notes"/></a> <%if (request.getAttribute(SwtConstants.NOTES_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled">Notes</a>
                            <%}%></td>

                        <!-- Save Button -->
                        <td><%if (request.getAttribute(SwtConstants.SAV_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="113" titleKey="tooltip.SaveChanges"
                               onMouseOut="collapsebutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseOver="highlightbutton(this)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onclick="javascript:submitChangeForm('update');"><fmt:message key="button.save"/></a>
                            <%}%> <%if (request.getAttribute(SwtConstants.SAV_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.save"/></a>
                            <%}%></td>
                        <!-- Cancel Button -->
                        <td><%if (request.getAttribute(SwtConstants.CAN_BUT_STS).equals(SwtConstants.STR_TRUE)) { %>
                            <a tabindex="114" titleKey="tooltip.CancelChanges"
                               onMouseOut="collapsebutton(this)" onKeyDown="submitEnter(this,event)"
                               onMouseOver="highlightbutton(this)"
                               onMouseDown="expandbutton(this)" onMouseUp="highlightbutton(this)"
                               onclick="releaseLock();cancelChanges();"><fmt:message key="button.cancel"/></a>
                            <%}%> <%if (request.getAttribute(SwtConstants.CAN_BUT_STS).equals(SwtConstants.STR_FALSE)) { %>
                            <a class="disabled" disabled="disabled"><fmt:message key="button.cancel"/></a>
                            <%}%></td>
                    </tr>
                </table>
            </div>
        </div>
            <%
		}
	%>
            <%
		if (!screenIdentifier.equals("manualInput")) {
	%>
        <div id="MovementDisplay"
             style="position: absolute; left: 870; top: 734px; width: 70px; height: 15px; visibility: visible;">
            <table width="60px" border="0" cellspacing="0" cellpadding="0"
                   height="20">
                <tr>
                    <td align="Right"><a tabindex="120" href=#
                                         onclick="javascript:openWindow(buildPrintURL('print','Movement Display '),'sectionprintdwindow','left=50,top=190,width=422,height=345,toolbar=0,resizable=yes, status=yes, scrollbars=no','true')"
                                         onMouseOut="MM_swapImgRestore()" onKeyDown="submitEnter(this,event)"
                                         onMouseOver="MM_swapImage('Help','','images/help_default.GIF ',1)"><img
                            src="images/help_default.GIF " name="Help" border="0"></a></td>
                    <td align="right" id="Print"><a onclick="printPage();"
                                                    onKeyDown="submitEnter(this,event)" tabindex="121"
                                                    onMouseOut="MM_swapImgRestore()"
                                                    onMouseOver="MM_swapImage('Print','','images/Print_R.gif ',1)"><img
                            src="images/Print.gif " name="Print" border="0"
                            titleKey="tooltip.printScreen"></a></td>
                </tr>
            </table>
        </div>
            <%
		}
	%>
        <div
                style="position: absolute; left: 6; top: 4; width: 700px; height: 15px; visibility: hidden;">
            <table border="0" cellspacing="0" cellpadding="0" height="20"
                   style="visibility: hidden">
                <tr>
                    <td id="reconenablebutton"><a
                            titleKey="tooltip.reconMatch"
                            onMouseOut="collapsebutton(this)" onKeyDown="submitEnter(this,event)"
                            onMouseOver="highlightbutton(this)" onMouseDown="expandbutton(this)"
                            onMouseUp="highlightbutton(this)"
                            onClick="openreconConfirmationbox('updateMatchStatusToReconciled')"
                            tabindex="120"><fmt:message key="button.reconcile"/></a></td>
                    <td id="recondisablebutton"><a class="disabled"
                                                   disabled="disabled"><fmt:message key="button.reconcile"/></a></td>
                </tr>
            </table>
        </div>
        <blockquote>&nbsp;</blockquote>
        <p>&nbsp;</p>
    </body>
</form>
</html>