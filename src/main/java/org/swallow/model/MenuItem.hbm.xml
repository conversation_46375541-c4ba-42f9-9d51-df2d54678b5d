<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
     <class name="org.swallow.model.MenuItem" table="P_MENU_ITEM" mutable="false">
		<cache usage="read-only"/>
		<id name="itemId" column="MENU_ITEM_ID" unsaved-value="null">
		<generator class="assigned"/>
		</id>
		
		<property name="description" column="MENU_ITEM_DESC" not-null="false"/>
		<property name="parentId" column="PARENT_MENU_ITEM_ID" not-null="false"/>
		<property name="programId" column="PROGRAM_ID" not-null="false"/>
		<property name="disabledFlag" column="DISABLED_FLAG" not-null="false"/>
		<property name="menuOrder" column="MENU_ORDER" not-null="false"/>
		<property name="menuGroupOrder" column="MENU_GROUP_ORDER" not-null="false"/>
		<property name="imageName" column="IMAGE_NAME" not-null="false"/>
		<property name="width" column="WIDTH" not-null="false"/>
		<property name="height" column="HEIGHT" not-null="false"/>
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>
		
		<many-to-one lazy="false"
				name="program"
				column="PROGRAM_ID"
				update="false"
				insert="false"
				cascade="none"
				class="org.swallow.model.Program"
				not-null="true"
				outer-join="true"
				foreign-key="FK_P_MENU_ITEM_S_PROGRAM"/>

    </class>
</hibernate-mapping>
