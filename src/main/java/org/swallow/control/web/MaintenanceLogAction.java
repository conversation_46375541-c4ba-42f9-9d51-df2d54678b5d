/*
 * @(#)MaintenanceLogAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.web;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.swallow.control.model.ErrorLog;
import org.swallow.control.model.MaintenanceLog;
import org.swallow.control.model.MaintenanceLogView;
import org.swallow.control.service.MaintenanceLogManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.PageInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.PageDetails;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;



/**
 * MaintenanceLogAction.java
 *
 * this class is used to display the Log details.
 */



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/maintenancelog", "/maintenancelog.do"})
public class MaintenanceLogAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("view", "jsp/control/maintenancelogviewnew");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/control/maintenancelog");
		viewMap.put("refresh", "jsp/control/maintenancelog");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}


	private MaintenanceLog maintenanceLog;
	public MaintenanceLog getMaintenanceLog() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		maintenanceLog = RequestObjectMapper.getObjectFromRequest(MaintenanceLog.class, request);
		return maintenanceLog;
	}

	public void setMaintenanceLog(MaintenanceLog maintenanceLog) {
		this.maintenanceLog = maintenanceLog;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("maintenanceLog", maintenanceLog);
	}
	@Autowired
	private MaintenanceLogManager mlogMgr = null;

	/**
	 * Final log instance for logging this class
	 */
	private final Log log = LogFactory.getLog(MaintenanceLogAction.class);

	private ApplicationContext ctx = null;

	/**
	 * This method is used to set the MaintenanceLogManager instance
	 *
	 * @param maintenanceLogManager
	 */
	public void setMaintenanceLogManager(
			MaintenanceLogManager maintenanceLogManager) {
		this.mlogMgr = maintenanceLogManager;
	}
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "defaultDetails":
				return defaultDetails();
			case "showDetails":
				return showDetails();
			case "next":
				return next();
			case "viewDetails":
				return viewDetails();
			case "displayViewLog":
				return displayViewLog();
			case "displayAngular":
				return displayAngular();
		}


		return unspecified();
	}

	/**
	 * This is a default method to be called when no specific action method is
	 * called
	 *
	 * @return forward
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [unspecified] - " + "Entry");
		request.setAttribute("maintenanceLogList", new ArrayList());

		log.debug(this.getClass().getName() + " - [unspecified] - " + "Exit");
		return defaultDetails();

	}

	/**
	 * This method is used to display the Maintenance Log Screen details when
	 * loading the page.
	 *            HttpServletResponse object
	 * @return - ActionForward object
	 * @throws SwtException -
	 *             SwtException object
	 */
	public String defaultDetails()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration */
// To remove: 		DynaValidatorForm dyForm = null;
		String systemDate = null;
		MaintenanceLog maintenanceLog = null;
		String hostId = null;
		SystemFormats sysformat = null;
		int currentPage = 1;
		int maxPage = 0;
		int totalCount = 0;
		boolean isNext = false;
		ArrayList maintenanceLogList = null;
		String currentFilter = null;
		String currentSort = null;
		String filterSortStatus = null;
		ArrayList<PageDetails> pageSummaryList = null;
		MaintenanceLogView maintenanceLogView = null;
		try {
			log.debug(this.getClass().getName() + "- [defaultDetails] - Entry");
// To remove: 			dyForm = (DynaValidatorForm) form;
			/* get system date usin SWTUtil as String */
			systemDate = SwtUtil.getSystemDateString();
			maintenanceLog = (MaintenanceLog) (getMaintenanceLog());
			maintenanceLog.setFromDateAsString(systemDate);
			maintenanceLog.setToDateAsString(systemDate);
			setMaintenanceLog(maintenanceLog);
			/* get hostid from Cachemanager */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * get system date formats from swtutil and stored as a systemformat
			 * object
			 */
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			/*
			 * get from date and date format then parse the from date using
			 * swtutil
			 */
			maintenanceLog.setFromDate(SwtUtil.parseDate(maintenanceLog
					.getFromDateAsString(), sysformat.getDateFormatValue()));
			maintenanceLog.setToDate(SwtUtil.parseDate(maintenanceLog
					.getToDateAsString(), sysformat.getDateFormatValue()));

			maintenanceLogList = new ArrayList();
			/* get the selected filter option from jsp */
			currentFilter = request.getParameter("selectedFilter");
			currentSort = request.getParameter("selectedSort");
			/* if any filter or sort option is selected bydefault its ALL */
			if (currentFilter == null) {
				currentFilter = "all";
			}
			if (currentSort == null) {
				currentSort = "none";
			}
			/*
			 * put filter and sort options together into a string with seperated
			 * by comma
			 */
			filterSortStatus = currentFilter + "," + currentSort;

			totalCount = mlogMgr.getMaintenanceLogList(hostId, maintenanceLog
							.getFromDate(), maintenanceLog.getToDate(),
					currentPage - 1, maxPage, maintenanceLogList,
					filterSortStatus, sysformat);

			/* Read PageSize from the property file and calculates the maxPages */
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			pageSummaryList = new ArrayList<PageDetails>();
			PageDetails pSummary = new PageDetails();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed
			// in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			if (maxPage > 1)
				isNext = true;

			setMaintenanceLog(maintenanceLog);
			Iterator itr = maintenanceLogList.iterator();
			maintenanceLogView = new MaintenanceLogView();
			while (itr.hasNext()) {
				maintenanceLogView = (MaintenanceLogView) (itr.next());
				maintenanceLogView.setLogDate_Date(SwtUtil.formatDate(
						maintenanceLogView.getId().getLogDate(), sysformat
								.getDateFormatValue()));

				maintenanceLogView.setLogDateIso(SwtUtil.formatDate(
						maintenanceLogView.getId().getLogDate(), sysformat
								.getDateFormatValue()));


			}
			if ("none".equals(currentSort)) {
				currentSort = "0|true"; // default sorting column
			}
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("maintenanceLogList", maintenanceLogList);
			request.setAttribute("fromDate", systemDate);
			request.setAttribute("toDate", systemDate);
			request.setAttribute("totalCount", totalCount);

			log.debug(this.getClass().getName() + "- [defaultDetails] - Exit");
		} catch (SwtException swtexp) {
			log
					.error("Exception Catch in MaintenanceLogAction.'defaultDetails' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error("Exception Catch in MaintenanceLogAction.'defaultDetails' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "defaultDetails", MaintenanceLogAction.class), request,
					"");
			return getView("fail");
		} finally {
// To remove: 			dyForm = null;
			systemDate = null;
			maintenanceLog = null;
			hostId = null;
			sysformat = null;
			maintenanceLogList = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			pageSummaryList = null;
			maintenanceLogView = null;
		}
		return getView("success");
	}

	/**
	 * This method is used fetch the Maintenance log
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String showDetails()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration */
// To remove: 		DynaValidatorForm dyForm = null;
		MaintenanceLog maintenanceLog = null;
		String hostId = null;
		String fromDateAsString = null;
		String toDateAsString = null;
		SystemFormats sysformat = null;
		String currentFilter = null;
		String currentSort = null;
		String filterSortStatus = null;
		int currentPage = 1;
		int totalCount = 0;
		int initialPageCount = 0;
		int maxPage = 0;
		boolean isNext = false;
		ArrayList maintenanceLogList = null;
		ArrayList<PageDetails> pageSummaryList = null;
		MaintenanceLogView maintenanceLogView = null;
		Iterator itr = null;
		try {
			log.debug(this.getClass().getName() + "- [showDetails] - Entry ");
			/* initialise dyna action form ,casting to MaintenanceLog */
// To remove: 			dyForm = (DynaValidatorForm) form;
			maintenanceLog = (MaintenanceLog) (getMaintenanceLog());
			hostId = CacheManager.getInstance().getHostId();
			/* get from date as string */
			fromDateAsString = maintenanceLog.getFromDateAsString();
			/* get to date as string */
			toDateAsString = maintenanceLog.getToDateAsString();
			/* get system format value as object of systemFormat */
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/* gets all filter values specified for all columns from jsp */
			currentFilter = (request.getParameter("selectedFilter") != null) ? request
					.getParameter("selectedFilter").trim().toString()
					: "all";

			/* gets the selected sort column from jsp */
			currentSort = (request.getParameter("selectedSort") != null) ? request
					.getParameter("selectedSort").trim().toString()
					: "none";
			/* put together filter and sort values seperated by comma */
			filterSortStatus = currentFilter + "," + currentSort;

			/*
			 * convert the value from date and covert to specified system format
			 * then set to maintenanceLog
			 */
			maintenanceLog.setFromDate(SwtUtil.parseDate(maintenanceLog
					.getFromDateAsString(), sysformat.getDateFormatValue()));
			maintenanceLog.setToDate(SwtUtil.parseDate(maintenanceLog
					.getToDateAsString(), sysformat.getDateFormatValue()));
			maintenanceLogList = new ArrayList();
			totalCount = mlogMgr.getMaintenanceLogList(hostId, maintenanceLog
							.getFromDate(), maintenanceLog.getToDate(),
					currentPage - 1, initialPageCount, maintenanceLogList,
					filterSortStatus, sysformat);

			/* Read PageSize from the property file and calculates the maxPages */
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);

			pageSummaryList = new ArrayList<PageDetails>();
			PageDetails pSummary = new PageDetails();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}
			if (maxPage > 1)
				isNext = true;
			setMaintenanceLog(maintenanceLog);
			itr = maintenanceLogList.iterator();
			maintenanceLogView = new MaintenanceLogView();

			while (itr.hasNext()) {
				maintenanceLogView = (MaintenanceLogView) (itr.next());
				maintenanceLogView.setLogDate_Date(SwtUtil.formatDate(
						maintenanceLogView.getId().getLogDate(), sysformat
								.getDateFormatValue()));
			}

			if ("none".equals(currentSort)) {
				/* default sorting column */
				currentSort = "0|true";
			}
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("fromDate", fromDateAsString);
			request.setAttribute("toDate", toDateAsString);
			request.setAttribute("totalCount", totalCount);

			request.setAttribute("maintenanceLogList", maintenanceLogList);
			log.debug(this.getClass().getName() + "- [showDetails] - Exit");
			return getView("refresh");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ e.getMessage());
			return getView("fail");
		} finally {
// To remove: 			dyForm = null;
			maintenanceLog = null;
			hostId = null;
			fromDateAsString = null;
			toDateAsString = null;
			sysformat = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
		}

	}

	/**
	 * This method is called when PREVIOUS LINK, NEXT LINK or any Page no is
	 * clicked. It fetches the system Log details for that page.
	 * @return
	 * @throws Exception
	 */
	public String next()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Variable Declaration for systemLog */
// To remove: 		DynaValidatorForm dyForm = null;
		/* Variable Declaration for maintenanceLog */
		MaintenanceLog maintenanceLog = null;
		/* Variable Declaration for sysformat */
		SystemFormats sysformat = null;
		/* Variable Declaration for maintenanceLogView */
		MaintenanceLogView maintenanceLogView = null;
		/* Variable Declaration for hostId */
		String hostId = null;
		/* Variable Declaration for fromDateAsString */
		String fromDateAsString = null;
		/* Variable Declaration for toDateAsString */
		String toDateAsString = null;
		/* Variable Declaration for currentFilter */
		String currentFilter = null;
		/* Variable Declaration for currentSort */
		String currentSort = null;
		/* Variable Declaration for filterSortStatus */
		String filterSortStatus = null;
		/* Variable Declaration for nextLinkStatus */
		String nextLinkStatus = null;
		/* Variable Declaration for prevLinkStatus */
		String prevLinkStatus = null;
		/* Variable Declaration for currentPage */
		int currentPage = 0;
		/* Variable Declaration for clickedPage */
		int clickedPage = 0;
		/* Variable Declaration for maxPage */
		int maxPage = 0;
		/* Variable Declaration for totalCount */
		int totalCount = 0;
		/* Variable Declaration for maintenanceLogList */
		ArrayList maintenanceLogList = null;
		/* Variable Declaration for pageSummaryList */
		ArrayList<PageDetails> pageSummaryList = null;
		Iterator itr = null;

		try {
			log.debug(this.getClass().getName() + "- [next] - Entry");
// To remove: 			dyForm = (DynaValidatorForm) form;
			maintenanceLog = (MaintenanceLog) (getMaintenanceLog());
			hostId = CacheManager.getInstance().getHostId();
			fromDateAsString = request.getParameter("fromDate");
			toDateAsString = request.getParameter("toDate");
			maintenanceLog.setFromDateAsString(fromDateAsString);
			maintenanceLog.setToDateAsString(toDateAsString);
			/* get current sytem format from swtutil */
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());
			/*
			 * convert the value from date and covert to specified system format
			 * then set to maintenanceLog
			 */
			maintenanceLog.setFromDate(SwtUtil.parseDate(maintenanceLog
					.getFromDateAsString(), sysformat.getDateFormatValue()));
			maintenanceLog.setToDate(SwtUtil.parseDate(maintenanceLog
					.getToDateAsString(), sysformat.getDateFormatValue()));

			/* gets all the filter values specified for all columns */
			currentFilter = (request.getParameter("selectedFilter") != null) ? request
					.getParameter("selectedFilter").trim().toString()
					: "all";

			/* gets the selectd sort column from jsp */
			currentSort = (request.getParameter("selectedSort") != null) ? request
					.getParameter("selectedSort").trim().toString()
					: "1|false";
			/* put together filter and sort values seperated by comma */
			filterSortStatus = currentFilter + "," + currentSort;

			currentPage = Integer
					.parseInt((request.getParameter("currentPage") != null) ? request
							.getParameter("currentPage").trim().toString()
							: "0");
			clickedPage = Integer
					.parseInt((request.getParameter("goToPageNo") != null) ? request
							.getParameter("goToPageNo").trim().toString()
							: "0");
			/*
			 * if maximum page is not null it is retrieved from jsp otherwise
			 * value is "0"
			 */
			maxPage = Integer
					.parseInt((request.getParameter("maxPages") != null) ? request
							.getParameter("maxPages").trim().toString()
							: "0");
			/*
			 * check Previous Link is Clicked it is true,assign currentpage is
			 * selected page
			 */
			if (clickedPage == -2) {
				currentPage--;
				clickedPage = currentPage;
			} else {
				/*
				 * check Next Link is Clicked it is true,assign currentpage is
				 * selected page
				 */
				if (clickedPage == -1) {
					currentPage++;
					clickedPage = currentPage;
				} else {
					currentPage = clickedPage;
				}
			}

			maintenanceLogList = new ArrayList();
			/* call the manager layer to get all the details of maitenance log */
			totalCount = mlogMgr.getMaintenanceLogList(hostId, maintenanceLog
							.getFromDate(), maintenanceLog.getToDate(),
					currentPage - 1, maxPage, maintenanceLogList,
					filterSortStatus, sysformat);

			/* Read PageSize from the property file and calculates the maxPages */
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			pageSummaryList = new ArrayList<PageDetails>();
			PageDetails pSummary = new PageDetails();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			/* check and assign the status of next and previous links */
			nextLinkStatus = (clickedPage < maxPage) ? "true" : "false";
			prevLinkStatus = (clickedPage > 1) ? "true" : "false";

			setMaintenanceLog(maintenanceLog);
			itr = maintenanceLogList.iterator();
			maintenanceLogView = new MaintenanceLogView();
			while (itr.hasNext()) {
				maintenanceLogView = (MaintenanceLogView) (itr.next());
				maintenanceLogView.setLogDate_Date(SwtUtil.formatDate(
						maintenanceLogView.getId().getLogDate(), sysformat
								.getDateFormatValue()));
			}
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", prevLinkStatus);
			request.setAttribute("nextEnabled", nextLinkStatus);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("totalCount", totalCount);

			request.setAttribute("maintenanceLogList", maintenanceLogList);
			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			request.setAttribute("fromDate", fromDateAsString);
			request.setAttribute("toDate", toDateAsString);
			log.debug(this.getClass().getName() + "- [next] - Exit");
			return getView("refresh");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [next] method : - "
					+ e.getMessage());
			throw new SwtException();
		} finally {
			maintenanceLog = null;
			sysformat = null;
			maintenanceLogView = null;
			hostId = null;
			fromDateAsString = null;
			toDateAsString = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			nextLinkStatus = null;
			prevLinkStatus = null;
		}
	}

	/**
	 * This method is used to get the maintenance log details for a selected row
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String viewDetails()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + "- [viewDetails] - Entry");
		try {

			String logDate = "";
			String userId = "";
			String ipAddress = "";
			String tableName = "";
			String reference = "";
			String action = "";

			/* following values are getting from jsp to view the opted record */
			/* get selected log date from jsp */
			logDate = request.getParameter("selectedLogDate");
			/* get selected user id from jsp */
			userId = request.getParameter("selectedUserId");
			/* get selected ip from jsp */
			ipAddress = request.getParameter("selectedIpAddress");
			tableName = request.getParameter("selectedTableName");
			reference = request.getParameter("selectedReference");
			action = request.getParameter("selectedAction");

			request.setAttribute("logDate", logDate);
			request.setAttribute("userId", userId);
			request.setAttribute("ipAddress", ipAddress);
			request.setAttribute("tableName", tableName);
			request.setAttribute("reference", reference);
			request.setAttribute("action", action);

			log.debug(this.getClass().getName() + "- [viewDetails] - Exit");

			return getView("view");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [viewDetails] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw new SwtException();
		}
	}

	/**
	 * This function is used to sets maximum Page The maximum page count is done
	 * by calculation ie. The total record value got from the DB and the
	 * pageSize is got from the properties file.
	 *
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Entering");
		/* Class instance declaration */
		int maxPage;
		int remainder;
		maxPage = (totalCount) / (pageSize);
		remainder = totalCount % pageSize;
		if (remainder > 0) {
			maxPage++;
		}
		log.debug(this.getClass().getName()
				+ "- [setMaxPageAttribute] - Exiting");
		return maxPage;
	}


	/**
	 * This method is used to prepare the maintenance log view data
	 * @return
	 * @throws SwtException
	 */
	public String displayViewLog()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration */
		// To host for application
		String hostId = null;
		String logDate = null;
		String userId = null;
		String ipAddress = null;
		String reference = null;
		String action = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		String dateFormat=null;
		String ccyCode = null;
		String tableName= null;
		String userName= null;
		Collection<MaintenanceLog> listViewLogRecords = new ArrayList<MaintenanceLog>();

		try {
			log.debug(this.getClass().getName() + " - [displayViewLog()] - Entry");
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());

			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			logDate = request.getParameter("logDate");
			/* get selected user id from jsp */
			userId = request.getParameter("userId");
			/* get selected ip from jsp */
			ipAddress = request.getParameter("ipAddress");
			tableName = request.getParameter("tableName");
			reference = request.getParameter("reference");
			action = request.getParameter("action");

			/*get user name from data base*/
			userName= !SwtUtil.isEmptyOrNull(userId)?mlogMgr.getUserName(userId):"";

			/* get system log details when select the view option */
			listViewLogRecords = mlogMgr.getSystemLogDetails(hostId, logDate,
					userId, ipAddress, tableName, reference, action);
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.MAINT_LOG_VIEW);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("logDate", !SwtUtil.isEmptyOrNull(logDate)?SwtUtil.formatDate(logDate, SwtUtil.getCurrentDateFormat(request.getSession()) + " HH:mm:ss"):"");
			responseConstructor.createElement("userId", userId);
			responseConstructor.createElement("userName", userName);
			responseConstructor.createElement("ipAddress", ipAddress);
			responseConstructor.createElement("tableName", tableName);
			responseConstructor.createElement("reference",reference);
			responseConstructor.createElement("action",action);
			xmlWriter.endElement(SwtConstants.SINGLETONS);

			/******* acctCcyMaintPeriodLogGrid ******/
			responseConstructor.formGridStart("maintLogViewGrid");
			responseConstructor.formColumn(getViewLogGridColumns(width, columnOrder, hiddenColumns, request));
			// form rows (records)
			responseConstructor.formRowsStart(listViewLogRecords.size());
			for (Iterator<MaintenanceLog> it = listViewLogRecords.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				MaintenanceLog maintViewLog = (MaintenanceLog) it.next();
				responseConstructor.formRowStart();
				String formattedOldVal= SwtUtil.isEmptyOrNull(maintViewLog.getOldValue()) ? "" : getColFormattedValue(maintViewLog.getColumnName(),maintViewLog.getOldValue(),dateFormat, ccyCode);
				String formattedNewVal= SwtUtil.isEmptyOrNull(maintViewLog.getNewValue()) ? "" : getColFormattedValue(maintViewLog.getColumnName(),maintViewLog.getNewValue(),dateFormat, ccyCode);
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_TAGNAME,maintViewLog.getColumnName());
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_FROM_TAGNAME,SwtUtil.encode64(formattedOldVal));
				responseConstructor.createRowElement(SwtConstants.CCY_ACC_MAINT_PERIOD_CHANGED_TO_USER_TAGNAME,SwtUtil.encode64(formattedNewVal));

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.MAINT_LOG_VIEW);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug(this.getClass().getName() + " - [displayViewLog()] - Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in MaintenanceLogAction.'displayViewLog' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MaintenanceLogAction.'displayViewLog' method : "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "displayViewLog", MaintenanceLogAction.class), request, "");
			return getView("fail");
		} finally {
			/* null the objects created already. */
			responseConstructor = null;
			xmlWriter = null;
			hostId = null;
			logDate = null;
			userId = null;
			ipAddress = null;
			reference = null;
			action = null;
			dateFormat = null;
			listViewLogRecords = null;
		}
	}


	private List<ColumnInfo> getViewLogGridColumns(String width, String columnOrder, String hiddenColumns,
												   HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getViewLogGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width =  SwtConstants.MAINT_LOG_VIEW_FIELD_TAGNAME + "=210" + ","
						+ SwtConstants.MAINT_LOG_VIEW_CHANGED_FROM_TAGNAME + "=400" + ","
						+ SwtConstants.MAINT_LOG_VIEW_CHANGED_TO_USER_TAGNAME + "=400" ;
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.MAINT_LOG_VIEW_FIELD_TAGNAME + ","
						+ SwtConstants.MAINT_LOG_VIEW_CHANGED_FROM_TAGNAME + ","
						+ SwtConstants.MAINT_LOG_VIEW_CHANGED_TO_USER_TAGNAME ;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;
				// Host Id column
				if (order.equals(SwtConstants.MAINT_LOG_VIEW_FIELD_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_LOG_VIEW_FIELD__HEADING, request),
							SwtConstants.MAINT_LOG_VIEW_FIELD_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths.get(SwtConstants.MAINT_LOG_VIEW_FIELD_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.MAINT_LOG_VIEW_FIELD_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.CCY_ACC_MAINT_PERIOD_FIELD_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// Entity Id column
				if (order.equals(SwtConstants.MAINT_LOG_VIEW_CHANGED_FROM_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_LOG_VIEW_CHANGED_FROM_HEADING, request),
							SwtConstants.MAINT_LOG_VIEW_CHANGED_FROM_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths.get(SwtConstants.MAINT_LOG_VIEW_CHANGED_FROM_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.MAINT_LOG_VIEW_CHANGED_FROM_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_LOG_VIEW_CHANGED_FROM_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
				// Account Id column
				if (order.equals(SwtConstants.MAINT_LOG_VIEW_CHANGED_TO_USER_TAGNAME)) {
					tmpColumnInfo = (new ColumnInfo(SwtUtil.getMessage(SwtConstants.MAINT_LOG_VIEW_CHANGED_TO_HEADING, request),
							SwtConstants.MAINT_LOG_VIEW_CHANGED_TO_USER_TAGNAME, SwtConstants.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths.get(SwtConstants.MAINT_LOG_VIEW_CHANGED_TO_USER_TAGNAME)), false,
							true, hiddenColumnsMap.get(SwtConstants.MAINT_LOG_VIEW_CHANGED_TO_USER_TAGNAME)));

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINT_LOG_VIEW_CHANGED_TO_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}


			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getViewLogGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getViewLogGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getViewLogGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	private String getColFormattedValue(String column, String value, String dateFormat, String ccyCode) {
		String formattedVal = null;
		if ("Target Avg Balance".equalsIgnoreCase(column) || "Minimum Target Balance".equalsIgnoreCase(column)
				|| "Minimum Reserve".equalsIgnoreCase(column) || "Tier".equalsIgnoreCase(column)
				|| "Fill Days".equalsIgnoreCase(column) || "Fill Balance".equalsIgnoreCase(column)) {
			try {
				formattedVal = SwtUtil.formatCurrencyWithoutDecimals(ccyCode, Double.parseDouble(value));
			} catch (NumberFormatException e) {
				log.error("NumberFormatException caught in " + this.getClass().getName()
						+ " - [getColFormattedValue]. Cause: " + e.getMessage());
			} catch (SwtException exp) {
				log.error(this.getClass().getName()
						+ " - SwtException Catched in [getColFormattedValue] method : - " + exp.getMessage());
			}
		} else if ("Start Date".equalsIgnoreCase(column) || "End Date".equalsIgnoreCase(column)) {
			try {
				Date initDate = new SimpleDateFormat("yyyyMMdd").parse(value);
				formattedVal = SwtUtil.formatDate(initDate, dateFormat);
			} catch (ParseException e) {
				log.error("ParseException caught in " + this.getClass().getName()
						+ " - [getColFormattedValue]. Cause: " + e.getMessage());
			}
		} else {
			formattedVal =  value;
		}

		return formattedVal;
	}


	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns,
											HttpServletRequest request) throws SwtException {
		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		try {
			// log debug message
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns

				width = SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_TAGNAME + "=80," +
						SwtConstants.MAINTENANCE_LOG_LIST_LOG_TIME_TAGNAME + "=77," +
						SwtConstants.MAINTENANCE_LOG_LIST_USER_ID_TAGNAME + "=135," +
						SwtConstants.MAINTENANCE_LOG_LIST_IP_ADDRESS_TAGNAME + "=120," +
						SwtConstants.MAINTENANCE_LOG_LIST_TABLE_NAME_TAGNAME + "=150," +
						SwtConstants.MAINTENANCE_LOG_LIST_REFERENCE_TAGNAME + "=275," +
						SwtConstants.MAINTENANCE_LOG_LIST_DUP_ACTION_TAGNAME + "=85";
			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_TAGNAME + "," +
						SwtConstants.MAINTENANCE_LOG_LIST_LOG_TIME_TAGNAME + "," +
						SwtConstants.MAINTENANCE_LOG_LIST_USER_ID_TAGNAME + "," +
						SwtConstants.MAINTENANCE_LOG_LIST_IP_ADDRESS_TAGNAME + "," +
						SwtConstants.MAINTENANCE_LOG_LIST_TABLE_NAME_TAGNAME + "," +
						SwtConstants.MAINTENANCE_LOG_LIST_REFERENCE_TAGNAME + "," +
						SwtConstants.MAINTENANCE_LOG_LIST_DUP_ACTION_TAGNAME;
			}

			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();



			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				ColumnInfo tmpColumnInfo = null;

				// LOG_DATE column
				if (order.equals(SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_HEADING, request),
							SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING,
							0,
							Integer.parseInt(widths.get(SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_TAGNAME)),
							false,
							true,
							hiddenColumnsMap.get(SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_TAGNAME)
					);

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// LOG_TIME column
				if (order.equals(SwtConstants.MAINTENANCE_LOG_LIST_LOG_TIME_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_LOG_TIME_HEADING, request),
							SwtConstants.MAINTENANCE_LOG_LIST_LOG_TIME_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING,
							1,
							Integer.parseInt(widths.get(SwtConstants.MAINTENANCE_LOG_LIST_LOG_TIME_TAGNAME)),
							false,
							true,
							hiddenColumnsMap.get(SwtConstants.MAINTENANCE_LOG_LIST_LOG_TIME_TAGNAME)
					);

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_LOG_TIME_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// USER_ID column
				if (order.equals(SwtConstants.MAINTENANCE_LOG_LIST_USER_ID_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_USER_ID_HEADING, request),
							SwtConstants.MAINTENANCE_LOG_LIST_USER_ID_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING,
							2,
							Integer.parseInt(widths.get(SwtConstants.MAINTENANCE_LOG_LIST_USER_ID_TAGNAME)),
							false,
							true,
							hiddenColumnsMap.get(SwtConstants.MAINTENANCE_LOG_LIST_USER_ID_TAGNAME)
					);

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_USER_ID_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// IP_ADDRESS column
				if (order.equals(SwtConstants.MAINTENANCE_LOG_LIST_IP_ADDRESS_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_IP_ADDRESS_HEADING, request),
							SwtConstants.MAINTENANCE_LOG_LIST_IP_ADDRESS_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING,
							3,
							Integer.parseInt(widths.get(SwtConstants.MAINTENANCE_LOG_LIST_IP_ADDRESS_TAGNAME)),
							false,
							true,
							hiddenColumnsMap.get(SwtConstants.MAINTENANCE_LOG_LIST_IP_ADDRESS_TAGNAME)
					);

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_IP_ADDRESS_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// TABLE_NAME column
				if (order.equals(SwtConstants.MAINTENANCE_LOG_LIST_TABLE_NAME_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_TABLE_NAME_HEADING, request),
							SwtConstants.MAINTENANCE_LOG_LIST_TABLE_NAME_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING,
							4,
							Integer.parseInt(widths.get(SwtConstants.MAINTENANCE_LOG_LIST_TABLE_NAME_TAGNAME)),
							false,
							true,
							hiddenColumnsMap.get(SwtConstants.MAINTENANCE_LOG_LIST_TABLE_NAME_TAGNAME)
					);

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_TABLE_NAME_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// REFERENCE column
				if (order.equals(SwtConstants.MAINTENANCE_LOG_LIST_REFERENCE_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_REFERENCE_HEADING, request),
							SwtConstants.MAINTENANCE_LOG_LIST_REFERENCE_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING,
							5,
							Integer.parseInt(widths.get(SwtConstants.MAINTENANCE_LOG_LIST_REFERENCE_TAGNAME)),
							false,
							true,
							hiddenColumnsMap.get(SwtConstants.MAINTENANCE_LOG_LIST_REFERENCE_TAGNAME)
					);

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_REFERENCE_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}

				// DUP_ACTION column
				if (order.equals(SwtConstants.MAINTENANCE_LOG_LIST_DUP_ACTION_TAGNAME)) {
					tmpColumnInfo = new ColumnInfo(
							SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_DUP_ACTION_HEADING, request),
							SwtConstants.MAINTENANCE_LOG_LIST_DUP_ACTION_TAGNAME,
							SwtConstants.COLUMN_TYPE_STRING,
							6,
							Integer.parseInt(widths.get(SwtConstants.MAINTENANCE_LOG_LIST_DUP_ACTION_TAGNAME)),
							false,
							true,
							hiddenColumnsMap.get(SwtConstants.MAINTENANCE_LOG_LIST_DUP_ACTION_TAGNAME)
					);

					tmpColumnInfo.setHeaderTooltip(SwtUtil.getMessage(SwtConstants.MAINTENANCE_LOG_LIST_DUP_ACTION_HEADING_TOOLTIP, request));
					lstColumns.add(tmpColumnInfo);
				}
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ getGridColumns ] - Exit");
		}
		// return XML columns
		return lstColumns;
	}


	public String displayAngular()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration */
		String systemDate = null;
		MaintenanceLog maintenanceLog = null;
		String hostId = null;
		SystemFormats sysformat = null;
		int currentPage = 1;
		int maxPage = 0;
		int totalCount = 0;
		boolean isNext = false;
		ArrayList maintenanceLogList = new ArrayList();
		String currentFilter = null;
		String currentSort = null;
		String filterSortStatus = null;
		ArrayList<PageDetails> pageSummaryList = null;
		MaintenanceLogView maintenanceLogView = null;
		Date selectedFromDate = null;
		Date selectedToDate = null;
		/* Variable Declaration for fromDateAsString */
		String fromDateAsString = null;
		/* Variable Declaration for toDateAsString */
		String toDateAsString = null;
		try {
			log.debug(this.getClass().getName() + "- [defaultDetails] - Entry");
			/* get system date usin SWTUtil as String */
			systemDate = SwtUtil.getSystemDateString();
			maintenanceLog = (MaintenanceLog) (getMaintenanceLog());

			if ((maintenanceLog.getFromDateAsString() == null) || (maintenanceLog.getToDateAsString() == null)) {
				maintenanceLog.setFromDateAsString(systemDate);
				maintenanceLog.setToDateAsString(systemDate);
			} else {
				maintenanceLog.setFromDateAsString(maintenanceLog.getFromDateAsString());
				maintenanceLog.setToDateAsString(maintenanceLog.getToDateAsString());
			}
			fromDateAsString = maintenanceLog.getFromDateAsString();
			toDateAsString = maintenanceLog.getToDateAsString();

			if (request.getParameter("selectedFromDateChooser") != null) {
				fromDateAsString = request.getParameter("selectedFromDateChooser");
			}
			if (request.getParameter("selectedToDateChooser") != null) {
				toDateAsString = request.getParameter("selectedToDateChooser");
			}

			setMaintenanceLog(maintenanceLog);
			/* get hostid from Cachemanager */
			hostId = CacheManager.getInstance().getHostId();
			/*
			 * get system date formats from swtutil and stored as a systemformat
			 * object
			 */
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/*
			 * get from date and date format then parse the from date using
			 * swtutil
			 */
//			maintenanceLog.setFromDate(SwtUtil.parseDate(maintenanceLog
//					.getFromDateAsString(), sysformat.getDateFormatValue()));
//			maintenanceLog.setToDate(SwtUtil.parseDate(maintenanceLog
//					.getToDateAsString(), sysformat.getDateFormatValue()));

			maintenanceLog.setFromDate(SwtUtil.parseDate(fromDateAsString, sysformat.getDateFormatValue()));
			maintenanceLog.setToDate(SwtUtil.parseDate(toDateAsString, sysformat.getDateFormatValue()));

			maintenanceLogList = new ArrayList();

			if (request.getParameter("currentPage") != null) {
				currentPage =  Integer.parseInt(request.getParameter("currentPage"));
			}
			/* get the selected filter option from jsp */
			currentFilter = request.getParameter("selectedFilter");
			currentSort = request.getParameter("selectedSort");
			/* if any filter or sort option is selected bydefault its ALL */
			if (currentFilter == null) {
				currentFilter = "all";
			}
			if (currentSort == null) {
				currentSort = "none";
			}
			/*
			 * put filter and sort options together into a string with seperated
			 * by comma
			 */

			selectedFromDate= maintenanceLog.getFromDate();
			selectedToDate = maintenanceLog.getToDate();

			filterSortStatus = currentFilter + "," + currentSort;

			totalCount = mlogMgr.getMaintenanceLogList(hostId, selectedFromDate, selectedToDate,
					currentPage - 1, maxPage, maintenanceLogList,
					filterSortStatus, sysformat);

			/* Read PageSize from the property file and calculates the maxPages */
			int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
			maxPage = setMaxPageAttribute(totalCount, pageSize);
			pageSummaryList = new ArrayList<PageDetails>();
			PageDetails pSummary = new PageDetails();
			pSummary.setCurrentPageNo(currentPage);
			pSummary.setMaxPages(maxPage);
			pSummary.setTotalCount(totalCount);
			pageSummaryList.add(pSummary);

			// Hide pagination if no of records are "0" or records can be
			// displayed
			// in one page.
			if (maxPage <= 1) {
				request.setAttribute("hidePagination", true);
			}

			if (maxPage > 1)
				isNext = true;

			setMaintenanceLog(maintenanceLog);
			Iterator itr = maintenanceLogList.iterator();
			maintenanceLogView = new MaintenanceLogView();
			while (itr.hasNext()) {
				maintenanceLogView = (MaintenanceLogView) (itr.next());
				maintenanceLogView.setLogDate_Date(SwtUtil.formatDate(
						maintenanceLogView.getId().getLogDate(), sysformat
								.getDateFormatValue()));

				maintenanceLogView.setLogDateIso(SwtUtil.formatDate(
						maintenanceLogView.getId().getLogDate(), sysformat
								.getDateFormatValue()));


			}
			if ("none".equals(currentSort)) {
				currentSort = "0|true"; // default sorting column
			}

			/*
			 * Code added by venkat on 28-mar-2011 for Mantis 1308:"Show 'Last
			 * refresh' time in screens that have refresh function"
			 */
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					SwtUtil.getUserCurrentEntity(request.getSession())));
			request.setAttribute("currentPage", Integer.toString(currentPage));
			request.setAttribute("prevEnabled", "false");
			request.setAttribute("nextEnabled", "" + isNext);
			request.setAttribute("maxPage", Integer.toString(maxPage));
			request.setAttribute("selectedFilter", currentFilter);
			request.setAttribute("selectedSort", currentSort);
			request.setAttribute("pageSummaryList", pageSummaryList);
			request.setAttribute("maintenanceLogList", maintenanceLogList);
			request.setAttribute("fromDate", fromDateAsString);
			request.setAttribute("toDate", toDateAsString);
			request.setAttribute("totalCount", totalCount);

			//Angular part
			ResponseHandler responseHandler = null;
			SwtResponseConstructor responseConstructor = null;
			SwtXMLWriter xmlWriter = null;

			String dateFormat = null;
			String width = null;
			String columnOrder = null;
			String hiddenColumns = null;
			String defaultEntity = null;
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());

			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.MAINTENANCE_LOG_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("defaultEntity", "entityId");
			responseConstructor.createRowElement("fromDate", fromDateAsString);
			responseConstructor.createRowElement("toDate", toDateAsString);
			responseConstructor.createRowElement("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request, SwtUtil.getUserCurrentEntity(request.getSession())));
			responseConstructor.createElement("displayedDate",systemDate);
			responseConstructor.createElement("dateFormat",	dateFormat);
			responseConstructor.createElement("totalCount",	totalCount);
			xmlWriter.endElement(SwtConstants.SINGLETONS);


			/******* ErrorLogList ******/
			responseConstructor.formGridStart("grid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns, request));
			/*****  Form Paging Start ***********/
			if(currentPage > maxPage) {
				currentPage = maxPage;
			}
			responseConstructor.formPaging(new PageInfo( setMaxPageAttribute(totalCount, pageSize),currentPage));
			/*****  Form Paging End ***********/
			// form rows (records)

			responseConstructor.formRowsStart(maintenanceLogList.size());
			for (Iterator<MaintenanceLogView> it = maintenanceLogList.iterator(); it.hasNext();) {
				// Obtain error log record from iterator
				MaintenanceLogView maintenanceLogRecord = (MaintenanceLogView) it.next();
				responseConstructor.formRowStart();
//				String dateFormatted = SwtUtil.formatDate(maintenanceLogRecord.getErrorDate().toString(), SwtUtil.getCurrentDateFormat(request.getSession()));
				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_TAGNAME,(maintenanceLogRecord.getLogDate_Date() == null || maintenanceLogRecord.getLogDate_Date().equals("")) ? "" :  SwtUtil.formatDate(maintenanceLogRecord.getLogDate_Date(), dateFormat ));
				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_LOG_TIME_TAGNAME, maintenanceLogRecord.getLogDate_Time());
				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_USER_ID_TAGNAME, maintenanceLogRecord.getId().getUserId());
				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_IP_ADDRESS_TAGNAME, maintenanceLogRecord.getId().getIpAddress());
				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_TABLE_NAME_TAGNAME, maintenanceLogRecord.getId().getTableName());
				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_REFERENCE_TAGNAME, maintenanceLogRecord.getId().getReference());
				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_DUP_ACTION_TAGNAME, maintenanceLogRecord.getId().getDupaction());
				//hidden property
//				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_MAINSEQNO_TAGNAME, maintenanceLogRecord.getMainSeqNo());
				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_LOG_DATE_ISO_TAGNAME, maintenanceLogRecord.getLogDateIso());
				responseConstructor.createRowElement(SwtConstants.MAINTENANCE_LOG_LIST_ACTION_TAGNAME, maintenanceLogRecord.getId().getAction());

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.MAINTENANCE_LOG_LIST);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			log.debug("Exiting MaintenanceLogAction.'display' method");
			return getView("data");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			log
					.error("Exception Catch in MaintenanceLogAction.'defaultDetails' method : "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			log
					.error("Exception Catch in MaintenanceLogAction.'defaultDetails' method : "
							+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							e, "defaultDetails", MaintenanceLogAction.class), request,
					"");
			return getView("fail");
		} finally {
			systemDate = null;
			maintenanceLog = null;
			hostId = null;
			sysformat = null;
			maintenanceLogList = null;
			currentFilter = null;
			currentSort = null;
			filterSortStatus = null;
			pageSummaryList = null;
			maintenanceLogView = null;
		}
	}

}