/*
 * @(#)UserOptionsAction.java 1.0 06/12/05
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.web;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.control.model.UserMaintenance;
import org.swallow.control.model.UserOptions;
import org.swallow.control.service.SectionManager;
import org.swallow.control.service.UserMaintenanceManager;
import org.swallow.control.service.UserOptionsManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.SysParams;
import org.swallow.maintenance.service.SysParamsManager;
import org.swallow.model.User;
import org.swallow.security.jwt.TokensProvider;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SessionManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.util.UserThreadLocalHolder;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import org.swallow.util.struts.ActionError;
import org.swallow.util.struts.ActionErrors;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;

import org.swallow.web.XSSFilter;




import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/useroptions", "/useroptions.do"})
public class UserOptionsAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/control/useroptions");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}


	private UserOptions useroptions;
	public UserOptions getUseroptions() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		useroptions = RequestObjectMapper.getObjectFromRequest(UserOptions.class, request);
		return useroptions;
	}

	public void setUseroptions(UserOptions useroptions) {
		this.useroptions = useroptions;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("useroptions", useroptions);
	}



	private final Log log=LogFactory.getLog(UserOptionsAction.class);
	@Autowired
	private UserOptionsManager useroptionsManager;
	/**
	 * @param useroptionsManager The useroptionsManager to set.
	 */
	public void setUseroptionsManager(UserOptionsManager useroptionsManager) {
		this.useroptionsManager = useroptionsManager;
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "report":
				return report();
			case "change":
				return change();
			case "update":
				return update();
		}


		return unspecified();
	}
	/**
	 * @return ActionForward
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		//log.debug("exiting 'unspecified' method");
		setButtonStatus(request,SwtConstants.STR_TRUE,SwtConstants.STR_FALSE,SwtConstants.STR_FALSE);
		//	setButtonStatus(request,SwtConstants.STR_TRUE,SwtConstants.STR_FALSE,SwtConstants.STR_FALSE);
		return display();
	}

	/**
	 * @param request
	 * @param entityId
	 * @throws SwtException
	 */
	private void putCurrencyGroupDetailsinRequest(HttpServletRequest request,
												  String roleId, String entityId) throws SwtException {
//		CurrencyDetailVO currencyDetailsVO = currencyManager
//				.getCurrencyDetailList(entityId, hostId, "All");
		List currrencyGroupList=new ArrayList();
		HttpSession session = request.getSession();

		//String roleId = user.getRoleId();
		currrencyGroupList =(List) SwtUtil.getSwtMaintenanceCache().getCurrencyGroupFullORViewAcessLVL(roleId,entityId);
		currrencyGroupList.add(0,new LabelValueBean(SwtConstants.ALL_LABEL,SwtConstants.ALL_VALUE));
		request.setAttribute("currencyGroupList", currrencyGroupList);


	}
	/**
	 * @param request
	 * @return String
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		CacheManager cacheManagerInst = CacheManager.getInstance();
		String hostId = cacheManagerInst.getHostId();
		return hostId;
	}

	/**
	 * @param request
	 * @throws SwtException
	 */
	private void putSectionListInReq(HttpServletRequest request)
			throws SwtException {
		String hostId =  putHostIdListInReq(request);
		SectionManager sectionManager=(SectionManager)(SwtUtil.getBean("sectionManager"));
		Collection sectionlist = sectionManager.getSectionListAsLebelBean(hostId);
		request.setAttribute("sectiondetails",sectionlist);
		request.setAttribute("sectiondetails", sectionlist);
	}
	/**
	 * @param request
	 * @throws SwtException
	 */
	/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
	private void putLanguageListInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		CacheManager cacheManagerInst = CacheManager.getInstance();
		Collection languagelist=(ArrayList) cacheManagerInst.getMiscParamsLVL("LANG",entityId);
		request.setAttribute("languagedetails",languagelist);
	}
	/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */



	/**
	 * @param request
	 * @throws SwtException
	 */
	private void putRoleEntityAccessListInReq(HttpServletRequest request)
			throws SwtException {
		//getting  rntityId and entityName from join of Entity and EntityAccess table
		CommonDataManager CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
		User currUser = (User)CDM.getUser();
		String roleId = currUser.getRoleId();

		UserMaintenanceManager usermaintenanceManager=(UserMaintenanceManager)(SwtUtil.getBean("usermaintenanceManager"));
		Collection roleEntity = usermaintenanceManager.getRoleEntityAccess(roleId);
		request.setAttribute("roleEntityList" , roleEntity);
	}

	/**
	 * @param request
	 * @throws SwtException
	 */
	private void putRoleListInReq(HttpServletRequest request)
			throws SwtException {
		String hostId =  putHostIdListInReq(request);
		UserMaintenanceManager usermaintenanceManager=(UserMaintenanceManager)(SwtUtil.getBean("usermaintenanceManager"));
		//getting  roleId and roleName from Role table
		Collection roleList= usermaintenanceManager.getRoleList(hostId);
		request.setAttribute("roleIdList", roleList);
	}



	/**
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private String putCurrentUserInReq(HttpServletRequest request)
			throws SwtException {
		CommonDataManager CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
		User currUser = (User)CDM.getUser();
		String userId = currUser.getId().getUserId();
		return userId;
	}

	private void getUserInfo(HttpServletRequest request, UserOptions userOptions)
			throws SwtException
	{
		Date logindate = null;
		Date lastFailedndate = null;
		String lastFailedIP = null;
		String lastLoginIP = null;
		Date lastLogOutDate = null;
		Date udate = null;
		String extAuthId = null;
		//get and set last date of password change
		udate = userOptions.getPasswordchangedate();
		SystemFormats systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
		if(udate!=null)
		{
			String uDate = SwtUtil.formatDate(udate,
					systemFormats.getDateFormatValue());
			SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
			uDate = uDate + " " + sdf.format(udate);

			request.setAttribute("uDate", uDate);
		}

		//get and set lastLogin date Info
		logindate = userOptions.getLastlogin();
		if(logindate != null)
		{
			String loginDate = SwtUtil.formatDate(logindate,systemFormats.getDateFormatValue());
			SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
			loginDate = loginDate + " " + sdf.format(logindate);

			request.setAttribute("loginDate" , loginDate);
		}


		lastFailedIP = userOptions.getLastLoginFailedIp();
		if(lastFailedIP != null)
		{
			request.setAttribute("lastFailedIP" , lastFailedIP);
		}
		lastLoginIP = userOptions.getLastLoginIp();
		if(lastLoginIP != null)
		{
			request.setAttribute("lastLoginIP" , lastLoginIP);
		}

		lastFailedndate = userOptions.getLastLoginFailed();

		if(lastFailedndate != null)
		{
			String lastFailedndateDate = SwtUtil.formatDate(lastFailedndate,systemFormats.getDateFormatValue());
			SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
			lastFailedndateDate = lastFailedndateDate + " " + sdf.format(lastFailedndate);
			request.setAttribute("lastFaileddate" , lastFailedndateDate);
		}



		lastLogOutDate = userOptions.getLastlogout();
		if(lastLogOutDate != null)
		{
			String logoutDate = SwtUtil.formatDate(lastLogOutDate,systemFormats.getDateFormatValue());
			SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
			logoutDate = logoutDate + " " + sdf.format(lastLogOutDate);
			request.setAttribute("logoutDate" , logoutDate);
		}




		//Mantis 5709
		extAuthId = userOptions.getExtAuthId();
		if(extAuthId != null)
		{
			request.setAttribute("extAuthId" , extAuthId);
		}
	}


	/**
	 * @return SwtException
	 * @throws Exception
	 */
	public String display()
			throws SwtException
	{
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SysParams sysParams = null;
		SysParamsManager sysParamsManager = null;
		try
		{

			UserMaintenanceManager usermaintenanceManager=(UserMaintenanceManager)(SwtUtil.getBean("usermaintenanceManager"));

			String userId = putCurrentUserInReq(request);
			String hostId =  putHostIdListInReq(request);
			putSectionListInReq(request);
			/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
			String entityId=SwtUtil.getUserCurrentEntity(request.getSession());
			putLanguageListInReq(request,entityId);
			/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
			putRoleEntityAccessListInReq(request);
			putRoleEntityAccessListInReq(request);
			//putRoleListInReq(request);


// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm)form;

			UserOptions useroptions=useroptionsManager.fetchUserDetails(hostId,userId);
//			UserOptions useroptions = (UserOptions)getUseroptions();
			putCurrencyGroupDetailsinRequest(request,useroptions.getRoleId(),useroptions.getCurrententity());
			setButtonStatus(request,SwtConstants.STR_TRUE,SwtConstants.STR_FALSE,SwtConstants.STR_FALSE);
			getUserInfo(request,useroptions);
			if(SwtUtil.isEmptyOrNull(useroptions.getAmountDelimiter())||SwtUtil.isEmptyOrNull(useroptions.getDateFormat())) {
				// Getting system parameter details from manager class
				sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
				sysParams = sysParamsManager.getSysParamsDetail(hostId, SwtUtil.getCurrentSystemFormats(request.getSession()));
				String amountDelimiter =sysParams.getAmountDelimiter();
				String dateformat=sysParams.getDateFormat();

				if(SwtUtil.isEmptyOrNull(useroptions.getAmountDelimiter())) {
					useroptions.setAmountDelimiter(amountDelimiter);
				}

				if(SwtUtil.isEmptyOrNull(useroptions.getDateFormat())) {
					useroptions.setDateFormat(dateformat);
				}
			}
			putUserRoleInReq(request,useroptions );
			setUseroptions(useroptions);
			// Setting the attributes
			request.setAttribute("screenFieldsStatus","true");
			request.setAttribute("methodName","view");

			/* START: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
			SwtUtil.getMenuEntityCurrGrpAccess(
					request,null,null);
			/* END: Code changed as par ING requirements to the Roles - Menu Access, 11-JUN-2007 */
			request.setAttribute("formatAltered", request
					.getParameter("formatAltered"));
			return getView("success");
		}catch(SwtException swtexp){
			SwtUtil.logException(swtexp,request,"");
			return getView("fail");
		}catch(Exception e){
			e.printStackTrace();
			SwtUtil. logException (SwtErrorHandler.getInstance().handleException(e,"display",UserOptionsAction.class), request,"");
			return getView("fail");
		}
	}

	/* Start:Vivekanandan:15-12-2008:Added
	 * as per given in SRS_Jasper_UserReport_1.0_in_progress */
	/**
	 * Method to generate User Reports when user click on Print button
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String report()throws SwtException{
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName()+" - [ report ]- "+"Entry");
		/* Method's local variable declaration */
		String userId;
		ServletOutputStream out;
		JasperPrint jasperPrint;
		JRPdfExporter pdfExporter;
		UserMaintenanceManager usermaintenanceManager;
		String fileName = null;
		/* Start : Vivekanandan : 20-02:2008 : Mantis 907
		 * Added variable to Export in Excel and PDF  */
		String fileType =null;
		JRXlsExporter xlsxporter;
		String sheetName[] = new String[1];
		/* End : Vivekanandan : 20-02:2008 : Mantis 907
		 * Added variable to Export in Excel and PDF  */
		try{
			/* Read UserId from request */
			userId = request.getParameter("selectedUserCodeId");
			/* Read file type from request */
			fileType = request.getParameter("fileType");
			/* Get bean object of UserMaintenanceManager from SwtUtil*/
			usermaintenanceManager = (UserMaintenanceManager)SwtUtil.getBean("usermaintenanceManager");

			out = response.getOutputStream();
			/* Get user details for given userId */
			jasperPrint = usermaintenanceManager.getUserReport(request, userId);

			response.setContentType("application/pdf");

			//START:Code added to change the report file name by Thirumurugan on 13-Jan-2009
			if(!"".equals(userId))
				fileName="UserReport-SmartPredict_"+userId+"_"+SwtUtil.formatDate(new Date(),"yyyyMMdd")+"_"+
						SwtUtil.formatDate(new Date(),"HHmmss");
			else
				fileName="UserReport-SmartPredict_"+SwtUtil.formatDate(new Date(),"yyyyMMdd")+"_"+
						SwtUtil.formatDate(new Date(),"HHmmss");

			/* Start : Vivekanandan : 20-02:2008 : Mantis 907
			 * Added conditions to Export in Excel and PDF  */
			/* Condition to check filType is pdf */
			if(fileType.equals("pdf")){
				pdfExporter = new JRPdfExporter();
				response.setContentType("application/pdf");
				response.setHeader("Content-disposition", "attachment; filename="
						+ fileName + ".pdf");


				//END:Code added to change the report file name by Thirumurugan on 09-Jan-2009
				pdfExporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);

				// Providing the output stream
				pdfExporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				pdfExporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,"UTF-8");

				// Export Report to PDF
				pdfExporter.exportReport();

				/* Condition to check fileType is xls */
			}else if(fileType.equals("excel")){
				xlsxporter = new JRXlsExporter();
				response.setContentType("application/xls");
				response.setHeader("Content-disposition", "attachment; filename="
						+ fileName + ".xls");


				xlsxporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);

				// Providing the output stream
				xlsxporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				xlsxporter.setParameter(JRExporterParameter.CHARACTER_ENCODING,"UTF-8");
				xlsxporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET,Boolean.TRUE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.TRUE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.TRUE);
				//modified for jasper jar  upgrade-Mantis 1648
				xlsxporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				/* Set the sheet name as userId */
				sheetName[0] = userId;
				xlsxporter.setParameter(JRXlsExporterParameter.SHEET_NAMES, sheetName);
				// Export Report to Excel
				xlsxporter.exportReport();
			}
			/* End : Vivekanandan : 20-02:2008 : Mantis 907
			 * Added conditions to Export in Excel and PDF  */
		}catch(Exception exp){
			log.debug(this.getClass().getName()+ "- [report] - Exception "
					+ exp.getMessage());
			log.error(this.getClass().getName()+ "- [report] - Exception "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "report", UserOptionsAction.class), request, "");
		}
		log.debug(this.getClass().getName()+" - [ report ]- "+"Exit");
		return null;
	}

	/* End:Vivekanandan:15-12-2008:Added
	 * as per given in SRS_Jasper_UserReport_1.0_in_progress */

	/**
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		try {

			UserMaintenanceManager usermaintenanceManager = (UserMaintenanceManager) (SwtUtil
					.getBean("usermaintenanceManager"));

			String userId = putCurrentUserInReq(request);
			String hostId = putHostIdListInReq(request);
			putSectionListInReq(request);

// To remove: 			DynaValidatorForm dyForm = (DynaValidatorForm) form;

			UserOptions useroptions = useroptionsManager.fetchUserDetails(
					hostId, userId);
			useroptions.setCurrententity(request.getParameter("currententity"));
			useroptions.setLanguage(request.getParameter("language"));
			useroptions.setPhonenumber(request.getParameter("phonenumber"));
			useroptions.setEmailId(request.getParameter("emailId"));
			useroptions.setAmountDelimiter(request.getParameter("amountDelimiter"));
			useroptions.setDateFormat(request.getParameter("dateFormat"));

			if (!SwtUtil.isEmptyOrNull(request.getParameter("entityChange"))
					&& request.getParameter("entityChange").equalsIgnoreCase("Y"))
				useroptions.setCurrentCcyGrpId("All");

			if(SwtUtil.isEmptyOrNull(useroptions.getAmountDelimiter())||SwtUtil.isEmptyOrNull(useroptions.getDateFormat())) {
				// Getting system parameter details from manager class
				SysParamsManager sysParamsManager = (SysParamsManager) SwtUtil.getBean("sysParamsManager");
				SysParams sysParams = sysParamsManager.getSysParamsDetail(hostId, SwtUtil.getCurrentSystemFormats(request.getSession()));
				String amountDelimiter =sysParams.getAmountDelimiter();
				String dateformat=sysParams.getDateFormat();

				if(SwtUtil.isEmptyOrNull(useroptions.getAmountDelimiter())) {
					useroptions.setAmountDelimiter(amountDelimiter);
				}

				if(SwtUtil.isEmptyOrNull(useroptions.getDateFormat())) {
					useroptions.setDateFormat(dateformat);
				}
			}

			putSectionListInReq(request);
			/*
			 * Start: code Added/Modified by Arumugam on 03-Dec-2010 for
			 * Mantis:0001296- Misc Param to added the entityId field in table
			 * and check the entityId
			 */
			putLanguageListInReq(request, useroptions.getCurrententity());
			/*
			 * End: code Added/Modified by Arumugam on 03-Dec-2010 for
			 * Mantis:0001296- Misc Param to added the entityId field in table
			 * and check the entityId
			 */
			putRoleEntityAccessListInReq(request);
			putUserRoleInReq(request,useroptions );
			putCurrencyGroupDetailsinRequest(request, useroptions.getRoleId(),
					useroptions.getCurrententity());
			getUserInfo(request, useroptions);

			/*
			 * Start : Code added for setting the user status to the form Object
			 * from session. To fix the Issue reported by Raghav, Getting null
			 * pointer exception while Updating the user details from User
			 * maintenance screen. Modified by Balaji on 22-07-2008
			 */

			User currentUser = (User) SwtUtil.getCurrentUser(request
					.getSession());
			useroptions.setStatus(currentUser.getStatus());
			/* End : Code added for setting user status */
			// setting the attributes
			setUseroptions(useroptions);

			request.setAttribute("methodName", "update");
			request.setAttribute("screenFieldsStatus", "true");
			setButtonStatus(request, SwtConstants.STR_FALSE,
					SwtConstants.STR_TRUE, SwtConstants.STR_TRUE);
			return getView("success");
		} catch (SwtException swtexp) {
			swtexp.printStackTrace();
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception e) {
			e.printStackTrace();
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "change",
							UserOptionsAction.class), request, "");
			return getView("fail");
		}
	}

	private void updateCurrentUser(UserMaintenance user, HttpServletRequest request, String hostId, String userId)
			throws SwtException {
		CommonDataManager cdm = ((CommonDataManager) request.getSession()
				.getAttribute(SwtConstants.CDM_BEAN));
		User currentUser = cdm.getUser();

		if (user.getId().getUserId().equals(currentUser.getId().getUserId())) {
			// Repopulating current user with the one that has been saved into
			// DB
			currentUser.getId().setUserId(user.getId().getUserId());
			currentUser.setCurrentEntity(user.getCurrententity());
			currentUser.setCurrentCcyGrpId(user.getCurrentCcyGrpId());
			currentUser.setRoleId(user.getRoleId());
			currentUser.setUserName(user.getUsername());
			currentUser.setLanguage(user.getLanguage());
			currentUser.setEmailId(user.getEmailId());
			currentUser.setPhoneNumber(user.getPhonenumber());
			currentUser.setPassword(user.getPassword());
			// Generate JWT token
			String bearerToken = TokensProvider.getInstance().createToken(currentUser, (request.getSession() != null?request.getSession().getId():null));
			cdm.setBearerToken(bearerToken);
			currentUser.setUpdateUser(UserThreadLocalHolder.getUser());
			// Updating the CDM request object for further reference.
			cdm.setUser(currentUser);
			request.setAttribute(SwtConstants.CDM_BEAN, cdm);
		}

	}

	/**
	 * Puts only the user's current role in the request.
	 *
	 * @param request
	 * @param userOptionsDB User options fetched from the database
	 * @throws SwtException
	 */
	private void putUserRoleInReq(HttpServletRequest request, UserOptions userOptionsDB) throws SwtException {
		String currentRoleId = userOptionsDB.getRoleId(); // Fetch the user's role ID
		List<LabelValueBean> userRoleList = new ArrayList<>();
		userRoleList.add(new LabelValueBean("User Role", currentRoleId)); // Add only the current role

		request.setAttribute("roleIdList", userRoleList); // Set it to the request
	}


	/**
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException
	{
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		ActionErrors errors = new ActionErrors();
		UserOptions useroptions = (UserOptions)getUseroptions();

		String hostId = putHostIdListInReq(request);

		String userId = putCurrentUserInReq(request);
		request.setAttribute("currencyGroupList",new ArrayList());
		putSectionListInReq(request);
		/*Start: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
		putLanguageListInReq(request,useroptions.getCurrententity());
		/*End: code Added/Modified by Arumugam on 03-Dec-2010 for Mantis:0001296- Misc Param to added the entityId field in table and check the entityId */
		putRoleEntityAccessListInReq(request);
		putRoleListInReq(request);
		// Get current user and validate
		User currentUser =(User) SwtUtil.getCurrentUser(request.getSession());
		if (currentUser == null) {
			CommonDataManager cdm = null;
			cdm = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN));
			request.setAttribute("errordesc",
					SwtUtil.getMessage("errors.authorization.attack", request));
			request.setAttribute("errorCause", "");
			Log log = LogFactory.getLog(XSSFilter.class);
			log.error(SwtUtil.getMessage("errors.authorization.attack.log", request) + "\n" +
					"{" + SwtUtil.getMessage("errors.user.log", request) + ": " + cdm.getUser().getId().getUserId() +
					", " + SwtUtil.getMessage("errors.ipAddress.log", request) + ": " + request.getRemoteAddr() +
					", " + SwtUtil.getMessage("errors.requestURI.log", request) + ": " + request.getAttribute("orginalURL") +
					", Url Path : "+request.getAttribute("orginalURL") + "}");

			try {
				response.sendRedirect(request.getContextPath() + "/invalidRequest.jsp");
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
			return getView("fail");
		}
		useroptions.setStatus(currentUser.getStatus());
		useroptions.getId().setHostId(hostId);

		// Validate input against allowed lists
		if (!validateInputAgainstAllowedLists(useroptions, request)) {
			CommonDataManager cdm = null;
			cdm = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN));
			request.setAttribute("errordesc",
					SwtUtil.getMessage("errors.authorization.attack", request));
			request.setAttribute("errorCause", "");
			Log log = LogFactory.getLog(XSSFilter.class);
			log.error(SwtUtil.getMessage("errors.authorization.attack.log", request) + "\n" +
					"{" + SwtUtil.getMessage("errors.user.log", request) + ": " + cdm.getUser().getId().getUserId() +
					", " + SwtUtil.getMessage("errors.ipAddress.log", request) + ": " + request.getRemoteAddr() +
					", " + SwtUtil.getMessage("errors.requestURI.log", request) + ": " + request.getAttribute("orginalURL") +
					", Url Path : "+request.getAttribute("orginalURL") + "}");

			try {
				response.sendRedirect(request.getContextPath() + "/invalidRequest.jsp");
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
			return ("fail");
		}

		try {
			// Prepare user maintenance object with validated and allowed changes
			UserMaintenance userMaintenance = prepareUserMaintenanceWithAllowedChanges(useroptions, currentUser);
			updateCurrentUser(userMaintenance, request, hostId,userId);
			// System info for logging
			SystemInfo systemInfo = new SystemInfo();
			systemInfo.setIpAddress(request.getRemoteAddr());
			useroptions.getId().setHostId(hostId);
			// Update user details
			useroptionsManager.updateUserDetails(
					useroptions,
					userMaintenance,
					systemInfo,
					SwtUtil.getCurrentSystemFormats(request.getSession())
			);

			// Update session attributes
			updateUserSessionAttributes(request, useroptions);

			// Set success attributes
			request.setAttribute("methodName", "view");
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("updated", "updated");

			return getView("success");

		} catch (SwtException swtexp) {
			// Error handling
			SwtUtil.logErrorInDatabase(swtexp);
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("success");
		} catch (Exception e) {
			// Generic error handling
			saveErrors(request, SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(e, "update", UserOptionsAction.class),
					request,
					""
			));
			return getView("success");
		}
	}

	private boolean validateInputAgainstAllowedLists(UserOptions useroptions, HttpServletRequest request) throws SwtException {
		CommonDataManager CDM = (CommonDataManager) request.getSession().getAttribute("CDM");
		User currUser = (User) CDM.getUser();

		UserMaintenanceManager usermaintenanceManager=(UserMaintenanceManager)(SwtUtil.getBean("usermaintenanceManager"));
		Collection entityList = usermaintenanceManager.getRoleEntityAccess(currUser.getRoleId());
		// Validate entity
		boolean validEntity = entityList.stream()
				.anyMatch(entity -> ((LabelValueBean) entity).getValue().equals(useroptions.getCurrententity()));

		// Validate currency group
		List currencyGroupList = (List) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupFullORViewAcessLVL(currUser.getRoleId(), useroptions.getCurrententity());

		currencyGroupList.add(0,new LabelValueBean(SwtConstants.ALL_LABEL,SwtConstants.ALL_VALUE));

		boolean validCurrencyGroup = currencyGroupList.stream()
				.anyMatch(group -> ((LabelValueBean) group).getValue().equals(useroptions.getCurrentCcyGrpId()));

		// Validate language
		List languageList = (ArrayList) CacheManager.getInstance()
				.getMiscParamsLVL("LANG", useroptions.getCurrententity());
		boolean validLanguage = languageList.stream()
				.anyMatch(lang -> ((LabelValueBean) lang).getValue().equals(useroptions.getLanguage()));

		// Additional field validations
		boolean validPhoneNumber = isValidPhoneNumber(useroptions.getPhonenumber());
		boolean validEmail = isValidEmail(useroptions.getEmailId());

		return validEntity && validCurrencyGroup && validLanguage && validPhoneNumber && validEmail;
	}

	private UserMaintenance prepareUserMaintenanceWithAllowedChanges(UserOptions useroptions, User currentUser) throws SwtException {
		String hostId = CacheManager.getInstance().getHostId();
		UserMaintenance userMaintenance = new UserMaintenance();

		// Set immutable fields from current user
		userMaintenance.getId().setHostId(hostId);
		userMaintenance.getId().setUserId(currentUser.getId().getUserId());
		userMaintenance.setUsername(currentUser.getUserName());
		userMaintenance.setSectionid(currentUser.getSectionId());
		userMaintenance.setRoleId(currentUser.getRoleId());
		userMaintenance.setStatus(useroptions.getStatus());


		// Only allow updates to specific fields
		userMaintenance.setCurrententity(useroptions.getCurrententity());
		userMaintenance.setCurrentCcyGrpId(useroptions.getCurrentCcyGrpId());
		userMaintenance.setLanguage(useroptions.getLanguage());
		userMaintenance.setPhonenumber(useroptions.getPhonenumber());
		userMaintenance.setEmailId(useroptions.getEmailId());

		// Fetch and retain existing password
		UserMaintenanceManager usermaintenanceManager =
				(UserMaintenanceManager) SwtUtil.getBean("usermaintenanceManager");
		userMaintenance.setPassword(
				usermaintenanceManager.fetchUserDetail(hostId, userMaintenance.getId().getUserId()).getPassword()
		);

		return userMaintenance;
	}

	private boolean isValidPhoneNumber(String phoneNumber) {
		// Simple validation to check if it's numbers only
		return phoneNumber == null || phoneNumber.isEmpty() || phoneNumber.matches("^\\d*$");
	}

	private boolean isValidEmail(String email) {
		// Simple validation to check for basic email structure
		return email == null || email.isEmpty() || email.matches(".*@.*\\..*");
	}

	private void updateUserSessionAttributes(HttpServletRequest request, UserOptions useroptions) throws SwtException {
		// Retrieve the user's session based on host and user ID
		HttpSession httpSession = SessionManager.getInstance()
				.getUserSession(CacheManager.getInstance().getHostId(), useroptions.getId().getUserId());

		if (httpSession != null) {
			// Get the CommonDataManager from the session
			CommonDataManager cdm = (CommonDataManager)httpSession.getAttribute(SwtConstants.CDM_BEAN);

			if (cdm != null && cdm.getUser() != null) {
				User changeUser = cdm.getUser();

				// Update user's current entity and currency group
				changeUser.setCurrentEntity(useroptions.getCurrententity());
				changeUser.setCurrentCcyGrpId(useroptions.getCurrentCcyGrpId());

				// Check for format changes
				boolean isDateFormatChanged= false;
				boolean isCurrencyFormatChanged= false;

				// Check currency format change
				String currentCurrencyFormat=changeUser.getAmountDelimiter();
				String newCurrencyFormat=useroptions.getAmountDelimiter();
				if (currentCurrencyFormat != null && !currentCurrencyFormat.equals(newCurrencyFormat)) {
					isCurrencyFormatChanged = true;
				}

				// Check date format change
				String currentDateFormat=changeUser.getDateFormat();
				String newDateFormat=useroptions.getDateFormat();
				if (currentDateFormat != null && !currentDateFormat.equals(newDateFormat)) {
					isDateFormatChanged = true;
				}

				// Set format change flag in request if needed
				if (isDateFormatChanged || isCurrencyFormatChanged ) {
					request.setAttribute("formatAltered","Y");
				}

				// Update system formats
				SystemFormats systemFormats = new SystemFormats();
				systemFormats.setCurrencyFormat(SwtConstants.CURRENCY_PAT + useroptions.getAmountDelimiter());

				// Determine date format
				String dateFormat = (SwtConstants.DATE_PAT + useroptions.getDateFormat()).equals("datePat1")
						? "dd/MM/yyyy"
						: "MM/dd/yyyy";

				// Update CommonDataManager with new formats
				cdm.setDateFormatValue(dateFormat);
				cdm.setCurrencyFormatValue(useroptions.getAmountDelimiter());
				systemFormats.setDateFormatValue(dateFormat);

				cdm.setSystemFormats(systemFormats);
				cdm.setDateFormat(SwtConstants.DATE_PAT + useroptions.getDateFormat());

				// Update user's date and currency formats
				changeUser.setDateFormat(useroptions.getDateFormat());
				changeUser.setAmountDelimiter(useroptions.getAmountDelimiter());
			}
		}
	}



	/**
	 * @param req
	 * @param changeStatus
	 * @param saveStatus
	 * @param cancelStatus
	 */
	private void setButtonStatus(HttpServletRequest req, String changeStatus,String saveStatus, String cancelStatus)
	{
		req.setAttribute(SwtConstants.CHG_BUT_STS,changeStatus );
		req.setAttribute(SwtConstants.SAV_BUT_STS ,saveStatus);
		req.setAttribute(SwtConstants.CAN_BUT_STS,cancelStatus );


	}



}