/*
 * @(#)EntityProcess.java 1.0 16/05/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.model;

import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * 
 * <AUTHOR>
 * 
 * Class contain setter,getter methods. These methods used to getting and
 * setting the values
 * 
 */
public class EntityProcess extends BaseObject implements
		org.swallow.model.AuditComponent {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	// Variable to hold run Time
	private String runTime = null;
	// Variable to hold run Order
	private String runOrder = null;
	// Object to hold the inner class object
	private Id id = new Id();
	// Variable to hold Log Table
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	static {
		logTable.put("runTime", "Run Time");
		logTable.put("runOrder", "Run Order");
	}

	// This class used to set and get primary key value for table
	public static class Id extends BaseObject {
		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		// Variable to hold process Name
		private String processName = null;
		// Variable to hold entityId
		private String entityId = null;

		public Id() {
		}

		// This constructor used to set the process name and entityid
		public Id(String processName, String entityId) {
			this.processName = processName;
			this.entityId = entityId;
		}

		/**
		 * Getter method for processName
		 * 
		 * @return processName as String
		 */
		public String getProcessName() {
			return processName;
		}

		/**
		 * Setter method for processName
		 * 
		 * @param processName
		 */
		public void setProcessName(String processName) {
			this.processName = processName;
		}

		/**
		 * Getter method for entityId
		 * 
		 * @return entityId as String
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * Setter method for entityId
		 * 
		 * @param entityId
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

	}

	/**
	 * Getter method for runTime
	 * 
	 * @return runTime as String
	 */
	public String getRunTime() {
		return runTime;
	}

	/**
	 * Setter method for runTime
	 * 
	 * @param runTime
	 */
	public void setRunTime(String runTime) {
		this.runTime = runTime;
	}

	/**
	 * Getter method for runOrder
	 * 
	 * @return runOrder as String
	 */
	public String getRunOrder() {
		return runOrder;
	}

	/**
	 * Setter method for runOrder
	 * 
	 * @param runOrder
	 */
	public void setRunOrder(String runOrder) {
		this.runOrder = runOrder;
	}

	/**
	 * Getter method for id
	 * 
	 * @return id as Id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 */
	public void setId(Id id) {
		this.id = id;
	}

}
