/*
 * Created on Dec 21, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.model;

import org.swallow.model.BaseObject;
import org.swallow.util.*;
import java.util.*;

/**
 * <AUTHOR>
 * 
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
public class Role extends BaseObject implements
		org.swallow.model.AuditComponent {
	private String hostId;
	private String roleId;
	private String roleName;
	private String alertType = SwtConstants.ALERT_BOTH;
	private String inputInterruptionAlertType = SwtConstants.ALERT_BOTH;
	private Date updateDate = new Date();
	private String updateUser;
	private String authorizeInput;
	private String allowMsdMultiMvtUpdates;
	/*Start code added by arumugam for mantis 1029 on 23-Nov-2009:Added account access in Role screen*/
	private String accountAccessControl;
	/*End code added by arumugam for mantis 1029 on 23-Nov-2009:Added account access in Role screen*/ 

	/*
	 * Code modified as per mail from <PERSON> and forwaded by <PERSON> for Amending the
	 * User Profile Changes on 17-APR-2008 Reference SRS :
	 * Smart-Predict_SRS_USER_PROFILE_0.2.doc by <PERSON> and <PERSON>
	 * Description: It is required to have a user whose sole purpose to be able
	 * to reset user passwords Start code:variable declared for setting &
	 * getting the values from the screen & database
	 */
	private String advancedUser;
	/*
	 * End code:variable declared for setting & getting the values from the
	 * screen & database
	 */

	/*
	 * Code modified for mantis issue 597 on 28-May-2008 Reference SRS :
	 * interface_interruption_notifications_1.pdf by Neil Description: It is
	 * required to check whether the user has privilege to view Input
	 * interruption notification--> Start code:code added for Input interruption
	 * notification
	 */
	private String inputInterruption;
	/* End code:code added Input Interruption Notification on 28-May-2008 */

	/*--START: CODE ADDED FOR SRS CURRENCY MONITOR 1.3 ON 11 - JUL - 2007 BY AJESH SINGH--*/
	private String restrictLocations;
	/*--START: CODE ADDED FOR SRS IntraDay Liquidity 0.3 ON 02 - DEC - 2013 BY ATEF SRIDI--*/
	private String maintainAnyIlmScenario;
	private String maintainAnyIlmGroup;
	private String maintainAnyReportHist;
	private String maintainAnyPCFeature;

	/*--END: CODE ADDED FOR SRS IntraDay Liquidity 0.3 ON 02 - DEC - 2013 BY ATEF SRIDI--*/


	/**
	 * @return Returns the restrictLocation.
	 */
	public String getRestrictLocations() {
		return restrictLocations;
	}

	/**
	 * @param restrictLocation
	 *            The restrictLocation to set.
	 */
	public void setRestrictLocations(String restrictLocations) {
		this.restrictLocations = restrictLocations;
	}

	/**
	 * @return Returns the authorizeInput.
	 */
	/*--END: CODE ADDED FOR SRS CURRENCY MONITOR 1.3 ON 11 - JUL - 2007 BY AJESH SINGH--*/

	private String allEntityOption;

	public String getAuthorizeInput() {
		return authorizeInput;
	}

	/**
	 * @param authorizeInput
	 *            The authorizeInput to set.
	 */
	public void setAuthorizeInput(String authorizeInput) {
		this.authorizeInput = authorizeInput;
	}

	public static Hashtable logTable = new Hashtable();
	static {
		logTable.put("roleName", "Role Name");
		logTable.put("alertType", "Alert Type");
		logTable.put("authorizeInput", "Authorize Input");
		logTable.put("restrictLocations", "Restrict Locations");

	}

	/**
	 * @return Returns the alertType.
	 */
	public String getAlertType() {
		return alertType;
	}

	/**
	 * @param alertType
	 *            The alertType to set.
	 */
	public void setAlertType(String alertType) {
		this.alertType = alertType;
	}

	/**
	 * @return Returns the hostId.
	 */
	public String getHostId() {
		return hostId;
	}

	/**
	 * @param hostId
	 *            The hostId to set.
	 */
	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	/**
	 * @return Returns the roleId.
	 */
	public String getRoleId() {
		return roleId;
	}

	/**
	 * @param roleId
	 *            The roleId to set.
	 */
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	/**
	 * @return Returns the roleName.
	 */
	public String getRoleName() {
		return roleName;
	}

	/**
	 * @param roleName
	 *            The roleName to set.
	 */
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return Returns the allEntityOption.
	 */
	public String getAllEntityOption() {
		return allEntityOption;
	}

	/**
	 * @param allEntityOption
	 *            The allEntityOption to set.
	 */
	public void setAllEntityOption(String allEntityOption) {
		this.allEntityOption = allEntityOption;
	}

	/*
	 * Code modified as per mail from Steve and forwaded by JP for Amending the
	 * User Profile Changes on 17-APR-2008
	 * Reference SRS : Smart-Predict_SRS_USER_PROFILE_0.2.doc by James Cook and
	 * David
	 * Description: It is required to have a user whose sole purpose to be able
	 * to reset user passwords
	 * Start code:methods will return and set the values that is being set from
	 * the screen or database
	 */

	public String getAdvancedUser() {
		return advancedUser;
	}

	public void setAdvancedUser(String advancedUser) {
		this.advancedUser = advancedUser;
	}

	/*
	 * End code:variable declared for setting & getting the values from the
	 * screen & database
	 */

	/*
	 * Code modified for mantis issue 597 on 28-May-2008 Reference SRS :
	 * interface_interruption_notifications_1.pdf by Neil Description: It is
	 * required to check whether the user has privilege to view Input
	 * interruption notification--> Start code:code added for Input interruption
	 * notification
	 */

	public String getInputInterruption() {
		return inputInterruption;
	}

	public void setInputInterruption(String inputInterruption) {
		this.inputInterruption = inputInterruption;
	}
	/* End :Code added Input Interruption Notification on 28-May-2008 */
	
	/*Start code added by arumugam for mantis 1029 on 23-Nov-2009:Added account access in Role screen*/
	/**
	 * Returns the AccountAccessControl.
	 * 
	 * @param none
	 * @return String
	 */
	public String getAccountAccessControl() {
		return accountAccessControl;
	}

	/**
	 * The AccountAccessControl to set.
	 * 
	 * @param accountAccessControl
	 * @return none           
	 */
	public void setAccountAccessControl(String accountAccessControl) {
		this.accountAccessControl = accountAccessControl;
	}
	/*End code added by arumugam for mantis 1029 on 23-Nov-2009:Added account access in Role screen*/

	public String getMaintainAnyIlmScenario() {
		return maintainAnyIlmScenario;
	}

	public void setMaintainAnyIlmScenario(String maintainAnyIlmScenario) {
		this.maintainAnyIlmScenario = maintainAnyIlmScenario;
	}

	public String getMaintainAnyIlmGroup() {
		return maintainAnyIlmGroup;
	}

	public void setMaintainAnyIlmGroup(String maintainAnyIlmGroup) {
		this.maintainAnyIlmGroup = maintainAnyIlmGroup;
	}

	public String getMaintainAnyReportHist() {
		return maintainAnyReportHist;
	}

	public void setMaintainAnyReportHist(String maintainAnyReportHist) {
		this.maintainAnyReportHist = maintainAnyReportHist;
	}

	public String getMaintainAnyPCFeature() {
		return maintainAnyPCFeature;
	}

	public void setMaintainAnyPCFeature(String maintainAnyPCFeature) {
		this.maintainAnyPCFeature = maintainAnyPCFeature;
	}

	public String getInputInterruptionAlertType() {
		return inputInterruptionAlertType;
	}

	public void setInputInterruptionAlertType(String inputInterruptionAlertType) {
		this.inputInterruptionAlertType = inputInterruptionAlertType;
	}

	public String getAllowMsdMultiMvtUpdates() {
		return allowMsdMultiMvtUpdates;
	}

	public void setAllowMsdMultiMvtUpdates(String allowMsdMultiMvtUpdates) {
		this.allowMsdMultiMvtUpdates = allowMsdMultiMvtUpdates;
	}
}
