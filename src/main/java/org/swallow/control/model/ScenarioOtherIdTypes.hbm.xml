<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.ScenarioOtherIdTypes" table="P_SCENARIO_OTHER_ID_TYPES" >


		<id name="otheridtype" column="OTHER_ID_TYPE">
			<generator class="assigned" />
		</id>
		<property name="description" column="DESCRIPTION" not-null="false"/>
			
	</class>
</hibernate-mapping>
