<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.control.model.Scenario" table="P_SCENARIO" >
		<composite-id class="org.swallow.control.model.Scenario$Id" name="id" unsaved-value="any">
		   <key-property name="scenarioId" access="field" column="SCENARIO_ID"/>
		</composite-id>
			
		<property name="title" column="TITLE" not-null="false"/>
		<property name="description" column="DESCRIPTION" not-null="false"/>
		<property name="activeFlag" column="ACTIVE_FLAG" not-null="false"/>
		<property name="systemFlag" column="SYSTEM_FLAG" not-null="false"/>
		<property name="displayOrder" column="DISPLAY_ORDER" not-null="false"/>
		<property name="summaryGrouping" column="SUMMARY_GROUPING" not-null="false"/>
		<property name="runEvery" column="RUN_EVERY" not-null="false"/>
		<property name="startTime" column="START_TIME" not-null="false"/>
		<property name="endTime" column="END_TIME" not-null="false"/>
		<property name="emailWhenDiff" column="EMAIL_PCT_DIFF" not-null="false"/>
		<property name="queryText" column="QUERY_TEXT" not-null="false"/>
		<property name="secHostCol" column="SEC_HOST_COL" not-null="false"/>
		<property name="secEntityCol" column="SEC_ENTITY_COL" not-null="false"/>
		<property name="secCurrencyCol" column="SEC_CURRENCY_COL" not-null="false"/>
		<property name="amtThresholdCol" column="AMT_THRESHOLD_COL" not-null="false"/>
		<property name="useGenericDisplay" column="USE_GENERIC_DISPLAY" not-null="false"/>
		<property name="facilityRefCols" column="FACILITY_REF_COLS" not-null="false"/>
		<property name="facilityParamVals" column="FACILITY_PARAM_VALS" not-null="false"/>
		<property name="categoryId" column="CATEGORY_ID" not-null="true"/>
		<many-to-one  lazy="false"
				   name="facility"
				   class="org.swallow.control.model.Facility"
				   column="FACILITY_ID" 
				   not-null="false" 
				   outer-join="true"
				   update="true" 
				   insert="true" />	 
	   <property name="signCol" column="SIGN_COL" not-null="false"/>			
	   <property name="accountColumn" column="ACCOUNT_ID_COL" not-null="false"/>
	   <property name="valueDateColumn" column="VALUE_DATE_COL" not-null="false"/>
	   <property name="mvtColumn" column="MOVEMENT_ID_COL" not-null="false"/>
	   <property name="matchColumn" column="MATCH_ID_COL" not-null="false"/>
	   <property name="sweepColumn" column="SWEEP_ID_COL" not-null="false"/>
	   <property name="paymentColumn" column="PAYMENT_ID_COL" not-null="false"/>			
	   <property name="recordScenarioInstance" column="RECORD_SCENARIO_INSTANCES" not-null="false"/>
	   <property name="criticalGuiHighlight" column="CRITICAL_GUI_HIGHLIGHT" not-null="false"/>	   
	   <property name="generationBasis" column="GENERATION_BASIS" not-null="false"/>
	   <property name="scheduleParametersXml" column="SCHEDULE_PARAMETERS_XML" not-null="false"/>
	   <property name="apiRequiredCols" column="API_REQUIRED_COLS" not-null="false"/>	  	   			
	   <property name="instanceUniqueExpression" column="INSTANCE_UNIQUE_EXPRESSION" not-null="false"/>			
	   <property name="instanceExpiryMins" column="INSTANCE_EXPIRY_MINS" not-null="false"/>			
	   <property name="allowReraiseAfterExpiry" column="ALLOW_RERAISE_AFTER_EXPIRY" not-null="false"/>			
	   <property name="reraiseIntervalMins" column="RERAISE_INTERVAL_MINS" not-null="false"/>			
	   <property name="afterTrigEvent" column="STATUS_AFTER_EVENT_TRIGGER" not-null="false"/>		 	
	   <property name="scenarioResolutionQueryText" column="PENDING_RESOLUTION_QUERY_TEXT" not-null="false"/>			
	   <property name="pendingResolutionTimeLimit" column="PENDING_RESOLUTION_TIME_LIMIT" not-null="false"/>
	   <property name="otherIdColumn" column="OTHER_ID_COL" not-null="false"/>			
	   <property name="otherIdTypeColumn" column="OTHER_ID_TYPE" not-null="false"/>	
	   <property name="customTreeLevel1" column="CUSTOM_TREE_LEVEL1" not-null="false"/>			
	   <property name="customTreeLevel2" column="CUSTOM_TREE_LEVEL2" not-null="false"/>
	   <property name="alertInstanceColumn" column="ALERT_INST_COL" not-null="false"/>	
	   <property name="resolutionRefCols" column="RESOLUTION_REF_COLS" not-null="false"
/>
	   <property name="queryExecutionListColumns" column="QUERY_EXECUTION_LIST_COLUMNS" not-null="false"/>		
	   			
	</class>
</hibernate-mapping>
