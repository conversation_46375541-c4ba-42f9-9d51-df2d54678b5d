/*
 * @(#)UserStatus.java 26/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.model;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.swallow.model.BaseObject;
import org.swallow.model.User;


public class UserStatus extends BaseObject implements Comparable{
	
	
	private Date logOutTime; 
	private String ipAddress;
	private User users;
	private String language;
	// Flag - To identify session time out due to inactivity.
	private boolean sessionTimeOutInactivity;
	private String killedBy = null;
    /**
     * @return Returns the language.
     */
    public String getLanguage() {
        return language;
    }
    /**
     * @param language The language to set.
     */
    public void setLanguage(String language) {
        this.language = language;
    }
	private Date updateDate = new Date();
	private String updateUser;
	private String displayDateTime;
	
    /**
     * @return Returns the displayDateTime.
     */
    public String getDisplayDateTime() {
        return displayDateTime;
    }
    /**
     * @param displayDateTime The displayDateTime to set.
     */
    public void setDisplayDateTime(String displayDateTime) {
        this.displayDateTime = displayDateTime;
    }
	private Id id = new Id();
	
	
	/**
	 * @return Returns the logOutTime.
	 */
	public Date getLogOutTime() {
		return logOutTime;
	}
	
	/**
	 * @return Returns the ipAddress.
	 */
	public String getIpAddress() {
		return ipAddress;
	}
	/**
	 * @param ipAddress The ipAddress to set.
	 */
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	
	
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	
	  public static class Id extends BaseObject{
		private String hostId ;
		private String userId;
		private Timestamp logOnTime;
		private String logDate_Time;
		public Id() {}

		public Id(String hostId, String userId,Timestamp logOnTime) {
			this.hostId = hostId;			
			this.userId=userId;
			this.logOnTime=logOnTime;
		}
		/**
		 * @return Returns the logDate_Time.
		 */
		public String getLogDate_Time() {
			SimpleDateFormat  sdf = new SimpleDateFormat("HH:mm:ss");
			return sdf.format(getLogOnTime());
			}
		/**
		 * @param logDate_Time The logDate_Time to set.
		 */
		public void setLogDate_Time(String logDate_Time) {
			this.logDate_Time = logDate_Time;
		}
			
		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}
		/**
		 * @param userId The userId to set.
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		
		/**
		 * @return Returns the logOnTime.
		 */
		public Timestamp getLogOnTime() {
			return logOnTime;
		}
		/**
		 * @param logOnTime The logOnTime to set.
		 */
		public void setLogOnTime(Timestamp logOnTime) {
			this.logOnTime = logOnTime;
		}
	}
	
	public void setId(Id id){
		this.id = id; 
		}	
	public Id getId(){
		return id; 
		}
	

	
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	/**
	 * @param logOutTime The logOutTime to set.
	 */
	public void setLogOutTime(Date logOutTime) {
		this.logOutTime = logOutTime;
	}
	
	/**
	 * @return Returns the users.
	 */
	public User getUsers() {
		return users;
	}
	/**
	 * @param users The users to set.
	 */
	public void setUsers(User users) {
		this.users = users;
	}
	
	public int compareTo(Object obj)
	{
		int retValue = 0;
		if(obj != null && obj instanceof UserStatus)
		{
			UserStatus userStatusObj = (UserStatus)obj;
			retValue = this.getId().getLogOnTime().compareTo(userStatusObj.getId().getLogOnTime());
		}
		return retValue;		
	}
	
	
	
    /**
     * @return Returns the sessionTimeOut.
     */
    public boolean isSessionTimeOutInactivity() {
        return sessionTimeOutInactivity;
    }
    /**
     * @param sessionTimeOut The sessionTimeOut to set.
     */
    public void setSessionTimeOutInactivity(boolean sessionTimeOutInactivity) {
        this.sessionTimeOutInactivity = sessionTimeOutInactivity;
    }
    /**
     * @return Returns the killedBy.
     */
	public String getKilledBy() {
		return killedBy;
	}
	/**
	 * @param sessionTimeOut The killedBy to set.
	 */
	public void setKilledBy(String killedBy) {
		this.killedBy = killedBy;
	}
	
	/**
	 * Update attributes except the Id
	 * @param us
	 * @return
	 */
	public UserStatus updateFrom(UserStatus us) {
		if(us == null) {
			return this;
		}
		this.logOutTime = us.logOutTime; 
		this.ipAddress = us.ipAddress;
		this.users = us.users;
		this.language = us.language;
		this.sessionTimeOutInactivity = us.sessionTimeOutInactivity;
		this.killedBy = us.killedBy;
		this.updateDate = us.updateDate;
		this.updateUser = us.updateUser;
		this.displayDateTime = us.displayDateTime;
		
		// Super
		try {
		    this.setUpdateDateNeedUpdated(us.isUpdateUserNeedUpdated());
		    this.setUpdateUserNeedUpdated(us.isUpdateDateNeedUpdated());
		} catch (Exception e) {
			// TODO: handle exception
		}
		return this;
	}
	
	
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("UserStatus [id=");
		builder.append(id);
		builder.append(", users=");
		builder.append(users);
		builder.append(", logOutTime=");
		builder.append(logOutTime);
		builder.append(", ipAddress=");
		builder.append(ipAddress);
		builder.append(", language=");
		builder.append(language);
		builder.append(", sessionTimeOutInactivity=");
		builder.append(sessionTimeOutInactivity);
		builder.append(", killedBy=");
		builder.append(killedBy);
		builder.append(", displayDateTime=");
		builder.append(displayDateTime);
		builder.append(", updateDate=");
		builder.append(updateDate);
		builder.append(", updateUser=");
		builder.append(updateUser);
		builder.append("]");
		return builder.toString();
	}
	
	
}
