/*
 * @(#)SystemLogDAO.java 16/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao;


import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.swallow.control.model.SystemLog;
import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;


public interface SystemLogDAO extends DAO{
	/**
	 * @param fromDate
	 * @param toDate
	 * @return
	 * @throws SwtException
	 */
	public int getSystemLogList(String hostId, Date fromDate, Date toDate,int currentPage,int maxPage,List sysLogList,String filterSortStatus,SystemFormats formats) throws SwtException;
	/**
	 * This function is used to save a system log in the database
	 * @param systemLog
	 * @throws SwtException
	 */
	public void saveSystemLog(SystemLog systemLog) throws SwtException;
}
