/*
 * Created on Dec 15, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.dao.hibernate;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.control.dao.UserProfilesDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.UserProfile;
import org.swallow.model.UserProfileDetail;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.SessionFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.hibernate.Transaction;
import org.hibernate.Session;




/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Repository("userProfilesDAO")
@Transactional
public class UserProfilesDAOHibernate extends CustomHibernateDaoSupport implements UserProfilesDAO {
    private final Log log = LogFactory.getLog(UserProfilesDAOHibernate.class);
    
    /**
     * Constructor with dependency injection
     * @param sessionfactory The session factory
     * @param entityManager The entity manager
     */
    public UserProfilesDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }
    
    /**
     * Fetch user profile details
     * @param hostId The host ID
     * @param userId The user ID
     * @param profileId The profile ID
     * @return List of user profiles
     */
    public List fetchDetails(String hostId, String userId, String profileId) {
        log.debug("entering 'fetchDetails' method");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<UserProfile> query = session.createQuery(
                "from UserProfile userpr where userpr.id.hostId = :hostId and userpr.id.userId = :userId and userpr.id.profileId = :profileId", 
                UserProfile.class);
            query.setParameter("hostId", hostId);
            query.setParameter("userId", userId);
            query.setParameter("profileId", profileId);
            List<UserProfile> userprofilelist = query.getResultList();
            
            log.debug("exiting 'fetchDetails' method");
            return userprofilelist;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in fetchDetails: " + e.getMessage());
            return List.of(); // Return empty list on error
        }
    }
    
    /**
     * Save user profile details
     * @param userprofiles The user profile to save
     */
    public void saveuserProfileDetails(UserProfile userprofiles) {
        Session session = null;
        Transaction tx = null;
        
        try {
            log.debug("entering 'saveuserProfileDetails' method");
            String isCurrentProfile = userprofiles.getCurrentProfile();
            
            log.debug("isCurrentProfile - " + isCurrentProfile);
            
            if (isCurrentProfile != null && isCurrentProfile.equals("Y")) {
                resetCurrentProfile(userprofiles.getId().getHostId(), userprofiles.getId().getUserId(), userprofiles.getId().getProfileId());
            }
            
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession();
            tx = session.beginTransaction();
            
            try {
                session.save(userprofiles);
                tx.commit();
            } catch (Exception e) {
                if (tx != null) tx.rollback();
                throw e;
            }
            
            log.debug("exiting 'saveuserProfileDetails' method");
        } catch (Exception exp) {
            log.error(this.getClass().getName() + "- [saveuserProfileDetails] - Exception " + exp.getMessage());
            try {
                throw SwtErrorHandler.getInstance().handleException(exp,
                        "saveuserProfileDetails", UserProfilesDAOHibernate.class);
            } catch (Exception e) {
                // Swallow exception as per original code
            }
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
    
    /**
     * Update user profile details
     * @param userprofiles The user profile to update
     */
    public void updateuserProfileDetails(UserProfile userprofiles) {
        Session session = null;
        Transaction tx = null;
        
        try {
            log.debug("entering 'updateuserProfileDetails' method");
            
            String isCurrentProfile = userprofiles.getCurrentProfile();
            
            log.debug("isCurrentProfile - " + isCurrentProfile);
            if (isCurrentProfile != null && isCurrentProfile.equals("Y")) {
                resetCurrentProfile(userprofiles.getId().getHostId(), userprofiles.getId().getUserId(), userprofiles.getId().getProfileId());
            }
    
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession();
            tx = session.beginTransaction();
            
            try {
                session.update(userprofiles);
                tx.commit();
            } catch (Exception e) {
                if (tx != null) tx.rollback();
                throw e;
            }
            
            log.debug("exiting 'updateuserProfileDetails' method");
        } catch (Exception exp) {
            log.error(this.getClass().getName() + "- [updateuserProfileDetails] - Exception " + exp.getMessage());
            try {
                throw SwtErrorHandler.getInstance().handleException(exp,
                        "updateuserProfileDetails", UserProfilesDAOHibernate.class);
            } catch (Exception e) {
                // Swallow exception as per original code
            }
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
    
    /**
     * Delete user profile details
     * @param userprofiles The user profile to delete
     */
    public void deleteUserProfileDetails(UserProfile userprofiles) {
        log.debug("entering 'deleteuserProfileDetails' method");
        
        Session session = null;
        Transaction tx = null;
        
        try {
            String hostId = userprofiles.getId().getHostId();
            String userId = userprofiles.getId().getUserId();
            String profileId = userprofiles.getId().getProfileId();
            
            // Get profile details to delete
            try (Session querySession = getHibernateTemplate().getSessionFactory().openSession()) {
                TypedQuery<UserProfileDetail> query = querySession.createQuery(
                    "from UserProfileDetail userpr where userpr.id.hostId = :hostId and userpr.id.userId = :userId and userpr.id.profileId = :profileId",
                    UserProfileDetail.class);
                query.setParameter("hostId", hostId);
                query.setParameter("userId", userId);
                query.setParameter("profileId", profileId);
                List<UserProfileDetail> userprofileDetaillist = query.getResultList();
                
                // Delete the profile details
                SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
                session = getHibernateTemplate().getSessionFactory()
                        .withOptions().interceptor(interceptor).openSession();
                tx = session.beginTransaction();
                
                try {
                    if (userprofileDetaillist != null && !userprofileDetaillist.isEmpty()) {
                        for (UserProfileDetail detail : userprofileDetaillist) {
                            session.delete(detail);
                        }
                    }
                    
                    tx.commit();
                } catch (Exception e) {
                    if (tx != null) tx.rollback();
                    throw e;
                }
            }
            
            log.debug("exiting 'deleteuserProfileDetails' method");
        } catch (Exception exp) {
            log.error(this.getClass().getName() + "- [deleteUserProfileDetails] - Exception " + exp.getMessage());
            try {
                throw SwtErrorHandler.getInstance().handleException(exp,
                        "deleteUserProfileDetails", UserProfilesDAOHibernate.class);
            } catch (Exception e) {
                // Swallow exception as per original code
            }
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
    
    /**
     * Reset current profile flag for all profiles except the specified one
     * @param hostId The host ID
     * @param userId The user ID
     * @param profileId The profile ID to exclude
     */
    private void resetCurrentProfile(String hostId, String userId, String profileId) {
        log.debug("entering 'resetCurrentProfile' method");
        
        Session session = null;
        Transaction tx = null;
        
        try {
            // First fetch the current profiles
            try (Session querySession = getHibernateTemplate().getSessionFactory().openSession()) {
                TypedQuery<UserProfile> query = querySession.createQuery(
                    "from UserProfile userpr where userpr.id.hostId = :hostId and userpr.id.userId = :userId " +
                    "and userpr.currentProfile = 'Y' and userpr.id.profileId != :profileId",
                    UserProfile.class);
                query.setParameter("hostId", hostId);
                query.setParameter("userId", userId);
                query.setParameter("profileId", profileId);
                List<UserProfile> currentProfiles = query.getResultList();
                
                log.debug("Current Profile - " + currentProfiles);
                
                // Reset the current profile flag of above profiles
                if (currentProfiles != null && !currentProfiles.isEmpty()) {
                    SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
                    session = getHibernateTemplate().getSessionFactory()
                            .withOptions().interceptor(interceptor).openSession();
                    tx = session.beginTransaction();
                    
                    try {
                        for (UserProfile userProfileObj : currentProfiles) {
                            userProfileObj.setCurrentProfile("N");
                            session.update(userProfileObj);
                        }
                        
                        tx.commit();
                    } catch (Exception e) {
                        if (tx != null) tx.rollback();
                        throw e;
                    }
                }
            }
            
            log.debug("exiting 'resetCurrentProfile' method");
        } catch (Exception exp) {
            log.error(this.getClass().getName() + "- [resetCurrentProfile] - Exception " + exp.getMessage());
            try {
                throw SwtErrorHandler.getInstance().handleException(exp,
                        "resetCurrentProfile", UserProfilesDAOHibernate.class);
            } catch (Exception e) {
                // Swallow exception as per original code
            }
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
    
    /**
     * Update user profile details and associated profile details
     * @param userprofiles The user profile to update
     * @param userProfileDetailsColl Collection of user profile details
     */
    public void updateuserProfileDetails(UserProfile userprofiles, Collection userProfileDetailsColl) {
        Session session = null;
        Transaction tx = null;
        
        try {
            log.debug("entering 'updateuserProfileDetails' method with collection");
            
            String hostId = userprofiles.getId().getHostId();
            String userId = userprofiles.getId().getUserId();
            String profileId = userprofiles.getId().getProfileId();
            
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession();
            tx = session.beginTransaction();
            
            try {
                // Handle current profile flag
                String isCurrentProfile = userprofiles.getCurrentProfile();
                log.debug("isCurrentProfile - " + isCurrentProfile);
                
                if (isCurrentProfile != null && isCurrentProfile.equals("Y")) {
                    // Find other current profiles and reset them
                    TypedQuery<UserProfile> query = session.createQuery(
                        "from UserProfile userpr where userpr.id.hostId = :hostId and userpr.id.userId = :userId " +
                        "and userpr.currentProfile = 'Y' and userpr.id.profileId != :profileId",
                        UserProfile.class);
                    query.setParameter("hostId", hostId);
                    query.setParameter("userId", userId);
                    query.setParameter("profileId", profileId);
                    List<UserProfile> currentProfiles = query.getResultList();
                    
                    log.debug("Current Profile - " + currentProfiles);
                    
                    if (currentProfiles != null && !currentProfiles.isEmpty()) {
                        for (UserProfile userProfileObj : currentProfiles) {
                            userProfileObj.setCurrentProfile("N");
                            session.update(userProfileObj);
                        }
                    }
                }
                
                // Update the user profile
                UserProfile userprofilesNew = (UserProfile) SwtUtil.copy(userprofiles);
                session.update(userprofilesNew);
                
                // Delete existing profile details
                TypedQuery<UserProfileDetail> detailQuery = session.createQuery(
                    "from UserProfileDetail userpr where userpr.id.hostId = :hostId and userpr.id.userId = :userId and userpr.id.profileId = :profileId",
                    UserProfileDetail.class);
                detailQuery.setParameter("hostId", hostId);
                detailQuery.setParameter("userId", userId);
                detailQuery.setParameter("profileId", profileId);
                List<UserProfileDetail> userprofileDetaillist = detailQuery.getResultList();
                
                if (userprofileDetaillist != null && !userprofileDetaillist.isEmpty()) {
                    for (UserProfileDetail detail : userprofileDetaillist) {
                        session.delete(detail);
                    }
                }
                
                // Save new profile details
                if (userProfileDetailsColl != null && !userProfileDetailsColl.isEmpty()) {
                    for (Object obj : userProfileDetailsColl) {
                        if (obj instanceof UserProfileDetail) {
                            UserProfileDetail detail = (UserProfileDetail) obj;
                            // Generate sequence number if not set
                            if (detail.getId().getSequenceNumber() == null) {
                                try {
                                    Long sequenceValue = org.swallow.util.SequenceFactory.getSequenceFromDbAsLong("S_USER_PROFILE_DETAIL_SEQUENCE");
                                    detail.getId().setSequenceNumber(sequenceValue);
                                    log.debug("Generated new user profile detail sequence number: " + sequenceValue);
                                } catch (Exception e) {
                                    log.error("Error generating user profile detail sequence", e);
                                    throw new SwtException("Failed to generate user profile detail sequence", e);
                                }
                            }
                        }
                        session.save(obj);
                    }
                }
                
                tx.commit();
            } catch (Exception e) {
                if (tx != null) tx.rollback();
                throw e;
            }
            
            log.debug("exiting 'updateuserProfileDetails' method with collection");
        } catch (Exception exp) {
            log.error(this.getClass().getName() + "- [updateuserProfileDetails] - Exception " + exp.getMessage());
            try {
                throw SwtErrorHandler.getInstance().handleException(exp,
                        "updateuserProfileDetails", UserProfilesDAOHibernate.class);
            } catch (Exception e) {
                // Swallow exception as per original code
            }
        } finally {
            if (session != null && session.isOpen()) {
                session.close();
            }
        }
    }
}