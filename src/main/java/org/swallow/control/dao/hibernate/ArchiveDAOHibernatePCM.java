/*
 * @(#)ArchiveDAOHibernate.java 1.0 05/09/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao.hibernate;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Collection;
import java.util.List;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import jakarta.persistence.TypedQuery;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import org.hibernate.Transaction;
import org.swallow.control.dao.ArchiveDAO;
import org.swallow.control.model.Archive;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.exception.SwtRecordNotExist;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> This is DAO class of the Archive Maintenance screen used to
 *         get Archive Id details.Add/Change Delete Archive Id details in the
 *         database and also test database link connection.
 * 
 */
@Repository ("archiveDAOPCM")
@Transactional
public class ArchiveDAOHibernatePCM extends CustomHibernateDaoSupport implements
		ArchiveDAO {
	public ArchiveDAOHibernatePCM(@Lazy @Qualifier("sessionFactoryPC") SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	private final Log log = LogFactory.getLog(ArchiveDAOHibernatePCM.class);
	private static final String HQL_ARCHIEVEDETAILS = "from Archive c where c.id.hostId = :hostId";

	/**
	 * 
	 */
	public Collection<Archive> getArchiveList(String hostId) throws SwtException {
		log.debug("Entering getArchiveList Method");
		List<Archive> archieveColl;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Archive> query = session.createQuery(HQL_ARCHIEVEDETAILS, Archive.class);
			query.setParameter("hostId", hostId);
			archieveColl = query.getResultList();
		}
		log.debug("Size of archieveColl--->" + archieveColl.size());
		log.debug("Exitting getArchiveList Method");
		return archieveColl;
	}

	/**
	 * 
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection<Archive> getcurrentDbList(String hostId, String moduleId) throws SwtException {
		log.debug("Entering getcurrentDbList Method");
		String HQL_DBDETAILS = "  from Archive a where a.id.hostId = :hostId and a.moduleId = :moduleId ";
		List<Archive> dbColl;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Archive> query = session.createQuery(HQL_DBDETAILS, Archive.class);
			query.setParameter("hostId", hostId);
			query.setParameter("moduleId", moduleId);
			dbColl = query.getResultList();
		}
		log.debug("Size of archieveColl--->" + dbColl.size());
		log.debug("Exitting getArchiveList Method");
		return dbColl;
	}
	
	public String getActiveDBLink(String hostId, String moduleId) throws SwtException {
		String dbLink = null;
		String HQL_DBDETAILS = "  from Archive a where a.id.hostId = :hostId and a.moduleId = :moduleId and a.defaultDb = 'Y'";
		List<Archive> dbColl;
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Archive> query = session.createQuery(HQL_DBDETAILS, Archive.class);
			query.setParameter("hostId", hostId);
			query.setParameter("moduleId", moduleId);
			dbColl = query.getResultList();
		}
		if (!dbColl.isEmpty()) {
			dbLink = dbColl.get(0).getDb_link();
		}
		return dbLink;
	}

	/**
	 * @param archive
	 * @throws SwtException
	 */
	public void updateArchive(Archive archive) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug("entering updateArchive of ArchiveDAOHibernate");
	
			log.debug("The archive record before update in daohibernate is == >>"
					+ archive);
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.update(archive);
			tx.commit();
			session.close();
			log.debug("exiting updatearchive");
			if(archive != null && archive.getModuleId().equals("PCM")) {
				SwtUtil.updatePCMViews();
			}
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [updateArchive] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateArchive", ArchiveDAOHibernatePCM.class);
		}
	}

	/**
	 * This is used to save the details in P_ARCHIVE table
	 * 
	 * @param archive
	 * @return none
	 * @throws SwtException
	 */
	public void saveArchive(Archive archive) throws SwtException {
		/* Methods local variable declaration */
		List<Archive> records = null;
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try {
			log.debug(this.getClass().getName() + "- [saveArchive] - Entering");
			try (Session sessionQ = getHibernateTemplate().getSessionFactory().openSession()) {
				TypedQuery<Archive> query = sessionQ.createQuery(
						"from Archive a where a.id.hostId = :hostId and a.id.archiveId = :archiveId",
						Archive.class);
				query.setParameter("hostId", archive.getId().getHostId());
				query.setParameter("archiveId", archive.getId().getArchiveId());
				records = query.getResultList();
			}
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate()
						.getSessionFactory()
						.withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			if (records.size() == 0) {
				/* Used to save the newly added details in DB */
				session.save(archive);
			} else {
				throw new SwtException(
						"errors.DataIntegrityViolationExceptioninAdd");
			}
			tx.commit();
			session.close();
			if(archive != null && archive.getModuleId().equals("PCM")) {
				SwtUtil.updatePCMViews();
			}
			log.debug(this.getClass().getName() + "- [saveArchive] - Exititng");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [saveArchive] - Exception " + exp.getMessage());
			SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
			SwtException swtExp = swtErrorHandler.handleException(exp,
					"saveArchive", ArchiveDAOHibernatePCM.class);
			throw swtExp;
		} finally {
			records = null;
		}

	}

	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method is used to delete the selected archive details from the table
	 * 
	 * @param archive
	 * @throws SwtException
	 */
	public void deleteArchive(Archive archive) throws SwtException {
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		String deleteHQL;
		int deleteCounter = 0;
		try {
			log.debug(this.getClass().getName()
					+ "- [deleteArchive] - Entering ");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			// Delete selected archive details.
			deleteHQL = "delete from Archive m where m.id.hostId=:hostId and m.id.archiveId=:archiveId";
			deleteCounter = session.createQuery(deleteHQL)
			        .setParameter("hostId", archive.getId().getHostId())
			        .setParameter("archiveId", archive.getId().getArchiveId())
			        .executeUpdate();

			
		    tx.commit();
				
			if(archive != null && "PCM".equals(archive.getModuleId())) {
				SwtUtil.updatePCMViews();
			}
		} catch (Exception e) {
			if (tx != null) {
		        tx.rollback();
		    }
			log.error(this.getClass().getName()
					+ "- [deleteArchive] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"deleteArchive", ArchiveDAOHibernatePCM.class);
			
		} finally {
			log.debug(this.getClass().getName() + "- [deleteArchive] - Exit ");
		    if (session != null) {
		        session.close();
		    }
		}
		if (deleteCounter == 0) {
		    throw new SwtRecordNotExist();
		}
	}

	/**
	 * This method is used to test the connection of the DB Link to which user
	 * connecting
	 * 
	 * @param dbLink
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean testConnection(String dbLink, String archiveType) throws SwtException {
		// declaration for connFlag
		boolean connFlag;
		// Declaration for session
		Session session = null;
		// declaration for connection
		Connection conn = null;
		// declaration for statement
		Statement statement = null;
		// declaration for query
		String query = null;
		// declaration for result set
		ResultSet resultSet = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [testConnection] - Entering ");
			connFlag = false;
			// get session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// get connection
			conn = SwtUtil.connection(session);
			// create statement
			statement = conn.createStatement();
			if("D".equalsIgnoreCase(archiveType)) {
				// framing query to execute
				query = "SELECT 'x' FROM DUAL@" + dbLink;
			}else {
				// framing query to execute
				query = "SELECT 'x' FROM " + dbLink + ".S_PATCHES_ARCHIVE";
			}
			// execute query
			resultSet = statement.executeQuery(query);
			// determining flag
			if (resultSet != null && resultSet.next()
					&& resultSet.getString(1).equalsIgnoreCase("X")) {
				connFlag = true;
			}
		} catch (SQLException se) {
			connFlag = false;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ "- [testConnection] - Exception " + e.getMessage());
			SwtException swtExp = SwtErrorHandler.getInstance()
					.handleException(e, "testConnection",
							ArchiveDAOHibernatePCM.class);
			throw swtExp;
		} finally {
			// nullify
			query = null;
			// Edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(resultSet, statement, conn, session);

			log.debug(this.getClass().getName() + "- [testConnection] - Exit ");
		}
		return connFlag;
	}

	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive
	 * setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * @param archiveID
	 * @throws SwtException
	 */
	public String getDBlink(String ArchiveID) throws SwtException {
		Archive archive = null;
		Session session = null;
		try {
			session = getHibernateTemplate().getSessionFactory().openSession();
/*//TODO:SBR => Use session.createQuery (...)			
			Criteria queryJob = session.createCriteria(Archive.class).add(
					Expression.like("id.hostId", SwtUtil.getCurrentHostId()))
					.add(Expression.like("id.archiveId", ArchiveID));

			archive = (Archive) queryJob.uniqueResult();
*/
		} catch (HibernateException e) {
			log.error(this.getClass().getName() + "- [getDBlink] - Exception "
					+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e, "getDBlink", ArchiveDAOHibernatePCM.class);
		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(session);
		}
		return archive.getDb_link();
	}

	public Archive getCurrentArchiveDb(String hostId) throws SwtException {
		log.debug(this.getClass().getName() + " - [ getCurrentDbLink ]- Entry");
		String HQL_DBDETAILS = "  from Archive a where a.id.hostId = ?0 and  a.defaultDb='Y' and a.moduleId = 'PCM'";
		List dbColl = getHibernateTemplate().find(HQL_DBDETAILS,
				new Object[] { hostId });
		log.debug(this.getClass().getName() + " - [ getCurrentDbLink ]- Exit");
		if(dbColl.size()==0)
			return null;
		else
			return ((Archive)dbColl.get(0) );
	}

	@Override
	public Archive getCurrentArchiveDb(String hostId, String archiveId) throws SwtException {
		log.debug(this.getClass().getName() + " - [ getCurrentArchiveDb with archiveId ]- Entry");

		if (SwtUtil.isEmptyOrNull(archiveId)) {
			// If archiveId is not provided, use the default behavior
			return getCurrentArchiveDb(hostId);
		} else {
			// If archiveId is provided, get the archive record without defaultDb condition
			return getArchiveById(hostId, archiveId);
		}
	}

	@Override
	public Archive getArchiveById(String hostId, String archiveId) throws SwtException {
		log.debug(this.getClass().getName() + " - [ getArchiveById ]- Entry");
		String HQL_DBDETAILS = "from Archive a where a.id.hostId = ?0 and a.id.archiveId = ?1";
		List dbColl = getHibernateTemplate().find(HQL_DBDETAILS,
				new Object[] { hostId, archiveId });
		log.debug(this.getClass().getName() + " - [ getArchiveById ]- Exit");
		if(dbColl.size()==0)
			return null;
		else
			return ((Archive)dbColl.get(0) );
	}
}