/*
 * @(#)MovementRecoveryDAOHibernate.java 1.0 03/05/06
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.dao.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.control.dao.MovementRecoveryDAO;
import org.swallow.control.model.MovementRecovery;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.swallow.work.model.MovementLock;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;






@Repository ("movementRecoveryDAO")
@Transactional
public class MovementRecoveryDAOHibernate extends CustomHibernateDaoSupport implements MovementRecoveryDAO{
	public MovementRecoveryDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}

	private final Log log = LogFactory.getLog(MovementRecoveryDAOHibernate.class);

	private static final String HQL_MovementLock = "select mLock.id.movementId, mLock.updateUser, mLock.updateDate, " +
			"m.positionLevel, m.valueDate, m.amount, m.currencyCode, m.accountId, m.bookCode, m.matchStatus, " +
			"mLock.id.hostId, m.id.entityId, m.inputDate from MovementLock mLock, Movement m where mLock.id.movementId = m.id.movementId";

	public Collection getMovementLockDetails(String hostId, String entityId) throws SwtException {
		log.debug("Entering into getMovementLockDetails() method");
		List movementLockList = null;
		Collection lockList = new ArrayList();

		String hql = HQL_MovementLock;
		movementLockList = getHibernateTemplate()
				.getSessionFactory()
				.getCurrentSession()
				.createQuery(hql)
				.list();

		log.debug("The data from the join query is==>" + movementLockList);

		if (movementLockList != null) {
			Iterator itrLock = movementLockList.iterator();
			while (itrLock.hasNext()) {
				Object[] row = (Object[]) itrLock.next();
				MovementRecovery movRec = new MovementRecovery();
				movRec.getId().setMovementId((Long) row[0]);
				movRec.setUpdateUser((String) row[1]);
				movRec.setUpdateDate((Date) row[2]);
				movRec.setPositionLevel((Integer) row[3]);
				movRec.setValueDate((Date) row[4]);
				movRec.setAmount((Double) row[5]);
				movRec.setCurrencyCode((String) row[6]);
				movRec.setAccountId((String) row[7]);
				movRec.setBookCode((String) row[8]);
				movRec.setStatus((String) row[9]);
				movRec.getId().setHostId((String) row[10]);
				movRec.setEntityId((String) row[11]);
				movRec.setInputDate((Date) row[12]);
				lockList.add(movRec);
				log.debug("The movementRecovery record from the database is==>" + movRec);
			}
		}
		log.debug("Exiting from the getMovementLockDetails() method");
		return lockList;
	}



	public void unlockMovement(MovementLock movLock)throws SwtException{
		Session session = null;
		Transaction tx = null;
		SwtInterceptor interceptor = null;
		try{
			log.debug("Entering into unlockMovement() method");
			interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
			session = getHibernateTemplate().getSessionFactory().withOptions().interceptor(interceptor).openSession();
			tx = session.beginTransaction();
			session.delete(movLock);
			tx.commit();
			session.close();
			log.debug("Exiting from the unlockMovement() method");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ "- [unlockMovement] - Exception " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"unlockMovement", MovementRecoveryDAOHibernate.class);
		}
	}
}