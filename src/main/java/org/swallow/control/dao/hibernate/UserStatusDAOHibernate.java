/*
 * @(#) UserStatusDAOHibernate.java 26/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao.hibernate;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Collection;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.control.dao.UserStatusDAO;
import org.swallow.control.model.UserStatus;
import org.swallow.exception.SwtException;
import org.swallow.model.User;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtUtil;

@Repository("userStatusDAO")
@Transactional
public class UserStatusDAOHibernate extends CustomHibernateDaoSupport implements UserStatusDAO {
	private final Log log = LogFactory.getLog(UserStatusDAOHibernate.class);
	
	public UserStatusDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}
	
	/**
	 * Get list of user statuses for a host
	 * @param hostId The host ID
	 * @return Collection of UserStatus objects
	 * @throws SwtException If an error occurs during retrieval
	 */
	public Collection getUserStatusList(String hostId) throws SwtException {
		log.debug("Entering getUserStatusList: ");
		
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<UserStatus> query = session.createQuery(
				"from UserStatus c where c.id.hostId = :hostId and c.logOutTime is null",
				UserStatus.class);
			query.setParameter("hostId", hostId);
			
			List<UserStatus> list = query.getResultList();
			log.debug("noofRecords.size : " + list.size());
			log.debug("exiting getUserStatusList ");
			return list;
		} catch (Exception e) {
			log.error("Exception in getUserStatusList: " + e.getMessage(), e);
			throw new SwtException("Error retrieving user status list: " + e.getMessage());
		}
	}
	
	/**
	 * Get list of users by user ID
	 * @param userId The user ID
	 * @return Collection of User objects
	 * @throws SwtException If an error occurs during retrieval
	 */
	public Collection getUserList(String userId) throws SwtException {
		log.debug("Entering getUserList(UserStatus userStatus): ");
		
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<User> query = session.createQuery(
				"from User c where c.id.userId = :userId",
				User.class);
			query.setParameter("userId", userId);
			
			List<User> list = query.getResultList();
			log.debug("noofRecords.size : " + list.size());
			log.debug("exiting getUserList(UserStatus userStatus): ");
			return list;
		} catch (Exception e) {
			log.error("Exception in getUserList: " + e.getMessage(), e);
			throw new SwtException("Error retrieving user list: " + e.getMessage());
		}
	}
	
	/**
	 * Clean S_USER_STATUS table
	 * @return true if successful, false otherwise
	 */
	@Transactional(propagation = Propagation.REQUIRED)
	public boolean truncateUserStatus() {
		log.debug("Entering truncateUserStatus(): ");
		
		Connection conn = null;
		Statement statement = null;
		boolean done = false;
		Session session = null;
		
		try {
			log.debug(this.getClass().getName() + " - [truncateUserStatus] - Entry");
			
			session = getHibernateTemplate().getSessionFactory().openSession();
			conn = SwtUtil.connection(session);
			statement = conn.createStatement();
			statement.executeUpdate("DELETE FROM S_USER_STATUS");
			conn.commit();
			
			done = true;
			log.debug("exiting truncateUserStatus(): ");
			return done;
		} catch (Exception e) {
			log.error("Exception caught in " + this.getClass().getName() + "-[truncateUserStatus]-" + e.getMessage(), e);
			try {
				if (conn != null) {
					conn.rollback();
				}
			} catch (SQLException e1) {
				log.error("Error during rollback: " + e1.getMessage(), e1);
			}
			return false;
		} finally {
			JDBCCloser.close(null, statement, conn, session);
		}
	}
	
	/**
	 * Get User Status Record based on userId and hostname
	 * @param hostId The host ID
	 * @param userId The user ID
	 * @return UserStatus object
	 * @throws SwtException If an error occurs during retrieval
	 */
	public UserStatus getUserStatusRecord(String hostId, String userId) throws SwtException {
		log.debug("Entering getUserStatusRecord: ");
		
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<UserStatus> query = session.createQuery(
				"from UserStatus c where c.id.hostId = :hostId and c.logOutTime is null and c.id.userId = :userId",
				UserStatus.class);
			query.setParameter("hostId", hostId);
			query.setParameter("userId", userId);
			
			List<UserStatus> list = query.getResultList();
			log.debug("noofRecords.size : " + list.size());
			
			UserStatus status = null;
			if (!list.isEmpty()) {
				status = list.get(0);
			}
			
			log.debug("exiting getUserStatusRecord ");
			return status;
		} catch (Exception e) {
			log.error("Exception in getUserStatusRecord: " + e.getMessage(), e);
			throw new SwtException("Error retrieving user status record: " + e.getMessage());
		}
	}
}
