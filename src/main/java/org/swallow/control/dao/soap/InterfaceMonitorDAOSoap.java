/*
 * @(#)InterfaceMonitorDAOSoap.java 1.0 15/06/2011
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.control.dao.soap;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;




import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.control.dao.InterfaceMonitorDAO;
import org.swallow.control.model.InterfaceMonitor;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.JDBCCloser;
import org.swallow.util.OpTimer;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Session;

/**
 * This class acts as a DAO layer where it has methods that are used to access
 * and manipulate Interface Monitor related Database values.<br>
 * 
 * Modified by: Marshal Joel Sudhan .I<br>
 * Date: 20-June-2011
 */

@Repository ("interfaceMonitorDAO")
@Transactional
public class InterfaceMonitorDAOSoap extends CustomHibernateDaoSupport implements
		InterfaceMonitorDAO {
	public InterfaceMonitorDAOSoap(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
	    super(sessionfactory, entityManager);
	}


	private final Log log = LogFactory.getLog("INTERFACE-MONITOR");

	public List<InterfaceMonitor> getInterfaceMonitorDetails(Date fromDate,
			Date toDate, OpTimer opTimer, SystemFormats format, boolean fromPCM)
			throws SwtException {
		// Holds the List of InterfaceMonitor details
		List<InterfaceMonitor> resultList = null;
		// Session object
		Session session = null;
		// Connection object
		Connection connection = null;
		// CallableStatement object
		CallableStatement callStatement = null;
		// ResultSet to hold monitor details and total
		ResultSet rsMonitorInfo = null;
		// Declares the InterfaceMonitor object
		InterfaceMonitor interfaceMonitor = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getInterfaceMonitorDetails] Begins");
			// Starts the timer
			opTimer.start(SwtConstants.DATA_FETCH);
			// Gets the current session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Establishes the dataBase connection from session
			connection = SwtUtil.connection(session);
			// Calls the Stored Procedure
			if (!fromPCM) {
				callStatement = connection
						.prepareCall("{call PKG_INTERFACE_MONITOR.I_MONITOR_HEADER(?,?,?)}");
			} else {
				callStatement = connection
						.prepareCall("{call PKG_PC_INTERFACE_MONITOR.I_MONITOR_HEADER(?,?,?)}");
			}
			// Sets the From date value as IN parameter
			callStatement.setDate(1, SwtUtil.truncateDateTime(fromDate));
			// Sets the To date value as IN parameter
			callStatement.setDate(2, SwtUtil.truncateDateTime(toDate));
			// Registers the CURSOR as OUT parameter
			callStatement.registerOutParameter(3,
					oracle.jdbc.OracleTypes.CURSOR);
			// Executes the CallableStatement
			callStatement.execute();
			// Gets the CURSOR value in the ResultSet
			rsMonitorInfo = (ResultSet) callStatement.getObject(3);
			// Checks the ResultSet and sets the values in the InterfaceMonitor
			// object
			if (rsMonitorInfo != null) {
				// Creates a new instance of resultList object
				resultList = new ArrayList<InterfaceMonitor>();
				// Loops through the ResultSet and sets them in InterfaceMonitor
				// object
				while (rsMonitorInfo.next()) {
					// Creates a new instance of InterfaceMonitor object
					interfaceMonitor = new InterfaceMonitor();
					// Sets the Interface Id
					interfaceMonitor.setInterfaceId(rsMonitorInfo.getString(1));
					// Sets the Interface Active value
					interfaceMonitor.setIsActiveInterface(rsMonitorInfo
							.getString(2));
					// Sets the Interface Status
					interfaceMonitor.setStatus(rsMonitorInfo.getString(3));
					// Sets the total count of processed messages
					interfaceMonitor.setProcessedMsgs(rsMonitorInfo
							.getString(4));
					// Sets the total count of awaiting messages
					interfaceMonitor
							.setAwaitingMsgs(rsMonitorInfo.getString(5));
					// Sets the total count of filtered messages
					interfaceMonitor
							.setFilteredMsgs(rsMonitorInfo.getString(6));
					// Sets the total count of bad messages
					interfaceMonitor.setBadMessages(rsMonitorInfo.getString(7));
					// Adds the InterfaceMonitor object in resultList
					resultList.add(interfaceMonitor);
				}
			}
			// Stops the timer
			opTimer.stop(SwtConstants.DATA_FETCH);
			log.debug(this.getClass().getName()
					+ " - [getInterfaceMonitorDetails] Ends");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getInterfaceMonitorDetails] method : - "
							+ exp.getMessage());
			// Re-throwing SwtException
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getInterfaceMessageList", InterfaceMonitorDAOSoap.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rsMonitorInfo, callStatement, connection, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getInterfaceMonitorDetails", InterfaceMonitorDAOSoap.class);
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getInterfaceMonitorDetails", InterfaceMonitorDAOSoap.class);
			
			if(thrownException!=null)
			throw thrownException;
			
		}
		return resultList;
	}

	public ArrayList<InterfaceMonitor> getStoredProcedureDetails(
			OpTimer opTimer, SystemFormats format, boolean fromPCM) throws SwtException {
		// Holds the ArrayList of InterfaceMonitor details
		ArrayList<InterfaceMonitor> storedProcList = null;
		// Session object
		Session session = null;
		// Connection object
		Connection connection = null;
		// CallableStatement object
		CallableStatement callStatement = null;
		// ResultSet to hold monitor details and total
		ResultSet rsStrdProcInfo = null;
		// Declares the InterfaceMonitor object
		InterfaceMonitor interfaceMonitor = null;
		// Holds the Last Received Date value in String format
		String lastRcvDateStr = null;
		// Holds the last received date of the message for the selected
		// Interface
		Date lastExecutionDate = null;
		// Declares the date format of the last received message date
		SimpleDateFormat lastMessageFormat = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [getStoredProcedureDetails] Begins");
			// Starts the timer
			opTimer.start(SwtConstants.DATA_FETCH);
			// Gets the current session
			session = getHibernateTemplate().getSessionFactory().openSession();
			// Establishes the dataBase connection from session
			connection = SwtUtil.connection(session);
			// Calls the Stored Procedure
			if(!fromPCM) {
				callStatement = connection
						.prepareCall("{call PKG_INTERFACE_MONITOR.I_MONITOR_DETAIL(?)}");
			} else {
				callStatement = connection
						.prepareCall("{call PKG_PC_INTERFACE_MONITOR.I_MONITOR_DETAIL(?)}");
			}
			// Registers the OUT parameter - CURSOR
			callStatement.registerOutParameter(1,
					oracle.jdbc.OracleTypes.CURSOR);
			// Executes the CallableStatement
			callStatement.execute();
			// Gets the CURSOR data in the ResultSet object
			rsStrdProcInfo = (ResultSet) callStatement.getObject(1);
			// Checks the ResultSet and stores the details in the ArrayList
			if (rsStrdProcInfo != null) {
				storedProcList = new ArrayList<InterfaceMonitor>();
				// Iterates the ResultSet adds the details in the ArrayList
				while (rsStrdProcInfo.next()) {
					// Instantiates the InterfaceMonitor
					interfaceMonitor = new InterfaceMonitor();
					// Sets the Interface id
					interfaceMonitor
							.setInterfaceId(rsStrdProcInfo.getString(1));
					// Sets the MessageType
					interfaceMonitor
							.setMessageType(rsStrdProcInfo.getString(2));
					// Sets the MessageStatus
					interfaceMonitor.setMessageStatus(rsStrdProcInfo
							.getString(3));
					// Gets the last received date object and cast it in to a
					// java.util.Date object
					lastExecutionDate = (Date) rsStrdProcInfo.getObject(4);
					// Checks the Last Execution date and formats it as per
					// General System Parameters
					if (lastExecutionDate != null) {
						// Format the last received date value as per the
						// General
						// System Parameter date settings
						lastRcvDateStr = SwtUtil.formatDate(lastExecutionDate,
								format.getDateFormatValue());
						// Instantiates the SimpleDateFormat to set the date
						// value
						// with time stamp in the given format
						lastMessageFormat = new SimpleDateFormat("HH:mm:ss");
						// Sets the last received message date as per the
						// General
						// System Parameter along with the time stamp
						lastRcvDateStr = lastRcvDateStr + " "
								+ lastMessageFormat.format(lastExecutionDate);
					} else {
						// Assigns an empty string if there is no last execution
						// date for certain Interface
						lastRcvDateStr = "";
					}
					// Sets the LastReceivedDate (as per the format of
					// GeneralSystemParameters)
					interfaceMonitor.setLastRecievedDate(lastRcvDateStr);
					// Adds the InterfaceMonitor in storedProcList
					storedProcList.add(interfaceMonitor);
				}
			}
			// Stops the timer
			opTimer.stop(SwtConstants.DATA_FETCH);
			log.debug(this.getClass().getName()
					+ " - [getStoredProcedureDetails] Ends");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getStoredProcedureDetails] method : - "
							+ e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getStoredProcedureDetails", InterfaceMonitorDAOSoap.class);
		} finally {
			
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			SwtException thrownException = null;
			Object[] exceptions = JDBCCloser.close(rsStrdProcInfo, callStatement, connection, session);
			
			if (exceptions[0] != null)
				thrownException = SwtErrorHandler.getInstance().handleException((SQLException) exceptions[0], "getInterfaceMonitorDetails", InterfaceMonitorDAOSoap.class);
			if (thrownException == null && exceptions[1] !=null) 
				thrownException = SwtErrorHandler.getInstance().handleException((HibernateException) exceptions[1], "getInterfaceMonitorDetails", InterfaceMonitorDAOSoap.class);
			
			if(thrownException!=null)
			throw thrownException;
			
		}
		return storedProcList;
	}
}