package org.swallow.control.service;

import java.util.concurrent.*;
import java.util.Map;
import java.util.HashMap;

public class BackgroundTaskManager {
    private static final ExecutorService executor = Executors.newCachedThreadPool();
    private static final Map<String, Future<?>> tasks = new ConcurrentHashMap<>();
    
    public static void submitTask(String taskId, Runnable task) {
        Future<?> future = executor.submit(task);
        tasks.put(taskId, future);
    }
    
    public static boolean cancelTask(String taskId) {
        Future<?> future = tasks.get(taskId);
        if (future != null) {
            boolean result = future.cancel(true);
            if (result) {
                tasks.remove(taskId);
            }
            return result;
        }
        return false;
    }
    
    public static boolean isTaskRunning(String taskId) {
        Future<?> future = tasks.get(taskId);
        return future != null && !future.isDone() && !future.isCancelled();
    }
    
    // Call this method when shutting down the application
    public static void shutdown() {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}