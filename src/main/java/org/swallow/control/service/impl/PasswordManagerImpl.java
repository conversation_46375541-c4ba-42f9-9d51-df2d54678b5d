/*
 * Created on Dec 20, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.control.service.impl;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.*;
import org.swallow.control.model.*;
import org.swallow.control.service.*;

import org.swallow.exception.*;
import org.swallow.util.SystemInfo;


import java.util.Collection;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
@Component("passwordManager")
public class PasswordManagerImpl implements PasswordManager {
	
	/*
	 * Created on Nov 4, 2005
	 *
	 * TODO To change the template for this generated file go to
	 * Window - Preferences - Java - Code Style - Code Templates
	 */

		/**
		 * Comment for <code>log</code>
		 */
		private final Log log = LogFactory.getLog(PasswordManagerImpl.class);
		
		/**
		 * Comment for <code>dao</code>
		 */
		@Autowired
		private PasswordDAO dao;
	    
		/**
		 * @param dao
	     *
	     */
	    public void setPasswordDAO(PasswordDAO dao) {
	        this.dao = dao;
	    }
	    
	    /** 
	     * @param hostId
	     * @return Collection
	     */
	    public Collection getPasswordRules(String hostId) throws SwtException
		{
			log.debug("entering 'getPasswordRules' method");
			Collection collPwd=dao.getPasswordRules(hostId);
			log.debug("exiting 'getPasswordRules' method");
			return collPwd;
		}
	   
	    /** 
	     * @param pwdRules
	     * @return 
	     */
	    public void updatePasswordRules(Password pwdRules,SystemInfo systemInfo)  throws SwtException{
	    	try{
			log.debug("entering 'updatePasswordRules' method");
			
	    	dao.updatePasswordRules(pwdRules);
	    	
	    	log.debug("exiting 'updatePasswordRules' method");
		    }catch(Exception e){
    		log.debug("Exception in PasswordManagerImpl.updatePasswordRules");
    		SwtErrorHandler swtErrorHandler = SwtErrorHandler.getInstance();
    		SwtException swtexp = swtErrorHandler.handleException(e, "updatePasswordRules",PasswordManagerImpl.class);
    		log.debug("Got The Exception - " + swtexp);
    		throw swtexp;
    	   }
        }
}	    
