/*
 * @(#)AccountAccessManagerImpl.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.AccountAccessDAO;
import org.swallow.control.model.AccountAccess;
import org.swallow.control.model.Role;
import org.swallow.control.service.AccountAccessManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.model.MenuAccess;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.work.model.Movement;

/**
 * <AUTHOR> Class that implements the AccountAccessManager.
 */
//unnessary code Removed by Arumugam on 01-Nov-2010 for Mantis:0001281- Account Access Control screen takes long time to save
@Component("accountAccessManager")
public class AccountAccessManagerImpl implements AccountAccessManager {
	/* Final instance for log */
	private final Log log = LogFactory.getLog(AccountAccessManagerImpl.class);

	/* Used to hold AccountAccessDAO reference object */
	@Autowired
	private AccountAccessDAO accountAccessDAO = null;

	/**
	 * This is used to set account access DAO
	 * 
	 * @param accountAccessDAO
	 * @return none
	 */
	public void setAccountAccessDAO(AccountAccessDAO accountAccessDAO) {
		this.accountAccessDAO = accountAccessDAO;
	}
	
	
	/**
	 * This is used to fetches account access details
	 * 
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @param Ccy
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection accountAccessDetails(String hostId, String roleId,
			String entityId, String Ccy) throws SwtException {
		/* Method's local variable declaration */
		Collection details = null;
		try {
			log.debug(this.getClass().getName()
					+ " - [accountAccessDetails] - " + "Entry");
			if (roleId != null)
				if (roleId.trim().equals(""))
					roleId = null;

			/* Retrieve account access details by calling DAO class */
			details = accountAccessDAO.accountAccessDetails(hostId, roleId,
					entityId, Ccy);
			log.debug(this.getClass().getName()
					+ " - [accountAccessDetails] - " + "Exit");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [accountAccessDetails] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"accountAccessDetails", AccountAccessManagerImpl.class);
		}
		return details;
	}

	/**
	 * This is used to delete the account access details from database
	 * 
	 * @param roleId
	 * @return none
	 * @throws SwtException
	 */
	public void deleteAcctAccessDetails(String roleId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [deleteAcctAccessDetails] - " + "Entry");
			accountAccessDAO.deleteAcctAccessDetails(roleId);
			log.debug(this.getClass().getName()
					+ " - [deleteAcctAccessDetails] - " + "Exit");

		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [deleteAcctAccessDetails] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAcctAccessDetails", AccountAccessManagerImpl.class);
		}
	}

	/**
	 * This is used to delete the account access details from database based on
	 * account Id
	 * 
	 * @param accountId
	 * @return none
	 * @throws SwtException
	 */
	public void deleteAcctDetails(String accountId) throws SwtException {

		try {
			log.debug(this.getClass().getName() + " - [deleteAcctDetails] - "
					+ "Entry");
			accountAccessDAO.deleteAcctDetails(accountId);
			log.debug(this.getClass().getName() + " - [deleteAcctDetails] - "
					+ "Exit");

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [deleteAcctDetails] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"deleteAcctDetails", AccountAccessManagerImpl.class);
		}
	}

	/**
	 * This is used to fetches role details based on host id
	 * 
	 * @param hostId
	 * @return List
	 * @throws SwtException
	 */

	public List getRoleDetails(String hostId) throws SwtException {
		/* Method's local variable declaration */
		ArrayList roleList = null;
		Iterator itr = null;
		Role role = null;
		try {
			log.debug(this.getClass().getName() + " - [getRoleDetails] - "
					+ "Entry");
			roleList = new ArrayList();
			/* Fetching role details by calling DAO class */
			itr = (accountAccessDAO.getRoleDetails(hostId)).iterator();
			/* Loop to iterate the details */
			while (itr.hasNext()) {
				role = (Role) (itr.next());
				/* Add the details in array list */
				roleList.add(new LabelValueBean(role.getRoleName(), role
						.getRoleId()));
			}
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getRoleDetails] method : - "
					+ e.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getRoleDetails] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getRoleDetails", AccountAccessManagerImpl.class);
		}
		log
				.debug(this.getClass().getName() + " - [getRoleDetails] - "
						+ "Exit");
		return roleList;
	}

	
	/**
	 * This is used to check the status of account.
	 * 
	 * @param accountAccess
	 * @param status
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkAcctAccess(AccountAccess accountAccess, String status)
			throws SwtException {
		/* Method's local variable declaration */
		boolean flag = true;
		Iterator itr = null;
		Role role = null;
		AccountAccess acct = null;

		try {
			log.debug(this.getClass().getName() + " - [checkAcctAccess] - "
					+ "Entry");
			/* Check the account access status by calling DAO class */
			itr = (accountAccessDAO.checkAcctAccess(accountAccess)).iterator();
			if (!itr.hasNext()) {
				flag = false;
			}
			/* Loop to iterate account access details */
			while (itr.hasNext()) {
				/* Get iterating values as object array */
				Object[] object = (Object[]) itr.next();
				/* Initializing User bean */
				acct = ((AccountAccess) object[1]);
				/*
				 * If the user's role has account access control enabled
				 * then apply the restrictions for the accounts which
				 * doesn't have matching or sweeping or input rights
				 * specified in the role account access mapping
				 */
				if ((status.trim().equals("Matching") && !(acct
						.getAllowMatching().equals("Y")))
						|| (status.trim().equals("Sweeping") && !(acct
								.getAllowSweeping().equals("Y")))
						|| (status.trim().equals("Input") && !(acct
								.getAllowManualInput().equals("Y")))) {
					/* Set the flag value as false */
					flag = false;
				}
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkAcctAccess] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"checkAcctAccess", AccountAccessManagerImpl.class);
		}
		log.debug(this.getClass().getName() + " - [checkAcctAccess] - "
				+ "Exit");
		return flag;

	}

		/**
	 * This is used to check the status of account for MSD screen.
	 * 
	 * @param accountAccess
	 * @param status
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkAcctAccessMSD(String movementId, String roleId,
			String entityId, String status) throws SwtException {
		/* Method's local variable declaration */
		boolean flag = true;
		Iterator itr = null;
		Iterator itrMovements = null;
		Role role = null;
		String hostId = null;
		AccountAccess accountAccess = null;
		AccountAccess acct = null;
		CacheManager cacheManagerInst = null;
		Movement movement = null;
		try {
			log.debug(this.getClass().getName() + " - [checkAcctAccessMSD] - "
					+ "Entry");
			cacheManagerInst = CacheManager.getInstance();
			/* Getting host id from cache manager file */
			hostId = cacheManagerInst.getHostId();
			/* Check the account access status by calling DAO class */
			itrMovements = (accountAccessDAO.checkAcctAccessMSD(movementId))
					.iterator();
			while (itrMovements.hasNext() && flag) {
				movement = (Movement) itrMovements.next();
				accountAccess = new AccountAccess();
				accountAccess.getId().setRoleId(roleId);
				/* Setting entity id using bean class */
				accountAccess.getId().setEntityId(entityId);
				/* Setting account id using bean class */
				accountAccess.getId().setAccountId(movement.getAccountId());
				/* Setting host id using bean class */
				accountAccess.getId().setHostId(hostId);
				itr = (accountAccessDAO.checkAcctAccess(accountAccess))
						.iterator();
				if (!itr.hasNext()) {
					flag = false;
				}
				/* Loop to iterate account access details */
				while (itr.hasNext()) {

					/* Get iterating values as object array */
					Object[] object = (Object[]) itr.next();
					/* Initializing User bean */
					acct = ((AccountAccess) object[1]);
					/*
					 * If the user's role has account access control enabled
					 * then apply the restrictions for the accounts which
					 * doesn't have matching or sweeping or input rights
					 * specified in the role account access mapping
					 */
					if ((status.trim().equals("Matching") && !(acct
							.getAllowMatching().equals("Y")))
							|| (status.trim().equals("Sweeping") && !(acct
									.getAllowSweeping().equals("Y")))
							|| (status.trim().equals("Input") && !(acct
									.getAllowManualInput().equals("Y")))) {
						/* Set the flag value as false */
						flag = false;
					}
				}
			}
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [checkAcctAccessMSD] method : - "
							+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"checkAcctAccess", AccountAccessManagerImpl.class);
		}
		log.debug(this.getClass().getName() + " - [checkAcctAccessMSD] - "
				+ "Exit");
		return flag;

	}

	// End: Modified by Bala on ******** for Mantis:1029-Allow Matching issue

	/**
	 * This is used to check the status of account.
	 * 
	 * @param RoleId
	 * @return int
	 * @throws SwtException
	 */
	public int checkMenuAccess(String roleId) throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [checkMenuAccess] - "
					+ "Entry");
			List menuAccesslist = null;
			int accessId = 0;
			MenuAccess menuacc = null;
			menuAccesslist = accountAccessDAO.checkMenuAccess(roleId);

			if (menuAccesslist.size() != 0) {
				menuacc = (MenuAccess) menuAccesslist.get(0);
				accessId = Integer.parseInt(menuacc.getAccessId());
			}

			log.debug(this.getClass().getName() + " - [checkMenuAccess] - "
					+ "Exit");
			return accessId;

		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [checkMenuAccess] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkMenuAccess", AccountAccessManagerImpl.class);
		}
	}

	/**
	 * This is used copying one role account access details to another role
	 * 
	 * @param copiedRoleId
	 * @param newRoleId
	 * @return
	 * @throws SwtException
	 */
	public void copyAccessDetails(String copiedRoleId, String newRoleId)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [copyAccessDetails] - "
					+ "Entry");
			if (copiedRoleId != null)
				if (copiedRoleId.trim().equals(""))
					copiedRoleId = null;
			/* copy account access details by calling DAO class */
			accountAccessDAO.copyAccessDetails(copiedRoleId, newRoleId);
			log.debug(this.getClass().getName() + " - [copyAccessDetails] - "
					+ "Exit");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [copyAccessDetails] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"copyAccessDetails", AccountAccessManagerImpl.class);
		}
	}
	
	/**
	 * This is used to save the account access details to database
	 * 
	 * @param accountAccess
	 * @param method
	 * @return none
	 * @throws SwtException
	 */
	public void saveAccountAccessControlDetails(Collection accountAccess,
			String userId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " - [saveAccountAccessControlDetails] - " + "Entry");
			accountAccessDAO.saveAccountAccessDetails(accountAccess, userId);
			log.debug(this.getClass().getName()
					+ " - [saveAccountAccessControlDetails] - " + "Exit");

		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [saveAccountAccessControlDetails] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveAccountAccessControlDetails",
					AccountAccessManagerImpl.class);
		}
	}
	/**
	 * This is used to check role account access details
	 * 
	 * @param accountAccess
	 * @return boolean
	 * @throws SwtException
	 */

	public boolean getRoleAccessDetails(AccountAccess accountAccess) throws SwtException {
		/* Method's local variable declaration */
		boolean accessFlag = false;
		try {
			log.debug(this.getClass().getName() + " - [getRoleAccessDetails] - "
					+ "Entry");
			/* Fetching role details by calling DAO class */
			accessFlag = (accountAccessDAO.getRoleAccessDetails(accountAccess));
			/* Loop to iterate the details */
		} catch (Exception e) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [getRoleAccessDetails] method : - "
					+ e.getMessage());
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getRoleAccessDetails] method : - "
					+ e.getMessage());
			e.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(e,
					"getRoleAccessDetails", AccountAccessManagerImpl.class);
		}
		log
				.debug(this.getClass().getName() + " - [getRoleAccessDetails] - "
						+ "Exit");
		return accessFlag;
	}
	
}
