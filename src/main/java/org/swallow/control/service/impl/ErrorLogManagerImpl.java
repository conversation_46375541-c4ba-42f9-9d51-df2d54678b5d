/*
 * @(#)ErrorLogManagerImpl.java   21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */


package org.swallow.control.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.control.dao.ErrorLogDAO;
import org.swallow.control.model.ErrorLog;
import org.swallow.control.service.ErrorLogManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.SystemFormats;
import org.swallow.util.UserThreadLocalHolder;

@Component ("errorLogManager")
public class ErrorLogManagerImpl implements ErrorLogManager {
	/**
	 * Comment for <code>log</code>
	 */
	private final Log log = LogFactory.getLog(ErrorLogManagerImpl.class);
	
	@Autowired
	private ErrorLogDAO dao;

	 /**
	  * @param dao
	  * @return
	  */
    public void setErrorLogDAO(ErrorLogDAO dao) {
        this.dao = dao;
    }

    /**
     * @param fromDate
     * @param toDate
     * @return Collection
     */
    public Collection getErrorLogList(String hostId,Date fromDate, Date toDate) throws SwtException{
		log.debug("Entering Manager Implementation 'getErrorLogList' method");
		return dao.getErrorLogList(hostId,fromDate, toDate);
	}

    /**
     * @param fromDate
     * @param toDate
     * @return Collection
     */
    public Collection getErrorLogList(String hostId,String userId , Date fromDate, Date toDate) throws SwtException{
		log.debug("Entering Manager Implementation 'getErrorLogList' method");
		return dao.getErrorLogList(hostId,userId,fromDate, toDate);
	}


    /**
     * This method sets the UserId and IP address into errorLog object and inserts that object
     * into database. If IpAddress will null then it will set 'DB_SERVER' as IPAddress. If userId
     * will be null then it will set SYSTEM as userId into errorlog object.
     * @param errorLog
     * @return
     * <AUTHOR> Tripathi
     */
	public void logError(ErrorLog errorLog){
		/*START : Code for updating S_ERROR_LOG properly in case of DB_SERVER error 12/06/07*/
        String ipAddress = UserThreadLocalHolder.getUserIPAddress();
        ipAddress = ipAddress != null ? ipAddress : "DB_SERVER";
        errorLog.setIpAddress(ipAddress);
        if(UserThreadLocalHolder.getUser()==null){
        	errorLog.setUserId("SYSTEM");
        }else{
        	errorLog.setUserId(UserThreadLocalHolder.getUser());
        }
        /*errorLog.setUserId(UserThreadLocalHolder.getUser());
		errorLog.setIpAddress(UserThreadLocalHolder.getUserIPAddress());*/
		/*END : Code for updating S_ERROR_LOG properly in case of DB_SERVER error */
		dao.logError(errorLog);
		log.debug("exiting logError");
	}

    public int getErrorLogListUsingStoredProc(Date fromDate,
            Date toDate, int currentPage, int maxPage, ArrayList errorLogList,
            String filterSortStatus, SystemFormats formats)
            throws SwtException {
        	try{
        	/*-Start- Code modified for Defect No. 116 in Mantiss on 03/11/2007 - */
            return dao.getErrorLogListUsingStoredProc(fromDate,
                toDate, currentPage, maxPage, errorLogList, filterSortStatus,
                formats);
        	}catch(Exception e) {
                throw SwtErrorHandler.getInstance().handleException(e,"getAuditLogList",ErrorLogManagerImpl.class);

        	}
        	/*-End- Code modified for Defect No. 116 in Mantiss on 03/11/2007 - */


        }
}
