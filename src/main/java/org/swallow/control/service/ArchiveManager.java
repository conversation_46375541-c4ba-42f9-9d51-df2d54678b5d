/*
 * @(#)ArchiveManager.java 1.0 21/12/05
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.control.service;

import java.util.Collection;
import java.util.List;

import org.swallow.control.dao.ArchiveDAO;
import org.swallow.control.model.Archive;
import org.swallow.exception.SwtException;

/**
 * <AUTHOR>
 * 
 * This class is Interface for Manager layer to get Archive Maintenance
 * screen.To Add/Change/Delete Archive Id and its details.Also test connection
 * of Database Link
 */
public interface ArchiveManager {
	public void setArchiveDAO(ArchiveDAO archiveDAO);

	/**
	 * 
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Collection getArchiveList(String hostId) throws SwtException;

	/**
	 * 
	 * @param hostId
	 * @param moduleId 
	 * @return
	 * @throws SwtException
	 */
	public Collection getcurrentDbList(String hostId, String moduleId) throws SwtException;
	/**
	 * 
	 * @param hostId
	 * @param moduleId
	 * @return String
	 * @throws SwtException
	 */
	public String getActiveDBLink(String hostId, String moduleId) throws SwtException;
	/**
	 * @param archive
	 * @throws SwtException
	 */
	public void saveArchive(Archive archive) throws SwtException;

	/**
	 * @param archive
	 * @throws SwtException
	 */
	public void deleteArchive(Archive archive) throws SwtException;

	/**
	 * @param archive
	 * @throws SwtException
	 */
	public void updateArchive(Archive archive) throws SwtException;

	/**
	 * @param archiveID
	 * @throws SwtException
	 */
	public String getDBlink(String ArchiveID) throws SwtException;

	// Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	// Archive setup: Remove redundant fields from Archive setup screen

	/**
	 * This method is used to test the connection of the DB Link to which user
	 * connecting
	 *
	 * @param dbLink
	 * @param moduleId
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean testConnection(String dbLink, String moduleId, String archiveType) throws SwtException;

	/**
	 *
	 * @param hostId
	 * @return
	 * @throws SwtException
	 */
	public Archive getCurrentArchiveDb(String hostId) throws SwtException;
}
