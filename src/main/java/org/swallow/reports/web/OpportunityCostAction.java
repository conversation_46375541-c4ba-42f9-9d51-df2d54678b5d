/*
 * @(#)ReportsAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.web;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.hibernate.SysParamsDAOHibernate;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.reports.model.Reports;
import org.swallow.reports.service.ReportsManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.JRCsvExporter;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;

import org.springframework.beans.factory.annotation.Autowired;


import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.StaxDriver;



import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/reports", "/reports.do"})
public class OpportunityCostAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/reports/opportunityCostReport");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	HttpServletRequest request = SwtUtil.getCurrentRequest();
	HttpServletResponse response = SwtUtil.getCurrentResponse();

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "OpportunityCostReport":
				return OpportunityCostReport();
			case "getOpportunityCostparameters":
				return getOpportunityCostparameters();
			default:
				break;
		}

		return unspecified();
	}

	private Reports reports;

	public Reports getReports() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		reports = RequestObjectMapper.getObjectFromRequest(Reports.class, request,"reports");
		return reports;
	}


	public void setReports(Reports reports) {
		this.reports = reports;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("reports", reports);
	}

	// Report manger declaration
	@Autowired
	private ReportsManager reportsManager = null;
	private final Log log = LogFactory.getLog(OpportunityCostAction.class);

	public String unspecified() throws SwtException {
		return null;
		// return displayList(mapping,form,request,response);
	}

	/*
	 * START : Modified by sandeepkumar for Mantis 1766 : Opportunity cost report:
	 * Improve performance
	 */
	/**
	 * Result of type Action forward This method will accept the input from the user
	 * and send the generated report to the user
	 *
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String OpportunityCostReport() throws SwtException {
		// dyna validator form to get and set the form values
// To remove: 		DynaValidatorForm dyForm = null;
		// variable to hold report
		Reports report = null;
		// Declaring the jasperPrint
		JasperPrint jasperPrint = null;
		// varible to hold threshold
		String threshold = null;
		// Initializing the outputstream
		ServletOutputStream out = null;
		// Initializing the PDF Exporter.
		JRPdfExporter pdfexporter = null;
		JRXlsExporter xlsxporter = null;
		JRCsvExporter csvexporter = null;
		String sheetName[] = new String[1];
		String selectedOutputFormat = null;
		String sqlQueryAsString = null;
		try {
			log.debug(this.getClass().getName() + "- [OpportunityCostReport] - Entering ");
			// Initializing the Dynavalidator Form
// To remove: 			dyForm = (DynaValidatorForm) form;
			// Get the reports from Dynavalidator Form
			report = (Reports) getReports();
			// To get the parameters from reports.
			threshold = report.getThreshold();
			// If the threshold is null then N will be assigned
			if (threshold == null || threshold.equals(SwtConstants.EMPTY_STRING)) {
				threshold = "0.0";
			}
			/* Read selectedOutputFormat from request */
			selectedOutputFormat = request.getParameter("selectedOutputFormat");

			// get output stream from servlet output stream
			out = response.getOutputStream();
			/* To get the filled report form reportsManager */
			jasperPrint = reportsManager.getOpportunityCost(request, SwtUtil.getCurrentHostId(), report.getEntityId(),
					report.getFromDateAsString(), threshold, request.getParameter("currencyCode"),
					request.getParameter("currencyText"), null, selectedOutputFormat);
			/* Initializing the JRDFExporter */
			/* Condition to check filType is pdf */
			if (SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_PDF.equals(selectedOutputFormat)) {
				// ........ Export PDF Report ...........//
				sqlQueryAsString = SwtUtil.getNamedQuery("turnover_report_query");
				pdfexporter = new JRPdfExporter();
				response.setContentType("application/pdf");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".pdf");
				pdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);

				// Providing the output stream
				pdfexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				pdfexporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");

				// Export Report to PDF
				pdfexporter.exportReport();

				/* Condition to check fileType is xls */
			} else if (SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_CSV.equals(selectedOutputFormat)) {
				// ........ Export CSV Report ...........//

				csvexporter = new JRCsvExporter();
				response.setContentType("application/csv");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".csv");

				csvexporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
				csvexporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
				csvexporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND, Boolean.FALSE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_IGNORE_GRAPHICS, Boolean.FALSE);
				csvexporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.TRUE);
				// Set the column names wrap parameter to false
				csvexporter.exportReport();

			} else if (SwtConstants.OPPORTUNITY_COST_REPORT_OUTPUT_EXCEL.equals(selectedOutputFormat)) {
				// ........ Export EXCEL Report ...........//
				xlsxporter = new JRXlsExporter();
				response.setContentType("application/xls");
				response.setHeader("Content-disposition",
						"attachment; filename=" + jasperPrint.getName() + "-SmartPredict_"
								+ SwtUtil.formatDate(new Date(), "yyyyMMdd") + "_"
								+ SwtUtil.formatDate(new Date(), "HHmmss") + ".xls");

				xlsxporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);

				// Providing the output stream
				xlsxporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);

				// Exporting as UTF-8
				xlsxporter.setParameter(JRExporterParameter.CHARACTER_ENCODING, "UTF-8");
				xlsxporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.TRUE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.TRUE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_COLUMNS, Boolean.TRUE);
				xlsxporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				// Export Report to Excel
				xlsxporter.exportReport();
			}

		} catch (Exception exp) {

			log.debug(this.getClass().getName() + "- [OpportunityCostReport] - Exception " + exp.getMessage());
			log.error(this.getClass().getName() + "- [OpportunityCostReport] - Exception " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "save", OpportunityCostAction.class), request,
					"");
			return getView("fail");

		} finally {
			// nullyfing objects
			report = null;
			jasperPrint = null;
			threshold = null;
			out = null;
			pdfexporter = null;
			csvexporter = null;
			xlsxporter = null;

		}
		log.debug(this.getClass().getName() + "- [OpportunityCostReport] - Exiting ");
		return null;
	}

	/*
	 * End : Modified by sandeepkumar for Mantis 1766 : Opportunity cost report:
	 * Improve performance
	 */
	/**
	 * @return a Result of type Action forward
	 * @throws SwtException
	 */
	public String getOpportunityCostparameters() throws SwtException {

		log.debug(this.getClass().getName() + "- [getOpportunityCostparameters] - Entering ");
		// Declaring the Dynavalidator Form
// To remove: 		DynaValidatorForm dyForm;
		// Declaring the report
		Reports report;
		// Declaring the local variables to pass to the user
		String userEntityId;
		String userEntityName = null;
		Collection userEntityList;
		EntityUserAccess entityObj;
		String entityChange = null;
		String currencyCode = null;
		String reportingCcy = null;
		String hostId = null;
		String configScheduler = null;
		String newOpportunityCostReportSchedConfig = null;
		String schedulerConfigXML = null;
		String reportType = null;
		boolean isCcyChanged = false;
		boolean isEntityChanged = false;
		String dateFormat = null;
		configScheduler = request.getParameter("configScheduler");
		newOpportunityCostReportSchedConfig = request.getParameter("newOpportunityCostReportSchedConfig");
		reportType = request.getParameter("reportType");
		// Intialising the Dynavalidator Form
// To remove: 		dyForm = (DynaValidatorForm) form;
		// Get the reports from Dynavalidator Form
		report = (Reports) getReports();
		// To get the current user Entity
		userEntityId = SwtUtil.getUserCurrentEntity(request.getSession());
		hostId = SwtUtil.getCurrentHostId();
		// To get the user accessible Entity list
		userEntityList = SwtUtil.getUserEntityAccessList(request.getSession());
		// Intialising the Dateformat
		SimpleDateFormat sdf = new SimpleDateFormat(SwtUtil.getCurrentDateFormat(request.getSession()));

		entityChange = request.getParameter("status");
		if ((entityChange != null) && (entityChange.equalsIgnoreCase("onEntityChange"))) {
			userEntityId = report.getEntityId();
			isEntityChanged = true;

		} else {
			if (userEntityId != null) {
				Iterator itr = userEntityList.iterator();
				while (itr.hasNext()) {
					entityObj = (EntityUserAccess) itr.next();
					if (entityObj.getEntityId().equals(userEntityId)) {
						userEntityName = entityObj.getEntityName();
						break;
					}
				}
			}
		}

		if (!SwtUtil.isEmptyOrNull(configScheduler) && ("true".equals(configScheduler))) {
			if (!SwtUtil.isEmptyOrNull(newOpportunityCostReportSchedConfig)
					&& "false".equals(newOpportunityCostReportSchedConfig)) {
				HashMap<String, String> schedulerConfigMap;
				schedulerConfigXML = SwtUtil.decode64(request.getParameter("schedulerConfigXML"));
				schedulerConfigMap = convertschedulerConfigXMLtoHashmap(schedulerConfigXML);
				report = new Reports();
				if (SwtUtil.isEmptyOrNull(((Reports) getReports()).getEntityId())) {
					report.setEntityId(schedulerConfigMap.get("entityid"));
				} else {
					report.setEntityId(((Reports) getReports()).getEntityId());
				}
				if (SwtUtil.isEmptyOrNull(((Reports) getReports()).getCurrencyCode())) {
					report.getId().setCurrencyCode(schedulerConfigMap.get("currencycode"));
				} else {
					report.getId().setCurrencyCode(((Reports) getReports()).getCurrencyCode());
				}

				report.setThreshold(schedulerConfigMap.get("threshold"));
				report.setReportType(schedulerConfigMap.get("reporttype"));
				report.setPdfOrExcelorCSV(schedulerConfigMap.get("pdforexcelorcsv"));
				// dateFormat User
				// String dateFormat = SwtUtil.getCurrentDateFormat(request.getSession());

				userEntityId = schedulerConfigMap.get("entityid");
				currencyCode = schedulerConfigMap.get("currencycode");
				dateFormat = schedulerConfigMap.get("dateformatasstring");

				if (!SwtUtil.isEmptyOrNull(dateFormat)
						&& !SwtUtil.isEmptyOrNull(schedulerConfigMap.get("fromdateasstring"))) {

					String valuedateToSet = null;
					if (SysParamsDAOHibernate.keywordQuery.containsKey(schedulerConfigMap.get("fromdateasstring"))) {
						valuedateToSet = schedulerConfigMap.get("fromdateasstring");
					} else {
						Date toDate = SwtUtil.parseDate(schedulerConfigMap.get("fromdateasstring"), dateFormat);
						valuedateToSet = SwtUtil.formatDate(toDate, SwtUtil.getCurrentDateFormat(request.getSession()));
					}

					report.setFromDateAsString(valuedateToSet);
				} else {
					report.setFromDateAsString(schedulerConfigMap.get("fromdateasstring"));
				}
			}
		}

		// To get the test date
		Date testDate = SwtUtil.getSysParamDateWithEntityOffset(userEntityId);
		// Formats for current system date formats
		String today = SwtUtil.formatDate(testDate,
				SwtUtil.getCurrentSystemFormats(request.getSession()).getDateFormatValue());
		/*
		 * Puts the list of currencies in request object and assign the default domestic
		 * currency
		 */

		if (SwtUtil.isEmptyOrNull(currencyCode)) {
			currencyCode = putCurrencyListInReq(request, SwtUtil.getCurrentHostId(), userEntityId);

			currencyCode = "All";
		} else {

			putCurrencyListInReq(request, SwtUtil.getCurrentHostId(), userEntityId);
		}
		reportingCcy = SwtUtil.getReportingCurrencyForEntity(hostId, userEntityId);

		// adding into label bean
		request.setAttribute("keywords", SwtUtil.getKeywords(request));

		// Set the useraccessible enetity list to request
		request.setAttribute("userEntity", userEntityList);
		request.setAttribute("reportingCcy", reportingCcy);
		// Set the parameters to be displayed to the user
		report.getId().setHostId(SwtUtil.getCurrentHostId());
		if (SwtUtil.isEmptyOrNull(report.getFromDateAsString())) {
			report.setFromDateAsString(today);
		}
		report.setEntityId(userEntityId);
		report.setEntityName(userEntityName);
		// to display in the JSP while on load
		report.getId().setCurrencyCode(currencyCode);
		request.setAttribute("configScheduler", configScheduler);
		request.setAttribute("newOpportunityCostReportSchedConfig", newOpportunityCostReportSchedConfig);
		request.setAttribute("reportType", reportType);
		request.setAttribute("schedulerConfigXML", request.getParameter("schedulerConfigXML"));
		request.setAttribute("jobId", request.getParameter("jobId"));
		// Set the report object to Action form
		setReports(report);
		log.debug(this.getClass().getName() + "- [getOpportunityCostparameters] - Exiting ");
		return getView("success");
	}

	/**
	 * This method is used to put the list of currency in request object
	 *
	 * @param request  - HttpServletRequest
	 *
	 * @param hostId   - String
	 *
	 * @param entityId - String
	 *
	 * @return String
	 *
	 * @throws SwtException Throw exception, if any
	 */
	private String putCurrencyListInReq(HttpServletRequest request, String hostId, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName() + "- [putCurrencyListInReq] - Entering ");
		/* String variable to hold default currency */
		String defaultCurrency = null;
		/* String variable to hold the roldId */
		String roleId = null;
		/* String variable to hold the domesticCurrency */
		String domesticCurrency = null;
		/* Collection object to hold all currencies in dropdown box */
		Collection currencyDropDown = null;
		/* Collection object to hold all currencies with ALLin dropdown box */
		Collection currrencyListWithAll = null;
		/* Getting the user's roleId from session */
		roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser().getRoleId();
		/* This is used to get all currencies in dropdown box */
		currencyDropDown = SwtUtil.getSwtMaintenanceCache().getCurrencyViewORFullAcessLVL(roleId, entityId);
		/*
		 * If the currencies is not null,it will remove the default currency from the
		 * dropdown list
		 */
		if (currencyDropDown != null) {
			currencyDropDown.remove(new LabelValueBean("Default", "*"));
		}

		/* Creates an instance for an ArrayList */
		currrencyListWithAll = new ArrayList();

		/* Showing 'All' in Currency drop down */
		currrencyListWithAll.add(new LabelValueBean(SwtConstants.ALL_LABEL, SwtConstants.ALL_VALUE));

		/*
		 * If the currencies in the dropdown list is not equal to null, it will add all
		 * currencies with ALL .
		 */
		if (currencyDropDown != null) {
			currrencyListWithAll.addAll(currencyDropDown);
		}

		/* Sets the currencyList With All in request object */
		request.setAttribute("currencies", currrencyListWithAll);

		/* Getting the domestic currency for user from db */
		domesticCurrency = SwtUtil.getDomesticCurrencyForUser(request, hostId, entityId);
		/*
		 * If the domestic currency is not equal to null, it assigns the domestic
		 * currency to default currency for display to the user.
		 */
		if (domesticCurrency != null) {
			defaultCurrency = domesticCurrency;
		}

		log.debug(this.getClass().getName() + "- [putCurrencyListInReq] - Exiting ");

		/* Returns the defaultCurrency */
		return defaultCurrency;
	}

	public HashMap<String, String> convertschedulerConfigXMLtoHashmap(String schedulerConfigXML) {
		XStream xStream = new XStream(new StaxDriver());
		xStream.registerConverter(new SwtUtil.MapEntryConverter());
		xStream.alias("schedconfig", Map.class);
		xStream.setClassLoader(Thread.currentThread().getContextClassLoader());
		HashMap<String, String> extractedMap = (HashMap<String, String>) xStream.fromXML(schedulerConfigXML);
		return extractedMap;
	}
}