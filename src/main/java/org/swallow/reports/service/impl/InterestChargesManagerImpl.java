/*
 * @(#)InterestChargesManagerImpl.java 1.0 10/08/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;

import net.sf.jasperreports.engine.JasperPrint;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.reports.dao.InterestChargesDAO;
import org.swallow.reports.model.InterestCharges;
import org.swallow.reports.service.InterestChargesManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtUtil;

/**
 * 
 * <AUTHOR> A Class that implements the InterestChargesManager .
 * 
 */
@Component("interestChargesManager")
public class InterestChargesManagerImpl implements InterestChargesManager {

	/*
	 * Final instance for Log
	 */
	private static final Log log = LogFactory
			.getLog(InterestChargesManagerImpl.class);
	/*
	 * Instance of DAO
	 */
	@Autowired
	private InterestChargesDAO interestchargesDAO;

	/*
	 * Setter method for the DAO
	 */
	public void setInterestChargesDAO(InterestChargesDAO interestchargesDAO) {
		this.interestchargesDAO = interestchargesDAO;
	}

	/**
	 * Gets the AccountId from Database table
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @return Collection object
	 */
	public Collection getMainAccountDetails(String hostId, String entityId,
			String currencyCode) throws SwtException {

		log.debug(this.getClass().getName()
				+ "- [getMainAccountDetails] - Entering ");
		/*
		 * Collection object to hold the collection of Account Id and Account
		 * Name
		 */
		Collection accountList = null;
		/*
		 * Collection object to hold the collection of Account Id in Label Value
		 * Bean
		 */
		Collection finalList = null;
		/* InterestCharges object */
		InterestCharges interestCharges = new InterestCharges();
		/* Creates an instance for ArrayList */
		finalList = new ArrayList();
		/* Creates an instance for ArrayList */
		accountList = new ArrayList();

		/* Used to the list of accountId */
		accountList = interestchargesDAO.getMainAccountDetails(hostId,
				entityId, currencyCode);
		/* Converting Collection to ArrayList */
		ArrayList list = (ArrayList) accountList;

		/* It is used to iterate the accountId and accountName */
		Iterator it_accountList = list.iterator();

		while (it_accountList.hasNext()) {

			/* Sets the accountId in bean object */
			interestCharges.getId().setAccountId(
					it_accountList.next().toString());
			/* Sets the accountName in bean object */
			interestCharges.setAccountName(it_accountList.next().toString());
			/* Puts the accountId and accountName in LabelValueBean */
			finalList.add(new LabelValueBean(interestCharges.getAccountName(),
					interestCharges.getId().getAccountId()));

		}
		log.debug(this.getClass().getName()
				+ "- [getMainAccountDetails] - Exiting ");

		/* Returns the collection Object */
		return finalList;
	}

	/*
	 * Start:Code Modified For Mantis 1622 by Sudhakar on 20-09-2012: Interest
	 * Charges by Account Report: From and To date range instead of Month/Year
	 */
	/**
	 * This method is used to compile and return the reports
	 * 
	 * @param request -
	 *            HttpServletRequest
	 * @param hostId -
	 *            String
	 * @param entityId -
	 *            String
	 * @param entityName -
	 *            String
	 * @param currencyId -
	 *            String
	 * @param currencyName -
	 *            String
	 * @param accountId -
	 *            String
	 * @param accountName -
	 *            String
	 * @param fromDate -
	 *            String
	 * @param toDate -
	 *            String
	 * @return JasperPrint
	 * @throws SwtException
	 * 
	 */
	public JasperPrint getInterestChargesReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String currencyId, String currencyName, String accountId,
			String accountName, String fromDate, String toDate, String dateFormat)
			throws SwtException {
		// To hold the InterestCharges details
		JasperPrint jasperPrint = null;
		try {
			log.debug(this.getClass().getName()
					+ "- [getInterestChargesReport] - Entering ");
			/* To call the getOpportunityCostReport in ReportsDAOHibernate. */
			jasperPrint = interestchargesDAO.getInterestChargesReport(request,
					hostId, entityId, entityName, currencyId, currencyName,
					accountId, accountName, fromDate, toDate, dateFormat);
			log.debug(this.getClass().getName()
					+ "- [getInterestChargesReport] - Exiting ");
			return jasperPrint;
		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getInterestChargesReport] method : - "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			throw swtexp;
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [getInterestChargesReport] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getInterestChargesReport",
					InterestChargesManagerImpl.class);
		}
		/*
		 * End:Code Modified For Mantis 1622 by Sudhakar on 20-09-2012: Interest
		 * Charges by Account Report: From and To date range instead of
		 * Month/Year
		 */

	}
}