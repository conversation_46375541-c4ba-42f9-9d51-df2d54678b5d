/*
 * @(#)CurrencyFundingManagerImpl.java 1.0 10/08/2009
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;

import net.sf.jasperreports.engine.JasperPrint;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.reports.dao.CurrencyFundingDAO;

import org.swallow.reports.model.CurrencyFunding;
import org.swallow.reports.service.CurrencyFundingManager;
import org.swallow.util.LabelValueBean;

/**
 * 
 * <AUTHOR> 
 * Class that implements the CurrencyFundingManager .
 * 
 */
@Component("currencyFundingManager")
public class CurrencyFundingManagerImpl implements CurrencyFundingManager {

	/* getting Log for logging */
	private static final Log log = LogFactory
			.getLog(CurrencyFundingManagerImpl.class);

	/* Instance variable of DAO */
	@Autowired
	private CurrencyFundingDAO currencyFundingDAO;

	/**
	 * Setter method for the DAO 
	 * @param currencyFundingDAO
	 */
	public void setCurrencyFundingDAO(CurrencyFundingDAO currencyFundingDAO) {
		this.currencyFundingDAO = currencyFundingDAO;
	}

	/**
	 * Gets the AccountIds from Database table
	 * 
	 * @param String hostId
	 * @param String entityId
	 * @param String currencyCode
	 * @return Collection
	 */
	public Collection getAccountDetails(String hostId, String entityId,
			String currencyCode) throws SwtException {

		log.debug(this.getClass().getName()
				+ "- [getAccountDetails] - Entering ");

		/* Collection object to hold the collection of Account Id */
		Collection accountList = null;
		/*
		 * Collection object to hold the collection of Account Id in Label Value
		 * Bean
		 */
		Collection finalList = null;
		/* currencyfunding object */
		CurrencyFunding currencyfunding = new CurrencyFunding();
		/* Creates an instance for ArrayList */
		finalList = new ArrayList();
		/* Creates an instance for ArrayList */
		accountList = new ArrayList();	

		/* get the list of accountIds from the dao */
		accountList = currencyFundingDAO.getAccountDetails(hostId, entityId,
				currencyCode);
		
		/* Converting Collection to ArrayList */
		ArrayList list = (ArrayList) accountList;

		/* It is used to iterate the accountId */
		Iterator it_accountList = list.iterator();

		while (it_accountList.hasNext()) {

			/* Sets the accountName, accountId in bean object */
			currencyfunding.setAccountName(
					it_accountList.next().toString());
			
			currencyfunding.setAccountId(
					it_accountList.next().toString());
			
			/* Puts the accountId, accountName in LabelValueBean */
			finalList.add(new LabelValueBean(currencyfunding.getAccountId(), currencyfunding.getAccountName()));

		}
		log.debug(this.getClass().getName()
				+ "- [getAccountDetails] - Exiting ");

		/* Returns the collection Object */
		return finalList;
	}

	/**
	 * This method is used to compile and return the reports
	 * 
	 * @param request - HttpServletRequest
	 * @param hostId - String
	 * @param entityId - String
	 * @param entityName - String
	 * @param currencyCode - String
	 * @param currencyName - String
	 * @param selectedAcctId - String
	 * @param accountName - String
	 * @param valueDate - String
	 * @param thresholdValue - String
	 * @param showDR - String
	 * @param showCR - String
	 * @return JasperPrint
	 * @throws SwtException
	 * 
	 */
	public JasperPrint getCurrencyFundingReport(HttpServletRequest request,
			String hostId, String entityId, String entityName,
			String currencyCode, String currencyName, String selectedAcctId,
			String accountName, String valueDate, String thresholdValue,
			String showDR, String showCR, String dateFormat) throws SwtException {
		log.debug(this.getClass().getName()
				+ "- [getCurrencyFundingReport] - Entering ");
		try {
			/* To call the getOpportunityCostReport in ReportsDAOHibernate. */
			JasperPrint jasperPrint = currencyFundingDAO
					.getCurrencyFundingReport(request, hostId, entityId,
							entityName, currencyCode, currencyName,
							selectedAcctId, accountName, valueDate,
							thresholdValue, showDR, showCR, dateFormat);
			log.debug(this.getClass().getName()
					+ "- [getCurrencyFundingReport] - Exiting ");
			return jasperPrint;

		} catch (Exception exp) {
			log.debug(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyFundingReport] method : - "
							+ exp.getMessage());
			log.error(this.getClass().getName()
							+ " - Exception Catched in [getCurrencyFundingReport] method : - "
							+ exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCurrencyFundingReport",
					CurrencyFundingManagerImpl.class);
		}
	}
}