/*
 * @(#)ReportsManager.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;

import net.sf.jasperreports.engine.JasperPrint;

import org.swallow.exception.SwtException;
import org.swallow.reports.model.Reports;

public interface ReportsManager {

	/**
	 * This method returns user list for a host id
	 * @param userId
	 * @return collection of user list
	 * @throws SwtException	 * 
	 */
	public Collection getUserList(String hostId) throws SwtException;

	/*
	 * Start : Added code to get the OpportunityCost to be displayed to the
	 * user.. Added by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16-Aug-08.--Mantis 656
	 */
	// Mantis 1110 : currency code,name are passed as parameter to DAO class
	/* Start : Modified by Vivekanandan A for Mantis 1203 on 03-Aug-2010 
	 * To get the selected date is holiday */
	/**
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param reportDate
	 * @param threshold
	 * @param currencyCode
	 * @param currencyName
	 * @param isHoliday
	 * @return JasperPrint
	 * @throws SwtException
	 *             This method will compile and return the reports to the Action
	 *             class.
	 */	
	/* START : Modified by Vivekanandan for Mantis 1203 : Weekend/Holiday the result should be zero
     * on 09-08-2010*/
	public JasperPrint getOpportunityCost(HttpServletRequest request,
			String hostId, String entityId, String reportDate, String threshold,String currencyCode,String currencyName, String dateFormat,String outputFormat)throws SwtException;
	/* END : Modified by Vivekanandan for Mantis 1203 : Weekend/Holiday the result should be zero
     * on 09-08-2010*/
	/* End : Modified by Vivekanandan A for Mantis 1203 on 03-Aug-2010 
	 * To get the selected date is holiday */

	/*
	 * End : Added code to get the OpportunityCost to be displayed to the user..-Mantis 656
	 * Added by Thirumurugan on 16-Aug-08.
	 */

	/**
	 * Added by Med Amine for Mantis 2145: Excluded movements Report
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param entityName
	 * @param reportDate
	 * @param threshold
	 * @param thresholdType
         * @param thresholdTime
	 * @return JasperPrint
	 * @throws SwtException
	 * This method will compile and return the reports to the Action class.
	 */
	public JasperPrint getExcludedMovements(HttpServletRequest request,
			String hostId, String entityId, String entityName,String reportDate, String threshold, String thresholdType,String thresholdTime)throws SwtException;
	
	
	/**
         * Added by Mefteh Bouazizi for Mantis 2145: Unsettled Movements Report
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param entityName
	 * @param reportDate
	 * @param threshold
	 * @param thresholdType
	 * @return JasperPrint
	 * @throws SwtException
	 * This method will compile and return the reports to the Action class.
	 */	
	public JasperPrint getUnsettledMovements(HttpServletRequest request,
			String hostId, String entityId,String entityName, String reportDate, String threshold, String thresholdType)throws SwtException;
	
	/**
	 * Get the daily ILM report for Group, Basel A or Basel B report type 
	 * @param request
	 * @param report
	 * @param hostId
	 * @param dbLink
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public JasperPrint getILMReportDaily(HttpServletRequest request,Reports report,String hostId,String dbLink,String roleId) throws SwtException;
	
	/**
	 * Get the date range ILM report for Group, Basel A or Basel B report type
	 * @param request
	 * @param report
	 * @param hostId
	 * @param dbLink
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public JasperPrint getILMReportMonthly(HttpServletRequest request,Reports report,String hostId,String dbLink,String roleId) throws SwtException;
	/**
	 * Get the date range ILM report for Group, Basel A or Basel B report type
	 * @param request
	 * @param report
	 * @param hostId
	 * @param dbLink
	 * @param roleId
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<Map<String, Object>> getILMReportExcelData(HttpServletRequest request,Reports report,String hostId,String dbLink,String roleId) throws SwtException;
	
	/**
	 * Get the selected date in currency timeframe
	 * @param entityId
	 * @param currencyId
	 * @param selecteddate
	 * @return Date
	 * @throws SwtException
	 */
	public Date getDateInCcyTimeframe(String entityId, String currencyId, Date selecteddate) throws SwtException;
	/**
	 * 
	 * @param reportType
	 * @param hostId
	 * @param entityId
	 * @param currencyCode
	 * @param ilmGroup
	 * @param scenarioId
	 * @param startDateRange
	 * @param endDateRange
	 * @param dbLink
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<String> checkMissingData(HttpServletRequest request,String reportType, String hostId,
			String entityId, String currencyCode, String ilmGroup,
			String scenarioId, String roleId, String startDateRange, String endDateRange,
			String dbLink) throws SwtException;
}
