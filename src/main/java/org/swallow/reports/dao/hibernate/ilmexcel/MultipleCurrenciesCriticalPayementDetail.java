package org.swallow.reports.dao.hibernate.ilmexcel;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;


public class MultipleCurrenciesCriticalPayementDetail extends MultipleCurrencies{
	

	public static Map<String, Object> getBeansMap(HashMap<String, Object> allData) throws Exception{
		Map<String, Object> beans = new HashMap<String, Object>();
		HashMap<String, String> StyleMap = new HashMap<String, String>();
		StyleMap = SwtUtil.getStyleMap();
		// Dictionary
		HashMap<String, String> dictionary = new HashMap<String, String>(){
			{
				put("report_title", SwtUtil.getMessageFromSession("ilmExcelReport.titleIntradayLiquidityManagementReport", UserThreadLocalHolder.getUserSession()));
				put("entity_id", SwtUtil.getMessageFromSession("ilmExcelReport.entity", UserThreadLocalHolder.getUserSession()));
				put("date_from", SwtUtil.getMessageFromSession("ilmExcelReport.fromDate", UserThreadLocalHolder.getUserSession()));
				put("date", SwtUtil.getMessageFromSession("ilmExcelReport.date", UserThreadLocalHolder.getUserSession()));
				put("date_end", SwtUtil.getMessageFromSession("ilmExcelReport.toDate", UserThreadLocalHolder.getUserSession())); 
				put("ccy_multiplier", SwtUtil.getMessageFromSession("ilmExcelReport.multiplier", UserThreadLocalHolder.getUserSession()));
				put("scenario", SwtUtil.getMessageFromSession("ilmExcelReport.scenario", UserThreadLocalHolder.getUserSession()));
				put("group", SwtUtil.getMessageFromSession("ilmExcelReport.group", UserThreadLocalHolder.getUserSession()));
				
				put("crit_paym_outflow", SwtUtil.getMessageFromSession("ilmExcelReport.criticalPaymentsOutflows", UserThreadLocalHolder.getUserSession()));
				put("crit_paym_inflow", SwtUtil.getMessageFromSession("ilmExcelReport.criticalPaymentsInflows", UserThreadLocalHolder.getUserSession()));
				
				put("very_crit_paym", SwtUtil.getMessageFromSession("ilmExcelReport.veryCriticalPayments", UserThreadLocalHolder.getUserSession()));
				put("expec_time", SwtUtil.getMessageFromSession("ilmExcelReport.expectedTime", UserThreadLocalHolder.getUserSession()));
				put("act_time", SwtUtil.getMessageFromSession("ilmExcelReport.actualTime", UserThreadLocalHolder.getUserSession()));
				put("amount", SwtUtil.getMessageFromSession("ilmExcelReport.amount", UserThreadLocalHolder.getUserSession()));
				put("sign", SwtUtil.getMessageFromSession("ilmExcelReport.sign", UserThreadLocalHolder.getUserSession()));

				
				put("crit", SwtUtil.getMessageFromSession("ilmExcelReport.critical", UserThreadLocalHolder.getUserSession()));
				put("expec_sett", SwtUtil.getMessageFromSession("ilmExcelReport.expectedSettlement", UserThreadLocalHolder.getUserSession()));
				put("act_sett", SwtUtil.getMessageFromSession("ilmExcelReport.actualSettlement", UserThreadLocalHolder.getUserSession()));
				put("cp_type1", SwtUtil.getMessageFromSession("ilmExcelReport.cpType1", UserThreadLocalHolder.getUserSession()));
				if("outflow".equals(inflowOrOutlfow)) {
					put("subtitle", SwtUtil.getMessageFromSession("ilmExcelReport.tabNameCriticalOutflowsDetail", UserThreadLocalHolder.getUserSession()));
				}else {
					
					put("subtitle", SwtUtil.getMessageFromSession("ilmExcelReport.tabNameCriticalInflowsDetail", UserThreadLocalHolder.getUserSession()));
				}
			}
		};
		beans.put("dic", dictionary);
		beans.put("styles", StyleMap);
		// Main data
		SimpleDateFormat sdfWithTime = new SimpleDateFormat(pDateFormat +" HH:mm:ss");
		SimpleDateFormat sdf = new SimpleDateFormat(pDateFormat);
		final Date   pValue_Date = sdf.parse(dateFrom);
		final Date   pValue_Date_End = sdf.parse(dateTo);
		
		// Header
		HashMap<String, Object> header = new HashMap<String, Object>(){
			{
				put("entity_id", pEntity_Id);
				put("entity_name", pEntityName);
				put("date_from",pValue_Date);
				put("date_to",pValue_Date_End);
				put("ccy_multiplier","Y".equals(pUseCcyMultiplier)?SwtUtil.getMessageFromSession("ilmExcelReport.ccyMultiplierEnabled", null):SwtUtil.getMessageFromSession("ilmExcelReport.ccyMultiplierDisabled", UserThreadLocalHolder.getUserSession()));
				put("scenario",pSeriesIdentifier);
				put("scenarioDesc",pSeriesIdentifierDesc);
				put("group",pGroup);
				put("groupName",pGroupName);
			}
		};
		
		beans.put("header", header);
		HashMap<String, String> criticalPayTypesCategory = new HashMap<String, String>(){
			{
				put("V", SwtUtil.getMessageFromSession("ilmExcelReport.veryCritical", UserThreadLocalHolder.getUserSession()));
				put("C", SwtUtil.getMessageFromSession("ilmExcelReport.critical", UserThreadLocalHolder.getUserSession()));
				put("N",SwtUtil.getMessageFromSession("ilmExcelReport.other", UserThreadLocalHolder.getUserSession()));
			}
		};
		// Throughput
				LinkedList<CurrencyCriticalDetails> dataForReport = new LinkedList<CurrencyCriticalDetails>();
				LinkedList<CriticalDetailsList> criticalDetailsData = new LinkedList<CriticalDetailsList>();
				ConcurrentHashMap<String, HashMap<String, Object>> mainRepData = (ConcurrentHashMap) allData.get("mainDataAvg");
				LinkedList<HashMap<String, Object>> allccy = (LinkedList<HashMap<String, Object>>) allData.get("allccy");
				LinkedList<HashMap<String, Object>> mainDataDaily = null;
				LinkedList<CriticalPayementDetails> critcalListHashmap = new LinkedList<CriticalPayementDetails>();
				Movement movement = null;
				LinkedList<Movement> movementListDetails = null;
				ConcurrentHashMap<String, LinkedList<HashMap<String, Object>>> criticalPayementDataList = null;
				List<HashMap<String, Object>> mainDataAvg = new LinkedList<HashMap<String,Object>>();
				LinkedList<String> ccyList = new LinkedList<String>();
				int i = 0;
				String criticalPaymentCategory = null;
				String criticalPayementTypeDesc = null;
				Date expectedTime = null;
				Date actualTime = null;
				Double amount = null;
				String sign = null;
				String actualTimebackGroundColor = null;
				String amountGroundColor = null;
				Double min_ncp_threshold = null;
				Double net_cum_pos = null;
				Double sourceOfIntraDayLiqExIncom = null;
				HashMap<String, Double> sourceOfLiquidity = new HashMap<String, Double>();
				
				for (String key : mainRepData.keySet()) {
					HashMap<String, Object> rec = mainRepData.get(key);
					sourceOfLiquidity.put(key, getValueOf(rec.get("min1_avlbl_assets_v")));
				}
				
				if("outflow".equals(inflowOrOutlfow))
					criticalPayementDataList = (ConcurrentHashMap) allData.get("criticalPayementOutlfowDataList");
				else
					criticalPayementDataList = (ConcurrentHashMap) allData.get("criticalPayementInflowDataList");
					
				
				CriticalPayementDetails criticalDetails  = null;
				
				CriticalDetailsList criticalPayement  = null;
				for(HashMap<String, Object> rec:allccy){
					ccyList.add((String)rec.get("ccy"));
					
					mainDataDaily = criticalPayementDataList.get((String)rec.get("ccy"));
					for(HashMap<String, Object> rec2:mainDataDaily){
						
						if(criticalPaymentCategory == null || !criticalPaymentCategory.equals(rec2.get("critical_payment_catg"))){
							if(criticalPaymentCategory != null){
								criticalDetails = new CriticalPayementDetails();
								criticalDetails.setMovementType(criticalPayementTypeDesc);
								criticalDetails.setMovementList(movementListDetails);
								critcalListHashmap.add(criticalDetails);
								criticalPayement.setCategory(criticalPayTypesCategory.get(criticalPaymentCategory));
								criticalPayement.setDetails(critcalListHashmap);
								criticalDetailsData.add(criticalPayement);
							}
							
							
							movementListDetails =null;
							criticalPayement = null;
							critcalListHashmap = null;
							
							movementListDetails = new LinkedList<Movement>();
							criticalPayement = new CriticalDetailsList();
							critcalListHashmap = new LinkedList<CriticalPayementDetails>();
						//	rec2.get("cp_type_desc").toString()
						}
						else if(criticalPayementTypeDesc == null || !criticalPayementTypeDesc.equals(rec2.get("cp_type_desc"))){
							if(criticalPayementTypeDesc != null){
								criticalDetails = new CriticalPayementDetails();
								criticalDetails.setMovementType(criticalPayementTypeDesc);
								criticalDetails.setMovementList(movementListDetails);
								critcalListHashmap.add(criticalDetails);
							}
							movementListDetails = null;
							movementListDetails = new LinkedList<Movement>();
						}
						
						String expectedSettlementDatetime = (String) rec2.get("expected_settlement_datetime");
						if (!SwtUtil.isEmptyOrNull(expectedSettlementDatetime)) {
							expectedTime = sdfWithTime.parse(expectedSettlementDatetime);
						}
						String settlementDatetime = (String) rec2.get("settlement_datetime");
						if (!SwtUtil.isEmptyOrNull(settlementDatetime)) {
							actualTime = sdfWithTime.parse(settlementDatetime);
						}
						amount = getValueOf(rec2.get("amount"));
						sign = (String) rec2.get("sign");
						if (SwtUtil.isEmptyOrNull(settlementDatetime) || SwtUtil.isEmptyOrNull(expectedSettlementDatetime)) {
							actualTimebackGroundColor = "white";
						} else {
							if(actualTime.after(expectedTime))
								actualTimebackGroundColor = "orange";
							else 
								actualTimebackGroundColor = "white";
						}
						min_ncp_threshold = getValueOf(rec.get("min_ncp_threshold"));
						
						net_cum_pos = getValueOf(rec2.get("net_cum_pos"));
						if(min_ncp_threshold != null && "Y".equals(pUseCcyMultiplier))
							min_ncp_threshold = min_ncp_threshold / getValueOf(rec.get("ccy_multiplier_value"));
						
						sourceOfIntraDayLiqExIncom = sourceOfLiquidity.get(rec.get("ccy"));
						if("outflow".equals(inflowOrOutlfow)) {
							if(sourceOfIntraDayLiqExIncom != null  && net_cum_pos!= null && net_cum_pos< -sourceOfIntraDayLiqExIncom ){
								amountGroundColor = "red";
							}
							else if(net_cum_pos != null && min_ncp_threshold != null && net_cum_pos < (min_ncp_threshold)){
								amountGroundColor = "orange";
							}
							else 
								amountGroundColor = "white";
						}else {
							amountGroundColor = "white";
						}
						
						movement = new Movement(expectedSettlementDatetime, settlementDatetime, amount, sign, actualTimebackGroundColor, amountGroundColor);
						
						movementListDetails.add(movement);
						
						criticalPaymentCategory = (String) rec2.get("critical_payment_catg");
						criticalPayementTypeDesc = (String) rec2.get("cp_type_desc");
					}
					
					if(criticalPaymentCategory != null){
						criticalDetails = new CriticalPayementDetails();
						criticalDetails.setMovementType(criticalPayementTypeDesc);
						criticalDetails.setMovementList(movementListDetails);
						critcalListHashmap.add(criticalDetails);
						criticalPayement.setCategory(criticalPayTypesCategory.get(criticalPaymentCategory));
						criticalPayement.setDetails(critcalListHashmap);
						criticalDetailsData.add(criticalPayement);
					}
					
					

					CurrencyCriticalDetails ccyCriticalPayement = new CurrencyCriticalDetails();
					ccyCriticalPayement.setCurrency((String)rec.get("ccy"));
					
					if("Y".equals(pUseCcyMultiplier))
						ccyCriticalPayement.setMultiplier((String)rec.get("multiplier_label"));
					else
						ccyCriticalPayement.setMultiplier("");
						
					ccyCriticalPayement.setDetails(criticalDetailsData);;
					dataForReport.add(ccyCriticalPayement);
					criticalDetailsData = new LinkedList<CriticalDetailsList>();
					critcalListHashmap = new LinkedList<CriticalPayementDetails>();
					criticalPaymentCategory = null;
					criticalPayementTypeDesc = null;
					i++;
					
				}
				beans.put("dataList", dataForReport);
				beans.put("ccyList", ccyList);
				beans.put("mainDataAvg", mainDataAvg);
				
				return beans;
			}
	
			protected static Double getValueOf(Object tmp) throws Exception{
				if(tmp != null)
					return (Double)tmp;
				else 
					return null;
			}
			
			
			public static class CriticalPayementDetails{
				private String movementType;
				
				public String getMovementType() {
					return movementType;
				}
				public void setMovementType(String movementType) {
					this.movementType = movementType;
				}


				private LinkedList<Movement> movementList;
				
				
				

				public LinkedList<Movement> getMovementList() {
					return movementList;
				}
				public void setMovementList(LinkedList<Movement> movementList) {
					this.movementList = movementList;
				}

			}
				public static class Movement{
					private String expectedTime = null;
					private String actualTime = null;
					private Double amount = null;
					private String sign = null;
					private String actualTimebackGroundColor = null;
					private String amountGroundColor = null;
					private String actualTimefontColor = null;
					private String amountfontColor = null;
					public String getExpectedTime() {
						return expectedTime;
					}
					public void setExpectedTime(String expectedTime) {
						this.expectedTime = expectedTime;
					}
					public String getActualTime() {
						return actualTime;
					}
					public void setActualTime(String actualTime) {
						this.actualTime = actualTime;
					}
					public Double getAmount() {
						return amount;
					}
					public void setAmount(Double amount) {
						this.amount = amount;
					}
					public String getSign() {
						return sign;
					}
					public void setSign(String sign) {
						this.sign = sign;
					}
					public String getActualTimebackGroundColor() {
						return actualTimebackGroundColor;
					}
					public void setActualTimebackGroundColor(String actualTimebackGroundColor) {
						this.actualTimebackGroundColor = actualTimebackGroundColor;
					}
					public String getAmountGroundColor() {
						return amountGroundColor;
					}
					public void setAmountGroundColor(String amountGroundColor) {
						this.amountGroundColor = amountGroundColor;
					}
					public String getActualTimefontColor() {
						return actualTimefontColor;
					}
					public void setActualTimefontColor(String actualTimefontColor) {
						this.actualTimefontColor = actualTimefontColor;
					}
					public String getAmountfontColor() {
						return amountfontColor;
					}
					public void setAmountfontColor(String amountfontColor) {
						this.amountfontColor = amountfontColor;
					}
					public Movement(String expectedTime, String actualTime, Double amount, String sign,
							String actualTimebackGroundColor, String amountGroundColor) {
						super();
						this.expectedTime = expectedTime;
						this.actualTime = actualTime;
						this.amount = amount;
						this.sign = sign;
						this.actualTimebackGroundColor = actualTimebackGroundColor;
						this.amountGroundColor = amountGroundColor;
						if(amountGroundColor == null)
							amountGroundColor = "white";
						if(actualTimebackGroundColor == null)
							actualTimebackGroundColor = "white";
						
						if("white".equals(actualTimebackGroundColor))
								actualTimefontColor = "black";
						else 
								actualTimefontColor = "white";
							
						if("white".equals(amountGroundColor))
								amountfontColor = "black";
						else 
								amountfontColor = "white";
							
					}
					
					
				}
				
				public static class CriticalDetailsList{
					private String category = null;
					private LinkedList<CriticalPayementDetails> details = null;
					public String getCategory() {
						return category;
					}
					public void setCategory(String currency) {
						this.category = currency;
					}
					public LinkedList<CriticalPayementDetails> getDetails() {
						return details;
					}
					public void setDetails(LinkedList<CriticalPayementDetails> details) {
						this.details = details;
					}
					
					
				}
				
				
				public static class CurrencyCriticalDetails{
					private String currency = null;
					private String multiplier = null;
					private LinkedList<CriticalDetailsList> details = null;
					public String getCurrency() {
						return currency;
					}
					public void setCurrency(String currency) {
						this.currency = currency;
					}
					public LinkedList<CriticalDetailsList> getDetails() {
						return details;
					}
					public void setDetails(LinkedList<CriticalDetailsList> details) {
						this.details = details;
					}
					public String getMultiplier() {
						return multiplier;
					}
					public void setMultiplier(String multiplier) {
						this.multiplier = multiplier;
					}
					
				}
}
