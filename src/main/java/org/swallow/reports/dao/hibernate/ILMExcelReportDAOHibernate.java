package org.swallow.reports.dao.hibernate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.maintenance.dao.ILMGeneralMaintenanceDAO;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.maintenance.service.ILMTransScenarioMaintenanceManager;
import org.swallow.reports.dao.hibernate.ilmexcel.MultipleCurrencies;
import org.swallow.reports.dao.hibernate.ilmexcel.MultipleCurrenciesAvailableLiquidity;
import org.swallow.reports.dao.hibernate.ilmexcel.MultipleCurrenciesCriticalPayement;
import org.swallow.reports.dao.hibernate.ilmexcel.MultipleCurrenciesCriticalPayementDetail;
import org.swallow.reports.dao.hibernate.ilmexcel.MultipleCurrenciesDailyLiquidity;
import org.swallow.reports.dao.hibernate.ilmexcel.MultipleCurrenciesPayement;
import org.swallow.reports.dao.hibernate.ilmexcel.MultipleCurrenciesThroughput;
import org.swallow.reports.model.Reports;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import jakarta.persistence.EntityManager;


public class ILMExcelReportDAOHibernate  extends CustomHibernateDaoSupport {
	
	public ILMExcelReportDAOHibernate(SessionFactory sessionFactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionFactory, entityManager);
        // TODO Auto-generated constructor stub
    }
    private final Log log = LogFactory.getLog(ILMExcelReportDAOHibernate.class);
	
	
	public ArrayList<Map<String, Object>> getSingleDayData(Reports report,
			String hostId, String dbLink, String roleId, String dateFomat) throws Exception {
		
		
		SimpleDateFormat sdf = new SimpleDateFormat(dateFomat);
		
		HashMap<String, Object> allData = new HashMap<String, Object>();
		LinkedList<HashMap<String, Object>> availableLiqAvg = new LinkedList<HashMap<String,Object>>();
		ConcurrentHashMap mainDataAvgList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap<String ,HashMap<String, Object>> mainDataAvg = new ConcurrentHashMap<String ,HashMap<String, Object>>();
		ConcurrentHashMap assetsList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap criticalPayementOutlfowDataList= new ConcurrentHashMap<String , LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap criticalPayementInflowDataList= new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap criticalPayementAvgOutflowDataList= new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap criticalPayementAvgInflowDataList= new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap throughputList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ArrayList<Map<String, Object>> beansList = new ArrayList<Map<String, Object>>();
		LinkedList<HashMap<String, Object>> mainRepData = SwtUtil.executeNamedSelectQuery("maindata_avg", hostId, report.getEntityId(), report.getCurrencyCode(), "default".equals(report.getDefaultIlmGrp())?null:report.getIlmGroup(), report.getScenario(), roleId, dbLink, sdf.parse(report.getFromDateAsString()), sdf.parse(report.getFromDateAsString()),  dateFomat, report.getUseCcyMultiplier());
		LinkedList<HashMap<String, Object>> allccy = SwtUtil.executeNamedSelectQuery("header", hostId, report.getEntityId(), report.getCurrencyCode(), roleId, dbLink, sdf.parse(report.getFromDateAsString()), report.getScenario());
		Double ccyMultiplier = new Double(1);
		String entityName = getEntityName(report.getEntityId(), roleId);
		String scenarioNameDesc = getScenarioNameDescription(report.getScenario());
		String groupName = null;
		
		boolean isGlobalGroup = false;
		
		for(HashMap<String, Object> rec:mainRepData){
			
			mainDataAvg.put((String) rec.get("ccy"), rec);
		}
		ExecutorService executor = Executors.newFixedThreadPool(3);
		
		isGlobalGroup =  (("default".equals(report.getDefaultIlmGrp())) || allccy.size() >1);
		for(HashMap<String, Object> rec:allccy){
			if("Y".equals(report.getUseCcyMultiplier())) {
				ccyMultiplier = (Double) rec.get("ccy_multiplier_value");
			}
			if(allccy.size() == 1) {
				if(SwtUtil.isEmptyOrNull(report.getIlmGroup()))
					report.setIlmGroup((String) rec.get("ilm_group_id"));
				
				if(isGlobalGroup == false && rec.get("ilm_group_id").equals(report.getIlmGroup())) {
					isGlobalGroup = true;
				}
			}else if (allccy.size() > 1){
				report.setIlmGroup(SwtUtil.getMessageFromSession("ilmExcelReport.currencyGlobalGroup", UserThreadLocalHolder.getUserSession()));
			}
			SingleCcyThread thread = new SingleCcyThread(hostId, report.getEntityId(), rec.get("ccy"),"default".equals(report.getDefaultIlmGrp())?rec.get("ilm_group_id"):report.getIlmGroup(), report.getScenario(), roleId,  dbLink,  sdf.parse(report.getFromDateAsString()), sdf.parse(report.getFromDateAsString()) ,dateFomat, report.getUseCcyMultiplier(), ccyMultiplier, report.getCriticalTransactions());
			thread.setThreadLists(mainDataAvgList, assetsList, criticalPayementOutlfowDataList, criticalPayementInflowDataList, criticalPayementAvgOutflowDataList, criticalPayementAvgInflowDataList, throughputList);
			executor.execute(thread);
		}

		// Initiates an orderly shutdown
        executor.shutdown();
        
		// Block the main thread until finishing all workers
       	try {
       		while(!executor.isTerminated()){
       			//System.out.println("Sleeping for 10 seconds:"+executor.isShutdown());
       			executor.awaitTermination(5, TimeUnit.SECONDS);
       		}
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
       
       	
       	groupName = getGroupName(report.getIlmGroup());
		report.setIlmGrpName(groupName);
		
		
		allData.put("mainDataAvg", mainDataAvg);
		allData.put("allccy", allccy);
		allData.put("throughputList", throughputList);
		allData.put("availableLiqAvgList", mainDataAvgList);
		allData.put("availableLiqAvg", availableLiqAvg);
		allData.put("assetsList", assetsList);
		allData.put("criticalPayementInflowDataList", criticalPayementInflowDataList);
		allData.put("criticalPayementOutlfowDataList", criticalPayementOutlfowDataList);
		allData.put("criticalPayementAvgOutflowDataList", criticalPayementAvgOutflowDataList);
		allData.put("criticalPayementAvgInflowDataList", criticalPayementAvgInflowDataList);
		
		Map<String, Object> myBeansMap = null;
		MultipleCurrencies.pHost_Id = hostId;
		MultipleCurrencies.pEntity_Id = report.getEntityId();
		MultipleCurrencies.pEntityName = entityName;
		MultipleCurrencies.pCurrency_Code = "All";
		MultipleCurrencies.pSeriesIdentifier = report.getScenario();
		MultipleCurrencies.pSeriesIdentifierDesc = scenarioNameDesc;
		MultipleCurrencies.isGlobalGroup = isGlobalGroup;
		MultipleCurrencies.pGroup = report.getIlmGroup();
		MultipleCurrencies.pGroupName = report.getIlmGrpName();
		MultipleCurrencies.pRoleId = roleId;
		MultipleCurrencies.pDBLink = dbLink;
		MultipleCurrencies.dateFrom = report.getFromDateAsString();
		MultipleCurrencies.dateTo = report.getFromDateAsString();
		MultipleCurrencies.pDateFormat = dateFomat;
		MultipleCurrencies.isDateRange = false;
		MultipleCurrencies.pUseCcyMultiplier = report.getUseCcyMultiplier();
		MultipleCurrencies.isShowAllCritPayment = "SHA".equals(report.getCriticalTransactions());
		myBeansMap = MultipleCurrencies.getBeansMap(allData);
		beansList.add(myBeansMap);		
		MultipleCurrenciesCriticalPayementDetail.pHost_Id = hostId;
		MultipleCurrenciesCriticalPayementDetail.pEntity_Id = report.getEntityId();
		MultipleCurrenciesCriticalPayementDetail.pEntityName = entityName;
		MultipleCurrenciesCriticalPayementDetail.pCurrency_Code = "All";
		MultipleCurrenciesCriticalPayementDetail.pSeriesIdentifier = report.getScenario();
		MultipleCurrenciesCriticalPayementDetail.pSeriesIdentifierDesc = scenarioNameDesc;
		MultipleCurrenciesCriticalPayementDetail.isGlobalGroup = isGlobalGroup;
		MultipleCurrenciesCriticalPayementDetail.pGroup = report.getIlmGroup();
		MultipleCurrenciesCriticalPayementDetail.pGroupName = report.getIlmGrpName();
		MultipleCurrenciesCriticalPayementDetail.pRoleId = roleId;
		MultipleCurrenciesCriticalPayementDetail.pDBLink = dbLink;
		MultipleCurrenciesCriticalPayementDetail.dateFrom = report.getFromDateAsString();
		MultipleCurrenciesCriticalPayementDetail.dateTo = report.getFromDateAsString();
		MultipleCurrenciesCriticalPayementDetail.pDateFormat = dateFomat;
		MultipleCurrenciesCriticalPayementDetail.pUseCcyMultiplier = report.getUseCcyMultiplier();
		MultipleCurrenciesCriticalPayementDetail.inflowOrOutlfow = "outflow";
		myBeansMap = MultipleCurrenciesCriticalPayementDetail.getBeansMap(allData);
		beansList.add(myBeansMap);
		
		if("Standard".equals(report.getScenario())) {
			MultipleCurrenciesCriticalPayementDetail.pHost_Id = hostId;
			MultipleCurrenciesCriticalPayementDetail.pEntity_Id = report.getEntityId();
			MultipleCurrenciesCriticalPayementDetail.pEntityName = entityName;
			MultipleCurrenciesCriticalPayementDetail.pCurrency_Code = "All";
			MultipleCurrenciesCriticalPayementDetail.pSeriesIdentifier = report.getScenario();
			MultipleCurrenciesCriticalPayementDetail.isGlobalGroup = isGlobalGroup;
			MultipleCurrenciesCriticalPayementDetail.pGroup = report.getIlmGroup();
			MultipleCurrenciesCriticalPayementDetail.pGroupName = report.getIlmGrpName();
			MultipleCurrenciesCriticalPayementDetail.pRoleId = roleId;
			MultipleCurrenciesCriticalPayementDetail.pDBLink = dbLink;
			MultipleCurrenciesCriticalPayementDetail.dateFrom = report.getFromDateAsString();
			MultipleCurrenciesCriticalPayementDetail.dateTo = report.getFromDateAsString();
			MultipleCurrenciesCriticalPayementDetail.pDateFormat = dateFomat;
			MultipleCurrenciesCriticalPayementDetail.pUseCcyMultiplier = report.getUseCcyMultiplier();
			MultipleCurrenciesCriticalPayementDetail.inflowOrOutlfow = "inflow";
			myBeansMap = MultipleCurrenciesCriticalPayementDetail.getBeansMap(allData);
			beansList.add(myBeansMap);
		}
		
		return beansList;
	}
	
	 
	public String getEntityName (String entityId, String roleId) {
		String entityName = null;
		Collection coll  = null;
		if(UserThreadLocalHolder.getUserSession() == null) {
			coll = SwtUtil.getSwtMaintenanceCache().getFullEntityAccessCollection(roleId);
		}else {
			coll = SwtUtil.getUserEntityAccessList(UserThreadLocalHolder
				.getUserSession());
		}
		if (coll != null) {
			Iterator itra = coll.iterator();
			while (itra.hasNext()) {
				EntityUserAccess entityUserAccessObj = (EntityUserAccess) itra
						.next();
				if (entityUserAccessObj.getEntityId().equals(entityId)) {
					entityName = entityUserAccessObj.getEntityName();
					break;
				}
			}
		}
		
		return entityName;
	}
	
	public String getScenarioNameDescription (String scenarioId) {
		String scenarioName = null;
		try{
			if(!scenarioId.equals("Standard")){
				ILMTransScenarioMaintenanceManager scenDAOHibernate = (ILMTransScenarioMaintenanceManager) (SwtUtil.getBean("ilmTransScenarioMaintenanceManager"));
				ILMScenario scenario = scenDAOHibernate.getEditableScenarioData(scenarioId);
				scenarioName = scenario.getIlmScenarioDescription();
			}else {
				return SwtUtil.getMessageFromSession("ilmExcelReport.standardScenarioNameDescription", UserThreadLocalHolder.getUserSession());
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		
		return scenarioName;
		
	}
	
	public String getGroupName (String groupId) {
		String groupName = null;
		try{
			if(groupId != null) {
				ILMGeneralMaintenanceDAO ilmGeneralMaintenanceDAO = (ILMGeneralMaintenanceDAO) (SwtUtil.getBean("ilmGeneralMaintenanceDAO"));
				ILMAccountGroup group = ilmGeneralMaintenanceDAO.getEditableData(groupId);
				if(group != null && group.getIlmGroupName() != null)
					groupName = group.getIlmGroupName();
				else 
					groupName = "";
			}else {
				return "";
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		
		return groupName;
		
	}
	
	
	
	public ArrayList<Map<String, Object>> getDateRangeData(Reports report,
			String hostId, String dbLink, String roleId, String dateFomat) throws Exception {
		
		
		SimpleDateFormat sdf = new SimpleDateFormat(dateFomat);
		HashMap<String, Object> allData = new HashMap<String, Object>();
		CommonDataManager CDM = null;
		boolean isGlobalGroup = false;
		LinkedList<HashMap<String, Object>> availableLiqAvg = new LinkedList<HashMap<String,Object>>();
		ConcurrentHashMap mainDataAvg = new ConcurrentHashMap<String , HashMap<String,Object>>();
		ConcurrentHashMap assetsList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap mainDataAvgList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap availableLiqAvgList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap availableLiqDailyList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap mainDataDailyList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap throughputList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap throughputDailyList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();;
		//ORDER IS WRONG when entity = all  should use concurent hashmap instead of synchonzied collectetion
		ConcurrentHashMap availableLiqRankedList = new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>(); 
		ConcurrentHashMap criticalPayementAvgOutflowDataList= new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap criticalPayementAvgInflowDataList= new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap criticalPayementRankedDetailsList= new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap criticalAvgRankedInflowDetailsList= new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		ConcurrentHashMap criticalAvgRankedOutflowDetailsList= new ConcurrentHashMap<String ,LinkedList<LinkedList<HashMap<String,Object>>>>();
		
		ArrayList<Map<String, Object>> beansList = new ArrayList<Map<String, Object>>();
		
		LinkedList<HashMap<String, Object>> allccy = SwtUtil.executeNamedSelectQuery("header", hostId, report.getEntityId(), report.getCurrencyCode(), roleId, dbLink, sdf.parse(report.getFromDateAsString()), report.getScenario());
		
		Double ccyMultiplier = new Double(1);
		String entityName = getEntityName(report.getEntityId(), roleId);
		String scenarioNameDesc = getScenarioNameDescription(report.getScenario());
		String groupName = null;

		
		ExecutorService executor = Executors.newFixedThreadPool(3);
		
		isGlobalGroup =  ("default".equals(report.getDefaultIlmGrp()) || allccy.size() >1);
		
		try{
		for(HashMap<String, Object> rec:allccy){
			if(allccy.size() == 1) {
				if(SwtUtil.isEmptyOrNull(report.getIlmGroup()))
					report.setIlmGroup((String) rec.get("ilm_group_id"));
				
				if(isGlobalGroup == false && rec.get("ilm_group_id").equals(report.getIlmGroup())) {
					isGlobalGroup = true;
				}
			}else if (allccy.size() > 1){
				report.setIlmGroup(SwtUtil.getMessageFromSession("ilmExcelReport.currencyGlobalGroup", UserThreadLocalHolder.getUserSession()));
			}
			//generateOOM();
			if("Y".equals(report.getUseCcyMultiplier())) {
				ccyMultiplier = (Double) rec.get("ccy_multiplier_value");
			}
			SingleCcyThreadDateRange thread = new SingleCcyThreadDateRange(hostId, report.getEntityId(), rec.get("ccy"),"default".equals(report.getDefaultIlmGrp())?rec.get("ilm_group_id"):report.getIlmGroup(), report.getScenario(), roleId,  dbLink,  sdf.parse(report.getFromDateAsString()), sdf.parse(report.getToDateAsString()) ,dateFomat, report.getUseCcyMultiplier(), ccyMultiplier, report.getCriticalTransactions());
			thread.setThreadLists(mainDataAvg, assetsList, mainDataAvgList, availableLiqAvgList, availableLiqDailyList, mainDataDailyList, throughputList, throughputDailyList, availableLiqRankedList, criticalPayementAvgOutflowDataList, criticalPayementAvgInflowDataList, criticalPayementRankedDetailsList, criticalAvgRankedInflowDetailsList, criticalAvgRankedOutflowDetailsList);
			executor.execute(thread);
		}

		// Initiates an orderly shutdown
        executor.shutdown();
        
		// Block the main thread until finishing all workers
       	try {
       		while(!executor.isTerminated()){
       			//System.out.println("Sleeping for 10 seconds:"+executor.isShutdown());
       			if(UserThreadLocalHolder.getUserSession() != null) {
	       			CDM = (CommonDataManager)	UserThreadLocalHolder.getUserSession().getAttribute("CDM");
	       			if(CDM != null && "true".equals(CDM.getCancelExport())) {
	       				executor.shutdownNow();
	       			}
       			}
       			executor.awaitTermination(5, TimeUnit.SECONDS);
       		}
		} catch (InterruptedException e) {
		}
       	if(CDM != null && "true".equals(CDM.getCancelExport())){
       		return null;
       	}
       	
       	groupName = getGroupName(report.getIlmGroup());
		report.setIlmGrpName(groupName);
		
		
		allData.put("allccy", allccy);
		allData.put("throughputList", throughputList);
		allData.put("availableLiqAvgList", availableLiqAvgList);
		allData.put("availableLiqAvg", availableLiqAvg);
	
		
		allData.put("mainDataAvg", mainDataAvg);
		allData.put("mainDataAvgList", mainDataAvgList);
		
		allData.put("mainDataDailyList", mainDataDailyList);
		allData.put("availableLiqDailyList", availableLiqDailyList);
		allData.put("throughputDailyList", throughputDailyList);
		
		allData.put("availableLiqRankedList", availableLiqRankedList);
		
		allData.put("assetsList", assetsList);
		
		allData.put("criticalPayementAvgOutflowDataList", criticalPayementAvgOutflowDataList);
		allData.put("criticalPayementAvgInflowDataList", criticalPayementAvgInflowDataList);
		
		allData.put("criticalPayementRankedDetailsList", criticalPayementRankedDetailsList);
		
		allData.put("criticalAvgRankedOutflowDetailsList", criticalAvgRankedOutflowDetailsList);
		allData.put("criticalAvgRankedInflowDetailsList", criticalAvgRankedInflowDetailsList);
		
		Map<String, Object> myBeansMap = null;
		MultipleCurrencies.pHost_Id = hostId;
		MultipleCurrencies.pEntity_Id = report.getEntityId();
		MultipleCurrencies.pEntityName = entityName;
		MultipleCurrencies.pCurrency_Code = "All";
		MultipleCurrencies.pSeriesIdentifier = report.getScenario();
		MultipleCurrencies.pSeriesIdentifierDesc = scenarioNameDesc;
		MultipleCurrencies.isGlobalGroup = isGlobalGroup;
		MultipleCurrencies.pGroup = report.getIlmGroup();
		MultipleCurrencies.pGroupName = report.getIlmGrpName();
		MultipleCurrencies.pRoleId = roleId;
		MultipleCurrencies.pDBLink = dbLink;
		MultipleCurrencies.dateFrom = report.getFromDateAsString();
		MultipleCurrencies.dateTo = report.getToDateAsString();
		MultipleCurrencies.pDateFormat = dateFomat;
		MultipleCurrencies.pUseCcyMultiplier = report.getUseCcyMultiplier();
		MultipleCurrencies.isShowAllCritPayment = "SHA".equals(report.getCriticalTransactions());
		MultipleCurrencies.isDateRange = true;
		
		myBeansMap = MultipleCurrencies.getBeansMap(allData);
		beansList.add(myBeansMap);
		

		MultipleCurrenciesDailyLiquidity.pHost_Id = hostId;
		MultipleCurrenciesDailyLiquidity.pEntity_Id = report.getEntityId();
		MultipleCurrenciesDailyLiquidity.pEntityName = entityName;
		MultipleCurrenciesDailyLiquidity.pCurrency_Code = "All";
		MultipleCurrenciesDailyLiquidity.pSeriesIdentifier = report.getScenario();
		MultipleCurrenciesDailyLiquidity.pSeriesIdentifierDesc = scenarioNameDesc;
		MultipleCurrenciesDailyLiquidity.isGlobalGroup = isGlobalGroup;
		MultipleCurrenciesDailyLiquidity.pGroup = report.getIlmGroup();
		MultipleCurrenciesDailyLiquidity.pGroupName = report.getIlmGrpName();
		MultipleCurrenciesDailyLiquidity.pRoleId = roleId;
		MultipleCurrenciesDailyLiquidity.pDBLink = dbLink;
		MultipleCurrenciesDailyLiquidity.dateFrom = report.getFromDateAsString();
		MultipleCurrenciesDailyLiquidity.dateTo = report.getToDateAsString();
		MultipleCurrenciesDailyLiquidity.pDateFormat = dateFomat;
		MultipleCurrenciesDailyLiquidity.pUseCcyMultiplier = report.getUseCcyMultiplier();
		myBeansMap = MultipleCurrenciesDailyLiquidity.getBeansMap(allData);
		beansList.add(myBeansMap);
		
		MultipleCurrenciesPayement.pHost_Id = hostId;
		MultipleCurrenciesPayement.pEntity_Id = report.getEntityId();
		MultipleCurrenciesPayement.pEntityName = entityName;
		MultipleCurrenciesPayement.pCurrency_Code = "All";
		MultipleCurrenciesPayement.pSeriesIdentifier = report.getScenario();
		MultipleCurrenciesPayement.pSeriesIdentifierDesc = scenarioNameDesc;
		MultipleCurrenciesPayement.isGlobalGroup = isGlobalGroup;
		MultipleCurrenciesPayement.pGroup = report.getIlmGroup();
		MultipleCurrenciesPayement.pGroupName = report.getIlmGrpName();
		MultipleCurrenciesPayement.pRoleId = roleId;
		MultipleCurrenciesPayement.pDBLink = dbLink;
		MultipleCurrenciesPayement.dateFrom = report.getFromDateAsString();
		MultipleCurrenciesPayement.dateTo = report.getToDateAsString();
		MultipleCurrenciesPayement.pDateFormat = dateFomat;
		MultipleCurrenciesPayement.pUseCcyMultiplier = report.getUseCcyMultiplier();
		myBeansMap = MultipleCurrenciesPayement.getBeansMap(allData);
		beansList.add(myBeansMap);
		
		MultipleCurrenciesAvailableLiquidity.pHost_Id = hostId;
		MultipleCurrenciesAvailableLiquidity.pEntity_Id = report.getEntityId();
		MultipleCurrenciesAvailableLiquidity.pEntityName = entityName;
		MultipleCurrenciesAvailableLiquidity.pCurrency_Code = "All";
		MultipleCurrenciesAvailableLiquidity.pSeriesIdentifier = report.getScenario();
		MultipleCurrenciesAvailableLiquidity.pSeriesIdentifierDesc = scenarioNameDesc;
		MultipleCurrenciesAvailableLiquidity.isGlobalGroup = isGlobalGroup;
		MultipleCurrenciesAvailableLiquidity.pGroup = report.getIlmGroup();
		MultipleCurrenciesAvailableLiquidity.pGroupName = report.getIlmGrpName();
		MultipleCurrenciesAvailableLiquidity.pRoleId = roleId;
		MultipleCurrenciesAvailableLiquidity.pDBLink = dbLink;
		MultipleCurrenciesAvailableLiquidity.dateFrom = report.getFromDateAsString();
		MultipleCurrenciesAvailableLiquidity.dateTo = report.getToDateAsString();
		MultipleCurrenciesAvailableLiquidity.pDateFormat = dateFomat;
		MultipleCurrenciesAvailableLiquidity.pUseCcyMultiplier = report.getUseCcyMultiplier();
		myBeansMap = MultipleCurrenciesAvailableLiquidity.getBeansMap(allData);
		
		beansList.add(myBeansMap);
		MultipleCurrenciesThroughput.pHost_Id = hostId;
		MultipleCurrenciesThroughput.pEntity_Id = report.getEntityId();
		MultipleCurrenciesThroughput.pEntityName = entityName;
		MultipleCurrenciesThroughput.pCurrency_Code = "All";
		MultipleCurrenciesThroughput.pSeriesIdentifier = report.getScenario();
		MultipleCurrenciesThroughput.pSeriesIdentifierDesc = scenarioNameDesc;
		MultipleCurrenciesThroughput.isGlobalGroup = isGlobalGroup;
		MultipleCurrenciesThroughput.pGroup = report.getIlmGroup();
		MultipleCurrenciesThroughput.pGroupName = report.getIlmGrpName();
		MultipleCurrenciesThroughput.pRoleId = roleId;
		MultipleCurrenciesThroughput.pDBLink = dbLink;
		MultipleCurrenciesThroughput.dateFrom = report.getFromDateAsString();
		MultipleCurrenciesThroughput.dateTo = report.getToDateAsString();
		MultipleCurrenciesThroughput.pDateFormat = dateFomat;
		MultipleCurrenciesThroughput.pUseCcyMultiplier = report.getUseCcyMultiplier();
		MultipleCurrenciesThroughput.inflowOrOutlfow = "inflow";
		myBeansMap = MultipleCurrenciesThroughput.getBeansMap(allData);
		beansList.add(myBeansMap);
		MultipleCurrenciesThroughput.pHost_Id = hostId;
		MultipleCurrenciesThroughput.pEntity_Id = report.getEntityId();
		MultipleCurrenciesThroughput.pEntityName = entityName;
		MultipleCurrenciesThroughput.pCurrency_Code = "All";
		MultipleCurrenciesThroughput.pSeriesIdentifier = report.getScenario();
		MultipleCurrenciesThroughput.pSeriesIdentifierDesc = scenarioNameDesc;
		MultipleCurrenciesThroughput.isGlobalGroup = isGlobalGroup;
		MultipleCurrenciesThroughput.pGroup = report.getIlmGroup();
		MultipleCurrenciesThroughput.pGroupName = report.getIlmGrpName();
		MultipleCurrenciesThroughput.pRoleId = roleId;
		MultipleCurrenciesThroughput.pDBLink = dbLink;
		MultipleCurrenciesThroughput.dateFrom = report.getFromDateAsString();
		MultipleCurrenciesThroughput.dateTo = report.getToDateAsString();
		MultipleCurrenciesThroughput.pDateFormat = dateFomat;
		MultipleCurrenciesThroughput.pUseCcyMultiplier = report.getUseCcyMultiplier();
		MultipleCurrenciesThroughput.inflowOrOutlfow = "outflow";
		myBeansMap = MultipleCurrenciesThroughput.getBeansMap(allData);
		beansList.add(myBeansMap);
		if("Standard".equals(report.getScenario())) {
			MultipleCurrenciesCriticalPayement.pHost_Id = hostId;
			MultipleCurrenciesCriticalPayement.pEntity_Id = report.getEntityId();
			MultipleCurrenciesCriticalPayement.pEntityName = entityName;
			MultipleCurrenciesCriticalPayement.pCurrency_Code = "All";
			MultipleCurrenciesCriticalPayement.pSeriesIdentifier = report.getScenario();
			MultipleCurrenciesCriticalPayement.pSeriesIdentifierDesc = scenarioNameDesc;
			MultipleCurrenciesThroughput.isGlobalGroup = isGlobalGroup;
			MultipleCurrenciesCriticalPayement.pGroup = report.getIlmGroup();
			MultipleCurrenciesCriticalPayement.pGroupName = report.getIlmGrpName();
			MultipleCurrenciesCriticalPayement.pRoleId = roleId;
			MultipleCurrenciesCriticalPayement.pDBLink = dbLink;
			MultipleCurrenciesCriticalPayement.dateFrom = report.getFromDateAsString();
			MultipleCurrenciesCriticalPayement.dateTo = report.getToDateAsString();
			MultipleCurrenciesCriticalPayement.pDateFormat = dateFomat;
			MultipleCurrenciesCriticalPayement.pUseCcyMultiplier = report.getUseCcyMultiplier();
			MultipleCurrenciesCriticalPayement.inflowOrOutlfow = "outflow";
			myBeansMap = MultipleCurrenciesCriticalPayement.getBeansMap(allData);
			beansList.add(myBeansMap);
		}
		if("Standard".equals(report.getScenario()) && "SHA".equals(report.getCriticalTransactions())) {
			MultipleCurrenciesCriticalPayement.pHost_Id = hostId;
			MultipleCurrenciesCriticalPayement.pEntity_Id = report.getEntityId();
			MultipleCurrenciesCriticalPayement.pEntityName = entityName;
			MultipleCurrenciesCriticalPayement.pCurrency_Code = "All";
			MultipleCurrenciesCriticalPayement.pSeriesIdentifier = report.getScenario();
			MultipleCurrenciesCriticalPayement.pSeriesIdentifierDesc = scenarioNameDesc;
			MultipleCurrenciesThroughput.isGlobalGroup = isGlobalGroup;
			MultipleCurrenciesCriticalPayement.pGroup = report.getIlmGroup();
			MultipleCurrenciesCriticalPayement.pGroupName = report.getIlmGrpName();
			MultipleCurrenciesCriticalPayement.pRoleId = roleId;
			MultipleCurrenciesCriticalPayement.pDBLink = dbLink;
			MultipleCurrenciesCriticalPayement.dateFrom = report.getFromDateAsString();
			MultipleCurrenciesCriticalPayement.dateTo = report.getToDateAsString();
			MultipleCurrenciesCriticalPayement.pDateFormat = dateFomat;
			MultipleCurrenciesCriticalPayement.pUseCcyMultiplier = report.getUseCcyMultiplier();
			MultipleCurrenciesCriticalPayement.inflowOrOutlfow = "inflow";
			myBeansMap = MultipleCurrenciesCriticalPayement.getBeansMap(allData);
			beansList.add(myBeansMap);
		}
		
		return beansList;
		}catch(Exception e){
			e.printStackTrace();
			log.error(this.getClass().getName()
					+ "- [getDateRangeData] - Exception " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getDateRangeData",
					ILMExcelReportDAOHibernate.class);
		} finally {
			sdf = null;
			allData = null;
			mainDataAvg = null;
			availableLiqAvg = null;
			CDM = null;
			assetsList = null;
			mainDataAvgList = null;
			availableLiqAvgList = null;
			availableLiqDailyList = null;
			mainDataDailyList = null;
			throughputList = null;
			throughputDailyList = null;
			availableLiqRankedList = null;
			criticalPayementAvgOutflowDataList= null;
			criticalPayementAvgInflowDataList= null;
			criticalPayementRankedDetailsList= null;
			criticalAvgRankedInflowDetailsList= null;
			criticalAvgRankedOutflowDetailsList= null;
			allccy = null;
			ccyMultiplier = null;
			entityName = null;
			scenarioNameDesc =null;
			executor = null;
		}
	}
	
	class SingleCcyThread implements Runnable{
		LinkedList<HashMap<String, Object>> availableLiqAvg = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalPayementOutflowDetails = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalPayementInflowDetails = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalPayementOutflowAvgDetails = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalPayementInflowAvgDetails = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> assets = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> throughput = null;
		
		private Object hostId = null;
		private Object entityId = null;
		private Object ccy = null;
		private Object groupId = null;
		private Object scenario = null;
		private Object roleId;
		private Object dbLink;
		private Object startDate ;
		private Object dateFormat;
		private Object useCcyMuliplier ;
		private Object ccyMuliplierValue ;
		private Object criticalTransactions ;
		
		
		ConcurrentHashMap mainDataAvgList = null;
		ConcurrentHashMap assetsList = null;
		ConcurrentHashMap criticalPayementOutlfowDataList = null;
		ConcurrentHashMap criticalPayementInflowDataList = null;
		ConcurrentHashMap criticalPayementAvgOutflowDataList = null;
		ConcurrentHashMap criticalPayementAvgInflowDataList = null;
		ConcurrentHashMap throughputList = null;
		
		
		
		@Override
		public void run() {
			try {
				
				
				throughput = SwtUtil.executeNamedSelectQuery("throughput_avg", hostId, entityId, ccy,groupId, scenario,  startDate, startDate,  dbLink, roleId);
				availableLiqAvg = SwtUtil.executeNamedSelectQuery("available_liq_avg", hostId, entityId, ccy,groupId, scenario, roleId,  dbLink,  startDate, startDate ,dateFormat, useCcyMuliplier);
				assets = SwtUtil.executeNamedSelectQuery("assets", hostId, entityId, ccy,groupId, scenario, roleId, dbLink,  startDate, startDate ,dateFormat, useCcyMuliplier );
				if("SHA".equals(criticalTransactions)) {
					criticalPayementInflowDetails = SwtUtil.executeNamedSelectQuery("critical_details", hostId, entityId, ccy, startDate,startDate, groupId, scenario, "C", roleId, dbLink  , ccyMuliplierValue, dateFormat +" HH24:MI:SS");
				}
				criticalPayementOutflowDetails = SwtUtil.executeNamedSelectQuery("critical_details", hostId, entityId, ccy, startDate,startDate, groupId, scenario, "D", roleId, dbLink  , ccyMuliplierValue, dateFormat +" HH24:MI:SS");
				criticalPayementOutflowAvgDetails = SwtUtil.executeNamedSelectQuery("critical_details_avg", hostId, entityId, ccy, startDate,startDate, groupId, scenario, "D", roleId, dbLink , ccyMuliplierValue);
				if("SHA".equals(criticalTransactions)) {
					criticalPayementInflowAvgDetails = SwtUtil.executeNamedSelectQuery("critical_details_avg", hostId, entityId, ccy, startDate,startDate, groupId, scenario, "C", roleId, dbLink  , ccyMuliplierValue);
				}

				throughputList.put(ccy, throughput);
				mainDataAvgList.put(ccy, availableLiqAvg);
				assetsList.put(ccy, assets);
				criticalPayementOutlfowDataList.put(ccy, criticalPayementOutflowDetails);
				criticalPayementInflowDataList.put(ccy, criticalPayementInflowDetails);
				criticalPayementAvgOutflowDataList.put(ccy, criticalPayementOutflowAvgDetails);
				criticalPayementAvgInflowDataList.put(ccy, criticalPayementInflowAvgDetails);
				
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}

		public SingleCcyThread(Object hostId, Object entityId,
				Object ccy, Object groupId, Object scenario, Object roleId, Object dbLink, Object startDate,
				Object endDate, Object dateFormat,  Object useCcyMuliplier,  Object ccyMuliplierValue, Object criticalTransactions) {

			this.hostId = hostId;
			this.entityId = entityId;
			this.ccy = ccy;
			this.groupId = groupId;
			this.scenario = scenario;
			this.roleId = roleId;
			this.dbLink = dbLink;
			this.startDate = startDate;
			this.dateFormat = dateFormat;
			this.useCcyMuliplier = useCcyMuliplier;
			this.ccyMuliplierValue = ccyMuliplierValue;
			this.criticalTransactions = criticalTransactions;
		}

		public void setThreadLists(	ConcurrentHashMap mainDataAvgList,
				ConcurrentHashMap assetsList,
				ConcurrentHashMap criticalPayementOutlfowDataList,
				ConcurrentHashMap criticalPayementInflowDataList,
				ConcurrentHashMap criticalPayementAvgOutflowDataList,
				ConcurrentHashMap criticalPayementAvgInflowDataList,
				ConcurrentHashMap throughputList) {
			this.mainDataAvgList = mainDataAvgList;
			this.assetsList = assetsList;
			this.criticalPayementOutlfowDataList = criticalPayementOutlfowDataList;
			this.criticalPayementInflowDataList = criticalPayementInflowDataList;
			this.criticalPayementAvgOutflowDataList = criticalPayementAvgOutflowDataList;
			this.criticalPayementAvgInflowDataList = criticalPayementAvgInflowDataList;
			this.throughputList = throughputList;
		}
		
		
	}
	class SingleCcyThreadDateRange implements Runnable{
		
		private Object hostId = null;
		private Object entityId = null;
		private Object ccy = null;
		private Object groupId = null;
		private Object scenario = null;
		private Object roleId;
		private Object dbLink;
		private Object startDate ;
		private Object endDate;
		private Object dateFormat;
		private Object useCcyMuliplier ;
		private Object ccyMuliplierValue ;
		private Object criticalTransactions;
		
		List mainDataAvgSingle = new LinkedList<HashMap<String,Object>>();
		List availableLiqAvg = new LinkedList<HashMap<String,Object>>();
		List availableLiqDaily = new LinkedList<HashMap<String,Object>>();
		List rankedLiq = new LinkedList<HashMap<String,Object>>();
		List mainDataDaily = null;
		List throughputDaily = null;
		List throughput = null;
		List assets = new LinkedList<HashMap<String,Object>>();
		
		
		ConcurrentHashMap mainDataAvg = null;
		ConcurrentHashMap assetsList = null;
		ConcurrentHashMap mainDataAvgList = null;
		ConcurrentHashMap availableLiqAvgList = null;
		ConcurrentHashMap availableLiqDailyList = null;
		ConcurrentHashMap mainDataDailyList =  null;
		ConcurrentHashMap throughputList = null;
		ConcurrentHashMap throughputDailyList = null;
		ConcurrentHashMap availableLiqRankedList = null;
		ConcurrentHashMap criticalPayementAvgOutflowDataList = null;
		ConcurrentHashMap criticalPayementAvgInflowDataList = null;
		ConcurrentHashMap criticalPayementRankedDetailsList = null;
		ConcurrentHashMap criticalAvgRankedInflowDetailsList = null;
		ConcurrentHashMap criticalAvgRankedOutflowDetailsList = null;
		
		LinkedList<HashMap<String, Object>> criticalPayementOutflowAvgDetails = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalPayementInflowAvgDetails = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalPayementRankedDetails = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalAvgRankedInflowDetails = new LinkedList<HashMap<String,Object>>();
		LinkedList<HashMap<String, Object>> criticalAvgRankedOutflowDetails = new LinkedList<HashMap<String,Object>>();
		
		@Override
		public void run() {
			try {
				throughput = SwtUtil.executeNamedSelectQuery("throughput_avg", hostId, entityId, ccy,groupId, scenario,  startDate, endDate,  dbLink, roleId);
				availableLiqAvg = SwtUtil.executeNamedSelectQuery("available_liq_avg", hostId, entityId, ccy,groupId, scenario, roleId,  dbLink,  startDate, endDate ,dateFormat, useCcyMuliplier);
				mainDataDaily = SwtUtil.executeNamedSelectQuery("maindata_daily", hostId, entityId, ccy,groupId, scenario, roleId,  dbLink, startDate, endDate ,dateFormat, useCcyMuliplier);
				availableLiqDaily = SwtUtil.executeNamedSelectQuery("available_liq_daily", hostId, entityId, ccy,groupId, scenario, roleId,  dbLink, startDate, endDate ,dateFormat, useCcyMuliplier);
				mainDataAvgSingle = SwtUtil.executeNamedSelectQuery("maindata_avg", hostId, entityId, ccy,groupId, scenario, roleId,  dbLink, startDate, endDate ,dateFormat, useCcyMuliplier);
				rankedLiq = SwtUtil.executeNamedSelectQuery("ranked_available_liq", hostId, entityId, ccy,groupId, scenario, roleId,  dbLink, startDate, endDate ,dateFormat, useCcyMuliplier);
				mainDataAvg.put(ccy, mainDataAvgSingle.get(0));
				mainDataAvgList.put(ccy, mainDataAvgSingle);
				throughputDaily = SwtUtil.executeNamedSelectQuery("throughput_hourly_daily", hostId, entityId, ccy,groupId, scenario, startDate, endDate, dbLink, roleId);
				assets = SwtUtil.executeNamedSelectQuery("assets", hostId, entityId, ccy,groupId, scenario, roleId, dbLink,  startDate, endDate ,dateFormat, useCcyMuliplier );
				criticalPayementOutflowAvgDetails = SwtUtil.executeNamedSelectQuery("critical_details_avg", hostId, entityId, ccy, startDate,endDate, groupId, scenario, "D", roleId, dbLink , ccyMuliplierValue);
				if("SHA".equals(criticalTransactions)) {
					criticalPayementInflowAvgDetails = SwtUtil.executeNamedSelectQuery("critical_details_avg", hostId, entityId, ccy, startDate,endDate, groupId, scenario, "C", roleId, dbLink  , ccyMuliplierValue);
				}
				criticalPayementRankedDetails = SwtUtil.executeNamedSelectQuery("critical_avg_ranked", hostId, entityId, ccy, groupId,scenario,roleId, dbLink  , startDate,endDate , ccyMuliplierValue);
				if("SHA".equals(criticalTransactions)) {
					criticalAvgRankedInflowDetails = SwtUtil.executeNamedSelectQuery("critical_monitored_avg_ranked", hostId, entityId, ccy, startDate,endDate, groupId, scenario, "C", roleId, dbLink , ccyMuliplierValue);
				}
				criticalAvgRankedOutflowDetails = SwtUtil.executeNamedSelectQuery("critical_monitored_avg_ranked", hostId, entityId, ccy, startDate,endDate, groupId, scenario, "D", roleId, dbLink , ccyMuliplierValue);
				mainDataDailyList.put(ccy, mainDataDaily);
				throughputList.put(ccy, throughput);
				availableLiqAvgList.put(ccy, availableLiqAvg);
				availableLiqDailyList.put(ccy, availableLiqDaily);
				availableLiqRankedList.put(ccy, rankedLiq);
				throughputDailyList.put(ccy, throughputDaily);
				assetsList.put(ccy, assets);
				criticalPayementAvgOutflowDataList.put(ccy, criticalPayementOutflowAvgDetails);
				criticalPayementAvgInflowDataList.put(ccy, criticalPayementInflowAvgDetails);
				criticalPayementRankedDetailsList.put(ccy, criticalPayementRankedDetails);
				criticalAvgRankedOutflowDetailsList.put(ccy, criticalAvgRankedOutflowDetails);
				criticalAvgRankedInflowDetailsList.put(ccy, criticalAvgRankedInflowDetails);
				
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
		
		public SingleCcyThreadDateRange(Object hostId, Object entityId,
				Object ccy, Object groupId, Object scenario, Object roleId, Object dbLink, Object startDate,
				Object endDate, Object dateFormat, Object useCcyMuliplier, Object ccyMuliplierValue, Object criticalTransactions) {
			
			this.hostId = hostId;
			this.entityId = entityId;
			this.ccy = ccy;
			this.groupId = groupId;
			this.scenario = scenario;
			this.roleId = roleId;
			this.dbLink = dbLink;
			this.startDate = startDate;
			this.endDate = endDate;
			this.dateFormat = dateFormat;
			this.useCcyMuliplier = useCcyMuliplier;
			this.ccyMuliplierValue = ccyMuliplierValue;
			this.criticalTransactions = criticalTransactions;
		}

		public void setThreadLists(ConcurrentHashMap mainDataAvg,ConcurrentHashMap assetsList,
				ConcurrentHashMap mainDataAvgList,
				ConcurrentHashMap availableLiqAvgList,
				ConcurrentHashMap availableLiqDailyList,
				ConcurrentHashMap mainDataDailyList,
				ConcurrentHashMap throughputList,
				ConcurrentHashMap throughputDailyList,
				ConcurrentHashMap availableLiqRankedList,
				ConcurrentHashMap criticalPayementAvgOutflowDataList,
				ConcurrentHashMap criticalPayementAvgInflowDataList,
				ConcurrentHashMap criticalPayementRankedDetailsList,
				ConcurrentHashMap criticalAvgRankedInflowDetailsList,
				ConcurrentHashMap criticalAvgRankedOutflowDetailsList) {
			this.assetsList = assetsList;
			this.mainDataAvg = mainDataAvg;
			this.mainDataAvgList = mainDataAvgList;
			this.availableLiqAvgList = availableLiqAvgList;
			this.availableLiqDailyList = availableLiqDailyList;
			this.mainDataDailyList = mainDataDailyList;
			this.throughputList = throughputList;
			this.throughputDailyList = throughputDailyList;
			this.availableLiqRankedList = availableLiqRankedList;
			this.criticalPayementAvgOutflowDataList = criticalPayementAvgOutflowDataList;
			this.criticalPayementAvgInflowDataList = criticalPayementAvgInflowDataList;
			this.criticalPayementRankedDetailsList = criticalPayementRankedDetailsList;
			this.criticalAvgRankedInflowDetailsList = criticalAvgRankedInflowDetailsList;
			this.criticalAvgRankedOutflowDetailsList = criticalAvgRankedOutflowDetailsList;
		}
		
		
		
	}
}