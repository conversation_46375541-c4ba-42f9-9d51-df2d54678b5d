/*
 * @(#)Reports.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.reports.model;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.maintenance.dao.hibernate.SysParamsDAOHibernate;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;
import org.swallow.util.UserThreadLocalHolder;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class Reports extends BaseObject{
	private final Log log = LogFactory.getLog(Reports.class);

	private Integer  totalMovements;
	private Integer matchQualityA;
	private Integer matchQualityB;
	private Integer matchQualityC;
	private Integer matchQualityD;
	private Integer matchQualityE;
	private Integer matchQualityZ;
	private Double totalAmount;

	private Date fromDate;
	private Date toDate;
	private String fromDateAsString;
	private String toDateAsString;
	private Date logDate_Date;
	private Date logDate_Time;

	private String reportType;

	/* Start : Added code to get the opportuity Cost Report .
	 * Added by Thirumurugan on 16-Aug-08.
	 */

	//Added  for opportunity cost Report
	private String  entityId;
	private String entityName;
	private String threshold;
	//Added for Mantis 2145: Excluded/Unsettled Movements Reports
	private String thresholdType;
	private String thresholdTime;
	private String currencyCode;
	private String ilmGroup;
	private String ilmGrpName;
	private String singleOrRange;
	private String defaultIlmGrp;
	
	private String scenario;
	private String sumInOutFlows;
	private String useCcyMultiplier ;
	//Added for Mantis 2787
	private String criticalTransactions;
	//Added for Mantis 4306: Enhance Basel C Reporting to Include Payment Totals by Payment Type
	private String costumerPayments = "Y";
	private String loroPayments = null;
	private String corporatePayments = null;
	private String otherPayments = null;
	private String branchPayments = null;
	
	private String pdfOrExcelorCSV = "P";
	
	
	
	public String getPdfOrExcelorCSV() {
		return pdfOrExcelorCSV;
	}
	public void setPdfOrExcelorCSV(String pdfOrExcelorCSV) {
		this.pdfOrExcelorCSV = pdfOrExcelorCSV;
	}
	
	public String getLoroPayments() {
		return loroPayments;
	}
	public void setLoroPayments(String loroPayments) {
		this.loroPayments = loroPayments;
	}
	public String getOtherPayments() {
		return otherPayments;
	}
	public void setOtherPayments(String otherPayments) {
		this.otherPayments = otherPayments;
	}
	public String getCorporatePayments() {
		return corporatePayments;
	}
	public void setCorporatePayments(String corporatePayments) {
		this.corporatePayments = corporatePayments;
	}
	public String getBranchPayments() {
		return branchPayments;
	}
	public void setBranchPayments(String branchPayment) {
		this.branchPayments = branchPayment;
	}

	public String getCostumerPayments() {
		return costumerPayments;
	}
	public void setCostumerPayments(String costumerPayment) {
		this.costumerPayments = costumerPayment;
	}

	/* End : Added code to get the opportuity Cost Report .
	 * Added by Thirumurugan on 16-Aug-08.
	 */

	
	private Id id = new Id();


	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	 public static class Id extends BaseObject{
		private String hostId ;
		private String userId;
		private String currencyCode;
		private Date inputDate;
		public Id() {}

		public Id(String hostId, String userId,String currencyCode, Date inputDate) {
			this.hostId = hostId;
			this.userId=userId;
			this.currencyCode=currencyCode;
			this.inputDate =inputDate;
		}



		/**
		 * @return Returns the currencyCode.
		 */
		public String getCurrencyCode() {
			return currencyCode;
		}
		/**
		 * @param currencyCode The currencyCode to set.
		 */
		public void setCurrencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the inputDate.
		 */
		public Date getInputDate() {
			return inputDate;
		}
		/**
		 * @param inputDate The inputDate to set.
		 */
		public void setInputDate(Date inputDate) {
			this.inputDate = inputDate;
		}
		/**
		 * @return Returns the userId.
		 */
		public String getUserId() {
			return userId;
		}
		/**
		 * @param userId The userId to set.
		 */
		public void setUserId(String userId) {
			this.userId = userId;
		}
	 }
	/**
	 * @return Returns the matchQualityD.
	 */
	public Integer getMatchQualityD() {
		return matchQualityD;
	}
	/**
	 * @param matchQualityD The matchQualityD to set.
	 */
	public void setMatchQualityD(Integer matchQualityD) {
		this.matchQualityD = matchQualityD;
	}
	/**
	 * @return Returns the matchQualityE.
	 */
	public Integer getMatchQualityE() {
		return matchQualityE;
	}
	/**
	 * @param matchQualityE The matchQualityE to set.
	 */
	public void setMatchQualityE(Integer matchQualityE) {
		this.matchQualityE = matchQualityE;
	}
	/**
	 * @return Returns the matchQualityZ.
	 */
	public Integer getMatchQualityZ() {
		return matchQualityZ;
	}
	/**
	 * @param matchQualityZ The matchQualityZ to set.
	 */
	public void setMatchQualityZ(Integer matchQualityZ) {
		this.matchQualityZ = matchQualityZ;
	}


	/**
	 * @return Returns the totalAmount.
	 */
	public Double getTotalAmount() {
		return totalAmount;
	}
	/**
	 * @param totalAmount The totalAmount to set.
	 */
	public void setTotalAmount(Double totalAmount) {
		this.totalAmount = totalAmount;
	}
	/**
	 * @return Returns the totalMovements.
	 */
	public Integer getTotalMovements() {
		return totalMovements;
	}
	/**
	 * @param totalMovements The totalMovements to set.
	 */
	public void setTotalMovements(Integer totalMovements) {
		this.totalMovements = totalMovements;
	}

	/**
	 * @return Returns the matchQualityA.
	 */
	public Integer getMatchQualityA() {
		return matchQualityA;
	}
	/**
	 * @param matchQualityA The matchQualityA to set.
	 */
	public void setMatchQualityA(Integer matchQualityA) {
		this.matchQualityA = matchQualityA;
	}
	/**
	 * @return Returns the matchQualityB.
	 */
	public Integer getMatchQualityB() {
		return matchQualityB;
	}
	/**
	 * @param matchQualityB The matchQualityB to set.
	 */
	public void setMatchQualityB(Integer matchQualityB) {
		this.matchQualityB = matchQualityB;
	}
	/**
	 * @return Returns the matchQualityC.
	 */
	public Integer getMatchQualityC() {
		return matchQualityC;
	}
	/**
	 * @param matchQualityC The matchQualityC to set.
	 */
	public void setMatchQualityC(Integer matchQualityC) {
		this.matchQualityC = matchQualityC;
	}



	/**
	 * @return Returns the logDate_Date.
	 */
	public Date getLogDate_Date() {
		return logDate_Date;
	}
	/**
	 * @param logDate_Date The logDate_Date to set.
	 */
	public void setLogDate_Date(Date logDate_Date) {
		this.logDate_Date = logDate_Date;
	}
	/**
	 * @return Returns the logDate_Time.
	 */
	public Date getLogDate_Time() {
		return logDate_Time;
	}
	/**
	 * @param logDate_Time The logDate_Time to set.
	 */
	public void setLogDate_Time(Date logDate_Time) {
		this.logDate_Time = logDate_Time;
	}
	/**
	 * @return Returns the toDate.
	 */
	public Date getToDate() {
		return toDate;
	}
	/**
	 * @param toDate The toDate to set.
	 */
	public void setToDate(Date toDate) {
		this.toDate = toDate;
	}
	/**
	 * @return Returns the toDateAsString.
	 */
	public String getToDateAsString() {
		return toDateAsString;
	}
	/**
	 * @param toDateAsString The toDateAsString to set.
	 */
	public void setToDateAsString(String toDateAsString) {
		String dateFormat = "dd/MM/yyyy";
		this.toDateAsString = toDateAsString;
		try {
			dateFormat = SwtUtil.getCurrentDateFormat(UserThreadLocalHolder.getUserSession());
		}catch(Exception e){
			dateFormat = "dd/MM/yyyy";
		}
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);

		try{
			if(!SwtUtil.isEmptyOrNull(toDateAsString) && !SysParamsDAOHibernate.keywordQuery.containsKey(toDateAsString)) {
				Date date = sdf.parse(toDateAsString);
				setToDate(date);
			}
		}catch(ParseException parExp){
			//parExp.printStackTrace();
		}

	}
	/**
	 * @return Returns the fromDate.
	 */
	public Date getFromDate() {
		return fromDate;
	}
	/**
	 * @param fromDate The fromDate to set.
	 */
	public void setFromDate(Date fromDate) {
		this.fromDate = fromDate;
	}
	/**
	 * @return Returns the fromDateAsString.
	 */
	public String getFromDateAsString() {
		return fromDateAsString;
	}
	/**
	 * @param fromDateAsString The fromDateAsString to set.
	 */

	public void setFromDateAsString(String fromDateAsString) {
		String dateFormat = "dd/MM/yyyy";
		this.fromDateAsString = fromDateAsString;
		
		try {
			dateFormat = SwtUtil.getCurrentDateFormat(UserThreadLocalHolder.getUserSession());
		}catch(Exception e){
			dateFormat = "dd/MM/yyyy";
		}
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
		try{
			
			if(!SwtUtil.isEmptyOrNull(fromDateAsString) && !SysParamsDAOHibernate.keywordQuery.containsKey(fromDateAsString)) {
				Date date = sdf.parse(fromDateAsString);
				setFromDate(date);
			}
		}catch(ParseException parExp){

		}
	}


	/**
	 * @return Returns the reportType.
	 */
	public String getReportType() {
		return reportType;
	}
	/**
	 * @param reportType The reportType to set.
	 */
	public void setReportType(String reportType) {
		this.reportType = reportType;
	}



	/* Start : Added code to get the opportuity Cost Report .
	 * Added by Thirumurugan on 16-Aug-08.
	 */

	//Get the Entity Id
	public String getEntityId() {
		return entityId;
	}
	//Set the Entity Id
	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}
	//Get the Entity name
	public String getEntityName() {
		return entityName;
	}
	//Set the Entity name
	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
	//Get the Threshold
	public String getThreshold() {
		return threshold;
	}
	//Set the Threshold
	public void setThreshold(String threshold) {
		this.threshold = threshold;
	}

	/* End : Added code to get the opportuity Cost Report .
     * Added by Thirumurugan on 16-Aug-08.
	 */

	// START: Added by Med Amine & Mefteh for Mantis 2145: Excluded/Unsettled Movements Reports
	public String getThresholdType() {
		return thresholdType;
	}
	public void setThresholdType(String thresholdType) {
		this.thresholdType = thresholdType;
	}

	public String getThresholdTime() {
		return thresholdTime;
	}
	public void setThresholdTime(String thresholdTime) {
		this.thresholdTime = thresholdTime;
	}
	//END: Added by Med Amine & Mefteh for Mantis 2145: Excluded/Unsettled Movement Reports	
	
	public String getIlmGroup() {
		return ilmGroup;
	}
	public void setIlmGroup(String ilmGroup) {
		this.ilmGroup = ilmGroup;
	}
	public String getIlmGrpName() {
		return ilmGrpName;
	}
	public void setIlmGrpName(String ilmGrpName) {
		this.ilmGrpName = ilmGrpName;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public String getSingleOrRange() {
		return singleOrRange;
	}
	public void setSingleOrRange(String singleOrRange) {
		this.singleOrRange = singleOrRange;
	}
	public String getDefaultIlmGrp() {
		return defaultIlmGrp;
	}
	public void setDefaultIlmGrp(String defaultIlmGrp) {
		this.defaultIlmGrp = defaultIlmGrp;
	}
	public String getScenario() {
		return scenario;
	}
	public void setScenario(String scenario) {
		this.scenario = scenario;
	}
	public String getSumInOutFlows() {
		return sumInOutFlows;
	}
	public void setSumInOutFlows(String sumInOutFlows) {
		this.sumInOutFlows = sumInOutFlows;
	}
	public String getCriticalTransactions() {
		return criticalTransactions;
	}
	public void setCriticalTransactions(String criticalTransactions) {
		this.criticalTransactions = criticalTransactions;
	}
	public String getUseCcyMultiplier() {
		return useCcyMultiplier;
	}
	public void setUseCcyMultiplier(String useCcyMultiplier) {
		this.useCcyMultiplier = useCcyMultiplier;
	}
	
}
