package org.swallow.util.jpa;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.FlushMode;
import org.hibernate.HibernateException;
import org.hibernate.LockMode;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.StaleObjectStateException;
import org.hibernate.grammars.hql.HqlLexer;
import org.hibernate.grammars.hql.HqlParser;
import org.hibernate.internal.SessionFactoryImpl;
import org.hibernate.query.hql.HqlTranslator;
import org.hibernate.query.hql.internal.HqlParseTreeBuilder;
import org.hibernate.query.hql.internal.SemanticQueryBuilder;
import org.hibernate.query.spi.HqlInterpretation;
import org.hibernate.query.spi.QueryEngineOptions;
import org.hibernate.query.sqm.internal.DomainParameterXref;
import org.hibernate.query.sqm.internal.SqmCreationOptionsStandard;
import org.hibernate.query.sqm.tree.SqmStatement;
import org.hibernate.query.sqm.tree.from.SqmRoot;
import org.hibernate.query.sqm.tree.select.SqmSelectStatement;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.swallow.BeanUtil;
import org.swallow.util.JakartaUtil;
import org.swallow.util.Single;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.dao.DataAccessException;
import org.springframework.lang.Nullable;
import org.springframework.orm.hibernate5.HibernateCallback;
import org.springframework.orm.hibernate5.SessionFactoryUtils;
import org.springframework.util.Assert;
import jakarta.persistence.EntityManager;
import jakarta.persistence.OptimisticLockException;
import jakarta.persistence.PersistenceException;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.metamodel.Attribute;
import jakarta.persistence.metamodel.EntityType;
import jakarta.persistence.metamodel.Metamodel;
import jakarta.persistence.metamodel.SingularAttribute;

public class CustomHibernateTemplate<T> /*extends HibernateTemplate*/ implements /*HibernateOperations,*/ InitializingBean{
	
	protected final Log logger = LogFactory.getLog(getClass());

	@Nullable
	private SessionFactory sessionFactory;

	@Nullable
	EntityManager entityManager;
	
	@Nullable
	HqlTranslator hqlTranslator;
	
	/**
	 * Create a new HibernateTemplate instance.
	 * @param sessionFactory the SessionFactory to create Sessions with
	 */
	public CustomHibernateTemplate(SessionFactory sessionFactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
		//super(sessionFactory, entityManager);
	    setSessionFactory(sessionFactory);
		setEntityManager(entityManager);
		afterPropertiesSet();
	}
	

	public List<?> find(String queryString, @Nullable Object... values) throws DataAccessException {
		queryString = adjustHibenateJpaParamsIndexes (queryString);
		if(entityManager == null) {
			entityManager = BeanUtil.getBean(EntityManager.class);
		}

		Query queryObject = entityManager.createQuery(queryString);
		//Query queryObject = sessionFactory.getCurrentSession()/*openStatelessSession()*/.createQuery(queryString);
		//queryObject.setMaxResults(Integer.MAX_VALUE);
		if (values != null) {
			for (int i = 0; i < values.length; i++) {
				queryObject.setParameter(i+1, values[i]);
			}
		}
		return nonNull(queryObject.getResultList());
		//return (List<?>) nonNull(queryObject.getResultStream().collect(Collectors.toList()));
	}
	
    /**
     * Find by primary key.
     * Search for an entity of the specified class and primary key.
     * If the entity instance is contained in the persistence context,
     * it is returned from there.
     * @param entityClass  entity class
     * @param primaryKey  primary key
     * @return the found entity instance or null if the entity does
     *         not exist
     * @throws IllegalArgumentException if the first argument does
     *         not denote an entity type or the second argument is 
     *         is not a valid type for that entity's primary key or
     *         is null
     */
    public <T> T find(Class<T> entityClass, Object primaryKey) {
        if(entityManager == null) {
            entityManager = BeanUtil.getBean(EntityManager.class);
        }
        // return entityManager.find(entityClass, primaryKey);
		if(sessionFactory == null) {
			sessionFactory = BeanUtil.getBean(SessionFactory.class);
		}
		return sessionFactory.getCurrentSession().find(entityClass, primaryKey);
    }

	public <T> T get(Class<T> entityClass, Object primaryKey) {
        return find(entityClass, primaryKey);
    }

    public Object findOne(String queryString, @Nullable Object... values) throws DataAccessException {
        return uniqueResult(queryString, values);
    }
    
	/**
	 * Prepare the given Query object, applying cache settings and/or
	 * a transaction timeout.
	 * @param queryObject the Query object to prepare
	 * @see #setCacheQueries
	 * @see #setQueryCacheRegion
	 */
	/*
	protected void prepareQuery(Query<?> queryObject) {
		if (isCacheQueries()) {
			queryObject.setCacheable(true);
			if (getQueryCacheRegion() != null) {
				queryObject.setCacheRegion(getQueryCacheRegion());
			}
		}
		if (getFetchSize() > 0) {
			queryObject.setFetchSize(getFetchSize());
		}
		if (getMaxResults() > 0) {
			queryObject.setMaxResults(getMaxResults());
		}

		ResourceHolderSupport sessionHolder =
				(ResourceHolderSupport) TransactionSynchronizationManager.getResource(obtainSessionFactory());
		if (sessionHolder != null && sessionHolder.hasTimeout()) {
			queryObject.setTimeout(sessionHolder.getTimeToLiveInSeconds());
		}
	}
	*/
	// uniqueResult
	
	
	private String adjustHibenateJpaParamsIndexes(String queryString) {
	    if(hqlTranslator == null) {
            hqlTranslator = BeanUtil.getBean(HqlTranslator.class);
        }
	    
		if(queryString.indexOf("?0") >= 0) {
			Class<?> expectedResultType = null;
			final SqmStatement<?> sqmStatement = hqlTranslator.translate( queryString, expectedResultType );
			final DomainParameterXref domainParameterXref;

			if ( sqmStatement.getSqmParameters().isEmpty() ) {
				domainParameterXref = DomainParameterXref.EMPTY;
			}
			else {
				domainParameterXref = DomainParameterXref.from( sqmStatement );
				for(int i=0; i<domainParameterXref.getQueryParameterCount(); i++) {
					queryString = queryString.replaceAll("\\?\\s*"+i, "?#"+(i+1));
				}
				queryString = queryString.replaceAll("\\?\\#([0-9]*)", "?$1");
			}
		}
		else if(queryString.indexOf("?1") >= 0) {
		    // Ignore
		}
		else if(queryString.indexOf("?") >= 0 && queryString.replaceAll("=\s*\\?", "=?").indexOf("=?") >= 0 ) {
		    // Replace old parameters usage
		    queryString = queryString.replaceAll("=\s*\\?", "=?");
		    final Single<Integer> counter = Single.of(0);
		    queryString = Pattern.compile("=\s*\\?")
		            .matcher(queryString)
		            .replaceAll(matche -> {
		                counter.setFirst(counter.getFirst() + 1);
		                return matche.group().replace("=?", "=?"+counter.getFirst()).toUpperCase();
		            });
		}
		else {
            try {
                hqlTranslator.translate(queryString, null);
            } catch (org.hibernate.query.SemanticException e) {
                Set<String> entities = whichEntitiesInHql(queryString);
                for (String entity : entities) {
                    Set attributes = getEntityAttributes(entityManager, entity);
                    for (Object attribute : attributes) {
                        if (attribute instanceof SingularAttribute) {
                            String attributeName = ((SingularAttribute) attribute).getName();
                            queryString = queryString.replaceAll("(?i)" + attributeName, attributeName);
                        }
                    }
                }
            }
		}
		return queryString;
	}

	private Set<String> whichEntitiesInHql(String hql){
	    Set<String> entities = new HashSet<>();
        Metamodel metamodel = entityManager.getMetamodel();

        // Find the entity class by its name (assuming you already have the class)
        for (EntityType<?> type : metamodel.getEntities()) {
            if (hql.contains(type.getName())) {
                entities.add(type.getName());
            }
        }
        return entities;
	}
	
	
	private Set<?> getEntityAttributes(EntityManager entityManager, String entityName) {
        // Get the JPA Metamodel
        Metamodel metamodel = entityManager.getMetamodel();

        // Find the entity class by its name (assuming you already have the class)
        EntityType<?> entityType = null;
        for (EntityType<?> type : metamodel.getEntities()) {
            if (type.getName().equals(entityName)) {
                entityType = type;
                break;
            }
        }

        if (entityType == null) {
            throw new IllegalArgumentException("Entity not found: " + entityName);
        }

        // Get and return the attributes of the entity
        return entityType.getAttributes();
    }

	
	private static <T> T nonNull(@Nullable T result) {
		Assert.state(result != null, "No result");
		return result;
	}
	
	public void afterPropertiesSet() {
		if (getEntityManager() == null) {
			throw new IllegalArgumentException("Property 'entityManager' is required");
		}
		if (getSessionFactory() == null) {
			throw new IllegalArgumentException("Property 'sessionFactory' is required");
		}
	}
	
	/**
	 * Set the Hibernate SessionFactory that should be used to create
	 * Hibernate Sessions.
	 */
	public void setSessionFactory(@Nullable SessionFactory sessionFactory) {
		this.sessionFactory = sessionFactory;
	}

	/**
	 * Return the Hibernate SessionFactory that should be used to create
	 * Hibernate Sessions.
	 */
	@Nullable
	public SessionFactory getSessionFactory() {
		return this.sessionFactory;
	}
	
	
	public EntityManager getEntityManager() {
		return entityManager;
	}


	public void setEntityManager(@Nullable EntityManager entityManager) {
		this.entityManager = entityManager;
	}


	public HqlTranslator getHqlTranslator() {
		return hqlTranslator;
	}


	public void setHqlTranslator(@Nullable HqlTranslator hqlTranslator) {
		this.hqlTranslator = hqlTranslator;
	}


	public Object save(Object entity) throws DataAccessException {
		/*return nonNull(executeWithNativeSession(session -> {
			checkWriteOperationAllowed(session);
			return session.save(entity);
		}));*/
		if(entityManager == null) {
			entityManager = BeanUtil.getBean(EntityManager.class);
		}
		//entityManager.persist(entity); // ????? persist Vs merge
		if(sessionFactory == null) {
		    sessionFactory = BeanUtil.getBean(SessionFactory.class);
		}

		// Try to find the object before saving
		boolean found = false;
		try {
		    // String queryString = "SELECT e FROM " + entity.getClass().getSimpleName() + " e WHERE e=?1";
		    /*
		    TypedQuery<?> query = entityManager.createQuery(queryString, entity.getClass());
		    query.setParameter(1, entity);
		    found = query.getResultList().size() > 0;
		    */
		    /*
		    Query query = sessionFactory.getCurrentSession().createQuery(queryString);
            query.setParameter(1, entity);
            found = query.getResultList().size() > 0;
            */
		    //sessionFactory.openStatelessSession(); voir commonutil aussi
            sessionFactory.getCurrentSession().refresh(entity);
            found = true;
        } catch (Exception e) {
            // e.printStackTrace();
            found = false;
        }
		
        if(!found) {
            try {
                sessionFactory.getCurrentSession().save(entity);
            } catch (HibernateException e) {
                sessionFactory.getCurrentSession().saveOrUpdate(entity);
            }
        }
        else {
            sessionFactory.getCurrentSession().saveOrUpdate(entity);
        }
      
		return entity;
	}
	
	/**
	 * Error: jakarta.persistence.OptimisticLockException: 
	 *   Row was updated or deleted by another transaction (or unsaved-value mapping was incorrect)
	 * 
	 * Can be solved using : session.save() an not session.saveOrUpdate()
	 * 
	 * @param entity
	 * @return
	 * @throws DataAccessException
	 */
   public Object saveOnly(Object entity) throws DataAccessException {
        if(sessionFactory == null) {
            sessionFactory = BeanUtil.getBean(SessionFactory.class);
        }
        sessionFactory.getCurrentSession().save(entity);
        return entity;
    }

	public void update(Object entity) throws DataAccessException {
		/*update(entity, null);*/
		if(entityManager == null) {
			entityManager = BeanUtil.getBean(EntityManager.class);
		}
		//entityManager.merge(entity);
        if (sessionFactory == null) {
            sessionFactory = BeanUtil.getBean(SessionFactory.class);
        }
		sessionFactory.getCurrentSession().update(entity);
	}
	
	@Deprecated
	public void updateNonPersistent(Object entity, Object primaryKey) throws DataAccessException {
        /*update(entity, null);*/
        if(entityManager == null) {
            entityManager = BeanUtil.getBean(EntityManager.class);
        }
        Object persistent = find(entity.getClass(), primaryKey);
        JakartaUtil.mergeObjects(persistent, entity);
        System.err.println("!!!!!!!!!!!!!!!!!!! Method updateNonPersistent() WILL BE REMOVED SOONER, Please use Update or SaveOrUpdate !!!!!!!!!!!!!!!");
        entityManager.merge(entity);
    }

	public void saveOrUpdate(Object entity) throws DataAccessException {
		/*executeWithNativeSession(session -> {
			checkWriteOperationAllowed(session);
			session.saveOrUpdate(entity);
			return null;
		});*/
		if(entityManager == null) {
			entityManager = BeanUtil.getBean(EntityManager.class);
		}
		//entityManager.merge(entity);
        if (sessionFactory == null) {
            sessionFactory = BeanUtil.getBean(SessionFactory.class);
        }
		sessionFactory.getCurrentSession().saveOrUpdate(entity);
	}

	public void flush() throws DataAccessException {
		/*executeWithNativeSession(session -> {
			session.flush();
			return null;
		});*/
		if(entityManager == null) {
			entityManager = BeanUtil.getBean(EntityManager.class);
		}
		//entityManager.flush();
        if (sessionFactory == null) {
            sessionFactory = BeanUtil.getBean(SessionFactory.class);
        }
		sessionFactory.getCurrentSession().flush();
	}

	   public Object delete(Object entity) throws DataAccessException {
	        /*return nonNull(executeWithNativeSession(session -> {
	            checkWriteOperationAllowed(session);
	            return session.save(entity);
	        }));*/
	        if(entityManager == null) {
	            entityManager = BeanUtil.getBean(EntityManager.class);
	        }
	        entityManager.remove(entity);
	        return entity;
	    }
	   
	public void deleteAll(Collection<?> entities) throws DataAccessException {
		/*
		executeWithNativeSession(session -> {
			checkWriteOperationAllowed(session);
			for (Object entity : entities) {
				session.delete(entity);
			}
			return null;
		});
		*/
		if(entityManager == null) {
			entityManager = BeanUtil.getBean(EntityManager.class);
		}
		for (Object entity : entities) {
			entityManager.remove(entity);
		}
	}
	
	public void clear() throws DataAccessException {
		/*
		executeWithNativeSession(session -> {
			session.clear();
			return null;
		});
		*/
		if(entityManager == null) {
			entityManager = BeanUtil.getBean(EntityManager.class);
		}
		entityManager.clear();
	}
	
    @SuppressWarnings({"unchecked", "deprecation"})
    public <T> List<T> loadAll(Class<T> entityClass) {
        /*
        return nonNull(executeWithNativeSession((HibernateCallback<List<T>>) session -> {
            Criteria criteria = session.createCriteria(entityClass);
            criteria.setResultTransformer(Criteria.DISTINCT_ROOT_ENTITY);
            prepareCriteria(criteria);
            return criteria.list();
        }));
        */
        if(entityManager == null) {
            entityManager = BeanUtil.getBean(EntityManager.class);
        }
        
        String queryString = "SELECT e FROM " + entityClass.getSimpleName() + " e";
        TypedQuery<T> query = entityManager.createQuery(queryString, entityClass);
        //return query.getResultList();
        return query.getResultStream().collect(Collectors.toList());
    }
    
    
    
	public List<?> findByNamedParam(String queryString, String paramName, Object value)
			throws DataAccessException {

		return findByNamedParam(queryString, new String[] {paramName}, new Object[] {value});
	}


	public List<?> findByNamedParam(String queryString, String[] paramNames, Object[] values)
			throws DataAccessException {

		if (paramNames.length != values.length) {
			throw new IllegalArgumentException("Length of paramNames array must match length of values array");
		}
		/*
		return nonNull(executeWithNativeSession((HibernateCallback<List<?>>) session -> {
			Query<?> queryObject = session.createQuery(queryString);
			prepareQuery(queryObject);
			for (int i = 0; i < values.length; i++) {
				applyNamedParameterToQuery(queryObject, paramNames[i], values[i]);
			}
			return queryObject.list();
		}));
		*/
		if(entityManager == null) {
			entityManager = BeanUtil.getBean(EntityManager.class);
		}
		Query queryObject = entityManager.createQuery(queryString);
		for (int i = 0; i < values.length; i++) {
			queryObject.setParameter(paramNames[i], values[i]);
		}
		// return nonNull(queryObject.getResultList());
		return (List<?>) nonNull(queryObject.getResultStream().collect(Collectors.toList()));
	}

	public Object uniqueResult(String queryString, @Nullable Object... values) throws DataAccessException {
		queryString = adjustHibenateJpaParamsIndexes (queryString);
		if(entityManager == null) {
			entityManager = BeanUtil.getBean(EntityManager.class);
		}
		Query queryObject = entityManager.createQuery(queryString);
		queryObject.setMaxResults(1);
		if (values != null) {
			for (int i = 0; i < values.length; i++) {
				queryObject.setParameter(i+1, values[i]);
			}
		}
		return queryObject.getSingleResult();
	}
	
	//@Override
	@Nullable
	public <T> T execute(HibernateCallback<T> action) throws DataAccessException {
		return doExecute(action, false);
	}

	/**
	 * Execute the action specified by the given action object within a
	 * native {@link Session}.
	 * <p>This execute variant overrides the template-wide
	 * {@link #isExposeNativeSession() "exposeNativeSession"} setting.
	 * @param action callback object that specifies the Hibernate action
	 * @return a result object returned by the action, or {@code null}
	 * @throws DataAccessException in case of Hibernate errors
	 */
	@Nullable
	public <T> T executeWithNativeSession(HibernateCallback<T> action) {
		return doExecute(action, true);
	}

	/**
	 * Execute the action specified by the given action object within a Session.
	 * @param action callback object that specifies the Hibernate action
	 * @param enforceNativeSession whether to enforce exposure of the native
	 * Hibernate Session to callback code
	 * @return a result object returned by the action, or {@code null}
	 * @throws DataAccessException in case of Hibernate errors
	 */
	@Nullable
	protected <T> T doExecute(HibernateCallback<T> action, boolean enforceNativeSession) throws DataAccessException {
		Assert.notNull(action, "Callback object must not be null");

		Session session = null;
		boolean isNew = false;
		try {
			session = obtainSessionFactory().getCurrentSession();
		}
		catch (HibernateException ex) {
			logger.debug("Could not retrieve pre-bound Hibernate session", ex);
		}
		if (session == null) {
			session = obtainSessionFactory().openSession();
			session.setHibernateFlushMode(FlushMode.MANUAL);
			isNew = true;
		}

		try {
			return action.doInHibernate(session);
		} catch (HibernateException ex) {
			throw SessionFactoryUtils.convertHibernateAccessException(ex);
		}
		catch (PersistenceException ex) {
			if (ex.getCause() instanceof HibernateException) {
				throw SessionFactoryUtils.convertHibernateAccessException((HibernateException)ex.getCause());
			}
			throw ex;
		}
		catch (RuntimeException ex) {
			// Callback code threw application exception...
			throw ex;
		}
		finally {
			if (isNew) {
				SessionFactoryUtils.closeSession(session);
			}
		}
		/*
		try {
			enableFilters(session);
			Session sessionToExpose =
					(enforceNativeSession || isExposeNativeSession() ? session : createSessionProxy(session));
			return action.doInHibernate(sessionToExpose);
		}
		catch (HibernateException ex) {
			throw SessionFactoryUtils.convertHibernateAccessException(ex);
		}
		catch (PersistenceException ex) {
			if (ex.getCause() instanceof HibernateException hibernateEx) {
				throw SessionFactoryUtils.convertHibernateAccessException(hibernateEx);
			}
			throw ex;
		}
		catch (RuntimeException ex) {
			// Callback code threw application exception...
			throw ex;
		}
		finally {
			if (isNew) {
				SessionFactoryUtils.closeSession(session);
			}
			else {
				disableFilters(session);
			}
		}
		*/
	}
	
	
	/**
	 * Obtain the SessionFactory for actual use.
	 * @return the SessionFactory (never {@code null})
	 * @throws IllegalStateException in case of no SessionFactory set
	 * @since 5.0
	 */
	protected final SessionFactory obtainSessionFactory() {
		SessionFactory sessionFactory = getSessionFactory();
		Assert.state(sessionFactory != null, "No SessionFactory set");
		return sessionFactory;
	}
	

public static void main(String[] args) {
    String queryString = "from AField AF WHERE AF.id.messageId=    ? ORDER BY AF.id.sequenceNo";
    System.err.println(queryString.indexOf("? ") >= 0 && queryString.replaceAll("=\s*\\?", "=?").indexOf("=?") >= 0);
    
    queryString = queryString.replaceAll("=\s*\\?", "=?");

    final Single<Integer> i = Single.of(0);
    queryString = Pattern.compile("=\s*\\?")
            .matcher(queryString)
            .replaceAll(matche -> {
                i.setFirst(i.getFirst() + 1);
                return matche.group().replace("=?", "=?"+i.getFirst()).toUpperCase();
            });
    System.err.println(queryString);
    
    
    
    
    
    
    
    
}
}
