package org.swallow.util;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Properties;
import java.util.StringTokenizer;

import org.hibernate.internal.util.StringHelper;
import org.hibernate.internal.util.collections.ArrayHelper;

import java.util.Map.Entry;

public final class PropertiesHelper {
	private static final String PLACEHOLDER_START = "${";

	public static String getString(String propertyName, Properties properties, String defaultValue) {
		String value = extractPropertyValue(propertyName, properties);
		return value == null ? defaultValue : value;
	}

	public static String extractPropertyValue(String propertyName, Properties properties) {
		String value = properties.getProperty(propertyName);
		if (value == null) {
			return null;
		} else {
			value = value.trim();
			return StringHelper.isEmpty(value) ? null : value;
		}
	}

	public static boolean getBoolean(String propertyName, Properties properties) {
		return getBoolean(propertyName, properties, false);
	}

	public static boolean getBoolean(String propertyName, Properties properties, boolean defaultValue) {
		String value = extractPropertyValue(propertyName, properties);
		return value == null ? defaultValue : Boolean.valueOf(value);
	}

	public static int getInt(String propertyName, Properties properties, int defaultValue) {
		String value = extractPropertyValue(propertyName, properties);
		return value == null ? defaultValue : Integer.parseInt(value);
	}

	public static Integer getInteger(String propertyName, Properties properties) {
		String value = extractPropertyValue(propertyName, properties);
		return value == null ? null : Integer.valueOf(value);
	}

	public static Map toMap(String propertyName, String delim, Properties properties) {
		Map map = new HashMap();
		String value = extractPropertyValue(propertyName, properties);
		if (value != null) {
			StringTokenizer tokens = new StringTokenizer(value, delim);

			while (tokens.hasMoreTokens()) {
				map.put(tokens.nextToken(), tokens.hasMoreElements() ? tokens.nextToken() : "");
			}
		}

		return map;
	}

	public static String[] toStringArray(String propertyName, String delim, Properties properties) {
		return toStringArray(extractPropertyValue(propertyName, properties), delim);
	}

	public static String[] toStringArray(String stringForm, String delim) {
		return stringForm != null ? StringHelper.split(delim, stringForm) : ArrayHelper.EMPTY_STRING_ARRAY;
	}

	public static Properties maskOut(Properties props, String key) {
		Properties clone = (Properties) props.clone();
		if (clone.get(key) != null) {
			clone.setProperty(key, "****");
		}

		return clone;
	}

	public static void resolvePlaceHolders(Properties properties) {
		Iterator itr = properties.entrySet().iterator();

		while (itr.hasNext()) {
			Entry entry = (Entry) itr.next();
			Object value = entry.getValue();
			if (value != null && String.class.isInstance(value)) {
				String resolved = resolvePlaceHolder((String) value);
				if (!value.equals(resolved)) {
					if (resolved == null) {
						itr.remove();
					} else {
						entry.setValue(resolved);
					}
				}
			}
		}

	}

	public static String resolvePlaceHolder(String property) {
		if (property.indexOf("${") < 0) {
			return property;
		} else {
			StringBuffer buff = new StringBuffer();
			char[] chars = property.toCharArray();

			for (int pos = 0; pos < chars.length; ++pos) {
				if (chars[pos] == '$' && chars[pos + 1] == '{') {
					String systemPropertyName = "";

					int x;
					for (x = pos + 2; x < chars.length && chars[x] != '}'; ++x) {
						systemPropertyName = systemPropertyName + chars[x];
						if (x == chars.length - 1) {
							throw new IllegalArgumentException("unmatched placeholder start [" + property + "]");
						}
					}

					String systemProperty = extractFromSystem(systemPropertyName);
					buff.append(systemProperty == null ? "" : systemProperty);
					pos = x + 1;
					if (pos >= chars.length) {
						break;
					}
				}

				buff.append(chars[pos]);
			}

			String rtn = buff.toString();
			return StringHelper.isEmpty(rtn) ? null : rtn;
		}
	}

	private static String extractFromSystem(String systemPropertyName) {
		try {
			return System.getProperty(systemPropertyName);
		} catch (Throwable var2) {
			return null;
		}
	}
}
