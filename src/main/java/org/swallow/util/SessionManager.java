/*
 * Created on Jan 7, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.util;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpSession;
import jakarta.servlet.http.HttpSessionBindingEvent;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.cluster.ClusterListener;
import org.swallow.cluster.RegisterZookeeper;
import org.swallow.cluster.cmd.ClusterCommandDispatcher;
import org.swallow.cluster.cmd.CommandModel;
import org.swallow.cluster.cmd.CommandNames;
import org.swallow.control.model.UserStatus;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.service.ForecastMonitorTemplateManager;
import org.swallow.security.jwt.TokensProvider;
import org.swallow.service.LogonManager;
import org.swallow.work.service.MovementLockManager;

/**
 * SessionManager.java<br>
 *
 * This class has methods that are used in managing the session where it:
 * <ul>
 * <li>Registers/Unregisters the session</li>
 * <li>Updates the User status in the database</li>
 * <li>Gets the logged users' list</li>
 * <li>Gets the user session</li>
 * <li>Kills the session</li>
 * <li>Gets the logged user status, and so on</li>
 * </ul>
 *
 * <AUTHOR>
 *
 */
public class SessionManager {

	private static SessionManager _instance;

	private final Log log = LogFactory.getLog(SessionManager.class);

	private ConcurrentHashMap<String, Object> _sessionMap;


	// To hold the sleep time for thread
	public static long SESSIONS_LOOKUP = 5000;
	private static int numberOfLoops = 0;
	private static int maxNumberOfLoops = 7200;
	// To hold the max interval time for kill inactive session (value by seconds)
	public static Long MAX_DIFFERENCE_TIME = null;
	public static String maxDifferenceTimeAsString = null;

	private SessionManager() {
		_sessionMap = new ConcurrentHashMap<String, Object>();
		maxDifferenceTimeAsString = PropertiesFileLoader.getInstance()
				.getPropertiesValue(SwtConstants.SESSION_VALIDATION_TIMEOUT_PERIOD);
		if(!SwtUtil.isEmptyOrNull(maxDifferenceTimeAsString)) {
			MAX_DIFFERENCE_TIME = Long.parseLong(maxDifferenceTimeAsString);
		}
		if(MAX_DIFFERENCE_TIME != null) {
			// Use anonymous thread class to kill orphan sessions
			(new Thread() {
				@SuppressWarnings("unchecked")
				public void run() {
					while (true) {
						try {
							log.debug("Anonymous Thread Started...");
							numberOfLoops++;
							if(numberOfLoops>=maxNumberOfLoops) {
								TokensProvider.getInstance().clearInvalidTokenList();
								numberOfLoops = 0;
							}
							// Variable to set the current date
							Date currentDate = new Date();
							// Variable String to set the session ID
							String sessionId = null;
							// Iterate all session
							Iterator iterSession = _sessionMap.values().iterator();
							// loop on all existing sessions and invalidate each
							// session not being accessed for xx time
							HttpSession session;
							while(iterSession.hasNext()){
								sessionId = null;
								session = null;
								boolean isValidSession = true;

								try {
									// Get an iterated session
									session = (HttpSession) iterSession.next();
									try {
										long sd = session.getCreationTime();
									} catch (IllegalStateException ise) {
										// the session is invalid
										isValidSession = false;
									}
									if (session != null && isValidSession) {
										try {
											// Check if the last session validation accessed is not null
											if (session
													.getAttribute(SwtConstants.LAST_SESSION_VALIDATION_ACCESSED) != null) {
												// Get the last request accessed date/time from session.
												Date lastSessionValidationAccessedTime = (Date) session
														.getAttribute(SwtConstants.LAST_SESSION_VALIDATION_ACCESSED);
												// Calculate the difference (seconds) between currentTime and lastSessionValidationAccessedTime
												long diffrence = (currentDate.getTime() - lastSessionValidationAccessedTime
														.getTime()) / 1000;
												// If user idle time is greater than
												if (diffrence >= MAX_DIFFERENCE_TIME) {
													// Kill all opened DB sessions
													if(SwtUtil.getCurrentUserId(session) != null) {
														killSession(SwtUtil.getCurrentHostId(), SwtUtil.getCurrentUserId(session), "SYSTEM");
														log.warn("[THIS IS NOT A REAL ERROR]:Session " + sessionId + " has been automatically killed...");
														continue;
													}
													else {
														log.info(this.getClass().getName()+" automatically killed user for LAST SESSION VALIDATION ");
														sessionId = session.getId();
														_sessionMap.remove(sessionId);
														addInvalidToken(session);
														session.setAttribute("KilledBy", "SYSTEM");
														session.invalidate();
														log.warn("[THIS IS NOT A REAL ERROR]:Session " + sessionId + " has been automatically killed...");
														continue;
													}



												}
											}else {
												if(session != null) {
													long diffrence = (currentDate.getTime() - session.getCreationTime()) / 1000;
													// If user idle time is greater than
													if (diffrence >= MAX_DIFFERENCE_TIME) {
														if(SwtUtil.getCurrentUserId(session) != null) {
															killSession(SwtUtil.getCurrentHostId(), SwtUtil.getCurrentUserId(session), "SYSTEM");
															log.warn("[THIS IS NOT A REAL ERROR]:Session " + sessionId + " has been automatically killed...");
															continue;
														}
														else {
															log.warn("[THIS IS NOT A REAL ERROR]:"+ this.getClass().getName()+" automatically killed user for LAST SESSION VALIDATION ");
															sessionId = session.getId();
															_sessionMap.remove(sessionId);
															addInvalidToken(session);
															session.setAttribute("KilledBy", "SYSTEM");
															session.invalidate();
															continue;
														}

													}
												}
											}

											if(SwtUtil.getSessionTimeOutPeriod() > 0)  {
												// checking for idle sessions
												long sessionTimeOutPeriod = SwtUtil.getSessionTimeOutPeriod()*60*1000;
												isValidSession = true;
												try {
													long sd = session.getCreationTime();
												} catch (IllegalStateException ise) {
													// the session is invalid
													isValidSession = false;
												}

												log.debug("sessionTimeOutPeriod - " + sessionTimeOutPeriod);
												if(isValidSession && session.getAttribute(SwtConstants.LAST_REQUEST_ACCESSED)  != null) {
													Object lastRequestAccessed = session.getAttribute(SwtConstants.LAST_REQUEST_ACCESSED);

													log.debug("lastRequestAccessed - " + lastRequestAccessed);
													if(lastRequestAccessed != null){
														Date lastRequestAccessedTime = (Date)lastRequestAccessed;
														log.debug("lastRequestAccessedTime - " + lastRequestAccessedTime);

														long diffrence = currentDate.getTime() -  lastRequestAccessedTime.getTime();
														log.debug(currentDate + " -  "+lastRequestAccessedTime+ " = diffrence - " + diffrence);
														if(diffrence > sessionTimeOutPeriod){
															log.warn("[THIS IS NOT A REAL ERROR]:"+this.getClass().getName()+" automatically killed user for SESSION TIME OUT INACTIVITY ");
															session.setAttribute(SwtConstants.SESSION_TIME_OUT_INACTIVITY,"Y");
															SwtUtil.addInactiveSession(session.getId());
															log.debug("Session " + session.getId() + " has been automatically killed...");
															killSession(SwtUtil.getCurrentHostId(), SwtUtil.getCurrentUserId(session), "SYSTEM");
															continue;
														}
													}
												} else {
													if(!isValidSession) {
														if(!SwtUtil.isEmptyOrNull(sessionId) && _sessionMap != null) {
															log.warn("[THIS IS NOT A REAL ERROR]:"+this.getClass().getName()+" automatically killed user for invalidation ");
															_sessionMap.remove(sessionId);
															addInvalidToken(session);
															continue;
														}else {
															log.warn("[THIS IS NOT A REAL ERROR]:"+this.getClass().getName()+" automatically killed user for invalidation ");
															iterSession.remove();
															addInvalidToken(session);
															continue;
														}
													}
												}
											}
										}catch (Exception exp) {
											log.error(this.getClass().getName()	+ " - Error while loop list of sessions:", exp);
											if(session != null) {
												sessionId = session.getId();
											}

											if(!SwtUtil.isEmptyOrNull(sessionId) && _sessionMap != null) {
												log.warn("[THIS IS NOT A REAL ERROR]:"+this.getClass().getName()+" automatically killed user for invalidation ");
												_sessionMap.remove(sessionId);
												addInvalidToken(session);
												session.invalidate();
												continue;
											}else {
												log.warn("[THIS IS NOT A REAL ERROR]:"+this.getClass().getName()+" automatically killed user for invalidation ");
												iterSession.remove();
												addInvalidToken(session);
												continue;
											}
										}
									}else {
										if(session != null) {
											sessionId = session.getId();
										}

										if(!SwtUtil.isEmptyOrNull(sessionId) && _sessionMap != null) {
											log.warn("[THIS IS NOT A REAL ERROR]:"+this.getClass().getName()+" automatically killed user for invalidation ");
											_sessionMap.remove(sessionId);
											addInvalidToken(session);
											continue;
										}else {
											log.warn("[THIS IS NOT A REAL ERROR]:"+this.getClass().getName()+" automatically killed user for invalidation ");
											iterSession.remove();
											addInvalidToken(session);
											continue;
										}
									}
								} catch (ConcurrentModificationException exp) {
									log.error(this.getClass().getName()	+ " - Session ID: " + sessionId + " Killed and deleted from list of sessions.");
									break;
								}
								catch (Exception exp) {
									log.error(this.getClass().getName()	+ " - Error while loop list of sessions:", exp);
									if(session != null) {
										sessionId = session.getId();
									}

									if(!SwtUtil.isEmptyOrNull(sessionId) && _sessionMap != null) {
										log.warn("[THIS IS NOT A REAL ERROR]:"+this.getClass().getName()+" automatically killed user for invalidation ");
										_sessionMap.remove(sessionId);
										addInvalidToken(session);
										continue;
									}else {
										log.warn("[THIS IS NOT A REAL ERROR]:"+this.getClass().getName()+" automatically killed user for invalidation ");
										iterSession.remove();
										addInvalidToken(session);
										continue;
									}
								}
							}
							// Sleep the Thread
							Thread.sleep(SESSIONS_LOOKUP);
							log.debug("Anonymous Thread End...");
						} catch (Exception ex) {
							log.error(this.getClass().getName()	+ " - Error while start the Anonymous Thread:",	ex);
						}
					}
				}
			}).start();
		}
	}

	public static SessionManager getInstance() {
		if (_instance == null) {
			_instance = new SessionManager();
		}

		return _instance;
	}


	public synchronized boolean registerSession(HttpSessionBindingEvent event) {
		log.debug("entering 'registerSession' function");

		String sessionId = event.getSession().getId();
		log.debug("sessionId - " + sessionId);
		_sessionMap.put(sessionId, event.getSession());
		
		/*
		try {
			System.err.println( " ==================> " + 
				ClusterCommandDispatcher.getInstance().send(CommandNames.NEW_SESSION, command ->{
					System.err.println("INSIDE SESSION MANAGER: GOT THE RESULT FROM ::::"+command.toString());
				} or null for synchrounous !,  sessionId, new Date())
			);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		*/

		// System.err.println("THE LIST LOCALLY:"+getLoggedUserStatusList());

		// System.err.println("THE LIST FROM CLUSTER:"+getLoggedUserStatusListFromCluster());




		log.debug("exiting 'registerSession' function");

		return true;
	}
	// Start : Method modified by Vivekanandan A for Mantis 1413 on 20 June 2011
	/**
	 * unregisterSession Method to do things before ungresiter the session
	 *
	 * @param session
	 * @return
	 */
	Object unregisterSessionLock = new Object();
	public synchronized boolean unregisterSession(HttpSession session) {
		synchronized (unregisterSessionLock) {
		// local variable declaration
		boolean retValue = false;

		try {
			log.debug(this.getClass().getName()+"-['unregisterSession']-Entry");

			log.debug("Session to be destroyed , Id - " + session.getId());
			addInvalidToken(session);
			//Condition to chcek _sessionMap is null
			if (_sessionMap.remove(session.getId()) == null) {
				log.debug("User can't be destroyed on this session id - "
						+ session.getId());
			} else {
				retValue = true;

				/*
				 * Method invoked to delete a movement being locked while
				 * session object gets un bind from the application
				 */
				deleteMovementLock(session);

				// method to delete template lock
				deleteTemplateLock(session);
				// update userstatus in user table
				updateUserStatusinDB(session);

				Enumeration attributeNames = session.getAttributeNames();
				while (attributeNames.hasMoreElements())
				{
					String key = (String)attributeNames.nextElement();
					session.removeAttribute(key);
				}

				log.debug("User is destroyed on this session id - "
						+ session.getId());
			}


		} catch (Exception exp) {
			log.debug(this.getClass().getName() + " [ unregisterSession() ] "
					+ exp);
			log.error(this.getClass().getName() + " [ unregisterSession() ] "
					+ exp);
		}finally{
			log.debug(this.getClass().getName()+"-['unregisterSession']-Exit");
		}
		return retValue;
	}
	}
	// End : Method modified by Vivekanandan A for Mantis 1413 on 20 June 2011

	private void updateUserStatusinDB(HttpSession session) {
		try {
			UserStatus userStatus = (UserStatus) session
					.getAttribute("userstatus");
			String killedBy = null;
			if (userStatus != null) {
				log.debug("userStatus - " + userStatus);
				userStatus.setUpdateDate(SwtUtil.getSystemDatewithTime());
				userStatus.setUpdateUser(UserThreadLocalHolder.getUser());
				LogonManager mgr = (LogonManager) SwtUtil
						.getBean("logonManager");
				userStatus.setLogOutTime(SwtUtil
						.getTestDateFromParams(userStatus.getId().getHostId()));
				// Sets the flag session time out due to inactivity in
				// userStatus
				if(session.getAttribute("KilledBy") != null)
					killedBy =(String) session.getAttribute("KilledBy");
				String sessionTimeOut = (String) session
						.getAttribute(SwtConstants.SESSION_TIME_OUT_INACTIVITY);
				if (sessionTimeOut != null
						&& sessionTimeOut.equalsIgnoreCase("Y")) {
					userStatus.setSessionTimeOutInactivity(true);
					userStatus.setUpdateUser("SYSTEM");
				}else if (killedBy != null) {
					userStatus.setUpdateUser(killedBy);
					userStatus.setKilledBy(killedBy);
				}else {
					if(SwtUtil.isEmptyOrNull(userStatus.getUpdateUser())) {
						killedBy = "SYSTEM";
						userStatus.setUpdateUser(killedBy);
						userStatus.setKilledBy(killedBy);
					}
				}
				mgr.updateUserStatus(userStatus);
			}
		} catch (SwtException e) {
			log.error(e);
			e.printStackTrace();
		}
	}

	/**
	 * The below method is used to delete a movement while being locked by an
	 * user This method will get invoked automatically for the below events when
	 * a session gets expires (while application is mere logged in and inactive ) /
	 * An user is logging out / An user is directly closing the main Window of
	 * the application/ Using the Keyboard on Pressing (ALT + F4) to close the
	 * main window of the application
	 *
	 * @param session
	 *            HttpSession
	 * @return
	 */
	public void deleteMovementLock(HttpSession session) {

		/* Variable declaration */
		String user = "";
		MovementLockManager mlmgr;

		try {
			log.debug(this.getClass().getName() + " [ deleteMovementLock() ] "
					+ "Entry");

			/* Used to get the Logged in User ID by passing the Session Object */
			user = SwtUtil.getCurrentUserId(session);

			/*
			 * Creating a MovementLockManager Instance to invoke a
			 * deleteMovementLock ()
			 */
			mlmgr = (MovementLockManager) SwtUtil
					.getBean("movementLockManager");

			/* Invoking deleteMovementLock() by passing the User ID */
			mlmgr.deleteMovementLock(user);

			log.debug(this.getClass().getName() + " [ deleteMovementLock() ] "
					+ "Exit");

		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName() + " [ deleteMovementLock() ] "
					+ swtexp);
			log.error(this.getClass().getName() + " [ deleteMovementLock() ] "
					+ swtexp);
		} catch (Exception e) {
			log.debug(this.getClass().getName() + " [ deleteMovementLock() ] "
					+ e);
			log.error(this.getClass().getName() + " [ deleteMovementLock() ] "
					+ e);
		}
	}

	// Start : Method Added by Vivekanandan A for Mantis 1413 on 20 June 2011

	/**
	 * The below method is used to delete a unlock a template while being locked
	 * by an user This method will get invoked automatically for the below
	 * events when a session gets expires (while application is mere logged in
	 * and inactive ) / An user is logging out / An user is directly closing the
	 * main Window of the application/ Using the Keyboard on Pressing (ALT + F4)
	 * to close the main window of the application
	 *
	 * @param session
	 *            HttpSession
	 * @return
	 */
	public void deleteTemplateLock(HttpSession session) {

		/* Variable declaration */
		// variable to hold user
		String user = null;
		// ForecastMonitorTemplateManager instance
		ForecastMonitorTemplateManager forecastMonitorTemplateManager = null;
		;

		try {
			log.debug(this.getClass().getName() + " [ deleteTemplateLock() ] "
					+ "Entry");

			/* Used to get the Logged in User ID by passing the Session Object */
			user = SwtUtil.getCurrentUserId(session);

			/*
			 * Creating a forecastMonitorTemplateManager Instance to invoke a
			 * getTemplatesUnLock ()
			 */
			forecastMonitorTemplateManager = (ForecastMonitorTemplateManager) SwtUtil
					.getBean("forecastMonitorTemplateManager");

			/* Invoking getTemplatesUnLock() by passing the User ID */
			forecastMonitorTemplateManager.getTemplatesUnLock(user);

		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " [ deleteTemplateLock() ] "
					+ swtexp);
		} catch (Exception e) {
			log.error(this.getClass().getName() + " [ deleteTemplateLock() ] "
					+ e);
		} finally {
			forecastMonitorTemplateManager = null;
			user = null;
			log.debug(this.getClass().getName() + " [ deleteTemplateLock() ] "
					+ "Exit");
		}
	}

	// End: Method Added by Vivekanandan A for Mantis 1413 on 20 June 2011

	public synchronized Hashtable getLoggedUserList() {
		log.debug("entering 'list' function");
		Hashtable userList = new Hashtable();
		try {
			Iterator itr = _sessionMap.values().iterator();
			HttpSession session = null;
			CommonDataManager cdm = null;
			while (itr.hasNext()) {

				session = (HttpSession) itr.next();
				try {
					if (session != null) {
						cdm = (CommonDataManager) (session
								.getAttribute(SwtConstants.CDM_BEAN));
						if (cdm != null && cdm.getUser() != null && cdm.getUser().getId() != null )  {
							userList.put(cdm.getUser().getId().getUserId(), session
									.getId());
						}
						// userList.add(cdm.getUser().getId().getUserId());
					}
				} catch (Exception e) {
//					log.error("Error on getLoggedUserList, cause: "+e.getMessage(), e);
				}
			}
			log.debug("Logged User List " + userList);
			log.debug("exiting 'list' function");
		} catch (Exception e) {
			log.error("Error on getLoggedUserList, cause: "+e.getMessage(), e);
		}
		return userList;
	}


	public synchronized Hashtable getLoggedUserListFromCluster() {
		log.debug("entering 'list' function");
		Hashtable userList = new Hashtable();
		List<CommandModel> listClusterResults;
		try {
			listClusterResults = ClusterCommandDispatcher.getInstance().send(CommandNames.LOGGED_LIST_USERS, null);
			listClusterResults.stream().map(CommandModel::getResult).forEach(item->{
				if(item instanceof Hashtable) {
					userList.putAll(((Hashtable)item));
				}
			});;
			// Adding local list (duplicates are removed using HashSet)
			userList.putAll(getLoggedUserList());
			log.debug("Logged User List " + userList);
			log.debug("exiting 'list' function");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error("Error on getLoggedUserListFromCluster, cause: "+e.getMessage(), e);
		}
		return userList;
	}

	/**
	 * Method to return the list of challenged users.
	 *
	 * @return {@link Hashtable}
	 */
	public synchronized Hashtable<String, String> getChallengedUserList() {
		log.debug("entering 'list' function");
		Hashtable<String, String> challengedUserList =
				new Hashtable<String, String>();
		try {

			Iterator itr = _sessionMap.values().iterator();
			HttpSession session = null;
			CommonDataManager cdm = null;
			while (itr.hasNext()) {
				session = (HttpSession) itr.next();
				try {
					if (session != null) {
						cdm = (CommonDataManager) (session.getAttribute(SwtConstants.CDM_BEAN_FOR_CHALLENGE));
						if ((cdm != null) && (cdm.getChallengedUser() != null)) {
							challengedUserList.put(
									cdm.getChallengedUser().getId().getUserId(),
									session.getId());
						}
					}
				} catch (Exception e) {
//				log.error("Error on getChallengedUserList, cause: "+e.getMessage(), e);
				}
			}
			log.debug("Challenged User List " + challengedUserList);
			log.debug("exiting 'list' function");
		} catch (Exception e) {
			log.error("Error on getChallengedUserList, cause: "+e.getMessage(), e);
		}
		return challengedUserList;
	}

	/**
	 * Method to return the list of used radius checkers
	 *
	 * @return {@link Hashtable}
	 */
	public synchronized Hashtable<String, SwtRadiusChecker> getRadiusCheckers() {
		log.debug("entering 'list' function");
		Hashtable<String, SwtRadiusChecker> radiusCheckers =
				new Hashtable<String, SwtRadiusChecker>();
		try {
			Iterator itr = _sessionMap.values().iterator();
			HttpSession session = null;
			CommonDataManager cdm = null;
			while (itr.hasNext()) {
				session = (HttpSession) itr.next();
				try {
					if (session != null) {
						cdm = (CommonDataManager) (session.getAttribute(SwtConstants.CDM_BEAN_FOR_CHALLENGE));
						if (cdm != null) {
							radiusCheckers.put(
									cdm.getChallengedUser().getId().getUserId(),
									cdm.getSwtRadiusChecker());
						}
					}
				} catch (Exception e) {
//					log.error("Error on getRadiusCheckers, cause: "+e.getMessage(), e);
				}
			}
		} catch (Exception e) {
			log.error("Error on getRadiusCheckers, cause: "+e.getMessage(), e);
		}
		log.debug("Radius server checker List " + radiusCheckers);
		log.debug("exiting 'list' function");

		return radiusCheckers;
	}

	public  ConcurrentHashMap getSessionMap() {
		return _sessionMap;
	}

	public synchronized ConcurrentHashMap getSessionMapFromCluster() {
		//default to "false"
		ConcurrentHashMap map = new ConcurrentHashMap();
		List<CommandModel> listClusterResults;
		try {
			listClusterResults = ClusterCommandDispatcher.getInstance().send(CommandNames.SESSION_MAP, null);
			listClusterResults.stream().map(CommandModel::getResult).forEach(item->{
				if(item instanceof ConcurrentHashMap) {
					map.putAll((ConcurrentHashMap)item);
				}
			});;

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		// Adding local list (duplicates are removed using HashSet)
		map.putAll(_sessionMap);
		return map;
	}

	public boolean sendMessage(String sessionId, String message) {
		log.debug("entering 'sendMessage' method");
		log.debug("sessionId - " + sessionId + ",message - " + message);
		boolean isMessageSend = false;
		try {
			if (sessionId != null && message != null && message.trim().length() > 0) {
				HttpSession session = (HttpSession) _sessionMap.get(sessionId);
				if (session != null) {
					Collection coll = (Collection) session
							.getAttribute("INTERNAL_MESSAGES");

					log.debug("List of Exiting Internal Message - " + coll);

					if (coll == null)
						coll = new ArrayList();

					coll.add(message);

					session.setAttribute("INTERNAL_MESSAGES", coll);
					isMessageSend = true;
				} else
					log.debug("Unable to find the session for sesisonId - "
							+ sessionId);
			} else {
				log.debug("Session Id or Message is null");
			}
		} catch (Exception e) {
			log.error("Error on sendMessage, cause: "+e.getMessage(), e);
		}
		return isMessageSend;
	}



	public boolean sendMessageFromCluster(String sessionId, String message) {
		log.debug("entering 'sendMessage' method");
		log.debug("sessionId - " + sessionId + ",message - " + message);
		boolean isMessageSend = false;
		List<CommandModel> listClusterResults;
		try {
			if (sessionId != null && message != null && message.trim().length() > 0) {

				listClusterResults = ClusterCommandDispatcher.getInstance().send(CommandNames.SEND_INTERNAL_MESSAGE,null, new Object[]{sessionId, message});
				listClusterResults.stream().map(CommandModel::getResult).forEach(item->{
					if(item instanceof Boolean && Boolean.TRUE.equals(item)) {
						log.debug("message sent to cluster Node");
					}
				});

				sendMessage(sessionId,message);
			} else {
				log.debug("Session Id or Message is null");
			}
		} catch (Exception e) {
			log.error("Error on sendMessage, cause: "+e.getMessage(), e);
		}
		return isMessageSend;
	}



	/**
	 * Get the session reference
	 *
	 * @param arg0
	 *            String hostId.
	 * @param arg1
	 *            String userId.
	 *
	 */
	Object getUserSessionLock = new Object();
	public  HttpSession getUserSession(String hostId, String userId) {
		synchronized (getUserSessionLock) {
		log.debug("entering 'getUserSession' function");
		Iterator itr = _sessionMap.values().iterator();
		HttpSession session = null;
		CommonDataManager cdm = null;
		try {
			while (itr.hasNext()) {
				session = (HttpSession) itr.next();
				try {
					if (session != null) {
						cdm = (CommonDataManager) (session
								.getAttribute(SwtConstants.CDM_BEAN));
						if (cdm != null) {
							if (cdm.getUser().getId().getHostId().equals(hostId)
									&& cdm.getUser().getId().getUserId().equals(userId)) {
								return session;
							}
						}
					}
				} catch (Exception e) {
//					log.error("Error on getUserSession, cause: "+e.getMessage(), e);
				}
			}
		} catch (Exception e) {
			log.error("Error on getUserSession, cause: "+e.getMessage(), e);
		}
		log.debug("Exiting 'getUserSession' function");
		return null;
	}
	}

	Object killSessionLock = new Object();
	public boolean killSession(String hostId, String userId, String killedBy) {
		synchronized (killSessionLock) {
		log.debug("entering 'killSession' function");
		log.debug("hostId - " + hostId + ",userId - " + userId+ "  "+killedBy);
		boolean retValue = false;
		Iterator itr = _sessionMap.values().iterator();
		HttpSession session = null;
		CommonDataManager cdm = null;
		String sessionId = null;

		try {
			while (itr.hasNext()) {
				session = (HttpSession) itr.next();
				try {
					if (session != null) {
						cdm = (CommonDataManager) (session
								.getAttribute(SwtConstants.CDM_BEAN));
						if (cdm != null) {
							if (cdm.getUser().getId().getHostId().equals(hostId)
									&& cdm.getUser().getId().getUserId().equals(userId)) {
								if (session != null) {
									sessionId = session.getId();
									session.setAttribute("KilledBy", killedBy);
									addInvalidToken(session);
									// Ignore if error happens due to illegal state
									try {
										session.invalidate();
									} catch (IllegalStateException e) {
//											if("Y".equals(session.getAttribute(SwtConstants.SESSION_TIME_OUT_INACTIVITY))){
//												log.error("Error on killing session after timeout inactivity, cause: "+e.getMessage(), e);
//											}
									}

								}
								if (sessionId != null) {
									_sessionMap.remove(sessionId);
									log.debug("Removed the session id  " + sessionId
											+ " from session map");
								}else {
									itr.remove();
									log.debug("Removed the session id  from session map");
								}
								retValue = true;
							}
						}
					}
				} catch (Exception e) {
//						log.error("Error on getUserSession, cause: "+e.getMessage(), e);
				}
			}

		} catch (Exception e) {
			log.error("Error on killSession, cause: "+e.getMessage(), e);
		}

		log.debug("exiting 'killSession' function");
		return retValue;
	}
	}

	/**
	 * Kill session user when cluster mode is enabled
	 * @param hostId
	 * @param userId
	 * @param killedBy
	 * @return
	 */
	public synchronized boolean killSessionFromCluster(String hostId, String userId, String killedBy) {
		log.debug("entering 'killSession' function");
		log.debug("hostId - " + hostId + ",userId - " + userId+ "  "+killedBy);
		List<CommandModel> listClusterResults;
		final boolean [] success = new boolean[1];
		try {
			listClusterResults = ClusterCommandDispatcher.getInstance().send(CommandNames.KILL_USER,null, new Object[]{hostId, userId, killedBy});
			listClusterResults.stream().map(CommandModel::getResult).forEach(item->{
				if(item instanceof Boolean && Boolean.TRUE.equals(item)) {
					success[0] = true;
				}
			});
		} catch (Exception e1) {
			log.error("Error on killing session using on cluster mode, cause: "+e1.getMessage(), e1);
		}
		if(success != null && success.length >0 && success[0]) {
			return true;
		}


		boolean retValue = false;

		Iterator itr = _sessionMap.values().iterator();
		HttpSession session = null;
		CommonDataManager cdm = null;
		String sessionId = null;
		try {
			while (itr.hasNext()) {
				session = (HttpSession) itr.next();
				try {
					if (session != null) {
						cdm = (CommonDataManager) (session
								.getAttribute(SwtConstants.CDM_BEAN));
						if (cdm != null) {
							if (cdm.getUser().getId().getHostId().equals(hostId)
									&& cdm.getUser().getId().getUserId().equals(userId)) {
								if (session != null) {
									sessionId = session.getId();
									session.setAttribute("KilledBy", killedBy);
									addInvalidToken(session);
									// Ignore if error happens due to illegal state
									try {
										session.invalidate();
									} catch (IllegalStateException e) {
//										if("Y".equals(session.getAttribute(SwtConstants.SESSION_TIME_OUT_INACTIVITY))){
//											log.error("Error on killing session after timeout inactivity, cause: "+e.getMessage(), e);
//										}
									}

								}
								if (sessionId != null) {
									_sessionMap.remove(sessionId);
									log.debug("Removed the session id  " + sessionId
											+ " from session map");
								}else {
									itr.remove();
									log.debug("Removed the session id  from session map");
								}
								retValue = true;
							}
						}
					}
				} catch (Exception e) {
//					log.error("Error on getUserSession, cause: "+e.getMessage(), e);
				}
			}

		} catch (Exception e) {
			log.error("Error on killSessionFromCluster, cause: "+e.getMessage(), e);
		}
		log.debug("exiting 'killSessionFromCluster' function");
		return retValue;
	}


	/**
	 * Return fresh Logged user status from the cluster instances
	 *
	 * @return
	 */
	public synchronized List<UserStatus> getLoggedUserStatusListFromCluster() {
		final Set<UserStatus> result = new HashSet<UserStatus>();
		try {
			List<CommandModel> listClusterResults = ClusterCommandDispatcher.getInstance().send(CommandNames.LIST_USERS, null);
			listClusterResults.stream().map(CommandModel::getResult).forEach(item->{
				if(item instanceof List) {
					result.addAll((List<UserStatus>)item);
				}
			});;

			// Adding local list (duplicates are removed using HashSet)
			result.addAll(getLoggedUserStatusList());

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			// TODO: Add logs !
		}
		return new ArrayList<UserStatus>(result);
	}

	/**
	 * This method is used to retrieve the status list of logged user from the
	 * session.<br>
	 * This method is a synchronized one which enables a simple strategy for
	 * preventing thread interference and memory consistency errors.<br>
	 *
	 * @return List of UserStatus
	 */
	public synchronized Collection<UserStatus> getLoggedUserStatusList() {
		// Declares the ArrayList for UserStatus
		ArrayList<UserStatus> list;
		// Declares the HttpSession object
		HttpSession session = null;
		// Declares the UserStatus object
		UserStatus userStatus = null;
		// Declares the Iterator object
		Iterator<Object> iterator = null;
		try {
			// Creates a new instance of ArrayList
			list = new ArrayList<UserStatus>();
			iterator = _sessionMap.values().iterator();
			// Executes the loop until there is value
			while (iterator.hasNext()) {
				// Sets the iterated values in session
				session = (HttpSession) iterator.next();
				try {
					if (session != null && session.getId() != null) {
						userStatus = (UserStatus) session
								.getAttribute("userstatus");
						if (userStatus != null)
							list.add(userStatus);
					}
				} catch (Exception e) {
					log.error("Error on getLoggedUserStatusList, cause: "+e.getMessage(), e);
				}
			}
			return list;
		} catch (Exception e) {
			log
					.error("Exception occured in SessionManager - getLoggedUserStatusList(). Cause: "
							+ e.getMessage());
			return null;
		} finally {
			// Closing the session
			try {
				if (session != null) {
					session = null;
				}
			} catch (Exception ignore) {
				log
						.error("Exception occured in SessionManager - getLoggedUserStatusList() while closing the session. Cause: "
								+ ignore.getMessage());
			}

			// Cleaning the UserStatus object
			if (userStatus != null) {
				userStatus = null;
			}
		}
	}
	/**
	 * Add token to an invalid list, if the token is used to login or to do any request the session will be invalid
	 * @param session
	 */
	private void addInvalidToken(HttpSession session) {
		try {
			if(session != null ) {
				CommonDataManager cdm = (CommonDataManager) (session
						.getAttribute(SwtConstants.CDM_BEAN));
				if(cdm != null) {
					TokensProvider.getInstance().addInvalidToken(cdm.getBearerToken());
				}

			}
		}catch(Exception e) {
		}
	}
}