/*
 * Created on Aug 22, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.util;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public interface SwtArchiveDatabase {

	public DataSource addArchiveDataSource(String dbName, String url, String username, String password);
	public void useArchive(String archiveDbName);
	public DataSource removeArchiveDataSource(String archiveDbName);	
}
