/*
 * @(#)CurrencyGroupDAOHibernate.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import jakarta.persistence.TypedQuery;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyGroupDAO;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.Transaction;
import org.hibernate.Session;




/**
 * 
 * This is DAO class for Currency Group screen.
 * 
 */
@Repository("currencyGroupDAO")
@Transactional
public class CurrencyGroupDAOHibernate extends CustomHibernateDaoSupport implements CurrencyGroupDAO {
    private final Log log = LogFactory.getLog(CurrencyGroupDAOHibernate.class);

    /**
     * Constructor with dependency injection
     * @param sessionfactory Hibernate session factory
     * @param entityManager JPA entity manager
     */
    public CurrencyGroupDAOHibernate(@Lazy SessionFactory sessionfactory, 
            @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    /**
     * Retrieves the list of currency groups from the database
     * @param hostId The host identifier
     * @param entityId The entity identifier
     * @return Collection of CurrencyGroup objects
     * @throws SwtException If an error occurs during retrieval
     */
    public Collection<CurrencyGroup> getCurrencyGroupList(String hostId, String entityId) throws SwtException {
        log.debug(this.getClass().getName() + " - [getCurrencyGroupList] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<CurrencyGroup> query = session.createQuery(
                "FROM CurrencyGroup c WHERE c.id.hostId = :hostId AND c.id.entityId = :entityId",
                CurrencyGroup.class);
            query.setParameter("hostId", hostId);
            query.setParameter("entityId", entityId);
            
            List<CurrencyGroup> result = query.getResultList();
            log.debug(this.getClass().getName() + " - [getCurrencyGroupList] - Exit");
            return result;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getCurrencyGroupList: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrencyGroupList", 
                    CurrencyGroupDAOHibernate.class);
        }
    }

    /**
     * Gets the number of currencies in a currency group
     * @param hostId The host identifier
     * @param entityId The entity identifier
     * @param currencyGroupId The currency group identifier
     * @return Number of currencies in the group
     * @throws SwtException If an error occurs during counting
     */
    public Integer getNoOfCurrencies(String hostId, String entityId, String currencyGroupId) throws SwtException {
        log.debug(this.getClass().getName() + " - [getNoOfCurrencies] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<Long> query = session.createQuery(
                "SELECT COUNT(c) FROM Currency c WHERE c.id.hostId = :hostId " +
                "AND c.id.entityId = :entityId AND c.currencyGroupId = :currencyGroupId " +
                "AND c.id.currencyCode != '*'",
                Long.class);
            
            query.setParameter("hostId", hostId);
            query.setParameter("entityId", entityId);
            query.setParameter("currencyGroupId", currencyGroupId);
            
            Long count = query.getSingleResult();
            log.debug(this.getClass().getName() + " - [getNoOfCurrencies] - Exit");
            return count.intValue();
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getNoOfCurrencies: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "getNoOfCurrencies", 
                    CurrencyGroupDAOHibernate.class);
        }
    }

    /**
     * Saves a new currency group to the database
     * @param currencyGroup The currency group to save
     * @throws SwtException If a duplicate record exists or if an error occurs during save
     */
    @Transactional
    public void saveCurrencyGroupDetails(CurrencyGroup currencyGroup) throws SwtException {
        log.debug(this.getClass().getName() + " - [saveCurrencyGroupDetails] - Entry");
        
        try {
            // Check for existing record
            try (Session checkSession = getHibernateTemplate().getSessionFactory().openSession()) {
                TypedQuery<Long> query = checkSession.createQuery(
                    "SELECT COUNT(c) FROM CurrencyGroup c WHERE c.id.hostId = :hostId " +
                    "AND c.id.entityId = :entityId AND c.id.currencyGroupId = :currencyGroupId",
                    Long.class);
                
                query.setParameter("hostId", currencyGroup.getId().getHostId());
                query.setParameter("entityId", currencyGroup.getId().getEntityId());
                query.setParameter("currencyGroupId", currencyGroup.getId().getCurrencyGroupId());
                
                if (query.getSingleResult() > 0) {
                    throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
                }
            }

            // Save new record
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            try (Session session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession()) {
                Transaction tx = session.beginTransaction();
                try {
                    session.save(currencyGroup);
                    tx.commit();
                } catch (Exception e) {
                    if (tx != null) tx.rollback();
                    throw e;
                }
            }
            
            log.debug(this.getClass().getName() + " - [saveCurrencyGroupDetails] - Exit");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in saveCurrencyGroupDetails: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "saveCurrencyGroupDetails", 
                    CurrencyGroupDAOHibernate.class);
        }
    }

    /**
     * Updates an existing currency group in the database
     * @param currencyGroup The currency group to update
     * @throws SwtException If an error occurs during update
     */
    @Transactional
    public void updateCurrencyGroupDetails(CurrencyGroup currencyGroup) throws SwtException {
        log.debug(this.getClass().getName() + " - [updateCurrencyGroupDetails] - Entry");
        
        try {
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            try (Session session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession()) {
                Transaction tx = session.beginTransaction();
                try {
                    session.update(currencyGroup);
                    tx.commit();
                } catch (Exception e) {
                    if (tx != null) tx.rollback();
                    throw e;
                }
            }
            
            log.debug(this.getClass().getName() + " - [updateCurrencyGroupDetails] - Exit");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in updateCurrencyGroupDetails: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "updateCurrencyGroupDetails", 
                    CurrencyGroupDAOHibernate.class);
        }
    }

    /**
     * Deletes a currency group from the database
     * @param currencyGroup The currency group to delete
     * @throws SwtException If an error occurs during deletion
     */
    @Transactional
    public void deleteCurrencyGroupRecord(CurrencyGroup currencyGroup) throws SwtException {
        log.debug(this.getClass().getName() + " - [deleteCurrencyGroupRecord] - Entry");
        
        try {
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            try (Session session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession()) {
                Transaction tx = session.beginTransaction();
                try {
                    session.delete(currencyGroup);
                    tx.commit();
                } catch (Exception e) {
                    if (tx != null) tx.rollback();
                    throw e;
                }
            }
            
            log.debug(this.getClass().getName() + " - [deleteCurrencyGroupRecord] - Exit");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in deleteCurrencyGroupRecord: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "deleteCurrencyGroupRecord", 
                    CurrencyGroupDAOHibernate.class);
        }
    }

    /**
     * Gets the list of currencies for a specific currency group
     * @param hostId The host identifier
     * @param entityId The entity identifier
     * @param currGrpId The currency group identifier
     * @return Collection of Currency objects
     * @throws SwtException If an error occurs during retrieval
     */
    public Collection<Currency> getCurrencyGroupCurrenciesList(String hostId, String entityId, 
            String currGrpId) throws SwtException {
        log.debug(this.getClass().getName() + " - [getCurrencyGroupCurrenciesList] - Entry");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<Currency> query = session.createQuery(
                "FROM Currency c WHERE c.id.hostId = :hostId AND c.id.entityId = :entityId " +
                "AND c.currencyGroupId = :currGrpId AND c.id.currencyCode != '*'",
                Currency.class);
            
            query.setParameter("hostId", hostId);
            query.setParameter("entityId", entityId);
            query.setParameter("currGrpId", currGrpId);
            
            List<Currency> result = query.getResultList();
            log.debug(this.getClass().getName() + " - [getCurrencyGroupCurrenciesList] - Exit");
            return result;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getCurrencyGroupCurrenciesList: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrencyGroupCurrenciesList", 
                    CurrencyGroupDAOHibernate.class);
        }
    }

    /**
     * Gets the currency group name for a specific entity and currency group ID
     * @param entityId The entity identifier
     * @param currencyGroupId The currency group identifier
     * @return The currency group name
     * @throws SwtException If an error occurs during retrieval
     */
    public String getCurrencyGroupName(String entityId, String currencyGroupId) throws SwtException {
        log.debug(this.getClass().getName() + " - [getCurrencyGroupName] - Entry");
        String result = null;
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            String hostId = SwtUtil.getCurrentHostId();
            
            TypedQuery<String> query = session.createQuery(
                "SELECT c.currencyGroupName FROM CurrencyGroup c " +
                "WHERE c.id.hostId = :hostId AND c.id.entityId = :entityId " +
                "AND c.id.currencyGroupId = :currencyGroupId",
                String.class);
            
            query.setParameter("hostId", hostId);
            query.setParameter("entityId", entityId);
            query.setParameter("currencyGroupId", currencyGroupId);
            try {
                result = query.getSingleResult();
            }catch (Exception e) {
                result   = null;
            }
            log.debug(this.getClass().getName() + " - [getCurrencyGroupName] - Exit");
            return result != null ? result : "";
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - Exception in getCurrencyGroupName: " + e.getMessage(), e);
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrencyGroupName", 
                    CurrencyGroupDAOHibernate.class);
        }
    }
}
