/*
 * @(#) CurrencyAliasDAOHibernate .java 01/10/07
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.dao.hibernate;

import java.util.Collection;
import java.util.List;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CurrencyAliasDAO;
import org.swallow.maintenance.model.CurrencyAlias;
import org.swallow.util.JDBCCloser;
import org.swallow.util.SwtInterceptor;
import org.swallow.util.SwtUtil;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import org.hibernate.Transaction;
import org.hibernate.Session;





/*
 * This is DAO class for Currency alias screen
 * 
 */
@Repository("currencyAliasDAO")
@Transactional
public class CurrencyAliasDAOHibernate extends CustomHibernateDaoSupport implements CurrencyAliasDAO {
    private final Log log = LogFactory.getLog(CurrencyAliasDAOHibernate.class);

    /**
     * Constructor with dependency injection
     * @param sessionfactory Hibernate session factory
     * @param entityManager JPA entity manager
     */
    public CurrencyAliasDAOHibernate(@Lazy SessionFactory sessionfactory, 
            @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
        super(sessionfactory, entityManager);
    }

    /**
     * Retrieves a list of currency aliases with their corresponding currency details
     * @param hostId The host identifier
     * @param entityId The entity identifier
     * @return Collection of currency alias details containing alias, currency code, and currency name
     * @throws SwtException If an error occurs during the retrieval process
     */
    public Collection<Object[]> getCurrencyAliasList(String hostId, String entityId) throws SwtException {
        log.debug("Entering into CurrencyAliasDAOHibernate.getCurrencyAliasList method");
        
        try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
            TypedQuery<Object[]> query = session.createQuery(
                "select ca.id.alias, ca.currencyCode, cm.currencyName " +
                "from CurrencyAlias ca, CurrencyMaster cm " +
                "where ca.id.hostId = :hostId " +
                "and ca.id.entityId = :entityId " +
                "and ca.currencyCode = cm.currencyCode " +
                "order by ca.id.alias",
                Object[].class);
            
            query.setParameter("hostId", hostId);
            query.setParameter("entityId", entityId);
            
            List<Object[]> result = query.getResultList();
            log.debug("Exiting from CurrencyAliasDAOHibernate.getCurrencyAliasList method");
            return result;
        } catch (Exception e) {
            log.error("Exception in getCurrencyAliasList: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "getCurrencyAliasList", 
                    CurrencyAliasDAOHibernate.class);
        }
    }

    /**
     * Saves a new currency alias record to the database
     * Performs duplicate check before saving
     * @param currencyAlias The currency alias object to save
     * @throws SwtException If a duplicate record exists or if an error occurs during the save process
     */
    @Transactional
    public void saveCurrencyAliasDetails(CurrencyAlias currencyAlias) throws SwtException {
        log.debug(this.getClass().getName() + " - [saveCurrencyAliasDetails] - Entering");
        
        try {
            // Check for existing record
            try (Session checkSession = getHibernateTemplate().getSessionFactory().openSession()) {
                TypedQuery<Long> query = checkSession.createQuery(
                    "select count(ca) from CurrencyAlias ca where ca.id.hostId = :hostId " +
                    "and ca.id.entityId = :entityId and ca.id.alias = :alias",
                    Long.class);
                
                query.setParameter("hostId", currencyAlias.getId().getHostId());
                query.setParameter("entityId", currencyAlias.getId().getEntityId());
                query.setParameter("alias", currencyAlias.getId().getAlias());
                
                if (query.getSingleResult() > 0) {
                    throw new SwtException("errors.DataIntegrityViolationExceptioninAdd");
                }
            }

            // Save new record
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            try (Session session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession()) {
                Transaction tx = session.beginTransaction();
                try {
                    session.save(currencyAlias);
                    tx.commit();
                } catch (Exception e) {
                    if (tx != null) tx.rollback();
                    throw e;
                }
            }
            
            log.debug(this.getClass().getName() + " - [saveCurrencyAliasDetails] - Exiting");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [saveCurrencyAliasDetails] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "saveCurrencyAliasDetails", 
                    CurrencyAliasDAOHibernate.class);
        }
    }

    /**
     * Deletes a currency alias record from the database
     * @param currencyAlias The currency alias object to delete
     * @throws SwtException If an error occurs during the deletion process
     */
    @Transactional
    public void deleteCurrencyAliasRecord(CurrencyAlias currencyAlias) throws SwtException {
        log.debug("Entering into CurrencyAliasDAOHibernate.deleteCurrencyAliasRecord method");
        
        try {
            SwtInterceptor interceptor = (SwtInterceptor) SwtUtil.getBean("SwtInterceptor");
            try (Session session = getHibernateTemplate().getSessionFactory()
                    .withOptions().interceptor(interceptor).openSession()) {
                Transaction tx = session.beginTransaction();
                try {
                    session.delete(currencyAlias);
                    tx.commit();
                } catch (Exception e) {
                    if (tx != null) tx.rollback();
                    throw e;
                }
            }
            
            log.debug("Exiting from CurrencyAliasDAOHibernate.deleteCurrencyAliasRecord method");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " - [deleteCurrencyAliasRecord] - Exception: " + e.getMessage());
            throw SwtErrorHandler.getInstance().handleException(e, "deleteCurrencyAliasRecord", 
                    CurrencyAliasDAOHibernate.class);
        }
    }
}
