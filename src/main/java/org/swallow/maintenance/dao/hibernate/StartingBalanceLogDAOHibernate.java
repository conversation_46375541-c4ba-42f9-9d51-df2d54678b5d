/*
 * @(#)StartingBalanceLogDAOHibernate.java 1.0 04/08/2006
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao.hibernate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.StringTokenizer;

import jakarta.persistence.Query;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.StartingBalanceLogDAO;
import org.swallow.util.*;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * This class is dao layer of the Start Balance Audit log screen and it is used
 * to get the balance log form database
 *
 */
@Repository ("startingBalanceLogDAO")
@Transactional
public class StartingBalanceLogDAOHibernate extends CustomHibernateDaoSupport
		implements StartingBalanceLogDAO {
	public StartingBalanceLogDAOHibernate(@Lazy SessionFactory sessionfactory, @Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
		super(sessionfactory, entityManager);
	}

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory
			.getLog(StartingBalanceLogDAOHibernate.class);

	// Start: Method modified by Vivekanandan A for mantis 1767 13-09-2012

	/**
	 * This method populates the records from table and returns the maximum
	 * number pages
	 *
	 * @param entityId
	 * @param date
	 * @param balanceTypeId
	 * @param currentPage
	 * @param maxPage
	 * @param balLogList
	 * @param filterSortStatus
	 * @param format
	 * @return int
	 * @exception SwtException
	 *
	 */
	public int getBalanceLogDetails(String entityId, String date,
									String balanceTypeId, int currentPage, int maxPage,
									ArrayList balLogList, String filterSortStatus, SystemFormats format)
			throws SwtException {

		/* Method's local variable and class declaration */
		// variable for hostid
		String hostId = null;
		// variable for pageSize
		int pageSize = 0;
		// variable for remainder
		int remainder = 0;
		// variable for totalCount
		int totalCount = 0;
		// variable for balLogHQL
		StringBuffer balLogHQL = null;
		// variable for queryBalLog
		Query queryBalLog = null;
		// variable for countBalLogHQL
		StringBuffer countBalLogHQL = null;
		// variable for querycountBalLog
		Query querycountBalLog = null;
		// variable for SessionFactory
		SessionFactory sessionFactory = null;
		// variable for Session
		Session session = null;
		// variable for balLogCountList
		List balLogCountList = null;
		// filter Date instance
		Date filteredDate = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [getBalanceLogDetails] - " + "Entry");
			// Initiating balLogHQL
			balLogHQL = new StringBuffer();
			// variable for countBalLogHQL
			countBalLogHQL = new StringBuffer();

			// get the Host id from the cacheManager
			hostId = CacheManager.getInstance().getHostId();
			// framing balance log queries to fetch balance log details
			balLogHQL.append("from StartingBalanceLog  s where s.hostId=?1 "
					+ "and s.entityId=?2 "
					+ "and s.balanceTypeId=?3 and s.balanceDate =?4 ");
			// append count to get the number balance log details
			countBalLogHQL.append("select count(*) ");
			// append balance log query to get the number balance log details
			countBalLogHQL.append(balLogHQL);
			// Obtain session factory bean
			sessionFactory = (SessionFactory) SwtUtil.getBean("sessionFactory");
			// Obtain hibernate session from session factory
			session = sessionFactory.openSession();

			// calls formQuery method to get filtered and sort values
			filteredDate = formQuery(countBalLogHQL, filterSortStatus, format);

			// create query to get count for balance log
			querycountBalLog = session.createQuery(countBalLogHQL.toString());
			// set the hostID in querycountBalLog
			querycountBalLog.setParameter(1, hostId);
			// set the entityId in querycountBalLog
			querycountBalLog.setParameter(2, entityId);
			// set the balanceTypeId in querycountBalLog
			querycountBalLog.setParameter(3, balanceTypeId);

			// set the date in querycountBalLog
			querycountBalLog.setParameter(4, SwtUtil.truncateDateTime(SwtUtil.parseDate(date, format.getDateFormatValue())));
			// Condition to check filter values is not null to set filter param
			if (filteredDate != null) {
				// pass the filtered date with time as timestamp
				querycountBalLog.setParameter(5, new java.sql.Date(filteredDate.getTime()));

			}

			// Retrieves and store log values assign balLogCountList
			balLogCountList = querycountBalLog.getResultList();

//			 * Condition to check the list is not null and its size is greater
//			 * than '0'

			if ((balLogCountList != null) && (balLogCountList.size() > 0)
					&& (balLogCountList.get(0) != null)) {
				// Retrieves the integer value for balLogCountList
				totalCount = ((Long) (balLogCountList.get(0))).intValue();
				// Condition to check if no records for given params
				if (totalCount != 0) {

//					 * This Block reads PageSize from the property file and
//					 * calculates the maxPages

					pageSize=SwtUtil.getPageSizeFromProperty(SwtConstants.SYSTEM_LOG_SCREEN_PAGE_SIZE);
					// Condition to check max page is '0'
					// maxPage is initially set to 0 when called for
					// the first time , yet to calculate maxPage
					if (maxPage == 0) {

//						 * Max page is set according to the page size and
//						 * totalcount

						maxPage = totalCount / pageSize;

						// The remainder is set in the totalcount and page size
						remainder = totalCount % pageSize;
						// condition the satisfy increment the maxpage
						if (remainder > 0) {
							maxPage++;
						}

					}

//					 * Function is called to form the correct HQL based on all
//					 * the filter and sorting conditions

					// get the filter and sort parameters
					filteredDate = formQuery(balLogHQL, filterSortStatus,
							format);
					// get the records the filter and sorting
					queryBalLog = session.createQuery(balLogHQL.toString());
					// set the hostId in queryBalLog
					queryBalLog.setParameter(1, hostId);
					// set the entityId in queryBalLog
					queryBalLog.setParameter(2, entityId);
					// set the balanceTypeID in queryBalLog
					queryBalLog.setParameter(3, balanceTypeId);
					// set the date in queryBalLog
					queryBalLog.setParameter(4, SwtUtil.truncateDateTime(SwtUtil.parseDate(date, format.getDateFormatValue())));
					// Condition to check filter values is not null to set
					// filter param
					if (filteredDate != null) {
						// pass the filtered date with time as timestamp
						queryBalLog.setParameter(5,  new java.sql.Date(filteredDate.getTime()));
					}

					// fetches the data for the currentPage
					SwtPager.next((org.hibernate.query.Query) queryBalLog, currentPage, maxPage, pageSize,
							balLogList);

				}

			}

		} catch (HibernateException hibernateException) {

			log
					.error(this.getClass().getName()
							+ " - hibernateException Catched in [getBalanceLogDetails] method : - "
							+ hibernateException.getMessage());

			throw new SwtException(hibernateException.getMessage());
		} catch (Exception ioException) {

			log
					.error(this.getClass().getName()
							+ " - ioException Catched in [getBalanceLogDetails] method : - "
							+ ioException.getMessage());

			throw new SwtException(ioException.getMessage());

		} finally {
			//edited by KaisBS for mantis 1745 : introducing the implementation of the JDBCCloser class
			JDBCCloser.close(session);

			// nullify all variables
			hostId = null;
			balLogHQL = null;
			countBalLogHQL = null;
			sessionFactory = null;
			queryBalLog = null;
			balLogCountList = null;
			querycountBalLog = null;
			filteredDate = null;
		}

		log.debug(this.getClass().getName() + " - [getBalanceLogDetails] - "
				+ "Exit");
		return totalCount;
	}

	// End: Method modified by Vivekanandan A for mantis 1767 13-09-2012

	// Start: Method modified by Vivekanandan A for mantis 1767 13-09-2012
	/**
	 * This method used to frame the query to get the balance log details from
	 * the database based on the filter and sort criteria
	 *
	 * @param query
	 * @param filterSortStatus
	 * @param formats
	 * @return StringBuffer
	 */
	private Date formQuery(StringBuffer query, String filterSortStatus,
						   SystemFormats formats) throws SwtException {

		/* Method's local and local variable declaration */
		// Variable to hold filter and sort values
		String[] filterSort = null;
		// variable to hold filter values
		String filterStatus = null;
		// Variable to hold sort values
		String sortStatus = null;
		// Variable to hold sorted values
		String[] sortValues = null;
		// variable to manipulate sorted values
		int filterSortValue = 0;
		// Variable to hold sort description
		String sortDesc = null;
		// variable to filtered values
		String[] filterValues = null;
		// StringTokenizer to hold filtered values
		StringTokenizer filterTokeniser = null;
		// StringTokenizer to hold sorted values
		StringTokenizer sortTokeniser = null;
		// Simple date format instance for filtered date
		SimpleDateFormat filterDateSDF = null;
		// Date instance to hold filtered Date
		Date filteredDate = null;
		try {
			log
					.debug(this.getClass().getName() + " - [formQuery] - "
							+ "Entry");

			// Split filter and sort status
			filterSort = filterSortStatus.split("#");
			// Filter status is collected from array */
			filterStatus = filterSort[0].toString();
			// sort status is collected from array
			sortStatus = filterSort[1].toString();

			// Initializing sort values array
			sortValues = new String[3];
			// split the sort values and store in the sort values array
			sortValues = sortStatus.split("|");

			// Collect tokens of sort status
			sortTokeniser = new StringTokenizer(sortStatus, "|");

			// Loop to collect sort values
			while (sortTokeniser.hasMoreTokens()) {
				/* Values from tokens are assigned to the sortValues array */
				sortValues[filterSortValue] = sortTokeniser.nextToken();

				filterSortValue++;
			}
			// Assign sortdesc
			sortDesc = sortValues[1];
			// Assign the filterValues
			filterValues = new String[9];

			/* Condition to check filter status not equal to all and undefined */
			if (!(filterStatus.equals("all"))
					&& !(filterStatus.equals("undefined"))) {
				// filter status is set to split
				filterTokeniser = new StringTokenizer(filterStatus, "|");
				// Get filtered value
				filterValues[0] = filterTokeniser.nextToken();
				// Condition to check filtered value is not All
				if (!filterValues[0].toString().equals(SwtConstants.ALL_LABEL)) {
					// Append the filtered date with the update date column
					query.append(" and s.updateDate = ?4");
					// Obtain filtered date as Date with current date format
					filterDateSDF = new SimpleDateFormat(formats
							.getDateFormatValue()
							+ " HH:mm:ss");
					filteredDate = filterDateSDF.parse(filterValues[0]);

				}

			}

			// Condition to check sortStatus is not none and query is to not get
			// count
			if ((!sortStatus.equals("none"))) {
				query.append(" order by ");

				// Condition to check sort description is true
				if (sortDesc.equalsIgnoreCase("true")) {
					// sort records descending to update date 
					query.append(" s.balanceLogSeq desc");
				} else {
					// sort records ascending to update date 
					query.append(" s.balanceLogSeq ");
				}
			} else {
				// Condition to check whether given query is to get count
				//if (query.indexOf("count(*)") != -1)
				query.append(" order by s.balanceLogSeq desc");
				// set desc by default for update date

			}

		} catch (Exception ioException) {
			log.error(this.getClass().getName()
					+ " - ioException Catched in [formQuery] method : - "
					+ ioException.getMessage());
			throw SwtErrorHandler.getInstance().handleException(ioException,
					"formQuery", this.getClass());
		} finally {
			// nullify values
			filterSort = null;
			sortValues = null;
			filterValues = null;
			sortTokeniser = null;
			filterTokeniser = null;
			filterStatus = null;
			sortStatus = null;
			sortDesc = null;
			log.debug(this.getClass().getName() + " - [formQuery] - " + "Exit");
		}
		return filteredDate;
	}
	// End: Method modified by Vivekanandan A for mantis 1767 13-09-2012

}
