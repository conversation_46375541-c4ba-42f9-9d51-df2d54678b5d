/*
 * @(#)CurrencyGroupDAO.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;

public interface CurrencyGroupDAO extends DAO {

	/**
	 * Retrieves the list of currency group from the dataBase table
	 * S_CURRENCY_GROUP
	 * 
	 * @param hostId
	 * @param entityId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyGroupList(String hostId, String entityId)
			throws SwtException;

	/**
	 * Save new currency group details in database
	 * S_CURRENCY_GROUP
	 * 
	 * @param currencyGroup
	 * @return
	 * @throws SwtException
	 */
	public void saveCurrencyGroupDetails(CurrencyGroup currencyGroup)
			throws SwtException;

	/**
	 * Update CurrencyGroup Details in the Database table S_CURRENCY_GROUP
	 * 
	 * @param currencyGroup
	 * @return None
	 * @throws SwtException
	 */
	public void updateCurrencyGroupDetails(CurrencyGroup currencyGroup)
			throws SwtException;

	/**
	 * Delete the CurrencyGroup Details from  S_CURRENCY_GROUP table
	 * 
	 * @param currencyGroup
	 * @return 
	 * @throws SwtException
	 */
	public void deleteCurrencyGroupRecord(CurrencyGroup currencyGroup)
			throws SwtException;

	
	/**
	 * Get the list of currencies for the currency group from the Database table
	 * S_CURRENCY
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currGrpId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection getCurrencyGroupCurrenciesList(String hostId,
			String entityId, String currGrpId) throws SwtException;

	
	/**
	 * Get the number of currencies for the currency group from the Database
	 * table S_CURRENCY
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currencyGroupId
	 * @return Integer
	 * @throws SwtException
	 */
	public Integer getNoOfCurrencies(String hostId, String entityId,
			String currencyGroupId) throws SwtException;

	
	/**
	 * Get the currency group Name from DataBase table S_CURRENCY_GROUP, This
	 * method is used in SwtMaintenanceCache
	 * 
	 * @param entityId
	 * @param currencyGroupId
	 * @return String
	 * @throws SwtException
	 */
	public String getCurrencyGroupName(String entityId, String currencyGroupId)
			throws SwtException;
}
