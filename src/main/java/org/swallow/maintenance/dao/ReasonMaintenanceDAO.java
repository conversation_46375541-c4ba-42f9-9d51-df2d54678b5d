/*
 * @(#)ReasonMaintenanceDAO.java 1.0 25/08/2008
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.dao;

import java.util.Collection;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.ReasonMaintenance;
/**
*
* <AUTHOR> G
* ReasonMaintenanceDAO interface, contains collection of methods to be used in DAO layer.
*
*/

public interface ReasonMaintenanceDAO extends DAO {
	
	/**
	 * Get the Reason code and Description from P_REASON_CODES table
	 * @param String hostId
	 * @param String entityId
	 * @return Collection object
	 */
	public Collection getReasonMaintenanceDetails(String hostId, String entityId) throws SwtException;
	
	/**
	 *  Insert the new Reason code and description into  P_REASON_CODES table
	 * @param ReasonMaintenance reasonMaintenance
	 * 
	 */
	public void saveReasonMaintenanceDetails(ReasonMaintenance reasonMaintenance)  throws SwtException;
	
	/**
	 *  Updates the Reason description into  P_REASON_CODES table
	 * @param ReasonMaintenance reasonMaintenance
	 * 
	 */	
	public void updateReasonMaintenanceDetails(ReasonMaintenance reasonMaintenance)  throws SwtException;
	
	/**
	 *  Deletes the Reason details from  P_REASON_CODES table
	 * @param ReasonMaintenance reasonMaintenance
	 * 
	 */
	public void deleteReasonMaintenanceRecord(ReasonMaintenance reasonMaintenance) throws SwtException;
	
}
