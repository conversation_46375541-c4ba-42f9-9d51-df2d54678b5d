/*
 * @(#)CountryOverrideManagerImpl.java 1.0 28/02/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.service.impl;

import java.util.Collection;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.CountryOverrideDAO;
import org.swallow.maintenance.model.CountryOverride;
import org.swallow.maintenance.service.CountryOverrideManager;
import org.swallow.util.LabelValueBean;

/**
 * <AUTHOR> A
 * 
 * Manager layer for S_COUNTRY_OVERRIDE, to display save , update and delete
 * default weekend and override weekend for each country
 */
@Component ("countryOverrideManager")
public class CountryOverrideManagerImpl implements CountryOverrideManager {

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CountryOverrideManagerImpl.class);

	// Country DAO instance
	@Autowired
	private CountryOverrideDAO countryOverrideDAO = null;

	/**
	 * This is used to set country DAO
	 * 
	 * @param CountryOverrideDAO
	 * @return
	 */
	public void setCountryOverrideDAO(CountryOverrideDAO countryOverrideDAO) {
		this.countryOverrideDAO = countryOverrideDAO;
	}

	/**
	 * Method to get country list
	 * 
	 * @param entityId
	 * @param hostId
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<CountryOverride> getCountryList(String entity,
			String hostId) throws SwtException {
		try {
			log
					.debug(this.getClass().getName()
							+ " - [getCountryList] - Entry");
			return countryOverrideDAO.getCountryList(entity, hostId);
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [getCountryList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCountryList", CountryOverrideManagerImpl.class);
		} finally {
			log.debug(this.getClass().getName() + " - [getCountryList] - Exit");
		}
	}

	/**
	 * Method to get list of non working days
	 * 
	 * @return Collection
	 * @throws SwtException
	 */
	public Collection<LabelValueBean> getDayList() throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [getDayList] - Entry");
			return countryOverrideDAO.getDayList();
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [getDayList] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getDayList", CountryOverrideManagerImpl.class);
		} finally {
			log.debug(this.getClass().getName() + " - [getDayList] - Exit");
		}

	}

	/**
	 * Method to save/ delete / update country override
	 * 
	 * @param countryOverride
	 * @param saveStatus
	 * @return
	 * @throws SwtException
	 */
	public void save(CountryOverride countryOverride, String saveStatus)
			throws SwtException {
		try {
			log.debug(this.getClass().getName() + " - [save] - Entry");
			countryOverrideDAO.save(countryOverride, saveStatus);
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp, "save",
					CountryOverrideManagerImpl.class);
		} finally {
			log.debug(this.getClass().getName() + " - [save] - Exit");
		}

	}

}