/*
 * @(#)CurrencyGroupAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.web;

import java.util.Collection;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.EntityRoleTO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.Holiday;
import org.swallow.maintenance.service.CurrencyGroupManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.springframework.beans.factory.annotation.Autowired;




import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;












/**
 *
 * This is action class for Currency Group screen.
 *
 */
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.ArrayList;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/currencygroup", "/currencygroup.do"})
public class CurrencyGroupAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/currencygroupmaintenancechild");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/maintenance/currencygroupmaintenance");
		viewMap.put("currencies", "jsp/maintenance/currencygroupcurrencies");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "displayListByEntity":
				return displayListByEntity();
			case "add":
				return add();
			case "save":
				return save();
			case "change":
				return change();
			case "update":
				return update();
			case "delete":
				return delete();
			case "currencies":
				return currencies();
		}




















		return displayListByEntity();
	}


	private CurrencyGroup currencyGroup;
	public CurrencyGroup getCurrencyGroup() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		currencyGroup = RequestObjectMapper.getObjectFromRequest(CurrencyGroup.class, request);
		return currencyGroup;
	}

	public void setCurrencyGroup(CurrencyGroup currencyGroup) {
		this.currencyGroup = currencyGroup;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("currencyGroup", currencyGroup);
	}



	String menuAccessId;
	public String getMenuAccessId() {
		return menuAccessId;
	}
	public void setMenuAccessId(String menuAccessId) {
		this.menuAccessId = menuAccessId;
	}

	String ismenuItem;
	public String getIsmenuItem() {
		return ismenuItem;
	}
	public void setIsmenuItem(String ismenuItem) {
		this.ismenuItem = ismenuItem;
	}

	String menuItemId;
	public String getMenuItemId() {
		return menuItemId;
	}
	public void setMenuItemId(String menuItemId) {
		this.menuItemId= menuItemId;
	}

	/**
	 * object of CurrencyGroupManager.java
	 */
	@Autowired
	private CurrencyGroupManager currencyGroupManager = null;

	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CurrencyGroupAction.class);

	/**
	 * This is a setter method for variable currencyGroupManager
	 *
	 * @param currencyGroupManager
	 */
	public void setCurrencyGroupManager(
			CurrencyGroupManager currencyGroupManager) {
		this.currencyGroupManager = currencyGroupManager;
	}

	/**
	 * This function is called by default when currency group window is opened
	 * first time, it further calls displayList.
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		log.debug(this.getClass().getName() + " - [unspecified] - "
				+ " returns displayList method");
		return displayListByEntity();
	}

	/**
	 * This function displays currency group details according to the default or
	 * selected entity id given
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String displayListByEntity() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " - [displayListByEntity] - "
					+ "Entry");

			/* Method's local variable and class Instance declaration */
			String hostId;
			String entityId;
			Collection coll;
			CurrencyGroup currGrp;

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			currGrp = getCurrencyGroup();




			/* Collect the entity id from the currencyGroup form bean */
			entityId = currGrp.getId().getEntityId();

			/* Condition to check entity id is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/*
				 * Set the currenct entity of the user from SwtUtil if entity is
				 * null
				 */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* EntityId is set in CurrencyGroup Bean */
				currGrp.getId().setEntityId(entityId);
			}

			setCurrencyGroup(currGrp);
			/*
			 * Pass the request and entityId parameter to set access id in
			 * request attribute
			 */
			putEntityAccessInReq(request, entityId);
			/*
			 * Pass the request parameter to set entity list in request
			 * attribute
			 */
			putEntityListInReq(request);

			/* Get collection of currency group list from currencyGroupManager */
			coll = currencyGroupManager.getCurrencyGroupList(hostId, entityId);
			/*
			 * Set the collection of currencyGroupDetails to the request
			 * Attribute
			 */
			request.setAttribute("currencyGroupDetails", coll);
			log.debug(this.getClass().getName() + " - [displayListByEntity] - "
					+ "Exit");

			return getView("success");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [displayListByEntity] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayListByEntity] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [displayListByEntity] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [displayListByEntity] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayListByEntity", CurrencyGroupAction.class),
					request, "");

			return getView("fail");
		}
	}

	/**
	 * Method to load the add currency group screen with the entity Id collected
	 * from the parent screen
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " - [add] - " + "Entry");
			/* Method's local variable and class instance declaration */
			String entityId;
			String entityName;
			CurrencyGroup currGrp;

			currGrp = getCurrencyGroup();




			/* Read entityId from request */
			entityId = request.getParameter("entityCode");

			/* Read entityName from Request */
			entityName = request.getParameter("entityText");

			/* Set entityId in currencyGroup bean */
			currGrp.getId().setEntityId(entityId);
			setCurrencyGroup(currGrp);

			request.setAttribute("entityName", entityName);
			request.setAttribute("methodName", "save");
			log.debug(this.getClass().getName() + " - [add] - " + "Exit");
			return getView("add");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", CurrencyGroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 *
	 * Method to validate the currency group details that are given in add
	 * currency group screen and returns an error message if the validation
	 * fails or save the currency group details in data base
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		log.debug(this.getClass().getName() + " - [save] - " + "Entry");

		/* Method's local variable and class instance declaration */
		String hostId=null;
		CurrencyGroup currGrp=null;
		String entityName=null;
		ActionMessages errors=null;

		try {
			errors = new ActionMessages();

			currGrp = getCurrencyGroup();



			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();
			/* Set hostId to Currency group bean */
			currGrp.getId().setHostId(hostId);
			/* Read the entity name from request */
			entityName = request.getParameter("entityText");
			request.setAttribute("methodName", "save");
			request.setAttribute("entityName", entityName);
			/* Send currencyGroup bean to save method of Manager */
			currencyGroupManager.saveCurrencyGroupDetails(currGrp);
			request.setAttribute("parentFormRefresh", "yes");
			log.debug(this.getClass().getName() + " - [save] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "save");
			/*
			 * Start : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}
			/*
			 * End : Modified for Mantis 1366-Remove entry to log if duplicate
			 * records added by betcy on 18-02-2011
			 */
			setCurrencyGroup(currGrp);
			return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", CurrencyGroupAction.class), request, "");
			return getView("fail");
		}finally
		{
			errors=null;
		}
	}

	/**
	 * Method to load the change currency group screen with the selected
	 * currency group detail to update the existing currency group
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " - [change] - " + "Entry");

			/* Method's local variable and class instance declaration */
			CurrencyGroup currGrp;
			String entityId;
			String entityName;
			String currencyGroupId;
			String currencyGroupName;

			currGrp = getCurrencyGroup();




			/* Read the entityId from request */
			entityId = request.getParameter("entityCode");
			/* Read the entityName from request */
			entityName = request.getParameter("entityText");
			/* Read the CurrencyGroupId from the request */
			currencyGroupId = request.getParameter("selectedCurrencyGroupId");
			/* Read the CurrencyGroupName from the request */
			currencyGroupName = request
					.getParameter("selectedCurrencyGroupName");

			/* Set entity id to the CurrencyGroup bean */
			currGrp.getId().setEntityId(entityId);
			/* Set currency group id to the CurrencyGroup bean */
			currGrp.getId().setCurrencyGroupId(currencyGroupId);
			/* Set currency group Name to the CurrencyGroup bean */
			currGrp.setCurrencyGroupName(currencyGroupName);
			setCurrencyGroup(currGrp);
			request.setAttribute("entityName", entityName);
			request.setAttribute("methodName", "update");
			log.debug(this.getClass().getName() + " - [change] - " + "Exit");
			return getView("add");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", CurrencyGroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * Method to validate the currency group details that are get from the
	 * change screen and returns error message if validation fails or update the
	 * currency group detail given for the currency groupId
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		log.debug(this.getClass().getName() + " - [update] - " + "Entry");

		/* Method's local variable and class instance declaration */
		String hostId;
		String entityName;
		String roleId;

		CurrencyGroup currGrp;
		HttpSession session;
		EntityRoleTO entityRoleTO;

		/* Reads the hostId from SwtUtil */
		hostId = SwtUtil.getCurrentHostId();

		currGrp = getCurrencyGroup();




		/* Read entity name from the request */
		entityName = request.getParameter("entityText");

		/* Set hostId to the currencyGroup bean */
		currGrp.getId().setHostId(hostId);

		setCurrencyGroup(currGrp);
		request.setAttribute("methodName", "update");
		request.setAttribute("entityName", entityName);

		try {
			/* Pass currencyGroup bean to the update method of the Manager */
			currencyGroupManager.updateCurrencyGroupDetails(currGrp);

			/* Retrieve the session from request */
			session = request.getSession();
			/* Retrieve the role id of the current user */
			roleId = (SwtUtil.getCurrentUser(session)).getRoleId();
			/*
			 * Initializing the EntityRoleTo with roleId and entityId as
			 * parameter
			 */
			entityRoleTO = new EntityRoleTO(roleId, currGrp.getId()
					.getEntityId());
			/*
			 * Remove the currency group from entity and role through
			 * SwtMaintenance
			 */
			SwtUtil.getSwtMaintenanceCache().remove(entityRoleTO);
			request.setAttribute("parentFormRefresh", "yes");
			log.debug(this.getClass().getName() + " - [update] - " + "Exit");

			return getView("add");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());
			request.setAttribute("methodName", "update");
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("add");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", CurrencyGroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * Method to delete the selected currency group and returns an error message
	 * if the deleted currency group is not exist in the table
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		log.debug(this.getClass().getName() + " - [delete] - " + "Entry");

		/* Method's local variable and class instance declaration */
		String entityId;
		String hostId;
		String currencyGroupId;
		CurrencyGroup currGrp;
		HttpSession session;
		String roleId;
		EntityRoleTO entityRoleTO;

		currGrp = getCurrencyGroup();




		entityId = request.getParameter("entityCode");
		hostId = SwtUtil.getCurrentHostId();

		/* Read the currencyGroupId from request */
		currencyGroupId = request.getParameter("selectedCurrencyGroupId");

		/* Set entityId to currencyGroup bean */
		currGrp.getId().setEntityId(entityId);
		/* Set the currency group id to CurrencyGroup bean */
		currGrp.getId().setCurrencyGroupId(currencyGroupId);
		/* Set hostId to currency group bean */
		currGrp.getId().setHostId(hostId);
		setCurrencyGroup(currGrp);

		try {
			/* Pass currencyGroup bean to the delete method of manager */
			currencyGroupManager.deleteCurrencyGroupRecord(currGrp);

			/* Read the session from request */
			session = request.getSession();
			/* Get the Role Id of the currenct user */
			roleId = (SwtUtil.getCurrentUser(session)).getRoleId();
			/* Initializing the EntityRoleTO with roleId and entityId */
			entityRoleTO = new EntityRoleTO(roleId, currGrp.getId()
					.getEntityId());
			SwtUtil.getSwtMaintenanceCache().remove(entityRoleTO);
			log.debug(this.getClass().getName() + " - [delete] - " + "Exit");
			return displayListByEntity();

		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return displayListByEntity();
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", CurrencyGroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * Method to fetch the List of currencies that are corresponding to the
	 * particular currency groupId
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String currencies()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName() + " - [currencies] - "
					+ "Entry");

			/* Method's local variable and class instance declaration */
			String hostId;
			String entityId;
			String currencyGroupId;
			Collection currenciesColl;

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Read the entityId from the request Parameter */
			entityId = request.getParameter("entityCode");
			/* Read the currencyGroupId from request */
			currencyGroupId = request.getParameter("selectedCurrencyGroupId");
			/*
			 * Retrieves the collection of currency list for currency group from
			 * manager
			 */
			currenciesColl = currencyGroupManager
					.getCurrencyGroupCurrenciesList(hostId, entityId,
							currencyGroupId);

			request.setAttribute("currencyGroupCurrencies", currenciesColl);
			log.debug(this.getClass().getName() + " - [currencies] - "
					+ "Exit");
			return getView("currencies");
		} catch (SwtException swtexp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [currencies] method : - "
					+ swtexp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [currencies] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.debug(this.getClass().getName()
					+ " - Exception Catched in [currencies] method : - "
					+ exp.getMessage());

			log.error(this.getClass().getName()
					+ " - Exception Catched in [currencies] method : - "
					+ exp.getMessage());
			exp.printStackTrace();
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "currencies", CurrencyGroupAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This function set list of Entities which are available for the role of
	 * the particular user, into request attribute
	 *
	 * @param request
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Entry");
		/* Method's class instance declaration */
		HttpSession session;
		Collection coll;
		/* Retrives the session from request */
		session = request.getSession();
		/* Retrieve the collection of user entityAccessList */
		coll = SwtUtil.getUserEntityAccessList(session);
		/* Retrieve theLabel value bean collection of user entityAccessList */
		coll = SwtUtil.convertEntityAcessCollectionLVL(coll, session);
		request.setAttribute("entityList", coll);
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Exit");
	}

	/**
	 * This function set the access right of entity, currency group and menu
	 * into request object
	 *
	 * @param request
	 * @param entityId
	 * @return Entity Access
	 */
	private int putEntityAccessInReq(HttpServletRequest request, String entityId)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putEntityAccessInReq] - "
				+ "Entry");
		/* Method local variable declaration */
		int accessInd;

		/* Collect the access for Menu Entity and Currency Group */
		accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId, null);
		/* Condition to check accessInd is '0' */
		if (accessInd == 0) {
			/* Set the full access for the user */
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_FULL_ACCESS + "");
		} else {
			/* Set the View access for user */
			request.setAttribute("EntityAccess",
					SwtConstants.ENTITY_READ_ACCESS + "");
		}

		log.debug(this.getClass().getName() + " - [putEntityAccessInReq] - "
				+ "Exit");

		return accessInd;
	}
}
