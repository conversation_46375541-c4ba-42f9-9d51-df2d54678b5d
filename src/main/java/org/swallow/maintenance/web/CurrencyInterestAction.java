/*
 * @(#)CurrencyInterestAction.java 1.0 23/08/06
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.web;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.Shortcut;
import org.swallow.dao.LogonDAO;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.impl.Obj2CsvImpl;
import org.swallow.export.service.impl.Obj2PdfImpl;
import org.swallow.export.service.impl.Obj2XlsImpl;
import org.swallow.export.service.impl.Obj2XmlCurrencyInterest;
import org.swallow.export.service.impl.Xml2CsvImpl;
import org.swallow.export.service.impl.Xml2PdfImpl;
import org.swallow.export.service.impl.Xml2XlsImpl;
import org.swallow.maintenance.model.CurrencyInterest;
import org.swallow.maintenance.service.CurrencyInterestManager;
import org.swallow.model.ExportObject;
import org.swallow.model.MenuItem;
import org.swallow.util.CommonDataManager;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.XSSUtil;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;




import org.swallow.util.struts.ActionMessage;
import org.swallow.util.struts.ActionMessages;


/**
 * CurrencyInterestAction.java
 *
 * this class is used to display the currency Interest.
 */













import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/currencyinterest", "/currencyinterest.do"})
public class CurrencyInterestAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("add", "jsp/maintenance/currencyInterestAdd");
		viewMap.put("fail", "error");
		viewMap.put("success", "jsp/maintenance/currencyinterestmaintenance");
		viewMap.put("change", "jsp/maintenance/currencyInterestAdd");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");

















	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "showDetails":
				return showDetails();
			case "add":
				return add();
			case "save":
				return save();
			case "update":
				return update();
			case "change":
				return change();
			case "delete":
				return delete();
			case "exportCurrencyInterest":
				return exportCurrencyInterest();
		}


		return unspecified();
	}



	/**
	 * Initializing logger object for this class
	 */
	private final Log log = LogFactory.getLog(CurrencyInterestAction.class);

	/**
	 * Initializing empty string
	 */
	private final String EMPTYSTRING = "";

	/**
	 * Used to hold CurrencyInterestManager reference object
	 */
	@Autowired
	private CurrencyInterestManager currencyInterestManager;

	/**
	 * The currencyInterestManager to set.
	 *
	 * @param currencyInterestManager
	 * @return
	 */
	public void setCurrencyInterestManager(
			CurrencyInterestManager currencyInterestManager) {
		this.currencyInterestManager = currencyInterestManager;
	}

	private CurrencyInterest currencyInterest;



	public CurrencyInterest getCurrencyInterest() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		currencyInterest = RequestObjectMapper.getObjectFromRequest(CurrencyInterest.class, request, "currencyInterest");
		return currencyInterest;
	}


	public void setCurrencyInterest(CurrencyInterest currencyInterest) {
		this.currencyInterest = currencyInterest;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("currencyInterest", currencyInterest);
	}


	/**
	 * This is the default method provided by Spring framework ,called
	 * internally. It further calls showDetails method.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [unspecified] - "
				+ "Returns showDetails");
		/* Return showDetails Action method */
		return showDetails();
	}

	/**
	 * This method is used to display the list of currencyInterest details on
	 * the UI from database.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */

	public String showDetails()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		/* Method's local variable and class instance declaration */
		String hostId = EMPTYSTRING;
		String entityId = EMPTYSTRING;
		String currencyCode = EMPTYSTRING;
		final String DEFAULTCURRENCYCODE = "All";
		int accessInd;
		Collection<CurrencyInterest> currencyInterestList = null;
		CurrencyInterest currencyInterest = null;
		String currencyCodeForDate = null;
		try {
			log.debug(this.getClass().getName() + " - [showDetails] - Entry");

			currencyInterest = getCurrencyInterest();





			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Get the entity id from currencyInterest bean */
			entityId = currencyInterest.getId().getEntityId();

			/* Get the currency code from currencyInterest bean object */
			currencyCode = currencyInterest.getId().getCurrencyCode();

			/* Set the host id to the Currency interest bean */
			currencyInterest.getId().setHostId(hostId);

			/* Condition to check entity id is null or no characters */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/*
				 * Get current entity of the user from SwtUtil and set to the
				 * bean
				 */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				currencyInterest.getId().setEntityId(entityId);
			}

			/* Condition to check currency code is null */
			if ((currencyCode == null) || (currencyCode.trim().length() <= 0)) {
				/* Set'ALL' as default currency code */
				currencyCode = DEFAULTCURRENCYCODE;

				/* Set Currency code to currencyInterest Bean */
				currencyInterest.getId().setCurrencyCode(currencyCode);
			}

			String entityDate = SwtUtil.formatDate(SwtUtil
					.getSysParamDateWithEntityOffset(entityId), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue());

			currencyInterest.setFromDateAsString(((currencyInterest
					.getFromDateAsString() == null) || (currencyInterest
					.getFromDateAsString().equals(""))) ? entityDate : currencyInterest
					.getFromDateAsString().trim());

			/*
			 * Condition to check to date is null if it is null it set the
			 * system date
			 */
			currencyInterest.setToDateAsString(((currencyInterest
					.getToDateAsString() == null) || (currencyInterest
					.getToDateAsString().equals(""))) ? entityDate
					: currencyInterest.getToDateAsString());

			/* Get the currency interest list from Data base */
			currencyInterestList = currencyInterestManager
					.getCurrencyInterestList(
							request,
							entityId,
							hostId,
							(currencyCode.trim().equals(DEFAULTCURRENCYCODE) ? "%"
									: currencyCode), currencyInterest
									.getFromDateAsString().trim(),
							currencyInterest.getToDateAsString());

			/* Set entity access list into request attribute */
			putEntityListInReq(request);

			/* Put the list of currencies into request attribute */
			putCurrencyListInReq(request, hostId, entityId, true);

			request.setAttribute("currencyInterestList", currencyInterestList);

			setCurrencyInterest(currencyInterest);

			request.setAttribute("methodName", "showDetails");

			request.setAttribute("lastRefTime", SwtUtil.getLastRefTime(request,
					entityId));
			/* Get the access id of the user from SwtUtil method */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);

			/* Condition to check accessInd is '0' */
			if (accessInd == 0) {

				/*
				 * If the condition is true then button status is set for full
				 * access
				 */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				/*
				 * If the condition is false then button status is set for read
				 * access
				 */
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);

				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			return getView("success");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [showDetails] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "showDetails", CurrencyInterestAction.class), request,
					"");

			return getView("fail");
		} finally {
			log.debug(this.getClass().getName() + " - [showDetails] - Exit");
		}
	}

	/**
	 * This method is called on clicking "ADD" button on the CurrencyInterest UI
	 * and loads add screen details.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String add()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + "- [add] - Enter");

		/* Method's local variable and class instance declaration */
		String hostId = EMPTYSTRING;

		CurrencyInterest currencyInterest = null;
		Collection currencyList;
		String entityId = null;

		try {
			entityId = request.getParameter("entityId");
			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			currencyInterest = getCurrencyInterest();





			/* Set entity id to currency interest bean */
			currencyInterest.getId().setEntityId(entityId);
			/* Set Currency code to currencyInterest Bean */

			currencyInterest.getId().setCurrencyCode(
					request.getParameter("currencyCode"));

			/*
			 * Set the currency list into the request attribute and returns the
			 * collection of currency list
			 */
			currencyList = putCurrencyListInReq(request, hostId,
					entityId, false);

			/*
			 * Condition to check currency code and currency list is not null or
			 * empty
			 */

			if (((currencyInterest.getId().getCurrencyCode() == null) || (currencyInterest
					.getId().getCurrencyCode().trim().length() <= 0))
					&& ((currencyList != null) && (currencyList.size() > 0))) {
				/*
				 * If true then currency code value is set by the value bean of
				 * currency list
				 */

				LabelValueBean lvb = (LabelValueBean) currencyList.iterator()
						.next();

				/* Set Currency code to currencyInterest Bean */
				currencyInterest.getId().setCurrencyCode(lvb.getValue());

			}
			/* Passing values to the parameter */
			currencyInterest.setInterestRate(new BigDecimal("0.0"));

			currencyInterest.setInterestRateDateAsString(SwtUtil.formatDate(SwtUtil
					.getSysParamDateWithEntityOffset(entityId), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue()));


			/* Set the access list of entity into the request attribute */
			putEntityListInReq(request);

			/* Put the entity descriptions into the request attribute */
			putDescriptionsInRequest(request, currencyInterest.getId()
					.getEntityId());

			setCurrencyInterest(currencyInterest);
			request.setAttribute("screenFieldsStatus", "true");
			request.setAttribute("methodName", "add");

			log.debug(this.getClass().getName() + " - [add]- " + "Exit");

			return getView("add");
		} catch (SwtException swtexp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {

			log.error(this.getClass().getName()
					+ " - Exception Catched in [add] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "add", CurrencyInterestAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This method is called on clicking save button on CurrencyInterestAdd UI
	 * to save the interest details for the currency
	 * @return Action forward object
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [save] - " + "Entry");

		/* Method's local variable and class instance declaration */
		String hostId = EMPTYSTRING;
		String selectedInterestRate = null;
		CurrencyInterest currencyInterest = null;
		SystemFormats sysformat = null;
		ActionMessages errors = null;
		// DynaValidatorForm dyForm = null;
		try {
			errors = new ActionMessages();
			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("methodName", "add");

			currencyInterest = getCurrencyInterest();
			/* Get the current system formats from swtUtil */
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Set host id to the currency interest bean */
			currencyInterest.getId().setHostId(hostId);

			/* Set entity id to the currency interest bean */
			currencyInterest.getId().setEntityId(
					request.getParameter("entityId").toString());

			/* Currency code is set to the currency interest bean */
			currencyInterest.getId().setCurrencyCode(
					request.getParameter("currencyCode").toString());

			/* Interest rate is set to the currency interest bean */
			currencyInterest
					.setInterestRate(new BigDecimal(request.getParameter("selectedInterestRate")));

			/* Interest RateDate is set to the currency interest bean */
			currencyInterest.getId().setInterestRateDate(
					SwtUtil.parseDate(currencyInterest
							.getInterestRateDateAsString(), sysformat
							.getDateFormatValue()));

			/* Update date is set to currency interest bean */
			currencyInterest.setUpdateDate(new Date());

			/* Update user is set to currency interest bean */
			currencyInterest.setUpdateUser(SwtUtil.getCurrentUserId(request
					.getSession()));

			/*
			 * CurrencyInterest bean is passed to the saveCurrencyInterest of
			 * currencyInterest Manager
			 */
			currencyInterestManager.saveCurrencyInterest(currencyInterest);

			/* Put the currency list to the request */
			putCurrencyListInReq(request, hostId, currencyInterest.getId()
					.getEntityId(), false);

			log.debug(this.getClass().getName() + " - [save] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ swtexp.getMessage());
			request.setAttribute("error", "true");
			request.setAttribute("parentFormRefresh", "no");
			request.setAttribute("methodName", "add");
			/* Set the list of currency into the request */
			putCurrencyListInReq(request, hostId, currencyInterest.getId()
					.getEntityId(), false);
			/* Put the entity description to the request attribute */
			putDescriptionsInRequest(request, currencyInterest.getId()
					.getEntityId());

			if (swtexp.getErrorCode().equals(
					"errors.DataIntegrityViolationExceptioninAdd")) {
				if (errors != null) {
					errors.add("", new ActionMessage(swtexp.getErrorCode()));
				}
				saveErrors(request, errors);

			} else {
				saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			}

			return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [save] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "save", CurrencyInterestAction.class), request, "");

			return getView("fail");
		} finally {
			errors = null;
		}
	}

	/**
	 * This method is called on clicking save button on the Change
	 * CurrencyInterest UI to update the currency interest details in database
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Entry");

		/* Method's local variable and class instance declaration */
		String hostId = EMPTYSTRING;
		String rateDate = EMPTYSTRING;
		SystemFormats sysformat;
		CurrencyInterest currencyInterest;

		try {

			request.setAttribute("parentFormRefresh", "yes");
			request.setAttribute("methodName", "add");

			/* Interest rate date is read from request */
			rateDate = request.getParameter("selectedInterestDate").trim();

			currencyInterest = getCurrencyInterest();





			/* CurrentSystem format is get from SwtUtil */
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Set host id to the currencyInterest Bean */
			currencyInterest.getId().setHostId(hostId);

			/* EntityId is set to the currencyInterest Bean */
			currencyInterest.getId().setEntityId(
					request.getParameter("entityId").toString());

			/* Currency code is set to the currencyInterest Bean */
			currencyInterest.getId().setCurrencyCode(
					request.getParameter("currencyCode").toString());

			/* Interest rate is set to the currency interest bean */
			currencyInterest
					.setInterestRate(new BigDecimal(request.getParameter("selectedInterestRate")));

			/* InterestRateDate is set to the currencyInterest Bean */
			currencyInterest.getId()
					.setInterestRateDate(
							SwtUtil.parseDate(rateDate, sysformat
									.getDateFormatValue()));

			/* Update date is set to the currencyInterest Bean */
			currencyInterest.setUpdateDate(new Date());

			/* Update user is set to the currencyInterest Bean */
			currencyInterest.setUpdateUser(SwtUtil.getCurrentUserId(request
					.getSession()));
			/*
			 * The bean is send to the update method of currency interest
			 * manager to update
			 */
			currencyInterestManager
					.updateCurrencyInterestDetail(currencyInterest);

			/* Set the list of currency into the request */
			putCurrencyListInReq(request, hostId, currencyInterest.getId()
					.getEntityId(), false);
 			setCurrencyInterest(currencyInterest);
			log.debug(this.getClass().getName() + " - [update] - " + "Exit");
			return getView("add");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return getView("add");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [update] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", CurrencyInterestAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This method is called on clicking change button on the CurrencyInterest
	 * UI to load Change currency Interest maintenance details
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String change()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		log.debug(this.getClass().getName() + " - [change] - " + "Entry");

		/* Method's local variable and class instance declaration */
		String hostId;
		CurrencyInterest currencyInterest;

		try {

			currencyInterest = getCurrencyInterest();





			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Set entityId to currencyInterest Bean */
			currencyInterest.getId().setEntityId(
					request.getParameter("entityId"));

			/* Set currency code to currencyInterest Bean */
			currencyInterest.getId().setCurrencyCode(
					request.getParameter("selectedCurrencyCode").trim());

			/* Set interestRate to currencyInterest Bean */
			/* To Display the Interest rate in BigDecimal format */
			currencyInterest.setInterestRate(new BigDecimal(request
					.getParameter("selectedInterestRate").trim()));

			/* Set interestRateDate to currencyInterest Bean */
			currencyInterest.setInterestRateDateAsString(request.getParameter(
					"selectedInterestDate").trim());

			setCurrencyInterest(currencyInterest);

			/* Set the access list of entity into the request attribute */
			putEntityListInReq(request);

			/* Put currency list in to the request attribute */
			putCurrencyListInReq(request, hostId, currencyInterest.getId()
					.getEntityId(), false);

			/* Put entity description to the request attribute */
			putDescriptionsInRequest(request, currencyInterest.getId()
					.getEntityId());

			request.setAttribute("screenFieldsStatus", "true");

			request.setAttribute("methodName", "change");

		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [change] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "change", CurrencyInterestAction.class), request, "");

			return getView("fail");
		}

		log.debug(this.getClass().getName() + " - [cahnge] - " + "Exit");
		return getView("add");
	}

	/**
	 * This method is used to delete the CurrencyInterest object from database.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		log.debug(this.getClass().getName() + " - [delete] - " + "Entry");

		/* Method's local variable declaration */
		String hostId = EMPTYSTRING;
		CurrencyInterest currencyInterest = null;
		CurrencyInterest orgCurrencyInterest = null;
		SystemFormats sysformat;

		try {


			orgCurrencyInterest = getCurrencyInterest();



			currencyInterest = new CurrencyInterest();

			/* Get the current system formats from SwtUtil */
			sysformat = SwtUtil.getCurrentSystemFormats(request.getSession());

			/* Reads the hostId from SwtUtil */
			hostId = SwtUtil.getCurrentHostId();

			/* Set host id to currencyInterest Bean */
			currencyInterest.getId().setHostId(hostId);

			/* Set entityId to currencyInterest Bean */
			currencyInterest.getId().setEntityId(
					orgCurrencyInterest.getId().getEntityId());

			/* Set currency code to currencyInterest Bean */
			currencyInterest
					.getId()
					.setCurrencyCode(
							((request.getParameter("selectedCurrencyCode") != null) && (request
									.getParameter("selectedCurrencyCode")
									.trim().length() > 0)) ? request
									.getParameter("selectedCurrencyCode")
									.trim() : EMPTYSTRING);


			if(currencyInterest != null && currencyInterest.getId().getEntityId() != null && currencyInterest.getId().getCurrencyCode() != null) {
				boolean accessScreen = false;
				boolean accessEntityCurrency = XSSUtil.checkCurrencyFullAccess(request, currencyInterest.getId().getEntityId() , currencyInterest.getId().getCurrencyCode());

				CommonDataManager cdm = (CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN);
				if(accessEntityCurrency) {
					LogonDAO logonDAO = (LogonDAO)SwtUtil.getBean("logonDAO");
					// The menu item to the relevant match
					MenuItem menuItem = logonDAO.getMenuItem(SwtConstants.MENU_ITEM_CURRENCY_INTERREST+"", cdm.getUser());

					// Change the menu access id of the screen if the menu access to the relevant match is not full access
					if (menuItem != null && SwtUtil.getHierarchicalAccessId(menuItem, request) == 0)
						accessScreen = true;

				}

				if(!accessEntityCurrency || !accessScreen) {
					request.setAttribute("errordesc", SwtUtil.getMessage("errors.authorization.attack", request));
					request.setAttribute("errorCause", "");
					log.error(SwtUtil.getMessage("errors.authorization.attack.log", request) + "\n" +
							"{" + SwtUtil.getMessage("errors.user.log", request) + ": " + cdm.getUser().getId().getUserId() +
							", " + SwtUtil.getMessage("errors.ipAddress.log", request) + ": " + request.getRemoteAddr() +
							", " + SwtUtil.getMessage("errors.requestURI.log", request) + ": " + request.getRequestURI() + "}");
					return getView("fail");
				}
			}
			/* Set interestRateDate to currencyInterest Bean */
			currencyInterest.getId().setInterestRateDate(
					SwtUtil.parseDate(request.getParameter(
							"selectedInterestDate").trim(), sysformat
							.getDateFormatValue()));
			/*
			 * Pass currency interest bean object to currency interest manager
			 */

			currencyInterestManager.deleteCurrencyInterest(currencyInterest);
			request.setAttribute("screenFieldsStatus", "true");

			request.setAttribute("methodName", "add");

			log.debug(this.getClass().getName() + " - [delete] - " + "Exit");
			return showDetails();
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ swtexp.getMessage());

			saveErrors(request, SwtUtil.logException(swtexp, request, ""));

			return showDetails();
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [delete] method : - "
					+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", CurrencyInterestAction.class), request, "");

			return getView("fail");
		}
	}

	/**
	 * This method is used to put collection of entity list into request.
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private void putEntityListInReq(HttpServletRequest request)
			throws SwtException {

		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Entry");

		/* Method's local variable declaration */
		HttpSession session;
		Collection entities;

		/* Read the session from request */
		session = request.getSession();
		/* Collects the user entity Access list from SwtUtil */
		entities = SwtUtil.getUserEntityAccessList(session);
		/*
		 * collects entity id and entity name of accessible entities from
		 * SwtUtil
		 */
		entities = SwtUtil.convertEntityAcessCollectionLVL(entities, session);
		request.setAttribute("entities", entities);
		log.debug(this.getClass().getName() + " - [putEntityListInReq] - "
				+ "Exit");

	}

	/**
	 * This method is used to put associated CurrencyCode list into the request
	 * that is further displayed on the UI.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param withAll
	 * @return Collection
	 * @throws SwtException
	 */
	private Collection putCurrencyListInReq(HttpServletRequest request,
											String hostId, String entityId, boolean withAll)
			throws SwtException {
		log.debug(this.getClass().getName() + " - [putCurrencyListInReq] - "
				+ "Entry");
		/* Method's local variable and class Instance declaration */
		Collection currencyDropDown;

		String roleId = "";
		ArrayList currencyList = null;

		/* Initialize Array list */
		currencyDropDown = new ArrayList();

		/*
		 * Collects the currency detail list from currency manager and stored in
		 * currencyDetailVO object
		 */

		roleId = ((CommonDataManager) request.getSession().getAttribute(
				SwtConstants.CDM_BEAN)).getUser().getRoleId();

		currencyList = (ArrayList) SwtUtil.getSwtMaintenanceCache()
				.getCurrencyViewORFullAcessLVL(roleId, entityId);

		/* Condition to check withAll is true */
		if (withAll) {
			/* Add the label value bean of currency */
			currencyDropDown.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
		}

		/* If collection of currency list is null */
		if (currencyList != null) {

			currencyList.remove(new LabelValueBean("Default", "*"));
			Iterator itr = currencyList.iterator();

			while (itr.hasNext()) {
				LabelValueBean lb = (LabelValueBean) (itr.next());

				if (lb.getLabel().trim().length() > 0) {
					String label = lb.getLabel();
					String value = lb.getValue();
					currencyDropDown.add(new LabelValueBean(label, value));
				}
			}
		}

		request.setAttribute("currencies", currencyDropDown);

		log.debug(this.getClass().getName() + " - [putCurrencyListInReq] - "
				+ "Exit");
		return currencyDropDown;
	}

	/**
	 * This method is used to set button status
	 *
	 * @param req
	 * @param addStatus
	 * @param changeStatus
	 * @param deleteStatus
	 * @return
	 * @throws SwtException
	 */
	private void setButtonStatus(HttpServletRequest req, String addStatus,
								 String changeStatus, String deleteStatus) throws SwtException {
		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Entry");
		req.setAttribute(SwtConstants.ADD_BUT_STS, addStatus);
		req.setAttribute(SwtConstants.CHG_BUT_STS, changeStatus);
		req.setAttribute(SwtConstants.DEL_BUT_STS, deleteStatus);
		log.debug(this.getClass().getName() + " - [setButtonStatus] - "
				+ "Exit");
	}

	/**
	 * This function puts the description corresponding to entityId in request
	 *
	 * @param request
	 * @param entityId
	 * @return
	 */
	private void putDescriptionsInRequest(HttpServletRequest request,
										  String entityId) {
		log.debug(this.getClass().getName()
				+ " - [putDescriptionsInRequest] - " + "Entry");
		Collection collEntity = (Collection) request.getAttribute("entities");

		/* Condition to check collection not equal to null */
		if (collEntity != null) {
			Iterator itr = collEntity.iterator();

			/* Loop to get the label value bean of the entity collection */
			while (itr.hasNext()) {
				LabelValueBean lvb = (LabelValueBean) (itr.next());

				/*
				 * Condition to check the label value is equal to the entityId
				 * passed
				 */
				if (lvb.getValue().equals(entityId)) {
					request.setAttribute("entityDesc", lvb.getLabel());

					break;
				}
			}
		}
		log.debug(this.getClass().getName()
				+ " - [putDescriptionsInRequest] - " + "Exit");
	}

	/**
	 * This method is used to export the currency Interest
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String exportCurrencyInterest() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Method's local variable declaration */
		// Variable to hold collEntity
		ArrayList<CurrencyInterest> collEntity = null;
		// Variable to hold currencyInterestList
		Collection<CurrencyInterest> currencyInterestList = null;
		// Variable to hold hostId
		String hostId = null;
		// Variable to hold entityId
		String entityId = null;
		// Variable to hold currencyCode
		String currencyCode = null;
		// Variable to hold fromDateAsString
		String fromDateAsString = null;
		// Variable to hold toDateAsString
		String toDateAsString = null;
		// Variable to hold interestRateDateAsString
		String interestRateDateAsString = null;
		// Variable to hold accessInd
		int accessInd = 0;
		// Variable to hold exportType
		String exportType = null;
		// Variable to hold fileName
		String fileName = null;
		// Variable to hold titleSuffix
		String titleSuffix = null;
		/* Class instance declaration */
		CurrencyInterest currencyInterest = null;
		// Variable to hold dyForm
		// DynaValidatorForm dyForm = null;
		/* To set the column columnData */
		ArrayList<ColumnDTO> columnData = null;
		// Variable to hold currencyInterestCode
		String currencyInterestCode = null;
		// Variable to hold cDTO
		ColumnDTO cDTO = null;
		// Variable to hold filterData
		ArrayList<FilterDTO> filterData = null;
		// Variable to hold fDTO
		FilterDTO fDTO = null;
		// Variable to hold hxInt
		Obj2XmlCurrencyInterest hxInt = null;
		// Variable to hold excelGen
		Obj2XlsImpl excelGen = null;
		// Variable to hold pdfGen
		Obj2PdfImpl pdfGen = null;
		// Variable to hold csvGen
		Obj2CsvImpl csvGen = null;
		// Variable to hold csvResponse
		String csvResponse = null;
		// Variable to hold defaultCurrencyCode
		String defaultCurrencyCode = null;
		//
		ArrayList<ArrayList<ExportObject>> data = null;
		try {

			log.debug(this.getClass().getName()
					+ "- [exportCurrencyInterest] - Entry");
			/* Read the export type from the request */
			exportType = request.getParameter("exportType");

			currencyInterest = getCurrencyInterest();





			defaultCurrencyCode = "All";
			/* Read the host id from Swtutil file */
			hostId = SwtUtil.getCurrentHostId();
			/* Retrieve the entityid,currency code from request */
			entityId = request.getParameter("entityCode");
			currencyCode = request.getParameter("selectedCurrencyCode");

			/* Condition to check entity id is null */
			if ((entityId == null) || (entityId.trim().length() <= 0)) {
				/* Retrieve user's current entity from session */
				entityId = SwtUtil.getUserCurrentEntity(request.getSession());
				/* Setting user's selected entity */
				currencyInterest.getId().setEntityId(entityId);
			}
			/* Used to put the entity list in request */
			putEntityListInReq(request);
			/* Reading currency code from request */
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				{
					currencyCode = "All";
				}
			} else {
				currencyCode = request
						.getParameter("currencyInterest.id.currencyCode");
			}
			/* Reading From date from request */
			fromDateAsString = request
					.getParameter("currencyInterest.fromDateAsString");
			/* Reading to date from request */
			toDateAsString = request
					.getParameter("currencyInterest.toDateAsString");
			/* Retrieve the currency code from bean Class */
			currencyInterestCode = currencyInterest.getId().getCurrencyCode();
			/* Condition to check currency code is null */
			if ((currencyInterestCode == null)
					|| (currencyInterestCode.trim().length() <= 0)) {
				/* Display all the currency */
				currencyInterestCode = "ALL";
				/* Retrieve the user's domestic currency from Swtutil File */
				currencyInterestCode = SwtUtil.getDomesticCurrencyForUser(
						request, hostId, entityId);
				/* Setting the currency code from Bean class */
				currencyInterest.getId().setCurrencyCode(currencyInterestCode);
			}
			/* Retrieve and Store the user's menu,entity and currency group */
			accessInd = SwtUtil.getMenuEntityCurrGrpAccess(request, entityId,
					null);
			/* Condition to check for access rights */
			if (accessInd == 0) {
				/* This is used to set the button status for full access */
				setButtonStatus(request, SwtConstants.STR_TRUE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_FULL_ACCESS + "");
			} else {
				/* This is used to set the button status for view access */
				setButtonStatus(request, SwtConstants.STR_FALSE,
						SwtConstants.STR_FALSE, SwtConstants.STR_FALSE);
				request.setAttribute("EntityAccess",
						SwtConstants.ENTITY_READ_ACCESS + "");
			}
			/* Condition to check from date is null */
			if (fromDateAsString == null || fromDateAsString.equals("")) {
				/* Retrieve the system date from swtutil file and set From Date */
				currencyInterest.setFromDateAsString(SwtUtil
						.getSystemDateString());
			} else {
				/* Condition to check from date length greater than zero */
				if (fromDateAsString.length() > 0) {
					currencyInterest.setFromDateAsString(fromDateAsString);

				} else {
					currencyInterest.setFromDateAsString(SwtUtil
							.getSystemDateString());

				}
			}
			/* Condition to check from date is null */
			if (toDateAsString == null || toDateAsString.equals("")) {
				currencyInterest.setToDateAsString(SwtUtil
						.getSystemDateString());
			} else {
				if (toDateAsString.length() > 0) {
					currencyInterest.setToDateAsString(toDateAsString);
				} else {
					currencyInterest.setToDateAsString(SwtUtil
							.getSystemDateString());

				}
			}
			/* Condition to check currency id is null */
			if (currencyCode == null) {
				currencyCode = "All";
			}

			/* Setting FromDate of Currency Exchange rate using bean class */
			currencyInterest.setFromDateAsString(((currencyInterest
					.getFromDateAsString() == null) || (currencyInterest
					.getFromDateAsString().equals(""))) ? SwtUtil
					.getSystemDateString() : currencyInterest
					.getFromDateAsString().trim());
			/* Setting To Date of Currency Exchange rate using bean class */
			currencyInterest.setToDateAsString(((currencyInterest
					.getToDateAsString() == null) || (currencyInterest
					.getToDateAsString().equals(""))) ? SwtUtil
					.getSystemDateString() : currencyInterest
					.getToDateAsString());
			/* Setting currency code from bean class */
			currencyInterest.getId().setCurrencyCode(currencyCode);
			/* Setting Interest rate from bean class */
			currencyInterest
					.setInterestRateDateAsString(interestRateDateAsString);
			request.setAttribute("currencyInterestList", currencyInterestList);
			request.setAttribute("fromDateAsString", fromDateAsString);
			request.setAttribute("currencyCode", "" + currencyCode);
			request.setAttribute("toDateAsString", toDateAsString);
			request.setAttribute("entityCode", entityId);

			/*
			 * Fetches currency interest details from database by calling
			 * manager class
			 */
			currencyInterestList = currencyInterestManager
					.getCurrencyInterestList(
							request,
							entityId,
							hostId,
							(currencyCode.trim().equals(defaultCurrencyCode) ? "%"
									: currencyCode), currencyInterest
									.getFromDateAsString().trim(),
							currencyInterest.getToDateAsString());
			/* Store it in ArrayList object */
			if (currencyInterestList != null) {
				collEntity = (ArrayList<CurrencyInterest>) currencyInterestList;
			}

			/* To set the column headings */
			columnData = new ArrayList<ColumnDTO>();
			cDTO = new ColumnDTO();
			cDTO.setHeading("Ccy");
			cDTO.setType("str");
			cDTO.setDataElement("ccy");
			columnData.add(cDTO);
			cDTO = new ColumnDTO();
			cDTO.setHeading("Name");
			cDTO.setType("str");
			cDTO.setDataElement("name");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Date");
			cDTO.setType("num");
			cDTO.setDataElement("date");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Rate");
			cDTO.setType("num");
			cDTO.setDataElement("rate");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("Update Date/Time");
			cDTO.setType("num");
			cDTO.setDataElement("updatedate");
			columnData.add(cDTO);

			cDTO = new ColumnDTO();
			cDTO.setHeading("User");
			cDTO.setType("str");
			cDTO.setDataElement("user");
			columnData.add(cDTO);

			/* Set and display the data in defined format */
			filterData = new ArrayList<FilterDTO>();
			fDTO = new FilterDTO();

			fDTO.setName("Entity");
			fDTO.setValue(entityId);
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("Currency");
			fDTO.setValue(currencyCode);
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("From");
			fDTO.setValue(currencyInterest.getFromDateAsString());
			filterData.add(fDTO);

			fDTO = new FilterDTO();
			fDTO.setName("To");
			fDTO.setValue(currencyInterest.getToDateAsString());
			filterData.add(fDTO);

			/* Gets the Screen name from request */
			fileName = request.getParameter("screen").replaceAll(" ", "");
			titleSuffix = PropertiesFileLoader.getInstance()
					.getPropertiesValue("windows.title.suffix");
			/*
			 * Start Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			/* check whether titleSuffix is null and set empty string */
			if (titleSuffix == null) {
				titleSuffix = "";
			}
			hxInt = new Obj2XmlCurrencyInterest();

			data  = hxInt.getExportData(columnData
					, filterData, (ArrayList) collEntity);

			/* To export the data in PDF,Excel and CSV format */
			if (exportType.equalsIgnoreCase("excel")) {
				excelGen = new Obj2XlsImpl();
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate()+ ".xls");
				excelGen.convertObject(request, response, columnData
						, filterData, data, null,null, fileName);
			} else if (exportType.equalsIgnoreCase("pdf")) {

				pdfGen = new Obj2PdfImpl();
				response.setContentType("application/pdf");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".pdf");
				pdfGen.convertObject(request, response, columnData
						, filterData, data, null,null, fileName);
			} else if (exportType.equalsIgnoreCase("csv")) {

				csvGen = new Obj2CsvImpl();
				response.setContentType("application/vnd.ms-excel");
				response.setHeader("Content-disposition",
						"attachment; filename=" + fileName + titleSuffix + "_"
								+ SwtUtil.FormatCurrentDate() + ".csv");
				csvResponse = csvGen.convertObject(request, columnData
						, filterData, data, null,null, fileName);
				/*
				 * End Code modified by Chidambaranathan for include timestamp
				 * in the export function for Mantis_1513 on 04-Aug-2011
				 */
				try {
					response.getOutputStream().print(csvResponse);

				} catch (IOException e) {
					log.error(this.getClass().getName()
							+ "- [exportCurrencyInterest] - IOException "
							+ e.getMessage());
					throw new SwtException(e.getMessage());
				}
			}
		} catch (SwtException swtexp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [exportCurrencyInterest] method : - "
							+ swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [exportCurrencyInterest] method : - "
							+ exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance()
					.handleException(exp, "exportCurrencyInterest",
							CurrencyInterestAction.class), request, "");
		} finally {
			log.debug(this.getClass().getName()
					+ "- [exportCurrencyInterest] - Exit");
			// Nullifying the already created objects
			collEntity = null;
			currencyInterestList = null;
			hostId = null;
			entityId = null;
			currencyCode = null;
			fromDateAsString = null;
			toDateAsString = null;
			interestRateDateAsString = null;
			exportType = null;
			fileName = null;
			titleSuffix = null;
			columnData = null;
			currencyInterestCode = null;
			cDTO = null;
			filterData = null;
			fDTO = null;
			hxInt = null;
			excelGen = null;
			pdfGen = null;
			csvGen = null;
			csvResponse = null;
		}
		return null;

	}
}
