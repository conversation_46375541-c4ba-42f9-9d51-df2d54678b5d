/**
 * @(#)AcctMaintenance.java 1.0 / Dec 20, 2005
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.math.BigDecimal;
import java.util.*;

import org.swallow.model.BaseObject;

/**
 * This class contains getters and setters for Account Maintenance screen
 */
public class AcctMaintenance extends BaseObject implements
		org.swallow.model.AuditComponent {

	// Default version id
	private static final long serialVersionUID = 1L;
	// To hold short name (key) and its description (value)
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();

	/**
	 * Static block puts short name and its description in a map
	 */
	static {
		logTable.put("acctname", "Account Name");
		logTable.put("currcode", "Currency Code");
		logTable.put("acctbiccode", "Account BIC Id");
		logTable.put("acctIBAN", "Account IBAN");
		logTable.put("corresacccode", "Correspondent Account Id");
		logTable.put("glcode", "GL Id");
		logTable.put("acctextraid", "EXTRA Id");
		logTable.put("accttype", "Account Type");
		logTable.put("acctstatusflg", "Account Status Flag");
		logTable.put("holidaycalendar", "Holiday Country");
		logTable.put("minacctcode", "Main Account Id");
		logTable.put("acctlevel", "Account Level");
		logTable.put("acctgrpflg", "Account Category");
		logTable.put("mansweepflg", "Manual Sweeping Flag");
		logTable.put("autoswpswitch", "Auto Sweeping Switch");
		logTable.put("eodSweeptime", "EOD Sweep Time");
		logTable.put("intraDaySweeptime", "Intraday Sweep Time");
		logTable.put("minseepamt", "Min Sweep Amount");
		logTable.put("maxsweepamte", "Max Sweep Amount");
		logTable.put("swpdays", "Sweep Days");
		logTable.put("tgtbalsign", "EOD Target Balance Sign");
		logTable.put("newcrfrId", "New Cr Format Id");
		logTable.put("newdrfrId", "New Dr Format Id");
		logTable.put("currcreditrate", "Current Credit Rate");
		logTable.put("curroverdraftrate", "Current Overdraft Rate");
		logTable.put("cutoff", "Cut Off");
		logTable.put("sweepbookcode", "Sweep Bookcode");
		logTable.put("targetbalance", "EOD Target Balance");
		logTable.put("sweepCode", "Sweep Code");
		logTable.put("autoOpenUnsettled", "Auto Open Unsettled");
		logTable.put("autoOpenUnexpected", "Auto Open Unexpected");
		logTable.put("sweepFrmbal", "Sweep From Balance");
		logTable.put("allPreAdviceEntity", "Allow Pre-Advice Entry");
		/* Start:Code Modified for MAntis 1690 by sandeepkumar on 5-oct-2012 */
		logTable.put("eodMinseepamt", "Intra-Day Min Sweep Amount");
		logTable.put("eodTargetbalance", "Intra-Day Target Balance");
		logTable.put("subAcctim", "Sub-Account Timing");
		logTable.put("acctClass","Account Class");
		logTable.put("acctMonitorSum","Monitor Sum");
		logTable.put("acctNewCrExternal","External CR Message Format");
		logTable.put("acctNewCrInternal","Internal CR Message Format");
		logTable.put("acctNewDrExternal","External DR Message Format");			
		logTable.put("acctNewDrInternal","Internal DR Message Format");
		logTable.put("creditExternalInter","Intermediary CR Msg Format");
		logTable.put("debitExternalInter","Intermediary DR Msg Format");
		logTable.put("acctPriorityOrder","Account Priority Order");
		logTable.put("aggAccount","Aggregate Account Flag");
		logTable.put("acctEmailAddr","Email Address");
		logTable.put("acctContactName","Contact Name");
		logTable.put("acctPhone","Phone Number");
		logTable.put("externalSOD","Back Val Adj External SOD");
		logTable.put("forecastSOD","Back Val Adj Prediction SOD");
		logTable.put("futureBalances","Future Balances");
		logTable.put("linkAccID","Linked Account ID");
		logTable.put("monitor","Display in Monitor");
		logTable.put("primaryExternal","Primary External Balance");
		logTable.put("primaryForecast","Primary Predict Balance");
		logTable.put("secondaryExternal","Secondary External Balance");
		logTable.put("secondaryForecast","Secondary Predict Balance");
		logTable.put("eodTgtBalsign","Intra-day Target Balance Sign");
		logTable.put("isIlmLiqContributor","Is Ilm Liquidity Contributor");
		logTable.put("isIlmCustomerAccount","Is Ilm Customer Account");
		logTable.put("isIlmCentralBankMember","Is Ilm Central Bank Member");
		logTable.put("accountPartyId","Account Party Id");
		logTable.put("defaultSettleMethod","default Settlement Method");
		logTable.put("fmi","Financial Markt Infrastructure");
		logTable.put("ThisEntityInclBalFlag","This Entity Incl Bal Flag");
		logTable.put("thisEntityInclFrom","This Entity Incl From");
		logTable.put("thisEntityInclTo","This Entity Incl To");
		logTable.put("servicingEntityId","Servicing Entity ID");
		logTable.put("accNameInSvcEntity","Acct Name In Serv Entity");
		logTable.put("svcEntityInclBalFlag","Serving Entity Incl Bal Flag");
		logTable.put("svcEntityInclFrom","Serving Entity Incl From");
		logTable.put("svcEntityInclTo","Serving Entity Incl To");
		/* End:Code Modified for MAntis 1690 by sandeepkumar on 5-oct-2012 */
	}
	// Variable Declaration for id
	private Id id = new Id();
	// Variable Declaration for updateUser
	private String updateUser = null;
	// Variable Declaration for updateDate
	private Date updateDate = new Date();
	// Variable Declaration for targetbalance
	private Double targetbalance = null;
	// Variable Declaration for targetbalanceasString
	private String targetbalanceasString = null;
	// Variable Declaration for eodTargetbalanceasString
	private String eodTargetbalanceasString = null;
	// Variable Declaration for acctname
	private String acctname = null;
	// Variable Declaration for currcode
	private String currcode = null;
	// Variable Declaration for statusflag
	private String statusflag = null;
	// Variable Declaration for entity
	private Entity entity = null;
	// Variable Declaration for acctbiccode
	private String acctbiccode = null;
	// Variable Declaration for corresacccode
	private String corresacccode = null;
	// Variable Declaration for glcode
	private String glcode = null;
	// Variable Declaration for accttype
	private String accttype = null;
	// Variable Declaration for acctstatusflg
	private String acctstatusflg = null;
	// Variable Declaration for holidaycalendar
	private String holidaycalendar = null;
	// Variable Declaration for minacctcode
	private String minacctcode = null;
	// Variable Declaration for acctlevel
	private String acctlevel = null;
	// Variable Declaration for acctgrpflg
	private String acctgrpflg = null;
	// Variable Declaration for mansweepflg
	private String mansweepflg = null;
	// Variable Declaration for autoswpswitch
	private String autoswpswitch = null;
	// Variable Declaration for eodSweeptime
	private String eodSweeptime = null;
	// Variable Declaration for intraDaySweeptime
	private String intraDaySweeptime = null;
	// Variable Declaration for minseepamt
	private Double minseepamt = null;
	// Variable Declaration for minseepamtasString
	private String minseepamtasString = null;
	// Variable Declaration for eodMinseepamtasString
	private String eodMinseepamtasString = null;
	// Variable Declaration for maxsweepamte
	private Double maxsweepamte = null;
	// Variable Declaration for maxsweepamteasString
	private String maxsweepamteasString = null;
	/*code modified by sandeepkumar for mantis 1690 */
	// Variable Declaration for swpdays
	private Integer swpdays = 0;
	// Variable Declaration for tgtbalsign
	private String tgtbalsign = null;
	// Variable Declaration for eodTgtBalsign
	private String eodTgtBalsign = null;
	// Variable Declaration for newcrfrId
	private String newcrfrId = null;
	// Variable Declaration for newdrfrId
	private String newdrfrId = null;
	// Variable Declaration for currcreditrate
	private Double currcreditrate = null;
	// Variable Declaration for currcreditrateasString
	private String currcreditrateasString = null;
	// Variable Declaration for curroverdraftrate
	private Double curroverdraftrate = null;
	// Variable Declaration for curroverdraftrateasString
	private String curroverdraftrateasString = null;
	// Variable Declaration for cutoff
	private String cutoff = null;
	// Variable Declaration for sweepbookcode
	private String sweepbookcode = null;
	// Variable Declaration for monitor
	private String monitor = null;
	// Variable Declaration for acctClass
	private String acctClass = null;
	// Variable Declaration for linkAccID
	private String linkAccID = null;
	// Variable Declaration for acctMonitorSum
	private String acctMonitorSum = null;
	// Variable Declaration for sweepFrmbal
	private String sweepFrmbal = null;
	// Variable Declaration for acctPriorityOrder
	private Integer acctPriorityOrder = null;
	// Variable Declaration for acctContactName
	private String acctContactName = null;
	// Variable Declaration for acctPhone
	private String acctPhone = null;
	// Variable Declaration for acctEmailAddr
	private String acctEmailAddr = null;
	// Variable Declaration for acctNewCrInternal
	private String acctNewCrInternal = null;
	// Variable Declaration for acctNewCrExternal
	private String acctNewCrExternal = null;
	// Variable Declaration for acctNewDrInternal
	private String acctNewDrInternal = null;
	// Variable Declaration for acctNewDrExternal
	private String acctNewDrExternal = null;
	// Variable Declaration for sweepCode
	private String sweepCode = null;
	// Displays "infinite" for infinite numbers
	private String latestCreditRate = null;
	// Variable Declaration for latestOverDraftRate
	private String latestOverDraftRate = null;
	// Variable Declaration for acctextraid
	private String acctextraid = null;
	// Variable Declaration for forecastSOD
	private String forecastSOD = null;
	// Variable Declaration for externalSOD
	private String externalSOD = null;
	// Variable Declaration for primaryForecast
	private String primaryForecast = null;
	// Variable Declaration for primaryExternal
	private String primaryExternal = null;
	// Variable Declaration for secondaryForecast
	private String secondaryForecast = null;
	// Variable Declaration for secondaryExternal
	private String secondaryExternal = null;
	// Variable Declaration for futureBalances
	private String futureBalances = null;
	// Variable Declaration for subAcctim
	private String subAcctim = null;
	// Variable Declaration for eodTargetbalance
	private Double eodTargetbalance = null;
	// Variable Declaration for eodMinseepamt
	private Double eodMinseepamt = null;
	// Variable Declaration for latestInterestDateRate
	private String latestInterestDateRate = null;
	// Variable Declaration for autoOpenUnsettled
	private String autoOpenUnsettled = null;
	// Variable Declaration for autoOpenUnexpected
	private String autoOpenUnexpected = null;
	// Variable Declaration for allPreAdviceEntity
	private String allPreAdviceEntity = null;
	// Variable Declaration for creditExternalInter
	private String creditExternalInter = null;
	// Variable Declaration for debitExternalInter
	private String debitExternalInter = null;
	// Variable Declaration for aggAccount
	private String aggAccount = null;
	// Variable Declaration for archiveData
	private String archiveData = null;
	// Variable to hold the currAccess
	private int currAccess = 0;
	// Variable Declaration for Account IBAN
	private String acctIBAN = null;
	// Determines if the account's funds contribute as a source of liquidity for ILM purposes
	private String isIlmLiqContributor = null;
	// Indicates whether the account is a customer for which the bank provides correspondent banking services
	private String isIlmCustomerAccount = null;
	// Indicates whether the account is related to the central bank for this entity/currency
	private String isIlmCentralBankMember = null;
	// Specifies a party related to the account for the purposes of, for example, identifying the customer party
	private String accountPartyId = null;
	// Specifies the default settelement method A combo-box should be provided, listing allowed values from the table (P_MISC_PARAMS) of allowed settlement method values 
	private String defaultSettleMethod = null;
	// Specifies the Financial Market Infrastructure relevant to this account.
	private String fmi = null;
	private String thisEntityInclBalFlag = null;
	private String thisEntityInclFrom = null;
	private String thisEntityInclTo = null;
	private String servicingEntityId = null;
	private String accNameInSvcEntity = null;
	private String svcEntityInclBalFlag = null;
	private String svcEntityInclFrom = null;
	private String svcEntityInclTo = null;

	
	
	private Set<AccountSpecificSweepFormat> accountSweepFormats;

	/**
	 * Class for composite key
	 */
	public static class Id extends BaseObject {
		// Default version id
		private static final long serialVersionUID = 1L;
		// Host Id
		private String hostId = null;
		// Entity Id
		private String entityId = null;
		// Account Id
		private String accountId = null;

		/**
		 * Default constructor
		 */
		public Id() {
		}

		/**
		 * Constructor, sets properties
		 * 
		 * @param hostId
		 * @param entityId
		 * @param accountId
		 */
		public Id(String hostId, String entityId, String accountId) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.accountId = accountId;
		}

		/**
		 * Getter method of hostId
		 * 
		 * @return the hostId
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * Setter method of hostId
		 * 
		 * @param hostId
		 *            the hostId to set
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}

		/**
		 * Getter method of entityId
		 * 
		 * @return the entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * Setter method of entityId
		 * 
		 * @param entityId
		 *            the entityId to set
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * Getter method of accountId
		 * 
		 * @return the accountId
		 */
		public String getAccountId() {
			return accountId;
		}

		/**
		 * Setter method of accountId
		 * 
		 * @param accountId
		 *            the accountId to set
		 */
		public void setAccountId(String accountId) {
			this.accountId = accountId;
		}

		 @Override
		    public boolean equals(Object o) {
		        if (this == o) return true;
		        if (o == null || getClass() != o.getClass()) return false;
		        Id id = (Id) o;
		        return Objects.equals(hostId, id.hostId) &&
		                Objects.equals(entityId, id.entityId) &&
		                Objects.equals(accountId, id.accountId);
		    }
		 
		 
		 @Override
		    public int hashCode() {
		        return Objects.hash(hostId, entityId, accountId);
		    }
		 

	}

	/**
	 * Getter method of id
	 * 
	 * @return the id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method of id
	 * 
	 * @param id
	 *            the id to set
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * Getter method of updateUser
	 * 
	 * @return the updateUser
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * Setter method of updateUser
	 * 
	 * @param updateUser
	 *            the updateUser to set
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * Getter method of updateDate
	 * 
	 * @return the updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * Setter method of updateDate
	 * 
	 * @param updateDate
	 *            the updateDate to set
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * Getter method of targetbalance
	 * 
	 * @return the targetbalance
	 */
	public Double getTargetbalance() {
		return targetbalance;
	}

	/**
	 * Setter method of targetbalance
	 * 
	 * @param targetbalance
	 *            the targetbalance to set
	 */
	public void setTargetbalance(Double targetbalance) {
		this.targetbalance = targetbalance;
	}

	/**
	 * Getter method of targetbalanceasString
	 * 
	 * @return the targetbalanceasString
	 */
	public String getTargetbalanceasString() {
		return targetbalanceasString;
	}

	/**
	 * Setter method of targetbalanceasString
	 * 
	 * @param targetbalanceasString
	 *            the targetbalanceasString to set
	 */
	public void setTargetbalanceasString(String targetbalanceasString) {
		this.targetbalanceasString = targetbalanceasString;
	}

	/**
	 * Getter method of eodTargetbalanceasString
	 * 
	 * @return the eodTargetbalanceasString
	 */
	public String getEodTargetbalanceasString() {
		return eodTargetbalanceasString;
	}

	/**
	 * Setter method of eodTargetbalanceasString
	 * 
	 * @param eodTargetbalanceasString
	 *            the eodTargetbalanceasString to set
	 */
	public void setEodTargetbalanceasString(String eodTargetbalanceasString) {
		this.eodTargetbalanceasString = eodTargetbalanceasString;
	}

	/**
	 * Getter method of acctname
	 * 
	 * @return the acctname
	 */
	public String getAcctname() {
		return acctname;
	}

	/**
	 * Setter method of acctname
	 * 
	 * @param acctname
	 *            the acctname to set
	 */
	public void setAcctname(String acctname) {
		this.acctname = acctname;
	}

	/**
	 * Getter method of currcode
	 * 
	 * @return the currcode
	 */
	public String getCurrcode() {
		return currcode;
	}

	/**
	 * Setter method of currcode
	 * 
	 * @param currcode
	 *            the currcode to set
	 */
	public void setCurrcode(String currcode) {
		this.currcode = currcode;
	}

	/**
	 * Getter method of statusflag
	 * 
	 * @return the statusflag
	 */
	public String getStatusflag() {
		return statusflag;
	}

	/**
	 * Setter method of statusflag
	 * 
	 * @param statusflag
	 *            the statusflag to set
	 */
	public void setStatusflag(String statusflag) {
		this.statusflag = statusflag;
	}

	/**
	 * Getter method of entity
	 * 
	 * @return the entity
	 */
	public Entity getEntity() {
		return entity;
	}

	/**
	 * Setter method of entity
	 * 
	 * @param entity
	 *            the entity to set
	 */
	public void setEntity(Entity entity) {
		this.entity = entity;
	}

	/**
	 * Getter method of acctbiccode
	 * 
	 * @return the acctbiccode
	 */
	public String getAcctbiccode() {
		return acctbiccode;
	}

	/**
	 * Setter method of acctbiccode
	 * 
	 * @param acctbiccode
	 *            the acctbiccode to set
	 */
	public void setAcctbiccode(String acctbiccode) {
		this.acctbiccode = acctbiccode;
	}

	/**
	 * Getter method of corresacccode
	 * 
	 * @return the corresacccode
	 */
	public String getCorresacccode() {
		return corresacccode;
	}

	/**
	 * Setter method of corresacccode
	 * 
	 * @param corresacccode
	 *            the corresacccode to set
	 */
	public void setCorresacccode(String corresacccode) {
		this.corresacccode = corresacccode;
	}

	/**
	 * Getter method of glcode
	 * 
	 * @return the glcode
	 */
	public String getGlcode() {
		return glcode;
	}

	/**
	 * Setter method of glcode
	 * 
	 * @param glcode
	 *            the glcode to set
	 */
	public void setGlcode(String glcode) {
		this.glcode = glcode;
	}

	/**
	 * Getter method of accttype
	 * 
	 * @return the accttype
	 */
	public String getAccttype() {
		return accttype;
	}

	/**
	 * Setter method of accttype
	 * 
	 * @param accttype
	 *            the accttype to set
	 */
	public void setAccttype(String accttype) {
		this.accttype = accttype;
	}

	/**
	 * Getter method of acctstatusflg
	 * 
	 * @return the acctstatusflg
	 */
	public String getAcctstatusflg() {
		return acctstatusflg;
	}

	/**
	 * Setter method of acctstatusflg
	 * 
	 * @param acctstatusflg
	 *            the acctstatusflg to set
	 */
	public void setAcctstatusflg(String acctstatusflg) {
		this.acctstatusflg = acctstatusflg;
	}

	/**
	 * Getter method of holidaycalendar
	 * 
	 * @return the holidaycalendar
	 */
	public String getHolidaycalendar() {
		return holidaycalendar;
	}

	/**
	 * Setter method of holidaycalendar
	 * 
	 * @param holidaycalendar
	 *            the holidaycalendar to set
	 */
	public void setHolidaycalendar(String holidaycalendar) {
		this.holidaycalendar = holidaycalendar;
	}

	/**
	 * Getter method of minacctcode
	 * 
	 * @return the minacctcode
	 */
	public String getMinacctcode() {
		return minacctcode;
	}

	/**
	 * Setter method of minacctcode
	 * 
	 * @param minacctcode
	 *            the minacctcode to set
	 */
	public void setMinacctcode(String minacctcode) {
		this.minacctcode = minacctcode;
	}

	/**
	 * Getter method of acctlevel
	 * 
	 * @return the acctlevel
	 */
	public String getAcctlevel() {
		return acctlevel;
	}

	/**
	 * Setter method of acctlevel
	 * 
	 * @param acctlevel
	 *            the acctlevel to set
	 */
	public void setAcctlevel(String acctlevel) {
		this.acctlevel = acctlevel;
	}

	/**
	 * Getter method of acctgrpflg
	 * 
	 * @return the acctgrpflg
	 */
	public String getAcctgrpflg() {
		return acctgrpflg;
	}

	/**
	 * Setter method of acctgrpflg
	 * 
	 * @param acctgrpflg
	 *            the acctgrpflg to set
	 */
	public void setAcctgrpflg(String acctgrpflg) {
		this.acctgrpflg = acctgrpflg;
	}

	/**
	 * Getter method of mansweepflg
	 * 
	 * @return the mansweepflg
	 */
	public String getMansweepflg() {
		return mansweepflg;
	}

	/**
	 * Setter method of mansweepflg
	 * 
	 * @param mansweepflg
	 *            the mansweepflg to set
	 */
	public void setMansweepflg(String mansweepflg) {
		this.mansweepflg = mansweepflg;
	}

	/**
	 * Getter method of autoswpswitch
	 * 
	 * @return the autoswpswitch
	 */
	public String getAutoswpswitch() {
		return autoswpswitch;
	}

	/**
	 * Setter method of autoswpswitch
	 * 
	 * @param autoswpswitch
	 *            the autoswpswitch to set
	 */
	public void setAutoswpswitch(String autoswpswitch) {
		this.autoswpswitch = autoswpswitch;
	}

	/**
	 * Getter method of eodSweeptime
	 * 
	 * @return the eodSweeptime
	 */
	public String getEodSweeptime() {
		return eodSweeptime;
	}

	/**
	 * Setter method of eodSweeptime
	 * 
	 * @param eodSweeptime
	 *            the eodSweeptime to set
	 */
	public void setEodSweeptime(String eodSweeptime) {
		this.eodSweeptime = eodSweeptime;
	}

	/**
	 * Getter method of intraDaySweeptime
	 * 
	 * @return the intraDaySweeptime
	 */
	public String getIntraDaySweeptime() {
		return intraDaySweeptime;
	}

	/**
	 * Setter method of intraDaySweeptime
	 * 
	 * @param intraDaySweeptime
	 *            the intraDaySweeptime to set
	 */
	public void setIntraDaySweeptime(String intraDaySweeptime) {
		this.intraDaySweeptime = intraDaySweeptime;
	}

	/**
	 * Getter method of minseepamt
	 * 
	 * @return the minseepamt
	 */
	public Double getMinseepamt() {
		return minseepamt;
	}

	/**
	 * Setter method of minseepamt
	 * 
	 * @param minseepamt
	 *            the minseepamt to set
	 */
	public void setMinseepamt(Double minseepamt) {
		this.minseepamt = minseepamt;
	}

	/**
	 * Getter method of minseepamtasString
	 * 
	 * @return the minseepamtasString
	 */
	public String getMinseepamtasString() {
		return minseepamtasString;
	}

	/**
	 * Setter method of minseepamtasString
	 * 
	 * @param minseepamtasString
	 *            the minseepamtasString to set
	 */
	public void setMinseepamtasString(String minseepamtasString) {
		this.minseepamtasString = minseepamtasString;
	}

	/**
	 * Getter method of eodMinseepamtasString
	 * 
	 * @return the eodMinseepamtasString
	 */
	public String getEodMinseepamtasString() {
		return eodMinseepamtasString;
	}

	/**
	 * Setter method of eodMinseepamtasString
	 * 
	 * @param eodMinseepamtasString
	 *            the eodMinseepamtasString to set
	 */
	public void setEodMinseepamtasString(String eodMinseepamtasString) {
		this.eodMinseepamtasString = eodMinseepamtasString;
	}

	/**
	 * Getter method of maxsweepamte
	 * 
	 * @return the maxsweepamte
	 */
	public Double getMaxsweepamte() {
		return maxsweepamte;
	}

	/**
	 * Setter method of maxsweepamte
	 * 
	 * @param maxsweepamte
	 *            the maxsweepamte to set
	 */
	public void setMaxsweepamte(Double maxsweepamte) {
		this.maxsweepamte = maxsweepamte;
	}

	/**
	 * Getter method of maxsweepamteasString
	 * 
	 * @return the maxsweepamteasString
	 */
	public String getMaxsweepamteasString() {
		return maxsweepamteasString;
	}

	/**
	 * Setter method of maxsweepamteasString
	 * 
	 * @param maxsweepamteasString
	 *            the maxsweepamteasString to set
	 */
	public void setMaxsweepamteasString(String maxsweepamteasString) {
		this.maxsweepamteasString = maxsweepamteasString;
	}
	/*
	 * Start:Code Modified for MAntis 1690 by sandeepkumar on 3-oct-2012 :Sweep
	 * days change are not logged in Maintenance log
	 */
	/**
	 * Getter method of swpdays
	 * 
	 * @return the swpdays
	 */
	public Integer getSwpdays() {
		return swpdays;
	}

	/**
	 * Setter method of swpdays
	 * 
	 * @param swpdays
	 *            the swpdays to set
	 */
	public void setSwpdays(Integer swpdays) {
		this.swpdays = swpdays;
	}
	/*
	 * End:Code Modified for MAntis 1690 by sandeepkumar on 3-oct-2012 :Sweep
	 * days change are not logged in Maintenance log
	 */
	/**
	 * Getter method of tgtbalsign
	 * 
	 * @return the tgtbalsign
	 */
	public String getTgtbalsign() {
		return tgtbalsign;
	}

	/**
	 * Setter method of tgtbalsign
	 * 
	 * @param tgtbalsign
	 *            the tgtbalsign to set
	 */
	public void setTgtbalsign(String tgtbalsign) {
		this.tgtbalsign = tgtbalsign;
	}

	/**
	 * Getter method of eodTgtBalsign
	 * 
	 * @return the eodTgtBalsign
	 */
	public String getEodTgtBalsign() {
		return eodTgtBalsign;
	}

	/**
	 * Setter method of eodTgtBalsign
	 * 
	 * @param eodTgtBalsign
	 *            the eodTgtBalsign to set
	 */
	public void setEodTgtBalsign(String eodTgtBalsign) {
		this.eodTgtBalsign = eodTgtBalsign;
	}

	/**
	 * Getter method of newcrfrId
	 * 
	 * @return the newcrfrId
	 */
	public String getNewcrfrId() {
		return newcrfrId;
	}

	/**
	 * Setter method of newcrfrId
	 * 
	 * @param newcrfrId
	 *            the newcrfrId to set
	 */
	public void setNewcrfrId(String newcrfrId) {
		this.newcrfrId = newcrfrId;
	}

	/**
	 * Getter method of newdrfrId
	 * 
	 * @return the newdrfrId
	 */
	public String getNewdrfrId() {
		return newdrfrId;
	}

	/**
	 * Setter method of newdrfrId
	 * 
	 * @param newdrfrId
	 *            the newdrfrId to set
	 */
	public void setNewdrfrId(String newdrfrId) {
		this.newdrfrId = newdrfrId;
	}

	/**
	 * Getter method of currcreditrate
	 * 
	 * @return the currcreditrate
	 */
	public Double getCurrcreditrate() {
		return currcreditrate;
	}

	/**
	 * Setter method of currcreditrate
	 * 
	 * @param currcreditrate
	 *            the currcreditrate to set
	 */
	public void setCurrcreditrate(Double currcreditrate) {
		this.currcreditrate = currcreditrate;
	}

	/**
	 * Getter method of currcreditrateasString
	 * 
	 * @return the currcreditrateasString
	 */
	public String getCurrcreditrateasString() {
		return currcreditrateasString;
	}

	/**
	 * Setter method of currcreditrateasString
	 * 
	 * @param currcreditrateasString
	 *            the currcreditrateasString to set
	 */
	public void setCurrcreditrateasString(String currcreditrateasString) {
		this.currcreditrateasString = currcreditrateasString;
	}

	/**
	 * Getter method of curroverdraftrate
	 * 
	 * @return the curroverdraftrate
	 */
	public Double getCurroverdraftrate() {
		return curroverdraftrate;
	}

	/**
	 * Setter method of curroverdraftrate
	 * 
	 * @param curroverdraftrate
	 *            the curroverdraftrate to set
	 */
	public void setCurroverdraftrate(Double curroverdraftrate) {
		this.curroverdraftrate = curroverdraftrate;
	}

	/**
	 * Getter method of curroverdraftrateasString
	 * 
	 * @return the curroverdraftrateasString
	 */
	public String getCurroverdraftrateasString() {
		return curroverdraftrateasString;
	}

	/**
	 * Setter method of curroverdraftrateasString
	 * 
	 * @param curroverdraftrateasString
	 *            the curroverdraftrateasString to set
	 */
	public void setCurroverdraftrateasString(String curroverdraftrateasString) {
		this.curroverdraftrateasString = curroverdraftrateasString;
	}

	/**
	 * Getter method of cutoff
	 * 
	 * @return the cutoff
	 */
	public String getCutoff() {
		return cutoff;
	}

	/**
	 * Setter method of cutoff
	 * 
	 * @param cutoff
	 *            the cutoff to set
	 */
	public void setCutoff(String cutoff) {
		this.cutoff = cutoff;
	}

	/**
	 * Getter method of sweepbookcode
	 * 
	 * @return the sweepbookcode
	 */
	public String getSweepbookcode() {
		return sweepbookcode;
	}

	/**
	 * Setter method of sweepbookcode
	 * 
	 * @param sweepbookcode
	 *            the sweepbookcode to set
	 */
	public void setSweepbookcode(String sweepbookcode) {
		this.sweepbookcode = sweepbookcode;
	}

	/**
	 * Getter method of monitor
	 * 
	 * @return the monitor
	 */
	public String getMonitor() {
		return monitor;
	}

	/**
	 * Setter method of monitor
	 * 
	 * @param monitor
	 *            the monitor to set
	 */
	public void setMonitor(String monitor) {
		this.monitor = monitor;
	}

	/**
	 * Getter method of acctClass
	 * 
	 * @return the acctClass
	 */
	public String getAcctClass() {
		return acctClass;
	}

	/**
	 * Setter method of acctClass
	 * 
	 * @param acctClass
	 *            the acctClass to set
	 */
	public void setAcctClass(String acctClass) {
		this.acctClass = acctClass;
	}

	/**
	 * Getter method of linkAccID
	 * 
	 * @return the linkAccID
	 */
	public String getLinkAccID() {
		return linkAccID;
	}

	/**
	 * Setter method of linkAccID
	 * 
	 * @param linkAccID
	 *            the linkAccID to set
	 */
	public void setLinkAccID(String linkAccID) {
		this.linkAccID = linkAccID;
	}

	/**
	 * Getter method of acctMonitorSum
	 * 
	 * @return the acctMonitorSum
	 */
	public String getAcctMonitorSum() {
		return acctMonitorSum;
	}

	/**
	 * Setter method of acctMonitorSum
	 * 
	 * @param acctMonitorSum
	 *            the acctMonitorSum to set
	 */
	public void setAcctMonitorSum(String acctMonitorSum) {
		this.acctMonitorSum = acctMonitorSum;
	}

	/**
	 * Getter method of sweepFrmbal
	 * 
	 * @return the sweepFrmbal
	 */
	public String getSweepFrmbal() {
		return sweepFrmbal;
	}

	/**
	 * Setter method of sweepFrmbal
	 * 
	 * @param sweepFrmbal
	 *            the sweepFrmbal to set
	 */
	public void setSweepFrmbal(String sweepFrmbal) {
		this.sweepFrmbal = sweepFrmbal;
	}

	/**
	 * Getter method of acctPriorityOrder
	 * 
	 * @return the acctPriorityOrder
	 */
	public Integer getAcctPriorityOrder() {
		return acctPriorityOrder;
	}

	/**
	 * Setter method of acctPriorityOrder
	 * 
	 * @param acctPriorityOrder
	 *            the acctPriorityOrder to set
	 */
	public void setAcctPriorityOrder(Integer acctPriorityOrder) {
		this.acctPriorityOrder = acctPriorityOrder;
	}

	/**
	 * Getter method of acctContactName
	 * 
	 * @return the acctContactName
	 */
	public String getAcctContactName() {
		return acctContactName;
	}

	/**
	 * Setter method of acctContactName
	 * 
	 * @param acctContactName
	 *            the acctContactName to set
	 */
	public void setAcctContactName(String acctContactName) {
		this.acctContactName = acctContactName;
	}

	/**
	 * Getter method of acctPhone
	 * 
	 * @return the acctPhone
	 */
	public String getAcctPhone() {
		return acctPhone;
	}

	/**
	 * Setter method of acctPhone
	 * 
	 * @param acctPhone
	 *            the acctPhone to set
	 */
	public void setAcctPhone(String acctPhone) {
		this.acctPhone = acctPhone;
	}

	/**
	 * Getter method of acctEmailAddr
	 * 
	 * @return the acctEmailAddr
	 */
	public String getAcctEmailAddr() {
		return acctEmailAddr;
	}

	/**
	 * Setter method of acctEmailAddr
	 * 
	 * @param acctEmailAddr
	 *            the acctEmailAddr to set
	 */
	public void setAcctEmailAddr(String acctEmailAddr) {
		this.acctEmailAddr = acctEmailAddr;
	}

	/**
	 * Getter method of acctNewCrInternal
	 * 
	 * @return the acctNewCrInternal
	 */
	public String getAcctNewCrInternal() {
		return acctNewCrInternal;
	}

	/**
	 * Setter method of acctNewCrInternal
	 * 
	 * @param acctNewCrInternal
	 *            the acctNewCrInternal to set
	 */
	public void setAcctNewCrInternal(String acctNewCrInternal) {
		this.acctNewCrInternal = acctNewCrInternal;
	}

	/**
	 * Getter method of acctNewCrExternal
	 * 
	 * @return the acctNewCrExternal
	 */
	public String getAcctNewCrExternal() {
		return acctNewCrExternal;
	}

	/**
	 * Setter method of acctNewCrExternal
	 * 
	 * @param acctNewCrExternal
	 *            the acctNewCrExternal to set
	 */
	public void setAcctNewCrExternal(String acctNewCrExternal) {
		this.acctNewCrExternal = acctNewCrExternal;
	}

	/**
	 * Getter method of acctNewDrInternal
	 * 
	 * @return the acctNewDrInternal
	 */
	public String getAcctNewDrInternal() {
		return acctNewDrInternal;
	}

	/**
	 * Setter method of acctNewDrInternal
	 * 
	 * @param acctNewDrInternal
	 *            the acctNewDrInternal to set
	 */
	public void setAcctNewDrInternal(String acctNewDrInternal) {
		this.acctNewDrInternal = acctNewDrInternal;
	}

	/**
	 * Getter method of acctNewDrExternal
	 * 
	 * @return the acctNewDrExternal
	 */
	public String getAcctNewDrExternal() {
		return acctNewDrExternal;
	}

	/**
	 * Setter method of acctNewDrExternal
	 * 
	 * @param acctNewDrExternal
	 *            the acctNewDrExternal to set
	 */
	public void setAcctNewDrExternal(String acctNewDrExternal) {
		this.acctNewDrExternal = acctNewDrExternal;
	}

	/**
	 * Getter method of sweepCode
	 * 
	 * @return the sweepCode
	 */
	public String getSweepCode() {
		return sweepCode;
	}

	/**
	 * Setter method of sweepCode
	 * 
	 * @param sweepCode
	 *            the sweepCode to set
	 */
	public void setSweepCode(String sweepCode) {
		this.sweepCode = sweepCode;
	}

	/**
	 * Getter method of latestCreditRate
	 * 
	 * @return the latestCreditRate
	 */
	public String getLatestCreditRate() {
		return latestCreditRate;
	}

	/**
	 * Setter method of latestCreditRate
	 * 
	 * @param latestCreditRate
	 *            the latestCreditRate to set
	 */
	public void setLatestCreditRate(String latestCreditRate) {
		this.latestCreditRate = latestCreditRate;
	}

	/**
	 * Getter method of latestOverDraftRate
	 * 
	 * @return the latestOverDraftRate
	 */
	public String getLatestOverDraftRate() {
		return latestOverDraftRate;
	}

	/**
	 * Setter method of latestOverDraftRate
	 * 
	 * @param latestOverDraftRate
	 *            the latestOverDraftRate to set
	 */
	public void setLatestOverDraftRate(String latestOverDraftRate) {
		this.latestOverDraftRate = latestOverDraftRate;
	}

	/**
	 * Getter method of acctextraid
	 * 
	 * @return the acctextraid
	 */
	public String getAcctextraid() {
		return acctextraid;
	}

	/**
	 * Setter method of acctextraid
	 * 
	 * @param acctextraid
	 *            the acctextraid to set
	 */
	public void setAcctextraid(String acctextraid) {
		this.acctextraid = acctextraid;
	}

	/**
	 * Getter method of forecastSOD
	 * 
	 * @return the forecastSOD
	 */
	public String getForecastSOD() {
		return forecastSOD;
	}

	/**
	 * Setter method of forecastSOD
	 * 
	 * @param forecastSOD
	 *            the forecastSOD to set
	 */
	public void setForecastSOD(String forecastSOD) {
		this.forecastSOD = forecastSOD;
	}

	/**
	 * Getter method of externalSOD
	 * 
	 * @return the externalSOD
	 */
	public String getExternalSOD() {
		return externalSOD;
	}

	/**
	 * Setter method of externalSOD
	 * 
	 * @param externalSOD
	 *            the externalSOD to set
	 */
	public void setExternalSOD(String externalSOD) {
		this.externalSOD = externalSOD;
	}

	/**
	 * Getter method of primaryForecast
	 * 
	 * @return the primaryForecast
	 */
	public String getPrimaryForecast() {
		return primaryForecast;
	}

	/**
	 * Setter method of primaryForecast
	 * 
	 * @param primaryForecast
	 *            the primaryForecast to set
	 */
	public void setPrimaryForecast(String primaryForecast) {
		this.primaryForecast = primaryForecast;
	}

	/**
	 * Getter method of primaryExternal
	 * 
	 * @return the primaryExternal
	 */
	public String getPrimaryExternal() {
		return primaryExternal;
	}

	/**
	 * Setter method of primaryExternal
	 * 
	 * @param primaryExternal
	 *            the primaryExternal to set
	 */
	public void setPrimaryExternal(String primaryExternal) {
		this.primaryExternal = primaryExternal;
	}

	/**
	 * Getter method of secondaryForecast
	 * 
	 * @return the secondaryForecast
	 */
	public String getSecondaryForecast() {
		return secondaryForecast;
	}

	/**
	 * Setter method of secondaryForecast
	 * 
	 * @param secondaryForecast
	 *            the secondaryForecast to set
	 */
	public void setSecondaryForecast(String secondaryForecast) {
		this.secondaryForecast = secondaryForecast;
	}

	/**
	 * Getter method of secondaryExternal
	 * 
	 * @return the secondaryExternal
	 */
	public String getSecondaryExternal() {
		return secondaryExternal;
	}

	/**
	 * Setter method of secondaryExternal
	 * 
	 * @param secondaryExternal
	 *            the secondaryExternal to set
	 */
	public void setSecondaryExternal(String secondaryExternal) {
		this.secondaryExternal = secondaryExternal;
	}

	/**
	 * Getter method of futureBalances
	 * 
	 * @return the futureBalances
	 */
	public String getFutureBalances() {
		return futureBalances;
	}

	/**
	 * Setter method of futureBalances
	 * 
	 * @param futureBalances
	 *            the futureBalances to set
	 */
	public void setFutureBalances(String futureBalances) {
		this.futureBalances = futureBalances;
	}

	/**
	 * Getter method of subAcctim
	 * 
	 * @return the subAcctim
	 */
	public String getSubAcctim() {
		return subAcctim;
	}

	/**
	 * Setter method of subAcctim
	 * 
	 * @param subAcctim
	 *            the subAcctim to set
	 */
	public void setSubAcctim(String subAcctim) {
		this.subAcctim = subAcctim;
	}

	/**
	 * Getter method of eodTargetbalance
	 * 
	 * @return the eodTargetbalance
	 */
	public Double getEodTargetbalance() {
		return eodTargetbalance;
	}

	/**
	 * Setter method of eodTargetbalance
	 * 
	 * @param eodTargetbalance
	 *            the eodTargetbalance to set
	 */
	public void setEodTargetbalance(Double eodTargetbalance) {
		this.eodTargetbalance = eodTargetbalance;
	}

	/**
	 * Getter method of eodMinseepamt
	 * 
	 * @return the eodMinseepamt
	 */
	public Double getEodMinseepamt() {
		return eodMinseepamt;
	}

	/**
	 * Setter method of eodMinseepamt
	 * 
	 * @param eodMinseepamt
	 *            the eodMinseepamt to set
	 */
	public void setEodMinseepamt(Double eodMinseepamt) {
		this.eodMinseepamt = eodMinseepamt;
	}

	/**
	 * Getter method of latestInterestDateRate
	 * 
	 * @return the latestInterestDateRate
	 */
	public String getLatestInterestDateRate() {
		return latestInterestDateRate;
	}

	/**
	 * Setter method of latestInterestDateRate
	 * 
	 * @param latestInterestDateRate
	 *            the latestInterestDateRate to set
	 */
	public void setLatestInterestDateRate(String latestInterestDateRate) {
		this.latestInterestDateRate = latestInterestDateRate;
	}

	/**
	 * Getter method of autoOpenUnsettled
	 * 
	 * @return the autoOpenUnsettled
	 */
	public String getAutoOpenUnsettled() {
		return autoOpenUnsettled;
	}

	/**
	 * Setter method of autoOpenUnsettled
	 * 
	 * @param autoOpenUnsettled
	 *            the autoOpenUnsettled to set
	 */
	public void setAutoOpenUnsettled(String autoOpenUnsettled) {
		this.autoOpenUnsettled = autoOpenUnsettled;
	}

	/**
	 * Getter method of autoOpenUnexpected
	 * 
	 * @return the autoOpenUnexpected
	 */
	public String getAutoOpenUnexpected() {
		return autoOpenUnexpected;
	}

	/**
	 * Setter method of autoOpenUnexpected
	 * 
	 * @param autoOpenUnexpected
	 *            the autoOpenUnexpected to set
	 */
	public void setAutoOpenUnexpected(String autoOpenUnexpected) {
		this.autoOpenUnexpected = autoOpenUnexpected;
	}

	/**
	 * Getter method of allPreAdviceEntity
	 * 
	 * @return the allPreAdviceEntity
	 */
	public String getAllPreAdviceEntity() {
		return allPreAdviceEntity;
	}

	/**
	 * Setter method of allPreAdviceEntity
	 * 
	 * @param allPreAdviceEntity
	 *            the allPreAdviceEntity to set
	 */
	public void setAllPreAdviceEntity(String allPreAdviceEntity) {
		this.allPreAdviceEntity = allPreAdviceEntity;
	}

	/**
	 * Getter method of creditExternalInter
	 * 
	 * @return the creditExternalInter
	 */
	public String getCreditExternalInter() {
		return creditExternalInter;
	}

	/**
	 * Setter method of creditExternalInter
	 * 
	 * @param creditExternalInter
	 *            the creditExternalInter to set
	 */
	public void setCreditExternalInter(String creditExternalInter) {
		this.creditExternalInter = creditExternalInter;
	}

	/**
	 * Getter method of debitExternalInter
	 * 
	 * @return the debitExternalInter
	 */
	public String getDebitExternalInter() {
		return debitExternalInter;
	}

	/**
	 * Setter method of debitExternalInter
	 * 
	 * @param debitExternalInter
	 *            the debitExternalInter to set
	 */
	public void setDebitExternalInter(String debitExternalInter) {
		this.debitExternalInter = debitExternalInter;
	}

	/**
	 * Getter method of aggAccount
	 * 
	 * @return the aggAccount
	 */
	public String getAggAccount() {
		return aggAccount;
	}

	/**
	 * Setter method of archiveData
	 * 
	 * @param archiveData
	 *            the archiveData to set
	 */
	public void setArchiveData(String archiveData) {
		this.archiveData = archiveData;
	}
	
	/**
	 * Getter method of archiveData
	 * 
	 * @return the archiveData
	 */
	public String getArchiveData() {
		return archiveData;
	}

	/**
	 * Setter method of aggAccount
	 * 
	 * @param aggAccount
	 *            the aggAccount to set
	 */
	public void setAggAccount(String aggAccount) {
		this.aggAccount = aggAccount;
	}

	/**
	 * Getter method of CurrAccess
	 * 
	 * @return the currAccess
	 */
	public int getCurrAccess() {
		return currAccess;
	}

	/**
	 * Setter method of CurrAccess
	 * 
	 * @param currAccess
	 *            the currAccess to set
	 */
	public void setCurrAccess(int currAccess) {
		this.currAccess = currAccess;
	}

	/**
	 * Getter method of acctIBAN
	 * 
	 * @return the acctIBAN
	 */
	public String getAcctIBAN() {
		return acctIBAN;
	}

	/**
	 * Setter method of acctIBAN
	 * 
	 * @param acctIBAN
	 *            the acctIBAN to set
	 */
	public void setAcctIBAN(String acctIBAN) {
		this.acctIBAN = acctIBAN;
	}

	/**
	 * @return the isIlmLiqContributor
	 */
	public String getIsIlmLiqContributor() {
		return isIlmLiqContributor;
	}

	/**
	 * @param isIlmLiqContributor the isIlmLiqContributor to set
	 */
	public void setIsIlmLiqContributor(String isIlmLiqContributor) {
		this.isIlmLiqContributor = isIlmLiqContributor;
	}

	/**
	 * @return the isIlmCustomerAccount
	 */
	public String getIsIlmCustomerAccount() {
		return isIlmCustomerAccount;
	}

	/**
	 * @param isIlmCustomerAccount the isIlmCustomerAccount to set
	 */
	public void setIsIlmCustomerAccount(String isIlmCustomerAccount) {
		this.isIlmCustomerAccount = isIlmCustomerAccount;
	}

	/**
	 * @return the isIlmCentralBankMember
	 */
	public String getIsIlmCentralBankMember() {
		return isIlmCentralBankMember;
	}

	/**
	 * @param isIlmCentralBankMember the isIlmCentralBankMember to set
	 */
	public void setIsIlmCentralBankMember(String isIlmCentralBankMember) {
		this.isIlmCentralBankMember = isIlmCentralBankMember;
	}

	/**
	 * @return the accountPartyId
	 */
	public String getAccountPartyId() {
		return accountPartyId;
	}

	/**
	 * @param accountPartyId the accountPartyId to set
	 */
	public void setAccountPartyId(String accountPartyId) {
		this.accountPartyId = accountPartyId;
	}

	public String getDefaultSettleMethod() {
		return defaultSettleMethod;
	}

	public void setDefaultSettleMethod(String defaultSettleMethod) {
		this.defaultSettleMethod = defaultSettleMethod;
	}

	public String getFmi() {
		return fmi;
	}

	public void setFmi(String fmi) {
		this.fmi = fmi;
	}

	public String getThisEntityInclBalFlag() {
		return thisEntityInclBalFlag;
	}

	public void setThisEntityInclBalFlag(String thisEntityInclBalFlag) {
		this.thisEntityInclBalFlag = thisEntityInclBalFlag;
	}

	public String getThisEntityInclFrom() {
		return thisEntityInclFrom;
	}

	public void setThisEntityInclFrom(String thisEntityInclFrom) {
		this.thisEntityInclFrom = thisEntityInclFrom;
	}

	public String getThisEntityInclTo() {
		return thisEntityInclTo;
	}

	public void setThisEntityInclTo(String thisEntityInclTo) {
		this.thisEntityInclTo = thisEntityInclTo;
	}

	public String getServicingEntityId() {
		return servicingEntityId;
	}

	public void setServicingEntityId(String servicingEntityId) {
		this.servicingEntityId = servicingEntityId;
	}

	public String getAccNameInSvcEntity() {
		return accNameInSvcEntity;
	}

	public void setAccNameInSvcEntity(String accNameInSvcEntity) {
		this.accNameInSvcEntity = accNameInSvcEntity;
	}

	public String getSvcEntityInclBalFlag() {
		return svcEntityInclBalFlag;
	}

	public void setSvcEntityInclBalFlag(String svcEntityInclBalFlag) {
		this.svcEntityInclBalFlag = svcEntityInclBalFlag;
	}

	public String getSvcEntityInclFrom() {
		return svcEntityInclFrom;
	}

	public void setSvcEntityInclFrom(String svcEntityInclFrom) {
		this.svcEntityInclFrom = svcEntityInclFrom;
	}

	public String getSvcEntityInclTo() {
		return svcEntityInclTo;
	}

	public void setSvcEntityInclTo(String svcEntityInclTo) {
		this.svcEntityInclTo = svcEntityInclTo;
	}
	
	
	public static Optional<AcctMaintenance> findAcctMaintenanceById(ArrayList<AcctMaintenance> acctMaintenances, Id id) {
	    return acctMaintenances.stream()
	            .filter(acctMaintenance -> acctMaintenance.getId().equals(id))
	            .findFirst();
	}
	
	@Override
	public boolean equals(Object o) {
	    if (this == o) return true;
	    if (o == null || getClass() != o.getClass()) return false;
	    AcctMaintenance that = (AcctMaintenance) o;
	    return Objects.equals(updateUser, that.updateUser) &&
	           Objects.equals(updateDate, that.updateDate) &&
	           Objects.equals(targetbalance, that.targetbalance) &&
	           Objects.equals(targetbalanceasString, that.targetbalanceasString) &&
	           Objects.equals(eodTargetbalanceasString, that.eodTargetbalanceasString) &&
	           Objects.equals(acctname, that.acctname) &&
	           Objects.equals(currcode, that.currcode) &&
	           Objects.equals(statusflag, that.statusflag) &&
	           Objects.equals(entity, that.entity) &&
	           Objects.equals(acctbiccode, that.acctbiccode) &&
	           Objects.equals(corresacccode, that.corresacccode) &&
	           Objects.equals(glcode, that.glcode) &&
	           Objects.equals(accttype, that.accttype) &&
	           Objects.equals(acctstatusflg, that.acctstatusflg) &&
	           Objects.equals(holidaycalendar, that.holidaycalendar) &&
	           Objects.equals(minacctcode, that.minacctcode) &&
	           Objects.equals(acctlevel, that.acctlevel) &&
	           Objects.equals(acctgrpflg, that.acctgrpflg) &&
	           Objects.equals(mansweepflg, that.mansweepflg) &&
	           Objects.equals(autoswpswitch, that.autoswpswitch) &&
	           Objects.equals(eodSweeptime, that.eodSweeptime) &&
	           Objects.equals(intraDaySweeptime, that.intraDaySweeptime) &&
	           Objects.equals(minseepamt, that.minseepamt) &&
	           Objects.equals(minseepamtasString, that.minseepamtasString) &&
	           Objects.equals(eodMinseepamtasString, that.eodMinseepamtasString) &&
	           Objects.equals(maxsweepamte, that.maxsweepamte) &&
	           Objects.equals(maxsweepamteasString, that.maxsweepamteasString) &&
	           Objects.equals(swpdays, that.swpdays) &&
	           Objects.equals(tgtbalsign, that.tgtbalsign) &&
	           Objects.equals(eodTgtBalsign, that.eodTgtBalsign) &&
	           Objects.equals(newcrfrId, that.newcrfrId) &&
	           Objects.equals(newdrfrId, that.newdrfrId) &&
	           Objects.equals(currcreditrate, that.currcreditrate) &&
	           Objects.equals(currcreditrateasString, that.currcreditrate) &&
    		   Objects.equals(currcreditrateasString, that.currcreditrateasString) &&
               Objects.equals(curroverdraftrate, that.curroverdraftrate) &&
               Objects.equals(curroverdraftrateasString, that.curroverdraftrateasString) &&
               Objects.equals(cutoff, that.cutoff) &&
               Objects.equals(sweepbookcode, that.sweepbookcode) &&
               Objects.equals(monitor, that.monitor) &&
               Objects.equals(acctClass, that.acctClass) &&
               Objects.equals(linkAccID, that.linkAccID) &&
               Objects.equals(acctMonitorSum, that.acctMonitorSum) &&
               Objects.equals(sweepFrmbal, that.sweepFrmbal) &&
               Objects.equals(acctPriorityOrder, that.acctPriorityOrder) &&
               Objects.equals(acctContactName, that.acctContactName) &&
               Objects.equals(acctPhone, that.acctPhone) &&
               Objects.equals(acctEmailAddr, that.acctEmailAddr) &&
               Objects.equals(acctNewCrInternal, that.acctNewCrInternal) &&
               Objects.equals(acctNewCrExternal, that.acctNewCrExternal) &&
               Objects.equals(acctNewDrInternal, that.acctNewDrInternal) &&
               Objects.equals(acctNewDrExternal, that.acctNewDrExternal) &&
               Objects.equals(sweepCode, that.sweepCode) &&
               Objects.equals(latestCreditRate, that.latestCreditRate) &&
               Objects.equals(latestOverDraftRate, that.latestOverDraftRate) &&
               Objects.equals(acctextraid, that.acctextraid) &&
               Objects.equals(forecastSOD, that.forecastSOD) &&
               Objects.equals(externalSOD, that.externalSOD) &&
               Objects.equals(primaryForecast, that.primaryForecast) &&
               Objects.equals(primaryExternal, that.primaryExternal) &&
               Objects.equals(secondaryForecast, that.secondaryForecast) &&
               Objects.equals(secondaryExternal, that.secondaryExternal) &&
               Objects.equals(futureBalances, that.futureBalances) &&
               Objects.equals(subAcctim, that.subAcctim) &&
               Objects.equals(eodTargetbalance, that.eodTargetbalance) &&
               Objects.equals(eodMinseepamt, that.eodMinseepamt) &&
               Objects.equals(latestInterestDateRate, that.latestInterestDateRate) &&
               Objects.equals(autoOpenUnsettled, that.autoOpenUnsettled) &&
               Objects.equals(autoOpenUnexpected, that.autoOpenUnexpected) &&
               Objects.equals(allPreAdviceEntity, that.allPreAdviceEntity)&& 	   
               Objects.equals(creditExternalInter, that.creditExternalInter)
               && Objects.equals(debitExternalInter, that.debitExternalInter)
               && Objects.equals(aggAccount, that.aggAccount)
               && Objects.equals(archiveData, that.archiveData)
               && currAccess == that.currAccess
               && Objects.equals(acctIBAN, that.acctIBAN)
               && Objects.equals(isIlmLiqContributor, that.isIlmLiqContributor)
               && Objects.equals(isIlmCustomerAccount, that.isIlmCustomerAccount)
               && Objects.equals(isIlmCentralBankMember, that.isIlmCentralBankMember)
               && Objects.equals(accountPartyId, that.accountPartyId)
               && Objects.equals(defaultSettleMethod, that.defaultSettleMethod)
               && Objects.equals(fmi, that.fmi)
               && Objects.equals(thisEntityInclBalFlag, that.thisEntityInclBalFlag)
               && Objects.equals(thisEntityInclFrom, that.thisEntityInclFrom)
               && Objects.equals(thisEntityInclTo, that.thisEntityInclTo)
               && Objects.equals(servicingEntityId, that.servicingEntityId)
               && Objects.equals(accNameInSvcEntity, that.accNameInSvcEntity)
               && Objects.equals(svcEntityInclBalFlag, that.svcEntityInclBalFlag)
               && Objects.equals(svcEntityInclFrom, that.svcEntityInclFrom)
               && Objects.equals(svcEntityInclTo, that.svcEntityInclTo);
	}

	public Set<AccountSpecificSweepFormat> getAccountSweepFormats() {
		return accountSweepFormats;
	}

	public void setAccountSweepFormats(Set<AccountSpecificSweepFormat> accountSweepFormats) {
		this.accountSweepFormats = accountSweepFormats;
	}


	public AcctMaintenance deepClone() {
		AcctMaintenance clone = new AcctMaintenance();

		// Clone Id object
		Id clonedId = new Id();
		clonedId.setHostId(this.id.getHostId());
		clonedId.setEntityId(this.id.getEntityId());
		clonedId.setAccountId(this.id.getAccountId());
		clone.setId(clonedId);

		// Clone primitive and immutable types
		clone.setUpdateUser(this.updateUser);
		clone.setUpdateDate(this.updateDate != null ? new Date(this.updateDate.getTime()) : null);
		clone.setTargetbalance(this.targetbalance);
		clone.setTargetbalanceasString(this.targetbalanceasString);
		clone.setEodTargetbalanceasString(this.eodTargetbalanceasString);
		clone.setAcctname(this.acctname);
		clone.setCurrcode(this.currcode);
		clone.setStatusflag(this.statusflag);

		// Clone Entity if it exists
		if (this.entity != null) {
			Entity clonedEntity = new Entity();
			clonedEntity = this.entity.deepCopy();
			clone.setEntity(clonedEntity);
		}

		// Clone all other String fields
		clone.setAcctbiccode(this.acctbiccode);
		clone.setCorresacccode(this.corresacccode);
		clone.setGlcode(this.glcode);
		clone.setAccttype(this.accttype);
		clone.setAcctstatusflg(this.acctstatusflg);
		clone.setHolidaycalendar(this.holidaycalendar);
		clone.setMinacctcode(this.minacctcode);
		clone.setAcctlevel(this.acctlevel);
		clone.setAcctgrpflg(this.acctgrpflg);
		clone.setMansweepflg(this.mansweepflg);
		clone.setAutoswpswitch(this.autoswpswitch);
		clone.setEodSweeptime(this.eodSweeptime);
		clone.setIntraDaySweeptime(this.intraDaySweeptime);
		clone.setMinseepamt(this.minseepamt);
		clone.setMinseepamtasString(this.minseepamtasString);
		clone.setEodMinseepamtasString(this.eodMinseepamtasString);
		clone.setMaxsweepamte(this.maxsweepamte);
		clone.setMaxsweepamteasString(this.maxsweepamteasString);
		clone.setSwpdays(this.swpdays);
		clone.setTgtbalsign(this.tgtbalsign);
		clone.setEodTgtBalsign(this.eodTgtBalsign);
		clone.setNewcrfrId(this.newcrfrId);
		clone.setNewdrfrId(this.newdrfrId);
		clone.setCurrcreditrate(this.currcreditrate);
		clone.setCurrcreditrateasString(this.currcreditrateasString);
		clone.setCurroverdraftrate(this.curroverdraftrate);
		clone.setCurroverdraftrateasString(this.curroverdraftrateasString);
		clone.setCutoff(this.cutoff);
		clone.setSweepbookcode(this.sweepbookcode);
		clone.setMonitor(this.monitor);
		clone.setAcctClass(this.acctClass);
		clone.setLinkAccID(this.linkAccID);
		clone.setAcctMonitorSum(this.acctMonitorSum);
		clone.setSweepFrmbal(this.sweepFrmbal);
		clone.setAcctPriorityOrder(this.acctPriorityOrder);
		clone.setAcctContactName(this.acctContactName);
		clone.setAcctPhone(this.acctPhone);
		clone.setAcctEmailAddr(this.acctEmailAddr);
		clone.setAcctNewCrInternal(this.acctNewCrInternal);
		clone.setAcctNewCrExternal(this.acctNewCrExternal);
		clone.setAcctNewDrInternal(this.acctNewDrInternal);
		clone.setAcctNewDrExternal(this.acctNewDrExternal);
		clone.setSweepCode(this.sweepCode);
		clone.setLatestCreditRate(this.latestCreditRate);
		clone.setLatestOverDraftRate(this.latestOverDraftRate);
		clone.setAcctextraid(this.acctextraid);
		clone.setForecastSOD(this.forecastSOD);
		clone.setExternalSOD(this.externalSOD);
		clone.setPrimaryForecast(this.primaryForecast);
		clone.setPrimaryExternal(this.primaryExternal);
		clone.setSecondaryForecast(this.secondaryForecast);
		clone.setSecondaryExternal(this.secondaryExternal);
		clone.setFutureBalances(this.futureBalances);
		clone.setSubAcctim(this.subAcctim);
		clone.setEodTargetbalance(this.eodTargetbalance);
		clone.setEodMinseepamt(this.eodMinseepamt);
		clone.setLatestInterestDateRate(this.latestInterestDateRate);
		clone.setAutoOpenUnsettled(this.autoOpenUnsettled);
		clone.setAutoOpenUnexpected(this.autoOpenUnexpected);
		clone.setAllPreAdviceEntity(this.allPreAdviceEntity);
		clone.setCreditExternalInter(this.creditExternalInter);
		clone.setDebitExternalInter(this.debitExternalInter);
		clone.setAggAccount(this.aggAccount);
		clone.setArchiveData(this.archiveData);
		clone.setCurrAccess(this.currAccess);
		clone.setAcctIBAN(this.acctIBAN);
		clone.setIsIlmLiqContributor(this.isIlmLiqContributor);
		clone.setIsIlmCustomerAccount(this.isIlmCustomerAccount);
		clone.setIsIlmCentralBankMember(this.isIlmCentralBankMember);
		clone.setAccountPartyId(this.accountPartyId);
		clone.setDefaultSettleMethod(this.defaultSettleMethod);
		clone.setFmi(this.fmi);
		clone.setThisEntityInclBalFlag(this.thisEntityInclBalFlag);
		clone.setThisEntityInclFrom(this.thisEntityInclFrom);
		clone.setThisEntityInclTo(this.thisEntityInclTo);
		clone.setServicingEntityId(this.servicingEntityId);
		clone.setAccNameInSvcEntity(this.accNameInSvcEntity);
		clone.setSvcEntityInclBalFlag(this.svcEntityInclBalFlag);
		clone.setSvcEntityInclFrom(this.svcEntityInclFrom);
		clone.setSvcEntityInclTo(this.svcEntityInclTo);

		// Clone AccountSpecificSweepFormat set if it exists
		if (this.accountSweepFormats != null) {
			Set<AccountSpecificSweepFormat> clonedFormats = new HashSet<>();
			for (AccountSpecificSweepFormat format : this.accountSweepFormats) {
				clonedFormats.add(format.deepCopy());
			}
			clone.setAccountSweepFormats(clonedFormats);
		}

		return clone;
	}

}
