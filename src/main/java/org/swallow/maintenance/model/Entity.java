/*
 * @ Entity.java 1.0 02/01/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
/*
 * Created on Dec 2, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Hashtable;

import org.swallow.exception.SwtException;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR>
 * 
 * Pojo to map S_ENTITY table
 */

public class Entity extends BaseObject implements
		org.swallow.model.AuditComponent {
	/** Default version id */
	/* Local Variable Declarartion */
	// Variable to hold serialVersionUID
	private static final long serialVersionUID = 1L;
	// Variable to hold id
	private Id id = new Id();
	// Variable to hold entityName
	private String entityName = null;
	// Variable to hold domesticCurrency
	private String domesticCurrency = null;
	// Variable to hold reprotingCurrency
	private String reprotingCurrency = null;
	// Variable to hold countryId
	private String countryId = null;
	// Variable to hold exchangeRateFormat
	private String exchangeRateFormat = null;
	// Variable to hold retentionFlag
	private String retentionFlag = null;
	// Variable to hold movementRetention
	private String movementRetention = null;
	/*
	 * Start:Code Modified for Mantis 1380 by Chinniah on 18-Aug-2011:GUI for
	 * Entity maintenance should be changed to allow entry of Input and Output
	 * retention period. Tags No tags attached.
	 */

	// Variable to hold inputRetention
	private String inputRetention = null;
	// Variable to hold outputRetention
	private String outputRetention = null;
	/*
	 * End:Code Modified for Mantis 1380 by Chinniah on 18-Aug-2011:GUI for
	 * Entity maintenance should be changed to allow entry of Input and Output
	 * retention period. Tags No tags attached.
	 */

	// Variable to hold cashFilterThreshold
	private Double cashFilterThreshold = null;
	// Variable to hold securitiesFilterThreshold
	private Double securitiesFilterThreshold = null;
	// Variable to hold ststsRetain
	private Integer ststsRetain = null;
	// Variable to hold updateDate
	private Date updateDate = null;
	// Variable to hold updateUser
	private String updateUser = null;
	// Variable to hold groupLevel1
	private String groupLevel1 = null;
	// Variable to hold groupLevel2
	private String groupLevel2 = null;
	// Variable to hold groupLevel3
	private String groupLevel3 = null;
	// Variable to hold metaGroupLevel1
	private String metaGroupLevel1 = null;
	// Variable to hold metaGroupLevel2
	private String metaGroupLevel2 = null;
	// Variable to hold metaGroupLevel3
	private String metaGroupLevel3 = null;

	// Variable to hold cashFilterThresholdAsString
	private String cashFilterThresholdAsString = null;
	// Variable to hold securitiesFilterThresholdAsString
	private String securitiesFilterThresholdAsString = null;

	// Variable to hold interestRateRetain
	private String interestRateRetain = null;
	// Variable to hold exchangeRateRetain
	private String exchangeRateRetain = null;
	// Variable to hold sweepPosition
	private Integer sweepPosition = null;
	// Variable to hold entityBIC
	private String entityBIC = null;
	// Variable to hold preAdvicePosition
	private Integer preAdvicePosition = null;
	// Variable to hold positionLvlDetails
	private String positionLvlDetails = null; // it stores the
	// positionlevelnames
	// details for this entity as hash
	// separated String
	// Variable to hold internalBalance
	private Integer internalBalance = null;
	// Variable to hold externalBalance
	private Integer externalBalance = null;
	// Variable to hold sweepCutoffLeadTime
	private Long sweepCutoffLeadTime = null;
	// Variable to hold smallMovementRetain
	private String smallMovementRetain = null;
	// Variable to hold largeSmallMovementThreshold
	private Double largeSmallMovementThreshold = null;
	// Variable to hold largeSmallMovementThresholdAsString
	private String largeSmallMovementThresholdAsString = null;
	// Variable to hold balance
	private String balance = null;
	// Variable to hold balanceLog
	private String balanceLog = null;
	// Variable to hold centralBankAccount
	private String centralBankAccount = null;
	// Variable to hold crrLimit
	private BigDecimal crrLimit = null;
	// Variable to hold crrLimitAsString
	private String crrLimitAsString;
	// Variable to hold crrLimitFromDate
	private Date crrLimitFromDate = null;
	// Variable to hold crrLimitFromDateAsString
	private String crrLimitFromDateAsString = null;
	// Variable to hold crrLimitToDate
	private Date crrLimitToDate = null;
	// Variable to hold crrLimitToDateAsString
	private String crrLimitToDateAsString = null;
	// Variable to hold startDay
	private String startDay = null;
	// Variable to hold ilmRetain
	private Integer ilmRetain = null;
	// Variable to hold ilmRetain
	private String ilmRetainAsString = null;
	// Variable to hold ilmCalcPastDays
	private Integer ilmCalcPastDays = null;
	// Variable to hold ilmCalcPastDays
	private String ilmCalcPastDaysAsString = null;
	// Variable to hold sysTimeZone
	private String entTimeZone= null;
	private String entServerTimeOffSet = null;

	// Hash table to put all details in the log table
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	static {
		logTable.put("entityName", "Entity Name");
		logTable.put("domesticCurrency", "Domestic Currency");
		logTable.put("reprotingCurrency", "Report Currency");
		logTable.put("countryId", "Country Code");
		logTable.put("entServerTimeOffSet", "Server Time Offset");
		logTable.put("exchangeRateFormat", "Exchange Rate Format");
		logTable.put("retentionFlag", "Retention Flag");
		logTable.put("movementRetention", "Movement Retention Period");
		logTable.put("inputRetention", "Input Retention Period");
		logTable.put("outputRetention", "Output Retention Period");
		logTable.put("cashFilterThreshold", "Cash Filter");
		logTable.put("securitiesFilterThreshold", "Securities Filter");
		logTable.put("ststsRetain", "Stats Retention Period");
		logTable.put("interestRateRetain", "Ccy Interest Rate");
		logTable.put("exchangeRateRetain", "Ccy Exchange Rate");
		logTable.put("sweepPosition", "Sweep");
		logTable.put("preAdvicePosition", "Pre advice");
		logTable.put("internalBalance", "Internal Balance");
		logTable.put("externalBalance", "External Balance");
		logTable.put("sweepCutoffLeadTime", "Sweep Cutoff Lead Time");

		logTable.put("largeSmallMovementThresholdAsString",
				"Small Movement Threshold Amount");
		logTable.put("smallMovementRetain", "Small Movement Retention");

		logTable.put("balance", "balance");
		logTable.put("balanceLog", "balanceLog");

		logTable.put("centralBankAccount", "Central Bank Account Id");
		logTable.put("crrLimit", "CRR Limit");
		logTable.put("crrLimitFromDate", "From Date");
		logTable.put("crrLimitToDate", "To Date");
		logTable.put("startDay", "Start day of Week");
		logTable.put("entTimeZone", "Entity TimeZone Name");

	}

	public static class Id extends BaseObject {
		/** Default version id */
		private static final long serialVersionUID = 1L;
		private String hostId = null;
		private String entityId = null;

		public Id() {
		}

		public Id(String hostId, String entityId) {
			this.hostId = hostId;
			this.entityId = entityId;
		}

		/**
		 * Getter method for entityId
		 * 
		 * @return entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * Setter method for entityId
		 * 
		 * @param entityId
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * Getter method for hostId
		 * 
		 * @return hostId
		 */
		public String getHostId() {
			return hostId;
		}

		/**
		 * Setter method for hostId
		 * 
		 * @param hostId
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
	}

	/*
	 * Start:Code Modified for Mantis 1380 by Chinniah on 18-Aug-2011:GUI for
	 * Entity maintenance should be changed to allow entry of Input and Output
	 * retention period. Tags No tags attached.
	 */
	/**
	 * Getter method for inputRetention
	 * 
	 * @return inputRetention as String
	 */

	public String getInputRetention() {
		return inputRetention;
	}

	/**
	 * Setter method for inputRetention
	 * 
	 * @param inputRetention
	 */

	public void setInputRetention(String inputRetention) {
		this.inputRetention = inputRetention;
	}

	/**
	 * Getter method for outputRetention
	 * 
	 * @return outputRetention as String
	 */

	public String getOutputRetention() {
		return outputRetention;
	}

	/**
	 * Setter method for outputRetention
	 * 
	 * @param outputRetention
	 */

	public void setOutputRetention(String outputRetention) {
		this.outputRetention = outputRetention;
	}

	/*
	 * End:Code Modified for Mantis 1380 by Chinniah on 18-Aug-2011:GUI for
	 * Entity maintenance should be changed to allow entry of Input and Output
	 * retention period. Tags No tags attached.
	 */
	/**
	 * @return Returns the entityName.
	 */
	public String getEntityName() {
		return entityName;
	}

	/**
	 * @param entityName
	 *            The entityName to set.
	 */
	public void setEntityName(String entityName) {
		this.entityName = entityName;
		
		if(this.getId() != null && this.getId().getEntityId() != null && this.getId().getHostId() != null) {
			try {
				this.setEntServerTimeOffSet(SwtUtil.getEntityOffsetTime(this.getId().getHostId(), this.getId().getEntityId()));
			} catch (SwtException e) {
			}
		}
	}

	public void setId(Id id) {
		this.id = id;
	}

	public Id getId() {
		return id;
	}

	/**
	 * @return Returns the metaGroupLevel1.
	 */
	public String getMetaGroupLevel1() {
		return metaGroupLevel1;
	}

	/**
	 * @param metaGroupLevel1
	 *            The metaGroupLevel1 to set.
	 */
	public void setMetaGroupLevel1(String metaGroupLevel1) {
		this.metaGroupLevel1 = metaGroupLevel1;
	}

	/**
	 * @return Returns the metaGroupLevel2.
	 */
	public String getMetaGroupLevel2() {
		return metaGroupLevel2;
	}

	/**
	 * @param metaGroupLevel2
	 *            The metaGroupLevel2 to set.
	 */
	public void setMetaGroupLevel2(String metaGroupLevel2) {
		this.metaGroupLevel2 = metaGroupLevel2;
	}

	/**
	 * @return Returns the metaGroupLevel3.
	 */
	public String getMetaGroupLevel3() {
		return metaGroupLevel3;
	}

	/**
	 * @param metaGroupLevel3
	 *            The metaGroupLevel3 to set.
	 */
	public void setMetaGroupLevel3(String metaGroupLevel3) {
		this.metaGroupLevel3 = metaGroupLevel3;
	}

	/**
	 * @return Returns the groupLevel1.
	 */
	public String getGroupLevel1() {
		return groupLevel1;
	}

	/**
	 * @param groupLevel1
	 *            The groupLevel1 to set.
	 */
	public void setGroupLevel1(String groupLevel1) {
		this.groupLevel1 = groupLevel1;
	}

	/**
	 * @return Returns the groupLevel2.
	 */
	public String getGroupLevel2() {
		return groupLevel2;
	}

	/**
	 * @param groupLevel2
	 *            The groupLevel2 to set.
	 */
	public void setGroupLevel2(String groupLevel2) {
		this.groupLevel2 = groupLevel2;
	}

	/**
	 * @return Returns the groupLevel3.
	 */
	public String getGroupLevel3() {
		return groupLevel3;
	}

	/**
	 * @param groupLevel3
	 *            The groupLevel3 to set.
	 */
	public void setGroupLevel3(String groupLevel3) {
		this.groupLevel3 = groupLevel3;
	}

	public Integer getStstsRetain() {
		return ststsRetain;
	}

	public void setStstsRetain(Integer ststsRetain) {
		this.ststsRetain = ststsRetain;
	}

	/**
	 * @return Returns the cashFilterThreshold.
	 */
	public Double getCashFilterThreshold() {
		return cashFilterThreshold;
	}

	/**
	 * @param cashFilterThreshold
	 *            The cashFilterThreshold to set.
	 */
	public void setCashFilterThreshold(Double cashFilterThreshold) {
		this.cashFilterThreshold = cashFilterThreshold;
	}

	/**
	 * @return Returns the countryId.
	 */
	public String getCountryId() {
		return countryId;
	}

	/**
	 * @param countryId
	 *            The countryId to set.
	 */
	public void setCountryId(String countryId) {
		this.countryId = countryId;
	}

	/**
	 * @return Returns the domesticCurrency.
	 */
	public String getDomesticCurrency() {
		return domesticCurrency;
	}

	/**
	 * @param domesticCurrency
	 *            The domesticCurrency to set.
	 */
	public void setDomesticCurrency(String domesticCurrency) {
		this.domesticCurrency = domesticCurrency;
	}


	/**
	 * @return Returns the movementRetention.
	 */

	public String getMovementRetention() {
		return movementRetention;
	}

	/**
	 * @param movementRetention
	 *            The movementRetention to set.
	 */
	public void setMovementRetention(String movementRetention) {
		this.movementRetention = movementRetention;
	}

	/**
	 * @return Returns the reprotingCurrency.
	 */
	public String getReprotingCurrency() {
		return reprotingCurrency;
	}

	/**
	 * @param reprotingCurrency
	 *            The reprotingCurrency to set.
	 */
	public void setReprotingCurrency(String reprotingCurrency) {
		this.reprotingCurrency = reprotingCurrency;
	}

	/**
	 * @return Returns the securitiesFilterThreshold.
	 */
	public Double getSecuritiesFilterThreshold() {
		return securitiesFilterThreshold;
	}

	/**
	 * @param securitiesFilterThreshold
	 *            The securitiesFilterThreshold to set.
	 */
	public void setSecuritiesFilterThreshold(Double securitiesFilterThreshold) {
		this.securitiesFilterThreshold = securitiesFilterThreshold;
	}

	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * @param updateDate
	 *            The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * @param updateUser
	 *            The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return Returns the exchangeRateFormat.
	 */
	public String getExchangeRateFormat() {
		return exchangeRateFormat;
	}

	/**
	 * @param exchangeRateFormat
	 *            The exchangeRateFormat to set.
	 */
	public void setExchangeRateFormat(String exchangeRateFormat) {
		this.exchangeRateFormat = exchangeRateFormat;
	}

	/**
	 * @return Returns the retentionFlag.
	 */
	public String getRetentionFlag() {
		return retentionFlag;
	}

	/**
	 * @param retentionFlag
	 *            The retentionFlag to set.
	 */
	public void setRetentionFlag(String retentionFlag) {
		this.retentionFlag = retentionFlag;
	}

	/**
	 * @return Returns the cashFilterThresholdAsString.
	 */
	public String getCashFilterThresholdAsString() {
		return cashFilterThresholdAsString;
	}

	/**
	 * @param cashFilterThresholdAsString
	 *            The cashFilterThresholdAsString to set.
	 */
	public void setCashFilterThresholdAsString(
			String cashFilterThresholdAsString) {
		this.cashFilterThresholdAsString = cashFilterThresholdAsString;
	}

	/**
	 * @return Returns the securitiesFilterThresholdAsString.
	 */
	public String getSecuritiesFilterThresholdAsString() {
		return securitiesFilterThresholdAsString;
	}

	/**
	 * @param securitiesFilterThresholdAsString
	 *            The securitiesFilterThresholdAsString to set.
	 */
	public void setSecuritiesFilterThresholdAsString(
			String securitiesFilterThresholdAsString) {
		this.securitiesFilterThresholdAsString = securitiesFilterThresholdAsString;
	}

	public static Hashtable<String, String> getLogTable() {
		return logTable;
	}

	public static void setLogTable(Hashtable<String, String> logTable) {
		Entity.logTable = logTable;
	}

	public String getEntityBIC() {
		return entityBIC;
	}

	public void setEntityBIC(String entityBIC) {
		this.entityBIC = entityBIC;
	}

	public String getExchangeRateRetain() {
		return exchangeRateRetain;
	}

	public void setExchangeRateRetain(String exchangeRateRetain) {
		this.exchangeRateRetain = exchangeRateRetain;
	}

	public String getInterestRateRetain() {
		return interestRateRetain;
	}

	public void setInterestRateRetain(String interestRateRetain) {
		this.interestRateRetain = interestRateRetain;
	}

	public Integer getPreAdvicePosition() {
		return preAdvicePosition;
	}

	public void setPreAdvicePosition(Integer preAdvicePosition) {
		this.preAdvicePosition = preAdvicePosition;
	}

	public Integer getSweepPosition() {
		return sweepPosition;
	}

	public void setSweepPosition(Integer sweepPosition) {
		this.sweepPosition = sweepPosition;
	}

	public String getPositionLvlDetails() {
		return positionLvlDetails;
	}

	public void setPositionLvlDetails(String positionLvlDetails) {
		this.positionLvlDetails = positionLvlDetails;
	}

	/**
	 * @return Returns the externalBalance.
	 */
	public Integer getExternalBalance() {
		return externalBalance;
	}

	/**
	 * @param externalBalance
	 *            The externalBalance to set.
	 */
	public void setExternalBalance(Integer externalBalance) {
		this.externalBalance = externalBalance;
	}

	/**
	 * @return Returns the internalBalance.
	 */
	public Integer getInternalBalance() {
		return internalBalance;
	}

	/**
	 * @param internalBalance
	 *            The internalBalance to set.
	 */
	public void setInternalBalance(Integer internalBalance) {
		this.internalBalance = internalBalance;
	}

	/**
	 * @return Returns the sweepCutoffLeadTime.
	 */
	public Long getSweepCutoffLeadTime() {
		return sweepCutoffLeadTime;
	}

	/**
	 * @param sweepCutoffLeadTime
	 *            The sweepCutoffLeadTime to set.
	 */
	public void setSweepCutoffLeadTime(Long sweepCutoffLeadTime) {
		this.sweepCutoffLeadTime = sweepCutoffLeadTime;
	}

	/**
	 * @return Returns the largeSmallMovementThreshold.
	 */
	public Double getLargeSmallMovementThreshold() {
		return largeSmallMovementThreshold;
	}

	/**
	 * @param largeSmallMovementThreshold
	 *            The largeSmallMovementThreshold to set.
	 */
	public void setLargeSmallMovementThreshold(
			Double largeSmallMovementThreshold) {
		this.largeSmallMovementThreshold = largeSmallMovementThreshold;
	}

	/**
	 * @return Returns the smallMovementRetain.
	 */
	public String getSmallMovementRetain() {
		return smallMovementRetain;
	}

	/**
	 * @param smallMovementRetain
	 *            The smallMovementRetain to set.
	 */
	public void setSmallMovementRetain(String smallMovementRetain) {
		this.smallMovementRetain = smallMovementRetain;
	}

	/**
	 * @return Returns the largeSmallMovementThresholdAsString.
	 */
	public String getLargeSmallMovementThresholdAsString() {
		return largeSmallMovementThresholdAsString;
	}

	/**
	 * @param largeSmallMovementThresholdAsString
	 *            The largeSmallMovementThresholdAsString to set.
	 */
	public void setLargeSmallMovementThresholdAsString(
			String largeSmallMovementThresholdAsString) {
		this.largeSmallMovementThresholdAsString = largeSmallMovementThresholdAsString;
	}

	public String getBalance() {
		return balance;
	}

	public void setBalance(String balance) {
		this.balance = balance;
	}

	public String getBalanceLog() {
		return balanceLog;
	}

	public void setBalanceLog(String balanceLog) {
		this.balanceLog = balanceLog;
	}

	/**
	 * @return the centralBankAccount
	 */
	public String getCentralBankAccount() {
		return centralBankAccount;
	}

	/**
	 * @param centralBankAccount
	 *            the centralBankAccount to set
	 */
	public void setCentralBankAccount(String centralBankAccount) {
		this.centralBankAccount = centralBankAccount;
	}

	/**
	 * @return the crrLimitAsString
	 */
	public String getCrrLimitAsString() {
		return crrLimitAsString;
	}

	/**
	 * @param crrLimitAsString
	 *            the crrLimitAsString to set
	 */
	public void setCrrLimitAsString(String crrLimitAsString) {
		this.crrLimitAsString = crrLimitAsString;
	}

	/**
	 * @return the crrLimitFromDate
	 */
	public Date getCrrLimitFromDate() {
		return crrLimitFromDate;
	}

	/**
	 * @param crrLimitFromDate
	 *            the crrLimitFromDate to set
	 */
	public void setCrrLimitFromDate(Date crrLimitFromDate) {
		this.crrLimitFromDate = crrLimitFromDate;
	}

	/**
	 * @return the crrLimitFromDateAsString
	 */
	public String getCrrLimitFromDateAsString() {
		return crrLimitFromDateAsString;
	}

	/**
	 * @param crrLimitFromDateAsString
	 *            the crrLimitFromDateAsString to set
	 */
	public void setCrrLimitFromDateAsString(String crrLimitFromDateAsString) {
		this.crrLimitFromDateAsString = crrLimitFromDateAsString;
	}

	/**
	 * @return the crrLimitToDate
	 */
	public Date getCrrLimitToDate() {
		return crrLimitToDate;
	}

	/**
	 * @param crrLimitToDate
	 *            the crrLimitToDate to set
	 */
	public void setCrrLimitToDate(Date crrLimitToDate) {
		this.crrLimitToDate = crrLimitToDate;
	}

	/**
	 * @return the crrLimitToDateAsString
	 */
	public String getCrrLimitToDateAsString() {
		return crrLimitToDateAsString;
	}

	/**
	 * @param crrLimitToDateAsString
	 *            the crrLimitToDateAsString to set
	 */
	public void setCrrLimitToDateAsString(String crrLimitToDateAsString) {
		this.crrLimitToDateAsString = crrLimitToDateAsString;
	}

	/**
	 * @return the startDay
	 */
	public String getStartDay() {
		return startDay;
	}

	/**
	 * @param startDay
	 *            the startDay to set
	 */
	public void setStartDay(String startDay) {
		this.startDay = startDay;
	}

	/**
	 * @return the crrLimit
	 */
	public BigDecimal getCrrLimit() {
		return crrLimit;
	}

	/**
	 * @param crrLimit
	 *            the crrLimit to set
	 */
	public void setCrrLimit(BigDecimal crrLimit) {
		this.crrLimit = crrLimit;
	}
	
	/**
	 * 
	 * @return ilmRetain
	 */
	public Integer getIlmRetain() {
		return ilmRetain;
	}
	/**
	 * 
	 * @param ilmRetain
	 */
	public void setIlmRetain(Integer ilmRetain) {
		this.ilmRetain = ilmRetain;
	}
    /**
     * 
     * @return ilmRetainAsString
     */
	public String getIlmRetainAsString() {
		return ilmRetainAsString;
	}
    /**
     * 
     * @param ilmRetainAsString
     */
	public void setIlmRetainAsString(String ilmRetainAsString) {
		this.ilmRetainAsString = ilmRetainAsString;
	}

	public Integer getIlmCalcPastDays() {
		return ilmCalcPastDays;
	}

	public void setIlmCalcPastDays(Integer ilmCalcPastDays) {
		this.ilmCalcPastDays = ilmCalcPastDays;
	}

	public String getIlmCalcPastDaysAsString() {
		return ilmCalcPastDaysAsString;
	}

	public void setIlmCalcPastDaysAsString(String ilmCalcPastDaysAsString) {
		this.ilmCalcPastDaysAsString = ilmCalcPastDaysAsString;
	}

	public String getEntTimeZone() {
		return entTimeZone;
	}

	public void setEntTimeZone(String sysTimeZone) {
		this.entTimeZone = sysTimeZone;
	}

	public String getEntServerTimeOffSet() {
		return entServerTimeOffSet;
	}

	public void setEntServerTimeOffSet(String entServerTimeOffSet) {
		this.entServerTimeOffSet = entServerTimeOffSet;
	}

	public Entity deepCopy() {
		Entity copy = new Entity();

		// Copy ID (assumed to be a deep copy as well)
		if (this.id != null) {
			copy.id = new Id(this.id.getHostId(), this.id.getEntityId());
		}

		// Shallow copy for Strings (immutable), deep for mutable types
		copy.entityName = this.entityName;
		copy.domesticCurrency = this.domesticCurrency;
		copy.reprotingCurrency = this.reprotingCurrency;
		copy.countryId = this.countryId;
		copy.exchangeRateFormat = this.exchangeRateFormat;
		copy.retentionFlag = this.retentionFlag;
		copy.movementRetention = this.movementRetention;
		copy.inputRetention = this.inputRetention;
		copy.outputRetention = this.outputRetention;
		copy.cashFilterThreshold = this.cashFilterThreshold != null ? new Double(this.cashFilterThreshold) : null;
		copy.securitiesFilterThreshold = this.securitiesFilterThreshold != null ? new Double(this.securitiesFilterThreshold) : null;
		copy.ststsRetain = this.ststsRetain;
		copy.updateDate = this.updateDate != null ? new Date(this.updateDate.getTime()) : null;
		copy.updateUser = this.updateUser;
		copy.groupLevel1 = this.groupLevel1;
		copy.groupLevel2 = this.groupLevel2;
		copy.groupLevel3 = this.groupLevel3;
		copy.metaGroupLevel1 = this.metaGroupLevel1;
		copy.metaGroupLevel2 = this.metaGroupLevel2;
		copy.metaGroupLevel3 = this.metaGroupLevel3;
		copy.cashFilterThresholdAsString = this.cashFilterThresholdAsString;
		copy.securitiesFilterThresholdAsString = this.securitiesFilterThresholdAsString;
		copy.interestRateRetain = this.interestRateRetain;
		copy.exchangeRateRetain = this.exchangeRateRetain;
		copy.sweepPosition = this.sweepPosition;
		copy.entityBIC = this.entityBIC;
		copy.preAdvicePosition = this.preAdvicePosition;
		copy.positionLvlDetails = this.positionLvlDetails;
		copy.internalBalance = this.internalBalance;
		copy.externalBalance = this.externalBalance;
		copy.sweepCutoffLeadTime = this.sweepCutoffLeadTime != null ? Long.valueOf(this.sweepCutoffLeadTime) : null;
		copy.smallMovementRetain = this.smallMovementRetain;
		copy.largeSmallMovementThreshold = this.largeSmallMovementThreshold != null ? new Double(this.largeSmallMovementThreshold) : null;
		copy.largeSmallMovementThresholdAsString = this.largeSmallMovementThresholdAsString;
		copy.balance = this.balance;
		copy.balanceLog = this.balanceLog;
		copy.centralBankAccount = this.centralBankAccount;
		copy.crrLimit = this.crrLimit != null ? new BigDecimal(this.crrLimit.toString()) : null;
		copy.crrLimitAsString = this.crrLimitAsString;
		copy.crrLimitFromDate = this.crrLimitFromDate != null ? new Date(this.crrLimitFromDate.getTime()) : null;
		copy.crrLimitFromDateAsString = this.crrLimitFromDateAsString;
		copy.crrLimitToDate = this.crrLimitToDate != null ? new Date(this.crrLimitToDate.getTime()) : null;
		copy.crrLimitToDateAsString = this.crrLimitToDateAsString;
		copy.startDay = this.startDay;
		copy.ilmRetain = this.ilmRetain;
		copy.ilmRetainAsString = this.ilmRetainAsString;
		copy.ilmCalcPastDays = this.ilmCalcPastDays;
		copy.ilmCalcPastDaysAsString = this.ilmCalcPastDaysAsString;
		copy.entTimeZone = this.entTimeZone;
		copy.entServerTimeOffSet = this.entServerTimeOffSet;

		return copy;
	}

}
