/*
 * @(#)EntityPositionLevelTO.java 1.0 06/07/03
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * This class is imutable transfer class that keeps the Entity Id and Position Level Names.
 *
 * <AUTHOR> Systems
 * @version 1.0 03 August 2006
 */
public class EntityPositionLevelTO implements Serializable {
	
    /**
     * Holds the Entity Id
     */
    private String entityId = "";

    /**
     * Holds the mapping of Position Level Ids and Names  
     */
    private List positionLevels = new ArrayList();

    /**
     * Creates a new EntityPositionLevelTO object.
     *
     * @param entityId DOCUMENT ME!
     * @param positionLevelNames DOCUMENT ME!
     */
    public EntityPositionLevelTO(String entityId, List positionLevels) {
        this.entityId = entityId;
        this.positionLevels = positionLevels;
    }

    /**
     * Returns the Entity Id
     *
     * @return Returns the entityId
     */
    public String getEntityId() {
        return entityId;
    }

    /**
     * Returns the Mapping of PositionLevel Ids and Names 
     *
     * @return Returns the positionLevelNames
     */
    public List getPositionLevels() {
        return positionLevels;
    }

    /**
     * This function overrides the equals function of Object class.
     *
     * @param obj Object to be compared
     *
     * @return true/false
     */
    public boolean equals(Object obj) {
        boolean retValue = false;

        if ((obj != null) && obj instanceof EntityPositionLevelTO) {
            retValue = ((EntityPositionLevelTO) obj).getEntityId().equals(entityId);
        }

        return retValue;
    }

    /**
     * This function overrides the hashCode function of Object class.
     *
     * @return Retunrs the Hash Code of object.
     */
    public int hashCode() {
        return entityId.hashCode();
    }
    
}
