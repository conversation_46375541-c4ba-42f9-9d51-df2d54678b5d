/*
 * Created on Dec 2, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.maintenance.model;

import org.swallow.model.BaseObject;
import org.swallow.util.*;
import java.util.*;
/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MatchParams extends BaseObject{

	private String paramDesc;
	private Date updateDate = new Date();
	private String updateUser;
	private Id id = new Id();
	
	
	
	
	
	/**
	 * @return Returns the paramDesc.
	 */
	public String getParamDesc() {
		return paramDesc;
	}
	/**
	 * @param paramDesc The paramDesc to set.
	 */
	public void setParamDesc(String paramDesc) {
		this.paramDesc = paramDesc;
	}
	/**
	 * @return Returns the updateDate.
	 */
	public Date getUpdateDate() {
		return updateDate;
	}
		public static class Id extends BaseObject{
	
		private Integer paramCode;
	
		public Id() {}

		public Id(Integer paramCode) {
			this.paramCode = paramCode;
			
		}
		
		/**
		 * @return Returns the paramCode.
		 */
		public Integer getParamCode() {
			return paramCode;
		}
		/**
		 * @param paramCode The paramCode to set.
		 */
		public void setParamCode(Integer paramCode) {
			this.paramCode = paramCode;
		}
	}
	
	public void setId(Id id){
		this.id = id; 
		}	
	public Id getId(){
		return id; 
		}
	

	
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
	
	/**
	 * @param updateDate The updateDate to set.
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
}
