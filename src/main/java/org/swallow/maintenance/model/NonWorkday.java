/*
 * @(#)NonWorkday.java 1.0 28/02/2010
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;

/**
 * <AUTHOR> A
 * 
 * CountryOverride. java is the POJO to map S_NON_WORKDAY_PARAMS table
 * implementing AuditComponent to log the records in S_MAINTENANCE_LOG
 */
public class NonWorkday extends BaseObject implements
		org.swallow.model.AuditComponent {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	// String variable to hold applyEntityCountry
	private String applyEntityCountry = null;
	// String variable to hold applyAccountCountry
	private String applyAccountCountry = null;
	// String variable to hold applyCurrencyCountry
	private String applyCurrencyCountry = null;
	// Date instance to hold updatDate
	private Date updateDate = new Date();
	// String variable to hold updateUser
	private String updateUser = null;
	// ID instance
	private Id id = new Id();

	// hash table to modify bean name
	public static Hashtable<String, String> logTable = new Hashtable<String, String>();
	static {
		logTable.put("nonWorkday", "NonWorkday");

	}

	// Id Class to hold primary and foriegn key values
	public static class Id extends BaseObject {

		/**
		 * 
		 */
		private static final long serialVersionUID = 1L;
		// String variable to hold hostId
		private String hostId = null;
		// String variable to hold entityId
		private String entityId = null;
		// String variable to hold facility
		private String facility = null;

		// default constructor
		public Id() {
		}

		/**
		 * Constructor to set forign key values
		 * 
		 * @param hostId
		 * @param entityId
		 * @param facility
		 * @return
		 */
		public Id(String hostId, String entityId, String facility) {
			this.hostId = hostId;
			this.entityId = entityId;
			this.facility = facility;

		}

		/**
		 * Getter method for entityId
		 * 
		 * @return String - entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/**
		 * Setter method for entityId
		 * 
		 * @param entityId
		 * @return
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}

		/**
		 * Getter method for hostId
		 * 
		 * @return String - hostId
		 */
		public String getHostId() {

			return hostId;
		}

		/**
		 * Setter method for hostId
		 * 
		 * @param hostId
		 * @return
		 */
		public void setHostId(String hostId) {

			this.hostId = hostId;

		}

		/**
		 * Getter method for facility
		 * 
		 * @return String - facility
		 */
		public String getFacility() {
			return facility;
		}

		/**
		 * Setter method for facility
		 * 
		 * @param facility
		 * @return
		 */
		public void setFacility(String facility) {
			this.facility = facility;
		}

	}

	/**
	 * Getter method for id
	 * 
	 * @return Id - id
	 */
	public Id getId() {
		return id;
	}

	/**
	 * Setter method for id
	 * 
	 * @param id
	 * @return
	 */
	public void setId(Id id) {
		this.id = id;
	}

	/**
	 * Getter method for updateDate
	 * 
	 * @return Date - updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/**
	 * Setter method for updateDate
	 * 
	 * @param updateDate
	 * @return
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/**
	 * Getter method for updateUser
	 * 
	 * @return String - updateUser
	 */
	public String getUpdateUser() {
		return updateUser;
	}

	/**
	 * Setter method for updateUser
	 * 
	 * @param updateUser
	 * @return
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * Getter method for applyEntityCountry
	 * 
	 * @return String - applyEntityCountry
	 */
	public String getApplyEntityCountry() {
		return applyEntityCountry;
	}

	/**
	 * Setter method for applyEntityCountry
	 * 
	 * @param applyEntityCountry
	 * @return
	 */
	public void setApplyEntityCountry(String applyEntityCountry) {
		this.applyEntityCountry = applyEntityCountry;
	}

	/**
	 * Getter method for applyAccountCountry
	 * 
	 * @return String - applyAccountCountry
	 */
	public String getApplyAccountCountry() {
		return applyAccountCountry;
	}

	/**
	 * Setter method for applyAccountCountry
	 * 
	 * @param applyAccountCountry
	 * @return
	 */
	public void setApplyAccountCountry(String applyAccountCountry) {
		this.applyAccountCountry = applyAccountCountry;
	}

	/**
	 * Getter method for applyCurrencyCountry
	 * 
	 * @return String - applyCurrencyCountry
	 */
	public String getApplyCurrencyCountry() {
		return applyCurrencyCountry;
	}

	/**
	 * Setter method for applyCurrencyCountry
	 * 
	 * @param applyCurrencyCountry
	 * @return
	 */
	public void setApplyCurrencyCountry(String applyCurrencyCountry) {
		this.applyCurrencyCountry = applyCurrencyCountry;
	}

}
