
/*
 * @(#)ILMScenario.java  03/12/2013
 * 
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.maintenance.model;

import java.util.Date;
import java.util.Hashtable;

import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;


public class ILMScenario extends BaseObject implements org.swallow.model.AuditComponent {
	

	private static final long serialVersionUID = 1L;
	
	private String ilmScenarioName = null;
	private String ilmScenarioDescription = null;
	private String hostId = null;
	private String entityId = null;
	private String currencyCode = null;
	private String filterCondition = null;
	private String exclusionCondition = null;
	private String publicPrivate = null;
	private String creditSuccessRate = null;
	private String creditPctDelayed = null;
	private String creditDelayTime =null;
	private String debitSuccessRate = null;
	private String debitPctDelayed = null;
	private String debitDelayTime = null;
	private String createdByUser = null;
	private Date createDate = null;
	private Date updateDate = null;
	private String nonScnCreditSuccessRate = null ;
	private String nonScnDebitSuccessRate = null ;
	private String createDateAsString = null;
	private String txnSetId = null;
	private String defaultLegendText = null;
	//private String publicPrivateAsString;	
	
	private String allowReporting = "N";
	private String collateralAvlbl = null;
	private String creditlineAvlbl = null;
	private String unencumberedLiqAssetAvlbl = null;
	private String otherSourcesAvlbl = null;
	private String dynamicTsetQuery = null;
	private String activeScenario = "N";
	private String systemScenario = "N";
	private String throughputMonitor = "N";
	private String joinMvt = "N";

	private Id id = new Id();
	

	public static Hashtable  logTable = new Hashtable();
	static {
		logTable.put("hostId","Host Id");
		logTable.put("entityId","Entity Id");
		logTable.put("currencyCode","Currency Code");
		logTable.put("ilmScenarioId","Ilm Scenario Id");
		logTable.put("ilmScenarioName","Ilm Scenario Name");
		logTable.put("defaultLegendText","Default Legend Text");
		logTable.put("ilmScenarioDescription","Ilm Scenario Description");
		logTable.put("filterCondition","Filter Condition");
		logTable.put("exclusionCondition","Exclusion Condition");
		logTable.put("publicPrivate","Public/Private");
		logTable.put("creditSuccessRate","Credit Success Rate");
		logTable.put("creditPctDelayed","Credit Pct Delayed");
		logTable.put("creditDelayTime","Credit Delay Time");
		logTable.put("debitSuccessRate","Debit Success Rate");
		logTable.put("debitPctDelayed","Debit Pct Delayed");
		logTable.put("debitDelayTime","Debit Delay Time");
		logTable.put("createdByUser","Created By User");
		logTable.put("createDate","Create Date");
		logTable.put("updateDate","Update Date");
		logTable.put("nonScnCreditSuccessRate","Non Scn Credit Success Rate");
		logTable.put("nonScnDebitSuccessRate","Non Scn Debit Success Rate");
		logTable.put("allowReporting","Allow Reporting");
		logTable.put("collateralAvlbl","Collateral Available");
		logTable.put("creditlineAvlbl","Credit Line Available");
		logTable.put("unencumberedLiqAssetAvlbl","Unencumb. Liq Asset Available");
		logTable.put("otherSourcesAvlbl","Other Sources Available");
		logTable.put("dynamicTsetQuery","Dynamic Tset Query");
		logTable.put("activeScenario","Active Scenario");
		logTable.put("systemScenario","Sytem Scenario");
	}
	
	
	/**
	 * @return the creditSuccessRate
	 */
	public String getCreditSuccessRate() {
		return creditSuccessRate;
	}

	/**
	 * @param creditSuccessRate the creditSuccessRate to set
	 */
	public void setCreditSuccessRate(String creditSuccessRate) {
		this.creditSuccessRate = creditSuccessRate;
	}

	/**
	 * @return the creditPctDelayed
	 */
	public String getCreditPctDelayed() {
		return creditPctDelayed;
	}

	/**
	 * @param creditPctDelayed the creditPctDelayed to set
	 */
	public void setCreditPctDelayed(String creditPctDelayed) {
		this.creditPctDelayed = creditPctDelayed;
	}

	/**
	 * @return the creditDelayTime
	 */
	public String getCreditDelayTime() {
		return creditDelayTime;
	}

	/**
	 * @param creditDelayTime the creditDelayTime to set
	 */
	public void setCreditDelayTime(String creditDelayTime) {
		this.creditDelayTime = creditDelayTime;
	}

	/**
	 * @return the debitSuccessRate
	 */
	public String getDebitSuccessRate() {
		return debitSuccessRate;
	}

	/**
	 * @param debitSuccessRate the debitSuccessRate to set
	 */
	public void setDebitSuccessRate(String debitSuccessRate) {
		this.debitSuccessRate = debitSuccessRate;
	}

	/**
	 * @return the debitPctDelayed
	 */
	public String getDebitPctDelayed() {
		return debitPctDelayed;
	}

	/**
	 * @param debitPctDelayed the debitPctDelayed to set
	 */
	public void setDebitPctDelayed(String debitPctDelayed) {
		this.debitPctDelayed = debitPctDelayed;
	}

	/**
	 * @return the debitDelayTime
	 */
	public String getDebitDelayTime() {
		return debitDelayTime;
	}

	/**
	 * @param debitDelayTime the debitDelayTime to set
	 */
	public void setDebitDelayTime(String debitDelayTime) {
		this.debitDelayTime = debitDelayTime;
	}

	public String getIlmScenarioName() {
		return ilmScenarioName;
	}

	public void setIlmScenarioName(String ilmScenarioName) {
		this.ilmScenarioName = ilmScenarioName;
	}

	public String getIlmScenarioDescription() {
		return ilmScenarioDescription;
	}

	public void setIlmScenarioDescription(String ilmScenarioDescription) {
		this.ilmScenarioDescription = ilmScenarioDescription;
	}


	public String getTxnSetId() {
		return txnSetId;
	}

	public void setTxnSetId(String txnSetId) {
		this.txnSetId = txnSetId;
	}


	public String getHostId() {
		return hostId;
	}

	public void setHostId(String hostId) {
		this.hostId = hostId;
	}

	public String getEntityId() {
		return entityId;
	}

	public void setEntityId(String entityId) {
		this.entityId = entityId;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}


	public String getFilterCondition() {
		return filterCondition;
	}

	public void setFilterCondition(String filterCondition) {
		this.filterCondition = filterCondition;
	}
	
	public String getExclusionCondition() {
		return exclusionCondition;
	}

	public void setExclusionCondition(String exclusionCondition) {
		this.exclusionCondition = exclusionCondition;
	}

	public String getCreatedByUser() {
		return createdByUser;
	}

	public void setCreatedByUser(String createdByUser) {
		this.createdByUser = createdByUser;
	}



	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getPublicPrivate() {
		return publicPrivate;
	}

	public void setPublicPrivate(String publicPrivate) {
		this.publicPrivate = publicPrivate;
	}

	public Id getId() {
		return id;
	}

	public void setId(Id id) {
		this.id = id;
	}



	/**
	 * Getter method for logTable
	 * 
	 * @return logTable as Hashtable
	 */

	public static Hashtable getLogTable() {
		return logTable;
	}

	/**
	 * Setter method for logTable
	 * 
	 * @param logTable
	 */

	public static void setLogTable(Hashtable logTable) {
		ILMScenario.logTable = logTable;
	}




//	public String getPublicPrivateAsString() {
//		return publicPrivateAsString;
//	}
//
//	public void setPublicPrivateAsString(String publicPrivateAsString) {
//		this.publicPrivateAsString = publicPrivateAsString;
//	}




	public String getCreateDateAsString() {
		return createDateAsString;
	}
	/**
	 * 
	 * @return nonScnCreditSuccessRate
	 */
	public String getNonScnCreditSuccessRate() {
		return nonScnCreditSuccessRate;
	}
	/**
	 * 
	 * @param nonScnCreditSuccessRate
	 */
	public void setNonScnCreditSuccessRate(String nonScnCreditSuccessRate) {
		this.nonScnCreditSuccessRate = nonScnCreditSuccessRate;
	}
	/**
	 * 
	 * @return nonScnDebitSuccessRate
	 */
	public String getNonScnDebitSuccessRate() {
		return nonScnDebitSuccessRate;
	}
	/**
	 * 
	 * @param nonScnDebitSuccessRate
	 */
	public void setNonScnDebitSuccessRate(String nonScnDebitSuccessRate) {
		this.nonScnDebitSuccessRate = nonScnDebitSuccessRate;
	}
	/**
	 * 
	 * @param createDateAsString
	 */
	public void setCreateDateAsString(String createDateAsString) {
		this.createDateAsString = createDateAsString;
	}


	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}


	public static class Id extends BaseObject{

		 
		private String ilmScenarioId;
		

		public String getIlmScenarioId() {
			return ilmScenarioId;
		}

		public void setIlmScenarioId(String ilmScenarioId) {
			this.ilmScenarioId = ilmScenarioId;
		}

		public Id() {
		}
		
		public Id(String ilmScenarioId) {
			 this.setIlmScenarioId(ilmScenarioId);
		}

	}
	
	public String getDefaultLegendText() {
		return defaultLegendText;
	}

	public void setDefaultLegendText(String defaultLegendText) {
		this.defaultLegendText = defaultLegendText;
	}

	public String getAllowReporting() {
		return allowReporting;
	}

	public void setAllowReporting(String allowReporting) {
		this.allowReporting = SwtUtil.isEmptyOrNull(allowReporting) ? "N" : allowReporting;
	}

	public String getCollateralAvlbl() {
		return collateralAvlbl;
	}

	public void setCollateralAvlbl(String collateralAvlbl) {
		this.collateralAvlbl = collateralAvlbl;
	}

	public String getCreditlineAvlbl() {
		return creditlineAvlbl;
	}

	public void setCreditlineAvlbl(String creditlineAvlbl) {
		this.creditlineAvlbl = creditlineAvlbl;
	}

	public String getUnencumberedLiqAssetAvlbl() {
		return unencumberedLiqAssetAvlbl;
	}

	public void setUnencumberedLiqAssetAvlbl(String unencumberedLiqAssetAvlbl) {
		this.unencumberedLiqAssetAvlbl = unencumberedLiqAssetAvlbl;
	}

	public String getOtherSourcesAvlbl() {
		return otherSourcesAvlbl;
	}

	public void setOtherSourcesAvlbl(String otherSourcesAvlbl) {
		this.otherSourcesAvlbl = otherSourcesAvlbl;
	}

	public String getDynamicTsetQuery() {
		return dynamicTsetQuery;
	}

	public void setDynamicTsetQuery(String dynamicTsetQuery) {
		this.dynamicTsetQuery = dynamicTsetQuery;
	}

	public String getActiveScenario() {
		return activeScenario;
	}

	public void setActiveScenario(String activeScenario) {
		this.activeScenario = activeScenario;
	}

	public String getSystemScenario() {
		return systemScenario;
	}

	public void setSystemScenario(String systemScenario) {
		this.systemScenario = systemScenario;
	}
	
	public String getThroughputMonitor() {
		return throughputMonitor;
	}

	public void setThroughputMonitor(String throughputMonitor) {
		this.throughputMonitor = throughputMonitor;
	}
	
	public String getJoinMvt() {
		return joinMvt;
	}

	public void setJoinMvt(String joinMvt) {
		this.joinMvt = joinMvt;
	}
	
	
	
}
