package org.swallow.cluster;

import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.cache.PathChildrenCacheEvent;
import org.apache.curator.framework.recipes.cache.PathChildrenCacheListener;
import org.apache.curator.utils.ZKPaths;
import org.swallow.batchScheduler.SwtJobInfo;
import org.swallow.batchScheduler.SwtJobScheduler;
import org.swallow.control.model.Scheduler;
import org.swallow.util.SwtUtil;

/**
 * Path cache listener to holds data exchange
 * 
 * <AUTHOR> Chebka
 *
 */
public class DataListener implements PathChildrenCacheListener{
	private static final Log log = LogFactory.getLog(DataListener.class);

	@Override
	public void childEvent(CuratorFramework curatorClient, PathChildrenCacheEvent event) throws Exception {
		String nodePath = ZKPaths.getNodeFromPath(event.getData().getPath());
//		String nodeValue = new String(event.getData().getData());
		
//		System.err.println("DataListener ----- -- - -- nodePath:"+nodePath+", nodeValue:"+nodeValue);
		if(!SwtUtil.isEmptyOrNull(nodePath)) {
		    String[] dataFromPath = nodePath.split("---");
			String eventDataType = dataFromPath[0];
			String eventUiid = null;
			String moduleId = null;

			switch (eventDataType) {
				case ZkUtils.PROPERTY_UPDATE_SCHEDULER:
					eventUiid = dataFromPath[1];
					if(eventUiid.equals(ZkUtils.getInstanceId())) {
						//System.err.println("from me no need to updatee");
						return;
					}
					//System.err.println("update scheduler !!!");
					try {
						Scheduler schedulerUpdate = (Scheduler)   SerializationUtils.deserialize(event.getData().getData());
						//System.err.println(schedulerUpdate);
						if(schedulerUpdate != null) {
							SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
							schedulerUpdate.setClusterUpdate(true);
							swtJobScheduler.addUpdate(schedulerUpdate, "U");
						}
					} catch (Exception e) {
					}
					break;
				case ZkUtils.PROPERTY_ADD_SCHEDULER:
					try {
						eventUiid = dataFromPath[1];
						if(eventUiid.equals(ZkUtils.getInstanceId())) {
							//System.err.println("from me no need to updatee");
							return;
						}
						
						Scheduler schedulerAdd = (Scheduler)   SerializationUtils.deserialize(event.getData().getData());
						if(schedulerAdd != null) {
							SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
							schedulerAdd.setClusterUpdate(true);
							swtJobScheduler.addUpdate(schedulerAdd, "A");
						}
					} catch (Exception e) {
					}
					break;
					
				case ZkUtils.PROPERTY_DELETE_SCHEDULER:	
					try {
						eventUiid = dataFromPath[1];
						if(eventUiid.equals(ZkUtils.getInstanceId())) {
							return;
						}
						
						SwtJobInfo schedulerToDelete = (SwtJobInfo)   SerializationUtils.deserialize(event.getData().getData());
						if(schedulerToDelete != null) {
							schedulerToDelete.setClusterUpdate(true);
							SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
							swtJobScheduler.killJob(schedulerToDelete, "D");
						}
					} catch (Exception e) {
					}
				break;
				case ZkUtils.PROPERTY_RUN_NOW_SCHEDULER:	
					try {
						eventUiid = dataFromPath[1];
						if(eventUiid.equals(ZkUtils.getInstanceId())) {
							return;
						}
						
						SwtJobInfo schedulerToRun = (SwtJobInfo)   SerializationUtils.deserialize(event.getData().getData());
						if(schedulerToRun != null) {
							schedulerToRun.setClusterUpdate(true);
							SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
							swtJobScheduler.runImmediate(schedulerToRun);
						}
					} catch (Exception e) {
					}
					break;
				case ZkUtils.PROPERTY_KILL_SCHEDULER:	
					try {
						eventUiid = dataFromPath[1];
						if(eventUiid.equals(ZkUtils.getInstanceId())) {
							return;
						}
						SwtJobInfo schedulerToKill = (SwtJobInfo)   SerializationUtils.deserialize(event.getData().getData());
						if(schedulerToKill != null) {
							schedulerToKill.setClusterUpdate(true);
							SwtJobScheduler swtJobScheduler = SwtJobScheduler.getInstance();
							swtJobScheduler.killJob(schedulerToKill, "T");
						}
					} catch (Exception e) {
					}
					break;
				case ZkUtils.PROPERTY_START_JOB:
					break;
				case ZkUtils.PROPERTY_CONNECTION_STATS:	
					break;
				case ZkUtils.PROPERTY_KILL_CONNECTION:	
					break;
				case ZkUtils.PROPERTY_SHUTDOWN_LOG:	
					String nodeValue = new String(event.getData().getData());
					log.warn("[THIS IS NOT A REAL ERROR] "+nodeValue);
					break;
				default:
					break;
			}
		}
		
		/*switch (event.getType()) {
			case CHILD_ADDED:
				Creator.console.outText("Node added: " + ZKPaths.getNodeFromPath(event.getData().getPath())+" => "+new String(event.getData().getData()));
				break;
	
			case CHILD_UPDATED:
				Creator.console.outText("Node changed: " + ZKPaths.getNodeFromPath(event.getData().getPath())+" => "+new String(event.getData().getData()));
				break;
	
			case CHILD_REMOVED:
				Creator.console.outText("Node removed: " + ZKPaths.getNodeFromPath(event.getData().getPath())+" => "+new String(event.getData().getData()));
				break;
			default:
				Creator.console.outText("Node removed: " + ZKPaths.getNodeFromPath(event.getData().getPath())+" => "+new String(event.getData().getData()));
				break;
		}*/
	}

}
