package org.swallow.exception;

public class SwtException extends Exception {
	private String errorCode;
	private String errorLogFlag;
	private String errorId;
	private String errorDesc;
	private String srcCodeLocation;	
	/*START : Code for updating S_ERROR_LOG properly for user information 12/06/07*/
	private String userId;
	/*END: Code for updating S_ERROR_LOG properly for user information 12/06/07*/
	
	public SwtException(){		
	}
	
	public SwtException(String errorCode){
		this.errorCode = errorCode;
	}
	
	public SwtException(String errorCode, Throwable root){
		super(errorCode, root);
		this.errorCode = errorCode;
	}
	
	
	public SwtException(String errorCode,String errorLogFlag){
		this.errorCode = errorCode;
		this.errorLogFlag = errorLogFlag; 
	}
	public SwtException(String errorCode,String errorLogFlag, String errorId, String errorDesc,String srcCodeLocation){
		this.errorCode = errorCode;
		this.errorLogFlag = errorLogFlag; 
		this.errorId=errorId;
		this.errorDesc=errorDesc;
		this.srcCodeLocation = srcCodeLocation;
	}
	
	public String getErrorCode(){
		return errorCode;
	}
	
	
	public String getMessage(){
		return errorCode;
	}
	public String getErrorLogFlag() {
		return errorLogFlag;
	}
	public void setErrorLogFlag(String errorLogFlag) {
		this.errorLogFlag = errorLogFlag;
	}
	
	public String getErrorDesc() {
		return errorDesc;
	}
	public void setErrorDesc(String errorDesc) {
		this.errorDesc = errorDesc;
	}
	public String getErrorId() {
		return errorId;
	}
	public void setErrorId(String errorId) {
		this.errorId = errorId;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	/**
	 * @return Returns the srcCodeLocation.
	 */
	public String getSrcCodeLocation() {
		return srcCodeLocation;
	}
	/**
	 * @param srcCodeLocation The srcCodeLocation to set.
	 */
	public void setSrcCodeLocation(String srcCodeLocation) {
		this.srcCodeLocation = srcCodeLocation;
	}
	/*START : Code for updating S_ERROR_LOG properly in case of DB_SERVER error 12/06/07*/

	/**
	 * @return UserId creating this swt exception object
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * set the userId while creating swtexception object
	 * @param userId
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	/*END : Code for updating S_ERROR_LOG properly in case of DB_SERVER error 12/06/07*/
}