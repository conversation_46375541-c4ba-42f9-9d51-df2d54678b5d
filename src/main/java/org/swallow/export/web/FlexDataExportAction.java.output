/*
 * @(#) FlexDataExportAction.java 1.0 21/12/05
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.web;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.export.service.SwtDynamicReport;
import org.swallow.export.service.impl.Xml2CsvImpl;
import org.swallow.export.service.impl.Xml2PdfImpl;
import org.swallow.export.service.impl.Xml2XlsImpl;
import org.swallow.util.SwtUtil;


import com.opensymphony.xwork2.ActionSupport;

import org.swallow.util.SwtConstants;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Namespace;
import org.apache.struts2.convention.annotation.Result;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.struts2.convention.annotation.Result;
import org.apache.struts2.convention.annotation.AllowedMethods;
import org.apache.struts2.convention.annotation.Action;
import org.apache.struts2.ServletActionContext;

/**
 * FlexDataExportAction.java<br>
 * 
 * This class has methods that are used to export the grid data to the following
 * formats:<br>
 * <ul>
 * <li>Excel</li>
 * <li>Comma Separated Values</li>
 * <li>PDF</li>
 * </ul>
 */
// @NOT MIGRATED YET !
@Action(value = "/flexdataexport", results = {
})

@AllowedMethods ({})
public class FlexDataExportAction extends CustomActionSupport {
public String execute() throws Exception {
    HttpServletRequest request = ServletActionContext.getRequest();

    // List of methods 
    String method = String.valueOf(request.getParameter("method"));

    switch (method) {
        default:
            break;
    }

    return unspecified();
}




	protected static final Log log = LogFactory.getLog(FlexDataExportAction.class);

	/**
	 * This method is used to export the grid data to comma separated
	 * values('.csv') format.<br>
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws SwtException
	 */

	public void csv(HttpServletRequest request, HttpServletResponse response)
			throws SwtException {
		/* Local Variable Declaration */
		// Variable that holds xmlConverter
		Xml2CsvImpl xmlConverter = null;
		// Variable that holds the response string which is in csv format
		String csvResponse = null;
		// Variable that holds the screen name
		String fileName = null;
		// Variable that holds message
		String message = null;
		try {
			log.debug(this.getClass().getName() + " - [csv] - " + "Entry");
			// Instantiating Xml2CsvImpl object
			xmlConverter = new Xml2CsvImpl();
			// Variable that holds the response string which is in csv format
			csvResponse = "";
			// Variable that holds the screen name
			fileName = request.getParameter("screen").replaceAll(" ", "");
			// Checks the data from request is null or not
			if (request.getParameter("data") != null) {
					try {
						// Converts the request data to csv format and sets in
						// csvResponse string object
						csvResponse = xmlConverter.convertXML(request
								.getParameter("data"), fileName);
					} catch (SwtException e) {
						message = e.getStackTrace()[0].getClassName() + "."
								+ e.getStackTrace()[0].getMethodName() + ":"
								+ e.getStackTrace()[0].getLineNumber() + " "
								+ e.getMessage();
						log
								.error(message
										+ " - SwtException Catched in [csv] method : - ");
						SwtUtil.logException(e, request, "");
					} catch (Exception e) {
						log.error(this.getClass().getName()
								+ " - Exception Catched in [csv] method : - "
								+ e.getMessage());
						throw e;
					}
				
			}

			response.setContentType("application/vnd.ms-excel");
			/*
			 * Start Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			response.setHeader("Content-disposition", "attachment; filename="
					+ fileName + SwtUtil.getWindowsTitleSuffix() + "_"
					+ SwtUtil.FormatCurrentDate() + ".csv");
			/*
			 * End Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			try {
				response.getOutputStream().print(csvResponse);
			} catch (IOException e) {
				log.error(this.getClass().getName()
						+ " - IOException Catched in [csv] method : - "
						+ e.getMessage());
				throw e;
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [csv] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "csv", FlexDataExportAction.class), request, "");
		} finally {
			log.debug(this.getClass().getName() + " - [csv] - " + "Exit");
			// Null the already created objects
			xmlConverter = null;
			csvResponse = null;
			fileName = null;
			message = null;
		}
	}

	/**
	 * This method is used to export the grid data to Excel format.<br>
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws SwtException
	 */
	public void excel(HttpServletRequest request, HttpServletResponse response)
			throws SwtException {
		/* Local Variable declarations */
		// Variable that holds xmlConverter
		Xml2XlsImpl xmlConverter = null;
		// Variable that holds fileName
		String fileName = null;
		// Variable that holds message
		String message = null;

		try {
			log.debug(this.getClass().getName() + " - [excel] - " + "Entry");
			xmlConverter = new Xml2XlsImpl();
			// Getting filename
			fileName = request.getParameter("screen").replaceAll(" ", "");
			response.setContentType("application/vnd.ms-excel");
			/*
			 * Start Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			response.setHeader("Content-disposition", "attachment; filename="
					+ fileName + SwtUtil.getWindowsTitleSuffix() + "_"
					+ SwtUtil.FormatCurrentDate() + ".xls");
			/*
			 * End Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			if (request.getParameter("data") != null) {
					try {
						xmlConverter.convertXML(request.getParameter("data"),
								response.getOutputStream(), fileName);
					} catch (SwtException e) {
						message = e.getStackTrace()[0].getClassName() + "."
								+ e.getStackTrace()[0].getMethodName() + ":"
								+ e.getStackTrace()[0].getLineNumber() + " "
								+ e.getMessage();
						log
								.error(message
										+ " - SwtException Catched in [excel] method : - ");
						SwtUtil.logException(e, request, "");
					} catch (Exception e) {
						log.error(this.getClass().getName()
								+ " - Exception Catched in [excel] method : - "
								+ e.getMessage());
						throw e;
					}
				
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [excel] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "excel", FlexDataExportAction.class), request, "");
		} finally {
			log.debug(this.getClass().getName() + " - [excel] - " + "Exit");
			// Null the already created objects
			xmlConverter = null;
			fileName = null;
			message = null;
		}
	}

	/**
	 * This method is used to export the grid data to PDF format.<br>
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws SwtException
	 */
	public void pdf(HttpServletRequest request, HttpServletResponse response)
			throws SwtException {
		/* Local Variable declarations */
		// Variable that holds xmlConverter
		Xml2PdfImpl xmlConverter = null;
		// Variable that holds fileName
		String fileName = null;
		// Variable that holds message
		String message = null;
		try {
			log.debug(this.getClass().getName() + " - [pdf] - " + "Entry");
			// Initializing Xml2PdfImpl object
			xmlConverter = new Xml2PdfImpl();
			// Getting filename
			fileName = request.getParameter("screen").replaceAll(" ", "");
			response.setContentType("application/pdf");

			/*
			 * Start Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			response.setHeader("Content-disposition", "attachment; filename="
					+ fileName + SwtUtil.getWindowsTitleSuffix() + "_"
					+ SwtUtil.FormatCurrentDate() + ".pdf");
			/*
			 * End Code modified by Chidambaranathan for include timestamp in
			 * the export function for Mantis_1513 on 04-Aug-2011
			 */
			if (request.getParameter("data") != null) {
					try {
						xmlConverter.convertXML(request.getParameter("data"),
								response.getOutputStream(), fileName);
					} catch (SwtException e) {
						message = e.getStackTrace()[0].getClassName() + "."
								+ e.getStackTrace()[0].getMethodName() + ":"
								+ e.getStackTrace()[0].getLineNumber() + " "
								+ e.getMessage();
						log
								.error(message
										+ " - SwtException Catched in [pdf] method : - ");
						SwtUtil.logException(e, request, "");
					} catch (Exception e) {
						log.error(this.getClass().getName()
								+ " - Exception Catched in [pdf] method : - "
								+ e.getMessage());
						throw e;
					}
			}
		} catch (Exception e) {
			log.error(this.getClass().getName()
					+ " - Exception Catched in [pdf] method : - "
					+ e.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					e, "pdf", FlexDataExportAction.class), request, "");

		} finally {
			log.debug(this.getClass().getName() + " - [pdf] - " + "Exit");
			// Null the already created objects
			xmlConverter = null;
			fileName = null;
			message = null;
		}
	}
}
