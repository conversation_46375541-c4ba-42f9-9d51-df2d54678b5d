<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version last-->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Chart" pageWidth="595" pageHeight="842" whenNoDataType="NoDataSection" columnWidth="575" columnSpacing="5" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10" scriptletClass="ar.com.fdvs.dj.core.DJDefaultScriptlet" whenResourceMissingType="Key" uuid="dd519195-158a-4da6-b086-6a58fc8ead81">
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="dj_style_1_" mode="Transparent" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Left" vAlign="Bottom" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="11" isBold="true" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2">
			<pen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="reportSubtitleStyle" mode="Transparent" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Left" vAlign="Bottom" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2">
			<pen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="dj_style_2_" mode="Opaque" forecolor="#FFFFFF" backcolor="#187EE7" radius="0" hAlign="Center" vAlign="Top" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="0" topPadding="2" bottomPadding="2">
			<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="dj_style_3_" mode="Opaque" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Center" vAlign="Top" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2" leftPadding="1">
			<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="Alternate_Verdana_for_detail_sample" mode="Opaque" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Center" vAlign="Top" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2" leftPadding="1">
			<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[new java.lang.Boolean(((Number)$V{REPORT_COUNT}).doubleValue() % 2 == 0)]]></conditionExpression>
			<style mode="Opaque" backcolor="#E0F0FF"/>
		</conditionalStyle>
	</style>
	<style name="dj_style_4_" mode="Transparent" forecolor="#000000" backcolor="#FFFFFF" radius="0" hAlign="Center" vAlign="Top" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2">
			<pen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<style name="dj_style_5_" mode="Opaque" forecolor="#000000" backcolor="#E6E6E6" radius="0" hAlign="Center" vAlign="Bottom" rotation="None" isBlankWhenNull="true" fontName="Arial" fontSize="10" isBold="false" isItalic="false" isUnderline="false" isPdfEmbedded="false">
		<box padding="2">
			<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="chartImageBase64" class="java.lang.String" isForPrompting="false"/>
	<parameter name="legendImageBase64" class="java.lang.String" isForPrompting="false"/>
	<parameter name="styledSubTitle" class="java.lang.String" isForPrompting="false"/>
	<field name="field_sample" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="50">
			<textField isStretchWithOverflow="true">
				<reportElement uuid="7552bba1-7a06-495f-a9c4-4a14fcb6fdf6" style="dj_style_1_" positionType="Float" x="0" y="0" width="575" height="30" isRemoveLineWhenBlank="true"/>
				<textElement markup="styled"/>
				<textFieldExpression><![CDATA["<style  isBold='true' pdfFontName='Helvetica'>Intraday Liquidity Export - "+new java.util.Date()+"</style>"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement uuid="89076b87-b299-42d5-b32b-fa51fb7dafcc" style="reportSubtitleStyle" positionType="Float" x="0" y="30" width="575" height="20" isRemoveLineWhenBlank="true"/>
				<textElement markup="styled"/>
				<textFieldExpression><![CDATA[$P{styledSubTitle}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="525">
			<printWhenExpression><![CDATA[new java.lang.Boolean(((Number)$V{PAGE_NUMBER}).doubleValue() == 1 && $P{legendImageBase64} == null)]]></printWhenExpression>
			<image scaleImage="FillFrame">
				<reportElement key="imageChartKey" uuid="324ac245-b6ab-4967-86e4-1a6b9f08824f" x="0" y="10" width="820" height="515" isRemoveLineWhenBlank="true"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<imageExpression><![CDATA[new ByteArrayInputStream(new org.apache.commons.codec.binary.Base64().decodeBase64($P{chartImageBase64}.getBytes("UTF-8")))]]></imageExpression>
			</image>
		</band>
		<band height="525">
			<printWhenExpression><![CDATA[new java.lang.Boolean(((Number)$V{PAGE_NUMBER}).doubleValue() == 1 && $P{legendImageBase64} != null)]]></printWhenExpression>
			<image scaleImage="FillFrame">
				<reportElement uuid="324ac245-b6ab-4967-86e4-1a6b9f08824f" x="0" y="10" width="680" height="515" isRemoveLineWhenBlank="true"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<imageExpression><![CDATA[new ByteArrayInputStream(new org.apache.commons.codec.binary.Base64().decodeBase64($P{chartImageBase64}.getBytes("UTF-8")))]]></imageExpression>
			</image>
			<image scaleImage="FillFrame">
				<reportElement uuid="324ac245-b6ab-4967-86e4-1a6b9f08824f" x="680" y="10" width="140" height="515" isRemoveLineWhenBlank="true"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<imageExpression><![CDATA[new ByteArrayInputStream(new org.apache.commons.codec.binary.Base64().decodeBase64($P{legendImageBase64}.getBytes("UTF-8")))]]></imageExpression>
			</image>
		</band>
	</detail>
</jasperReport>
