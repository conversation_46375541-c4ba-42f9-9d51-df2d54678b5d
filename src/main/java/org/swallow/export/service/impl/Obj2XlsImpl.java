/*
 * @(#)Obj2XlsImpl.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.export.service.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.PageOrientation;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.swallow.exception.SwtException;
import org.swallow.export.model.ColumnDTO;
import org.swallow.export.model.FilterDTO;
import org.swallow.export.service.Obj2Xls;
import org.swallow.model.ExportObject;
import org.swallow.model.GraphObject;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;

public class Obj2XlsImpl implements Obj2Xls {

	// Initializes Log
	private final Log log = LogFactory.getLog(Obj2XlsImpl.class);

	/**
	 * This method is used to convert the resulting elements (columnData, rowData, totalData and graphData) to corresponding
	 * XLS format.<br>
	 *
	 * @param request
	 * @param response
	 * @param columnData
	 * @param filterData
	 * @param rowData
	 * @param totalData
	 * @param graphData
	 * @param screenName
	 * @throws SwtException
	 */
	public void convertObject(HttpServletRequest request, HttpServletResponse response, ArrayList<ColumnDTO> columnData,
			ArrayList<FilterDTO> filterData,
			ArrayList<ArrayList<ExportObject>> rowData, Collection totalData,
			ArrayList<ArrayList<GraphObject>> graphData,
			String screenName) throws SwtException
	{

		// Initializes the rowCount
		int rowCount;
		// Declaring WritableWorkbook object
		WritableWorkbook writableWorkbook = null;
		// Declaring WritableSheet object
		WritableSheet sheet = null;

		ArrayList<ExportObject> subNodes = null;

		// Declaring filterFormat object
		WritableCellFormat filterFormat = null;
		// Declaring the filterLabel object
		Label filterLabel = null;
		// Declaring the headingLabel object
		Label headingLabel = null;
		// Declaring the rowLabel object
		Label rowLabel = null;
		// Declaring the totalLabel object
		Label totalLabel = null;
		// Declaring the filterFont object
		WritableFont filterFont = null;
		// Declaring the headerFont object
		WritableFont headerFont = null;
		// Declaring the tableFont object
		WritableFont tableFont = null;
		// Declaring WritableFont object for RED
		WritableFont writableFontRed = null;
		// Declaring the WritableFont object for BLACK
		WritableFont writableFontBlack = null;
		// Declaring the writableFnt object
		WritableFont writableFnt = null;
		// Declaring the blackWritableFont object
		WritableFont blackWritableFont = null;
		// Declaring the headerRowFormat object
		WritableCellFormat headerRowFormat = null;
		// Declaring the blue row format (one of the alternate colors of the
		// data grid rows)
		WritableCellFormat blueRowFormat = null;
		// Declaring the whiteRowFormat (one of the alternate colors of the data
		// grid rows)
		WritableCellFormat whiteRowFormat = null;
		// Declaring the row format
		WritableCellFormat pRowFormat = null;
		// Declaring the row cell format
		WritableCellFormat nRowFormat = null;
		// Declaring the holiday row format
		WritableCellFormat holidayRowFormat = null;
		// Declaring cellFormatLeft object
		WritableCellFormat cellFormatLeft = null;
		// Declaring the cell format for central alignment
		WritableCellFormat cellFormatCentre = null;
		// Declaring the cell format to right alignment
		WritableCellFormat cellFormatRight = null;
		// Declaring the cell format for red font size
		WritableCellFormat cellFormatRed = null;
		// Declaring the cell format for black
		WritableCellFormat cellFormatBlack = null;
		// Declaring the cell format for total row data
		WritableCellFormat totalRowFormat = null;
		// Declaring totalCellFmtLeft object
		WritableCellFormat totalCellFmtLeft = null;
		// Declaring the writableFormat for total row
		WritableCellFormat writableFormat = null;
		// Declaring the cell format for total node
		WritableCellFormat writableCell = null;
		// Declaring the cell format for red font
		WritableCellFormat writableCellFormat = null;
		// Declaring the writableCelFmtBlack object
		WritableCellFormat writableCellFmtBlack = null;

		// String declared to hold the sheet name
		String sheetName = null;
		// will hold the output stream of the response
		OutputStream out;
		// common data manager from the session
		CommonDataManager CDM = null;
		// hold the cancelExport value in CDM
		String cancelExport = ""; 

		try {
			log.debug(this.getClass().getName() + "- [convertObject] - Start");
			CDM = (CommonDataManager) request.getSession()
					.getAttribute("CDM");
			rowCount = 0;
			// the output stream of the response
			out = response.getOutputStream();
			// Writes the data from the OutputStream to the writableWorkBook
			writableWorkbook = Workbook.createWorkbook(out);
			// Sheet name is assigned based on the screen name length
			sheetName = (screenName.length() <= SwtConstants.WORKSHEET_LENGTH) ? screenName
					: screenName.substring(0, screenName.indexOf("-"));
			if (sheetName.length() > SwtConstants.WORKSHEET_LENGTH) {
				sheetName = sheetName.substring(0,
						SwtConstants.WORKSHEET_LENGTH);
				// Error logged to say that Sheet name is not shown fully since
				// it exceeds the length of SwtConstants.WORKSHEET_LENGTH (actually 31)
				log.error(this.getClass().getName()
						+ " - [convertObject] - Sheet Name exceeds max limit so name is truncated");
			}

			// Converts the work book details to a sheet
			sheet = writableWorkbook.createSheet(sheetName, 0);
			// Sets the sheet orientation to Landscape
			sheet.setPageSetup(PageOrientation.LANDSCAPE);
			// Sets the font style of the data to Arial and Font size to 10
			filterFont = new WritableFont(WritableFont.createFont("Arial"), 10);
			// Setting the font to Bold
			filterFont.setBoldStyle(WritableFont.BOLD);
			// Initializing the WritableCellFormat object with the filter details
			filterFormat = new WritableCellFormat(filterFont);
			// Appending the screen name, document's title and date with the resulting data
			sheet.addCell(new Label(0, 1, screenName + " "
					+ SwtUtil.getWindowsTitleSuffix()
					+ SwtUtil.getSysParamDate()));
			// Setting the initial row count to 3
			rowCount = 3;
			// Loops through the filterNodeList and sets filter label to the sheet
			for (int i = 0; i < filterData.size(); i++) {
				filterLabel = new Label(0, rowCount, ""
						+ filterData.get(i).getName());
				// Sets the cell format to the filter label
				filterLabel.setCellFormat(filterFormat);
				// Adds a cell to the sheet
				sheet.addCell(filterLabel);

				// Gets the filter data and adds it as a cell value in the WritableSheet object
				sheet.addCell(new Label(1, rowCount, filterData.get(i)
						.getValue() == null ? "" : filterData.get(i).getValue()));
				// incrementing the row count
				rowCount++;
			}
			// incrementing the row count
			rowCount++;
			// Setting the header font to Arial and font size to 12
			headerFont = new WritableFont(WritableFont.createFont("Arial"), 12);
			// Setting the font color to white
			headerFont.setColour(Colour.WHITE);
			// Setting the font weight to bold
			headerFont.setBoldStyle(WritableFont.BOLD);

			// Initializing the WritableCellFormat object with the header details
			headerRowFormat = new WritableCellFormat(headerFont);
			// Aligning the header info to center
			headerRowFormat.setAlignment(Alignment.CENTRE);
			// Setting the vertical alignment of the header info to bottom
			headerRowFormat.setVerticalAlignment(VerticalAlignment.BOTTOM);
			// Setting border line and thickness to the header info
			headerRowFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			// Setting the background color for header info
			headerRowFormat.setBackground(Colour.BLUE);
			// Loops through the columnNodeList and sets the label to the sheet
			for (int i = 0; i < columnData.size(); i++) {
				// Instantiating the heading label
				headingLabel = new Label(i, rowCount, columnData.get(i).getHeading());
				// Sets the cell format to the heading label
				headingLabel.setCellFormat(headerRowFormat);
				// Adds the heading label as a cell to the sheet
				sheet.addCell(headingLabel);
				// if screen is entity monitor set the column width
				if (screenName.equals(SwtConstants.ENTITY_MONITOR_TITLE)) {
					sheet.setColumnView(i, 23);
				}
			}
			// Creating a writable font with font style as Arial and font size as 10
			tableFont = new WritableFont(WritableFont.createFont("Arial"), 10);
			// Initializing the WritableCellFormat object with the table information
			blueRowFormat = new WritableCellFormat(tableFont);
			// Setting the row alignment to left
			blueRowFormat.setAlignment(Alignment.LEFT);
			// Setting the vertical alignment of the row to bottom
			blueRowFormat.setVerticalAlignment(VerticalAlignment.BOTTOM);
			// Setting a border line and it's thickness
			blueRowFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			// Setting the background color for the row
			blueRowFormat.setBackground(Colour.LIGHT_TURQUOISE);
			blueRowFormat.setWrap(true);

			// Initializing the WritableCellFormat object with the table information
			whiteRowFormat = new WritableCellFormat(tableFont);
			// Setting the row alignment to left
			whiteRowFormat.setAlignment(Alignment.LEFT);
			// Setting the vertical alignment of the row to bottom
			whiteRowFormat.setVerticalAlignment(VerticalAlignment.BOTTOM);
			// Setting a border line and it's thickness
			whiteRowFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			// Setting the background color for the row
			whiteRowFormat.setBackground(Colour.WHITE);
			whiteRowFormat.setWrap(true);

			// Initializing the WritableCellFormat object with the table information
			pRowFormat = new WritableCellFormat(tableFont);
			// Setting the row alignment to left
			pRowFormat.setAlignment(Alignment.LEFT);
			// Setting the vertical alignment of the row to bottom
			pRowFormat.setVerticalAlignment(VerticalAlignment.BOTTOM);
			// Setting a border line and it's thickness
			pRowFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			// Setting the background color for the row
			pRowFormat.setBackground(Colour.GREY_25_PERCENT);
			pRowFormat.setWrap(true);

			// Initializing the WritableCellFormat object with the table information
			nRowFormat = new WritableCellFormat(tableFont);
			// Setting the row alignment to left
			nRowFormat.setAlignment(Alignment.LEFT);
			// Setting the vertical alignment of the row to bottom
			nRowFormat.setVerticalAlignment(VerticalAlignment.BOTTOM);
			// Setting a border line and it's thickness
			nRowFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			// Setting the background color for the row
			nRowFormat.setBackground(Colour.GREY_50_PERCENT);
			nRowFormat.setWrap(true);

			// Initializing the WritableCellFormat object with the table information
			holidayRowFormat = new WritableCellFormat(tableFont);
			// Setting the row alignment to left
			holidayRowFormat.setAlignment(Alignment.LEFT);
			// Setting the vertical alignment of the row to bottom
			holidayRowFormat.setVerticalAlignment(VerticalAlignment.BOTTOM);
			// Setting a border line and it's thickness
			holidayRowFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			// Setting the background color for the row
			holidayRowFormat.setBackground(Colour.GREY_40_PERCENT);
			holidayRowFormat.setWrap(true);
			
			cancelExport = CDM.getCancelExport();
			if(SwtUtil.isEmptyOrNull(cancelExport)||(!cancelExport.equals("true")))
				cancelExport="false";
			
			// Loops through the row node list and adds data to the node
			for (int i = 0; i < rowData.size(); i++) {
				cancelExport = CDM.getCancelExport();
				if (cancelExport.equals("true"))
					break;
				// Increments the row count
				rowCount++;
				// Gets the child nodes
				subNodes = rowData.get(i);
				// Loops through the columnNodeList and sets the child nodes
				for (int j = 0; j < subNodes.size(); j++) {
					
					// Checks the sub nodes and adds the first child node to it
					if (subNodes.get(j) != null) {
						ExportObject row  = subNodes.get(j);
						// Checks the first child of the sub nodes and sets
						// the attributes to it
						if (row.getValue() != null) {
							if (columnData.get(j).getDataElement().
									equalsIgnoreCase(row.getColumnName())) {
								
								// Instantiates the rowLabel object
								rowLabel = new Label(j, rowCount, ""+ row.getValue());
								// Checks the modulus value of rowNodeList
								// and sets the background color to the row
								if (i % 2 == 1) {
									// sets the background color to blue
									rowLabel.setCellFormat(blueRowFormat);
								} else {
									// sets the background color to white
									rowLabel.setCellFormat(whiteRowFormat);
								}

								if (row.getSum() != null) {
									
									if (row.getSum().equalsIgnoreCase("N")) 
										rowLabel.setCellFormat(nRowFormat);// N
									else if (row.getSum().equalsIgnoreCase("P")) 
										rowLabel.setCellFormat(pRowFormat);// P
									
								}

								// Override the background color if there is a holiday flag set
								if (row.isHoliday()) 
										rowLabel.setCellFormat(holidayRowFormat); 
									
								// If the node type is 'str', align the cell value to left
								if (row.getType().equalsIgnoreCase("str")) {
									// Instantiates and gets the cell format
									cellFormatLeft = new WritableCellFormat(rowLabel.getCellFormat());
									// Sets the cell alignment to left
									cellFormatLeft.setAlignment(Alignment.LEFT);
									// Sets the cell format of the row label
									rowLabel.setCellFormat(cellFormatLeft);
								} else if (row.getType().equalsIgnoreCase("bool")
										|| row.getType().equalsIgnoreCase("char")) {
									
									// Instantiates and gets the cell format
									cellFormatCentre = new WritableCellFormat(
											rowLabel.getCellFormat());
									// If the node type is 'bool' or 'char', text alignment should be center
									cellFormatCentre.setAlignment(Alignment.CENTRE);
									// Sets the cell format to the row label
									rowLabel.setCellFormat(cellFormatCentre);
									
								} else if (row.getType().equalsIgnoreCase("num")) {
									
									// Instantiates and gets the cell format for 'num' type
									cellFormatRight = new WritableCellFormat(rowLabel.getCellFormat());
									// If the node type is 'num' then the text alignment should be right
									cellFormatRight.setAlignment(Alignment.RIGHT);
									// Sets the cell format
									rowLabel.setCellFormat(cellFormatRight);
									
								}

								// Set color of the text depending on the negative flag
								if (row.isNegative()) {
									
									// Instantiates the cell format for negative node item
									cellFormatRed = new WritableCellFormat(
											rowLabel.getCellFormat());
									// Instantiates and gets the font for negative node item
									writableFontRed = new WritableFont(cellFormatRed.getFont());
									// Sets the font color to red
									writableFontRed.setColour(Colour.RED);
									// Sets the color to WritableFont object
									cellFormatRed.setFont(writableFontRed);
									// Sets the WritableFont object to Label
									rowLabel.setCellFormat(cellFormatRed);

								} else {
									// Instantiates the cell format for positive node item
									cellFormatBlack = new WritableCellFormat(
											rowLabel.getCellFormat());
									// Instantiates and gets the font for positive node item
									writableFontBlack = new WritableFont(cellFormatBlack.getFont());
									// Sets the font color to black
									writableFontBlack.setColour(Colour.BLACK);
									cellFormatBlack.setFont(writableFontBlack);
									// Sets the color to WritableFont object
									rowLabel.setCellFormat(cellFormatBlack);
								}
								
								sheet.addCell(rowLabel);
							}
						}
					}
				}
			}

			// Initializing the WritableCellFormat object with the table information
			totalRowFormat = new WritableCellFormat(tableFont);
			// Setting the row alignment to left
			totalRowFormat.setAlignment(Alignment.LEFT);
			// Setting the vertical alignment of the row to bottom
			totalRowFormat.setVerticalAlignment(VerticalAlignment.BOTTOM);
			// Setting a border line and it's thickness
			totalRowFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
			// Setting the background color for the row
			totalRowFormat.setBackground(Colour.YELLOW);
			// Wraps the text in the total row
			totalRowFormat.setWrap(true);

			if (totalData != null) {
				
				rowCount++;
					// First display the "total" word in the first
					// column
					totalLabel = new Label(0, rowCount, "total");
					totalLabel.setCellFormat(totalRowFormat);
					sheet.addCell(totalLabel);
					// Main population of totals data
					// Main population of totals data
					Iterator it = totalData.iterator();
					ExportObject row = (ExportObject) it.next();
					for (int j = 1; j < columnData.size(); j++) {												
						if(columnData.get(j).getDataElement().
								equalsIgnoreCase(row.getColumnName()))
						{	

						// if total value is empty then set
						// the flag
						if (row.getValue() != null){
							// Instantiates and sets the Label for total row format
							totalLabel = new Label(j, rowCount,"" + row.getValue());
							// Sets the cell format to the label
							totalLabel.setCellFormat(totalRowFormat);
							
							// If the node type is 'str', align the cell value to left
							if (columnData.get(j).getType().equalsIgnoreCase("str")) {
								
								// Instantiates and gets the cell format
								totalCellFmtLeft = new WritableCellFormat(
										totalLabel.getCellFormat());
								// Sets the cell alignment to left
								totalCellFmtLeft.setAlignment(Alignment.LEFT);
								// Sets the cell format to the label
								totalLabel.setCellFormat(totalCellFmtLeft);
								
							} else if (columnData.get(j).getType().equalsIgnoreCase("bool")
									|| columnData.get(j).getType().equalsIgnoreCase(
													"char")) {
								
								// Instantiates and gets the cell format
								writableFormat = new WritableCellFormat(
										totalLabel.getCellFormat());
								// Sets the cell format as center
								writableFormat.setAlignment(Alignment.CENTRE);
								// Sets the cell format to the label
								totalLabel.setCellFormat(writableFormat);
								
							} else if (columnData.get(j).getType()
									.equalsIgnoreCase("num")) {
								
								// Instantiates and gets the cell format
								writableCell = new WritableCellFormat(totalLabel.getCellFormat());
								// Sets the cell alignment to right
								writableCell.setAlignment(Alignment.RIGHT);
								// Sets the cell format to label object
								totalLabel.setCellFormat(writableCell);
								
							}
							// Set color of the text depending on the negative flag
							if (columnData.get(j).getType().equalsIgnoreCase("num")) {
								
								if (Double.valueOf(row.getValue().replace(",", "").replace(".", "")) >= 0) {
									
									// Instantiates and getsthe cell format for red font
									writableCellFormat = new WritableCellFormat(totalLabel
													.getCellFormat());
									// Instantiates and gets the font for WritableFont object
									writableFnt = new WritableFont(writableCellFormat
													.getFont());
									// Sets the font to red
									writableFnt.setColour(Colour.RED);
									// Sets the font to the cell format
									writableCellFormat.setFont(writableFnt);
									// Sets the cell format to the label
									totalLabel.setCellFormat(writableCellFormat);
								} else {
									
									// Instantiates and gets the cell format for black font
									writableCellFmtBlack = new WritableCellFormat(
											totalLabel.getCellFormat());
									// Instantiates and gets the font for WritableFont object
									blackWritableFont = new WritableFont(
											writableCellFmtBlack.getFont());
									// Sets the color as black for WritableFont
									blackWritableFont.setColour(Colour.BLACK);
									// Sets the font to WritableCellFormat
									writableCellFmtBlack.setFont(blackWritableFont);
									// Sets the WritableCellFormat to label
									totalLabel.setCellFormat(writableCellFmtBlack);
									
								}
							}
							// Adds the label to the WritableSheet
							sheet.addCell(totalLabel);							
						}
						if(it.hasNext())
							row = (ExportObject) it.next();
						}else {
							// Instantiates and sets the Label for total row format
							totalLabel = new Label(j, rowCount,"");
							// Sets the cell format to the label
							totalLabel.setCellFormat(totalRowFormat);
							sheet.addCell(totalLabel);

						}
				}
			}
			
			// if we have cancel of the export then change the header and the content of the response
			if (cancelExport.equals("true")){
				response.setContentType("text/html");
				response.setHeader("Content-disposition","inline");
			
			}else{
				// Writes the data in writableWorkbook
				writableWorkbook.write();
				// Closing the writableWorkbook
				writableWorkbook.close();
			}
			
			// Cleaning the OutputStream
			if (out != null)
				try{
					out.close();
				}catch (IOException ex){
					response.setContentType("text/html");
					response.setHeader("Content-disposition","inline");

				}
		}catch(OutOfMemoryError exp){
			throw new SwtException("errors.OutOfMemoryError");
		} catch (RowsExceededException rowsException) {
			log.error("RowsExceededException occured in "
					+ this.getClass().getName() + " - [convertObject]. Cause: "
					+ rowsException.getMessage());
			throw new SwtException(rowsException.getMessage());
		} catch (WriteException writeException) {
			log.error("WriteException occured in " + this.getClass().getName()
					+ " - [convertObject]. Cause: " + writeException.getMessage());
			throw new SwtException(writeException.getMessage());
		} catch (IOException exception) {
			log.error("IOException occured in " + this.getClass().getName()
					+ " - [convertObject]. Cause: " + exception.getMessage());
			throw new SwtException(exception.getMessage());
		} catch (Exception exception) {
			log.error("Exception occured in " + this.getClass().getName()
					+ " - [convertObject]. Cause: " + exception.getMessage());
			throw new SwtException(exception.getMessage());
		} finally {
			// Set the cancelExport attribute to false. 
			// The user could export again before changing this attribute from flex part
			if (CDM != null) {
				CDM.setCancelExport("false");
			}
			// Cleaning unreferenced objects goes here
			filterFont = null;
			headingLabel = null;
			filterLabel = null;
			filterFormat = null;
			subNodes = null;
			sheet = null;
			headerFont = null;
			tableFont = null;
			writableFnt = null;
			blackWritableFont = null;
			rowLabel = null;
			totalLabel = null;
			writableFontRed = null;
			writableFontBlack = null;
			writableCell = null;
			writableCellFmtBlack = null;
			writableCellFormat = null;
			writableFnt = null;
			writableFontBlack = null;
			writableFontRed = null;
			writableFormat = null;
			writableWorkbook = null;
			cellFormatCentre = null;
			cellFormatBlack = null;
			cellFormatLeft = null;
			cellFormatRed = null;
			cellFormatRight = null;
			totalRowFormat = null; 
			totalCellFmtLeft = null;
			whiteRowFormat = null;
			pRowFormat = null;
			nRowFormat = null;
			holidayRowFormat = null;
			log.debug(this.getClass().getName() + " - [convertObject] Ends");
		}
	}
}
