package org.swallow.config;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Properties;
import javax.sql.DataSource;
import org.hibernate.SessionFactory;import jakarta.persistence.EntityManager;
import org.hibernate.dialect.Dialect;
import org.hibernate.internal.SessionFactoryImpl;
import org.hibernate.query.hql.HqlTranslator;
import org.hibernate.query.hql.internal.StandardHqlTranslator;
import org.hibernate.query.hql.spi.SqmCreationOptions;
import org.hibernate.query.spi.QueryEngineOptions;
import org.hibernate.query.sqm.internal.SqmCreationOptionsStandard;
import org.hibernate.query.sqm.spi.SqmCreationContext;
import org.swallow.security.SwtSecureBasicDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.orm.hibernate5.HibernateExceptionTranslator;
import org.springframework.orm.hibernate5.LocalSessionFactoryBean;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;

/**
 * Created by allan on 23/08/16.
 */

@Configuration
@ComponentScan("org.swallow")
@EnableTransactionManagement
@Order(1)
public class DatabaseConfig {

	@Value("classpath:./connection.properties")
	private Resource resource;
	
    @Bean
    @Primary
	@Order(1)
    public DataSource dataSource() {
    	Properties props = new Properties();
		try {
			props.load(resource.getInputStream());
		} catch (IOException e) {
			throw new RuntimeException("Error while loading file: connection.properties, cause: "+e.getMessage(), e);
		}

        SwtSecureBasicDataSource dataSource = new SwtSecureBasicDataSource();
        dataSource.setConnectionModule("PCM");
        dataSource.setDriverClassName(props.getProperty("datasource.driver"));
        dataSource.setUrl(props.getProperty("datasource.url"));
        dataSource.setUsername(props.getProperty("datasource.username"));
        dataSource.setPassword(props.getProperty("datasource.password"));

//        dataSource.setDefaultAutoCommit(false);
        return dataSource;
    }


    @Bean
    @Qualifier(value = "dbaEntityManager")
    public EntityManager dbaEntityManager(EntityManagerFactory entityManagerFactory) {
        return entityManagerFactory.createEntityManager();
    }

    @Bean
    @Primary
    public PlatformTransactionManager transactionManager() throws SQLException {
        JpaTransactionManager txManager = new JpaTransactionManager();
        txManager.setEntityManagerFactory(entityManagerFactory());
        txManager.setDataSource(dataSource());
        return txManager;
    }
    
    @Bean
    public EntityManagerFactory entityManagerFactory() throws SQLException {

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setGenerateDdl(false);

        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
        factory.setJpaVendorAdapter(vendorAdapter);
        factory.setPackagesToScan("org.swallow");
        factory.setDataSource(dataSource());
        factory.afterPropertiesSet();

        return factory.getObject();
    }

    @Bean
    public HibernateExceptionTranslator hibernateExceptionTranslator() {
        return new HibernateExceptionTranslator();
    }
    
    // @Bean
    public Properties hibernateProperties() {
        Properties properties = new Properties();
        properties.put("hibernate.dialect", "org.hibernate.dialect.OracleDialect");
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.format_sql", "false");
        properties.put("hbm2ddl.auto", "false");
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.connection.autocommit", "false");
        properties.put("hibernate.allow_update_outside_transaction", "true");
        properties.put("hibernate.module", "PREDICT");
        // Add more Hibernate properties as needed
        return properties;
    }
    
    @Bean
    @Primary
    public LocalSessionFactoryBean sessionFactory() {
        LocalSessionFactoryBean sessionFactory = new LocalSessionFactoryBean();
        sessionFactory.setDataSource(dataSource());
        sessionFactory.setPackagesToScan("org.swallow.model", "org.swallow.maintenance.model", "org.swallow.control.model", "org.swallow.work.model"); // Package(s) containing your entity classes
        sessionFactory.setHibernateProperties(hibernateProperties());
        
        // Add ALL hbm.xml files
        //sessionFactory.setMappingResources("org/swallow/maintenance/model/*.hbm.xml");
        try {
            Resource[] resources = new PathMatchingResourcePatternResolver()
                    .getResources("classpath:org/swallow/**/*.hbm.xml");
            sessionFactory.setMappingLocations(resources);
        } catch (IOException e) {
            throw new RuntimeException("I/O Error occurred while mapping HBM.XML resources, cause: "+ e.getMessage(), e);
        }
        
        return sessionFactory;
    }
    
    @Bean
    public HqlTranslator hqlTranslator(SessionFactory sessionfactory) {
    	if(sessionfactory instanceof SessionFactoryImpl) {
    		SessionFactoryImpl sessionfactoryImpl = (SessionFactoryImpl)sessionfactory;
    		final QueryEngineOptions options = sessionfactoryImpl.getSessionFactoryOptions();
    		final Dialect dialect = sessionfactoryImpl.getJdbcServices().getDialect();
    		HqlTranslator translator = resolveHqlTranslator( options, dialect, sessionfactoryImpl, new SqmCreationOptionsStandard( options ) );
    		
    		
    		
    		return translator;
    	}
    	return  null;
    }
    
	private HqlTranslator resolveHqlTranslator(
			QueryEngineOptions runtimeOptions,
			Dialect dialect,
			SqmCreationContext sqmCreationContext,
			SqmCreationOptions sqmCreationOptions) {
		if ( runtimeOptions.getCustomHqlTranslator() != null ) {
			return runtimeOptions.getCustomHqlTranslator();
		}
		else if ( dialect.getHqlTranslator() != null ) {
			return dialect.getHqlTranslator();
		}
		else {
			return new StandardHqlTranslator( sqmCreationContext, sqmCreationOptions );
		}
	}
}