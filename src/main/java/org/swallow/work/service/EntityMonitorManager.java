/*
 * @(#)EntityMonitorManager.java 1.0 12/02/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */

package org.swallow.work.service;

import java.util.Collection;

import org.swallow.control.model.EntityAccess;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.PersonalCurrency;
import org.swallow.util.OpTimer;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.PersonalCcyEntityMap;
import org.swallow.work.model.PersonalEntityList;
import org.swallow.work.web.form.EntityRecord;

/**
 * EntityMonitorManager.java
 * 
 * EntityMonitorManager used to get the Entity Monitor details
 * 
 * <AUTHOR> D
 * @date Feb 12, 2011
 */
public interface EntityMonitorManager {

	// START Code modified by Vivekanandan A for Mantis 1991 on 09-07-2012
	/**
	 * Interface to get entity monitor details
	 * 
	 * @param String
	 *            currGrp
	 * @param String
	 *            userId
	 * @param String
	 *            startDate
	 * @param SystemFormats
	 *            format
	 * @param OpTimer
	 *            opTimer
	 * @param String
	 *            currencyCode
	 * @param String
	 *            entityOffsetTime
	 * @throws SwtException
	 * 
	 * @returns EntityRecord object
	 */
	public EntityRecord getEntityMonitorDetails(String currGrp, String userId,
			String startDate, SystemFormats format, OpTimer opTimer,
			String currencyCode, String entityOffsetTime) throws SwtException;

	// END Code modified by Vivekanandan A for Mantis 1991 on 09-07-2012

	/**
	 * This is used to get the currency group access details
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            roleId
	 * @return Collection<CurrencyGroup>
	 * @throws SwtException
	 */
	public Collection<CurrencyGroup> getCurrencyGroupAccessDetails(
			String hostId, String roleId) throws SwtException;

	/**
	 * This method retrieves the details from the Personal Entity List.
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            screenId
	 * @param String
	 *            hostId
	 * @return Collection of Personal Entity List
	 * @throws SwtException
	 */
	public Collection<PersonalEntityList> getPersonalEntityList(String userId,
			String screenId, String hostId, String...aggrEntity) throws SwtException;

	/**
	 * This method is used to persist the Personal Entity List in the database
	 * 
	 * @param isUpdate
	 * @param personalEntityList
	 * @throws SwtException
	 */
	public void savePersonalEntityList(PersonalEntityList personalEntityList,
			Boolean isUpdate) throws SwtException;

	/**
	 * This method retrieves the details from the Personal Currency List.
	 * 
	 * @param roleId
	 * @param hostId
	 * @param userId
	 * @param screenId
	 * @return Collection of personal currency list
	 * @throws SwtException
	 */
	public PersonalCurrency getPersonalCurrency(String hostId, String userId,
			String screenId, String roleId) throws SwtException;

	/**
	 * This method is used to persist the Personal Currency Entity List in the
	 * database.
	 * 
	 * @param personalCcyEntityMap
	 * @return
	 * @throws SwtException
	 */
	public void savePersonalCurrencyEntityMap(
			PersonalCcyEntityMap personalCcyEntityMap) throws SwtException;

	/**
	 * This method is used to persist the Personal Currency List in the
	 * database.
	 * 
	 * @param PersonalCurrency
	 * @return
	 * @throws SwtException
	 */
	public void savePersonalCcy(PersonalCurrency personalCurrency)
			throws SwtException;

	/**
	 * This method is used to fetch the Personal Entity List in the database.
	 * 
	 * @param personalEntityList
	 * @return PersonalEntityList
	 * @throws SwtException
	 */
	public PersonalEntityList fetchRecord(PersonalEntityList personalEntityList)
			throws SwtException;

	/**
	 * This method is used to save the personal entity list
	 * 
	 * @param personalEntityList
	 * @return
	 * @throws SwtException
	 */
	public void saveRecord(PersonalEntityList personalEntityList)
			throws SwtException;

	/**
	 * This method is used to get the user entity list
	 * 
	 * @param hostId
	 * @param roleId
	 * @return Collection<EntityAccess>
	 * @throws SwtException
	 */
	public Collection<EntityAccess> getUserEntityList(String hostId,
			String roleId) throws SwtException;
	
	/**
	 * This method is used to fetch the Personal Sum Entity List in the database.
	 * 
	 * @param personalEntityList
	 * @return PersonalEntityList
	 * @throws SwtException
	 */
	public PersonalEntityList fetchSumRecord(PersonalEntityList personalEntityList)
			throws SwtException;
	
	/**
	 * This method is used to save the personal Sum entity list
	 * 
	 * @param personalEntityList
	 * @param sumEntitiesList
	 * @return
	 * @throws SwtException
	 */
	public void saveSumRecord(PersonalEntityList personalEntityList, String sumEntitiesList)
			throws SwtException;

	/**
	 * Delete personal Sum entity list 
	 * @param hostId
	 * @param userId
	 * @param screenId
	 * @param entityId
	 * @return
	 * @throws SwtException 
	 */
	public boolean deleteSumRecord(String hostId, String userId, String screenId, String entityId) throws SwtException;


}
