/*
 * Created on Jan 4, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.service;

import java.util.Collection;

import org.swallow.work.model.MatchQueue;

/**
 * <AUTHOR>
 * 
 * Class to hold match detail Value objects
 */
public class MatchDetailVO {
	private Collection currencyList;
	private Collection matchListDetails;
	private Collection matchListDetailsToday;
	private Collection matchListDetailsTomorr;
	private Collection matchListDetailsDayAfter;
	/** Start: -- code changes to submit the page on click of each tab * */
	// This Collection will contain matchlist details for selected Tab
	// the collection which were existing for 'today', 'tomorrow' and 'all' will
	// not be used anymore
	private Collection<MatchQueue> matchListDetailsForSelectedTab;
	/*
	 * Start : Mantis Issue 1306 : Code added for Enhancement to Non-Working
	 * Days (Holidays and Weekends) by Arumugam on 08-Mar-2011
	 */
	private String tabFlag = null;

	/**
	 * @return Returns the tabFlag.
	 */
	public String getTabFlag() {
		return tabFlag;
	}

	/**
	 * @param tabFlag
	 *            The tabFlag to set.
	 */
	public void setTabFlag(String tabFlag) {
		this.tabFlag = tabFlag;
	}

	/*
	 * End : Mantis Issue 1306 : Code added for Enhancement to Non-Working Days
	 * (Holidays and Weekends) by Arumugam on 08-Mar-2011
	 */

	public MatchDetailVO() {
		super();
		// TODO Auto-generated constructor stub
	}

	/**
	 * @param currencyList
	 * @param matchListDetails
	 * @param matchListDetailsToday
	 * @param matchListDetailsTomorr
	 * @param matchListDetailsDayAfter
	 * @param matchListDetailsForSelectedTab
	 */
	public MatchDetailVO(Collection currencyList, Collection matchListDetails,
			Collection matchListDetailsToday,
			Collection matchListDetailsTomorr,
			Collection matchListDetailsDayAfter,
			Collection matchListDetailsForSelectedTab) {
		super();
		this.currencyList = currencyList;
		this.matchListDetails = matchListDetails;
		this.matchListDetailsToday = matchListDetailsToday;
		this.matchListDetailsTomorr = matchListDetailsTomorr;
		this.matchListDetailsDayAfter = matchListDetailsDayAfter;
		this.matchListDetailsForSelectedTab = matchListDetailsForSelectedTab;
	}

	/** End: -- code changes to submit the page on click of each tab * */

	/**
	 * @return Returns the currencyList.
	 */
	public Collection getCurrencyList() {
		return currencyList;
	}

	/**
	 * @param currencyList
	 *            The currencyList to set.
	 */
	public void setCurrencyList(Collection currencyList) {
		this.currencyList = currencyList;
	}

	/**
	 * @return Returns the matchListDetails.
	 */
	public Collection getMatchListDetails() {
		return matchListDetails;
	}

	/**
	 * @param matchListDetails
	 *            The matchListDetails to set.
	 */
	public void setMatchListDetails(Collection matchListDetails) {
		this.matchListDetails = matchListDetails;
	}

	/**
	 * @return Returns the matchListDetailsDayAfter.
	 */
	public Collection getMatchListDetailsDayAfter() {
		return matchListDetailsDayAfter;
	}

	/**
	 * @param matchListDetailsDayAfter
	 *            The matchListDetailsDayAfter to set.
	 */
	public void setMatchListDetailsDayAfter(Collection matchListDetailsDayAfter) {
		this.matchListDetailsDayAfter = matchListDetailsDayAfter;
	}

	/**
	 * @return Returns the matchListDetailsToday.
	 */
	public Collection getMatchListDetailsToday() {
		return matchListDetailsToday;
	}

	/**
	 * @param matchListDetailsToday
	 *            The matchListDetailsToday to set.
	 */
	public void setMatchListDetailsToday(Collection matchListDetailsToday) {
		this.matchListDetailsToday = matchListDetailsToday;
	}

	/**
	 * @return Returns the matchListDetailsTomorr.
	 */
	public Collection getMatchListDetailsTomorr() {
		return matchListDetailsTomorr;
	}

	/**
	 * @param matchListDetailsTomorr
	 *            The matchListDetailsTomorr to set.
	 */
	public void setMatchListDetailsTomorr(Collection matchListDetailsTomorr) {
		this.matchListDetailsTomorr = matchListDetailsTomorr;
	}

	/**
	 * @return Returns the matchListDetailsForSelectedTab.
	 */
	public Collection getMatchListDetailsForSelectedTab() {
		return matchListDetailsForSelectedTab;
	}

	/**
	 * @param matchListDetailsForSelectedTabObj
	 *            The matchListDetailsForSelectedTab to set.
	 */
	@SuppressWarnings("unchecked")
	public void setMatchListDetailsForSelectedTab(
			Object matchListDetailsForSelectedTabObj) {
		Collection<MatchQueue> matchListDetailsForSelectedTab = (Collection<MatchQueue>) matchListDetailsForSelectedTabObj;
		this.matchListDetailsForSelectedTab = matchListDetailsForSelectedTab;
	}
}
