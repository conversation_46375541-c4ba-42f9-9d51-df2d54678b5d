/*
 * Created on Dec 15, 2005
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.service.impl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;

import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.MessageFormats;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;

import org.swallow.work.dao.SweepSearchDAO;
import org.swallow.work.model.Sweep;
import org.swallow.work.service.SweepSearchManager;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to Window -
 * Preferences - Java - Code Style - Code Templates
 */
@Component("sweepsearchManager")
public class SweepSearchManagerImpl implements SweepSearchManager {
    private final Log log = LogFactory.getLog(SweepSearchManagerImpl.class);
    @Autowired
    private SweepSearchDAO sweepsearchDAO;

    public void setSweepsearchDAO(SweepSearchDAO sweepsearchDAO) {
        this.sweepsearchDAO = sweepsearchDAO;
    }

    public ArrayList getCurrencyDetails(String hostId, String entityId)
        throws SwtException {
        log.debug("entering currencydetails method");

        ArrayList currList = new ArrayList();
        try {
			Iterator itr = (sweepsearchDAO.getCurrencyDetails(hostId, entityId)).iterator();
			Currency curr = null;

			currList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				curr = (Currency) (itr.next());
				currList.add(new LabelValueBean(curr.getCurrencyMaster()
													.getCurrencyName(),
						curr.getCurrencyMaster().getCurrencyCode()));
			}
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'getCurrencyDetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"getCurrencyDetails", SweepSearchManagerImpl.class);
		}

        log.debug("exiting currencydetails method");

        return currList;
    }

    public ArrayList getAccountDetails(String hostId, String entityId,
        String currencyCode) throws SwtException {
        ArrayList accountList = new ArrayList();
        try {
			Iterator itr = (sweepsearchDAO.getAccountDetails(hostId, entityId,
					currencyCode)).iterator();
			AcctMaintenance acmaint = null;

			accountList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				acmaint = (AcctMaintenance) (itr.next());
				accountList.add(new LabelValueBean(acmaint.getAcctname(),
						acmaint.getId().getAccountId()));
			}
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'getAccountDetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"getAccountDetails", SweepSearchManagerImpl.class);
		}

        log.debug("exiting accountdetails method");

        return accountList;
    }

    public ArrayList getGeneratedDetails(String hostId, String entityId)
        throws SwtException {
        ArrayList genList = new ArrayList();
        try {
			List records = sweepsearchDAO.getGeneratedDetails(hostId, entityId);

			//log.debug("fffffffffffff");
			genList.add(new LabelValueBean("", ""));

			if (records.size() > 0) {
				for (int i = 0; i < records.size(); i++) {
					String temp = (String) records.get(i);

					//log.debug("Record Number >> " + i + "<<Record Value>>" + temp);
					genList.add(new LabelValueBean(temp, temp));
				}
			}
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'getGeneratedDetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"getGeneratedDetails", SweepSearchManagerImpl.class);
		}

        return genList;
    }

    public ArrayList getSubmittedDetails(String hostId, String entityId)
        throws SwtException {
		ArrayList subList = new ArrayList();
        try {
			List records = sweepsearchDAO.getSubmittedDetails(hostId, entityId);
			log.debug("fffffffffffff");
			subList.add(new LabelValueBean("", ""));

			if (records.size() > 0) {
				for (int i = 0; i < records.size(); i++) {
					String temp = (String) records.get(i);
					log.debug("Record Number >> " + i + "<<Record Value>>" + temp);
					subList.add(new LabelValueBean(temp, temp));
				}
			}
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'getSubmittedDetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"getSubmittedDetails", SweepSearchManagerImpl.class);
		}

        return subList;
    }

    public ArrayList getAuthorizedDetails(String hostId, String entityId)
        throws SwtException {
        ArrayList authList = new ArrayList();
        try {
			List records = sweepsearchDAO.getAuthorizedDetails(hostId, entityId);

			//log.debug("fffffffffffff");
			authList.add(new LabelValueBean("", ""));

			if (records.size() > 0) {
				for (int i = 0; i < records.size(); i++) {
					String temp = (String) records.get(i);

					//log.debug("Record Number >> " + i + "<<Record Value>>" + temp);
					authList.add(new LabelValueBean(temp, temp));
				}
			}
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'getAuthorizedDetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"getAuthorizedDetails", SweepSearchManagerImpl.class);
		}

        return authList;
    }

    public ArrayList getMessageDetails(String hostId, String entityId)
        throws SwtException {
        ArrayList msgList = new ArrayList();
        try {
			Iterator itr = (sweepsearchDAO.getMessageDetails(hostId, entityId)).iterator();
			MessageFormats msg = null;

			msgList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				msg = (MessageFormats) (itr.next());
				msgList.add(new LabelValueBean(msg.getFormatName(),
						msg.getId().getFormatId()));
			}
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'getMessageDetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"getMessageDetails", SweepSearchManagerImpl.class);
		}

        log.debug("exiting message details method");

        return msgList;
    }

    public ArrayList getBookCodeDetails(String hostId, String entityId)
        throws SwtException {
        log.debug("entering bookcodedetails method");

        ArrayList bookList = new ArrayList();
        try {
			Iterator itr = (sweepsearchDAO.getBookCodeDetails(hostId, entityId)).iterator();
			BookCode bookcode = null;

			bookList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				bookcode = (BookCode) (itr.next());
				bookList.add(new LabelValueBean(bookcode.getBookName(),
						bookcode.getId().getBookCode()));
			}
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'getBookCodeDetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"getBookCodeDetails", SweepSearchManagerImpl.class);
		}

        log.debug("exiting bookcodedetails method");

        return bookList;
    }

    public ArrayList getSweepDetails(String hostId) throws SwtException {
        ArrayList sweepList = new ArrayList();
        try {
			Iterator itr = (sweepsearchDAO.getSweepDetails(hostId)).iterator();
			Sweep sweep = null;

			sweepList.add(new LabelValueBean("", ""));

			while (itr.hasNext()) {
				//log.debug("Inside the loop");
				sweep = (Sweep) (itr.next());

				Long sweepval = sweep.getId().getSweepId();
				String sweepvalue = sweepval.toString();
				sweepList.add(new LabelValueBean(sweepvalue, sweepvalue));
			}
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'getSweepDetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"getSweepDetails", SweepSearchManagerImpl.class);
		}

        //log.debug("exiting sweepdetails method");
        return sweepList;
    }

    public Collection fetchsweepdetails(Long sweepid, String hostid)
        throws SwtException {
		Collection collList = null;

		try {
            collList = sweepsearchDAO.fetchsweepdetails(sweepid, hostid);
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'fetchsweepdetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"fetchsweepdetails", SweepSearchManagerImpl.class);
		}

		return collList;
    }

    public Collection fetchMovementDetails(String hostid, String entityid,
        Long movementid) throws SwtException {
		Collection collList = null;

		try {
            collList = sweepsearchDAO.fetchMovementDetails(hostid, entityid, movementid);
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'fetchMovementDetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"fetchMovementDetails", SweepSearchManagerImpl.class);
		}

		return collList;
    }

    public ArrayList getPositionLevelDetails(String hostid, String entityid)
        throws SwtException {
        return null;
    }

    public Collection fetchdetails(String sortorder, String acctype,
        String status, String type, String currency, String message,
        String accountid, String bookcode, String inputuser, String authuser,
        String subuser, String entityid, String hostid,
        String postcutoff, Double amountover, Double amountunder,
        String fromdate, String todate, SystemFormats sysformat,
        Object[] currencyCodeArray, String archiveid) throws SwtException {
        //log.debug("sysformat.getDateFormatValue()===>"+sysformat.getDateFormatValue());
        Collection sweepColl = null;

        try {
			String format = "";

			if (sysformat.getDateFormatValue().equalsIgnoreCase("MM/dd/yyyy")) {
				format = "M";
			} else {
				format = "D";
			}

			sweepColl = sweepsearchDAO.fetchdetails(sortorder, acctype,
					status, type, currency, message, accountid, bookcode,
					inputuser, authuser, subuser, entityid, hostid,
					postcutoff, amountover, amountunder, fromdate, todate, format,
					currencyCodeArray, archiveid);
			Iterator itr = sweepColl.iterator();
			Sweep sweep = new Sweep();

			while (itr.hasNext()) {
				sweep = (Sweep) itr.next();

				sweep.setValueDateAsString(SwtUtil.formatDate(
						sweep.getValueDate(), sysformat.getDateFormatValue()));
			}
		} catch (Exception exp) {
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'fetchdetails' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"fetchdetails", SweepSearchManagerImpl.class);
		}

        return sweepColl;
    }

    /* (non-Javadoc)
     * @see org.swallow.work.service.SweepSearchManager#fetchsweepCutOff(java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String)
     */
    public Sweep fetchsweepCutOff(String accountIdCr, String accountIdDr,
        String entityIdCr, String entityIddr, String hostid, Long sweepvalue,
        Sweep sweep, String archiveId) throws SwtException {
		Sweep sweepColl = null;

		try {
            sweepColl = sweepsearchDAO.fetchsweepCutOff(accountIdCr,
                accountIdDr, entityIdCr, entityIddr, hostid, sweepvalue, sweep, archiveId);
		} catch (Exception exp) {
			exp.printStackTrace();
			log.debug(
				"Exception Catch in SweepSearchManagerImpl.'fetchsweepCutOff' method : " +
				exp.getMessage());
			exp.printStackTrace();
			throw SwtErrorHandler.getInstance().handleException(exp,
				"fetchsweepCutOff", SweepSearchManagerImpl.class);
		}

        return sweepColl;
    }
	public Collection fetchsweepdetailsArchive(Long sweepid, String hostid, String archiveId)
	        throws SwtException {
			Collection collList = null;

			try {
	            collList = sweepsearchDAO.fetchsweepdetailsArchive(sweepid, hostid, archiveId);
			} catch (Exception exp) {
				log.debug(
					"Exception Catch in SweepSearchManagerImpl.'fetchsweepdetails' method : " +
					exp.getMessage());
				exp.printStackTrace();
				throw SwtErrorHandler.getInstance().handleException(exp,
					"fetchsweepdetails", SweepSearchManagerImpl.class);
			}

			return collList;
	    }
}
