/*
 * @(#)ILMAnalysisMonitorManagerImpl.java  1.0 29/11/13
 * 
 * Copyright (c) 2006-2013 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyAccessTO;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.ILMAccountGroup;
import org.swallow.maintenance.model.ILMScenario;
import org.swallow.reports.model.ThroughputRatioReport;
import org.swallow.util.CommonDataManager;
import org.swallow.work.dao.ILMAnalysisMonitorDAO;
import org.swallow.work.model.ILMSummaryRecord;
import org.swallow.work.model.ThroughputMonitorRecord;
import org.swallow.work.service.ILMAnalysisMonitorManager;

import net.sf.jasperreports.engine.JasperPrint;

/**
 * <pre>
 * This Manager Layer for ilm analysis monitor, Used to
 * - 
 * - 
 * - 
 * </pre>
 * 
*/
@Component("ilmAnalysisMonitorManager")
public class ILMAnalysisMonitorManagerImpl implements ILMAnalysisMonitorManager {
	/**
	 * Log object
	 */
	private static final Log log = LogFactory
			.getLog(ILMAnalysisMonitorManagerImpl.class);

	/**
	 * ILMAnalysisMonitorDAO object
	 */
	@Autowired
	private ILMAnalysisMonitorDAO ilmAnalysisMonitorDAO = null;



	/**
	 * @param iLMAnalysisDao --
	 *            ILMAnalysisMonitorDAO object
	 */
	public void setIlmAnalysisMonitorDAO(ILMAnalysisMonitorDAO ilmAnalysisMonitorDAO) {
		this.ilmAnalysisMonitorDAO = ilmAnalysisMonitorDAO;
	}

	public String getChartsData(String hostId, String entityId,
			String currencyId, Date selectedDate, String userId, String dbLink,
			String selectedFigures, String sumByCutOff, String includeOpenMvnts) throws SwtException {
		
		return ilmAnalysisMonitorDAO.getChartsData(hostId, entityId,
				currencyId, selectedDate, userId, dbLink, selectedFigures, sumByCutOff, includeOpenMvnts);
	}


	public List getGroupsDetailForGrid(String hostId, String entityId,
			String currencyId, Date selectedDate, String userId,
			String dbLink, String isGlobal, String includeOpenMvnts) throws SwtException {
		
		return ilmAnalysisMonitorDAO.getGroupsDetailForGrid(hostId, entityId, currencyId, selectedDate, userId, dbLink, isGlobal, includeOpenMvnts);
	}

	public List getScenariosDetail(String hostId, String entityId,
			String currencyId, String userId, String isGlobal)
			throws SwtException {
		return ilmAnalysisMonitorDAO.getScenariosDetail(hostId, entityId, currencyId, userId, isGlobal);
		
	}

	public ILMAccountGroup getGroupDetails(String hostId, String entityId, String currencyId,
			String group) throws SwtException {
		return ilmAnalysisMonitorDAO.getGroupDetails(hostId, entityId, currencyId, group);
	}


	public HashMap<String, String> getProcessState(String hostId, String entityId,
			String currencyId, Date selectedDate, String roleId,String userId, String dbLink) throws SwtException {
		return ilmAnalysisMonitorDAO.getProcessState(hostId, entityId,
				currencyId, selectedDate, roleId, userId, dbLink);
	}

	public String getCurrencyTimeframe(String entityId, String currencyId,  Date selectedDate, String roleId) throws SwtException {
		
		return ilmAnalysisMonitorDAO.getCurrencyTimeframe(entityId, currencyId, selectedDate, roleId);
	}

	public String getCcyMultiplierAndDecimalPlaces(String entityId, String currencyId) throws SwtException {
		
		return ilmAnalysisMonitorDAO.getCcyMultiplierAndDecimalPlaces(entityId, currencyId);
	}
	public String getCurrentDbLink(String hostId) throws SwtException {
		
		return ilmAnalysisMonitorDAO.getCurrentDbLink(hostId);
	}
	public String recalculateData( String hostId, String entityId, String currencyId ,String selectedDateStr, String dbLink, String sequenceNumber,CommonDataManager CDM ) throws SwtException {
		
		return ilmAnalysisMonitorDAO.recalculateData(hostId, entityId, currencyId, selectedDateStr, dbLink, sequenceNumber, CDM);
	}

	public String[] getNowDates(String entityId, String currencyId) throws SwtException{
		return ilmAnalysisMonitorDAO.getNowDates(entityId, currencyId);
	}
	
	public Collection<CurrencyAccessTO>  getEntityCurrency(String entityId, String roleId) throws SwtException{
		return ilmAnalysisMonitorDAO.getEntityCurrency(entityId, roleId);
	}

	public String isAllEntityAvailable(String hostId, String currencyId, String roleId)
			throws SwtException {
		return ilmAnalysisMonitorDAO.isAllEntityAvailable(hostId, currencyId, roleId);
	}

	public Collection<EntityUserAccess> getEntitiesHasCurrencies(String hostId,
			String roleId) throws SwtException {
		return ilmAnalysisMonitorDAO.getEntitiesHasCurrencies(hostId, roleId);
	}

	public HashMap<String, String> getGroupsAndScenarioNames(String entityId , String currencyId  ) throws SwtException{
		return ilmAnalysisMonitorDAO.getGroupsAndScenarioNames(entityId, currencyId);
	}

	public void cleanUpProcessDriver(String uniqueSequenceId, String userId) throws SwtException {
		ilmAnalysisMonitorDAO.cleanUpProcessDriver(uniqueSequenceId, userId);
	}

	public ILMAccountGroup getILMGroupDetails(String ilmGroup)
			throws SwtException {
		
		return ilmAnalysisMonitorDAO.getILMGroupDetails(ilmGroup);
	}

	public ILMScenario getILMScenarioDetails(String ilmScenario)
			throws SwtException {
		
		return ilmAnalysisMonitorDAO.getILMScenarioDetails(ilmScenario);
	}

	/**
	 * Get the Ilm ThroughPut Ratio Data + charts data
	 * @param defaultEntityId
	 * @param defaultCcyId
	 * @param selectedAccountGroup
	 * @param selectedScenario
	 * @param sysDateAsString
	 * @return
	 */
	public ArrayList<ThroughputMonitorRecord> getIlmThroughPutRatioData(String defaultEntityId, String defaultCcyId,
			String selectedAccountGroup, String selectedScenario, Date selectedDate, String hostId, String roleId, String dbLink, String calculateAs) throws SwtException  {
		return ilmAnalysisMonitorDAO.getIlmThroughPutRatioData(defaultEntityId, defaultCcyId,
				selectedAccountGroup, selectedScenario, selectedDate, hostId, roleId, dbLink, calculateAs) ;
	}

	
	public JasperPrint getILMThroughputRatioReport(ThroughputRatioReport report) throws SwtException {
		return ilmAnalysisMonitorDAO.getILMThroughputRatioReport(report);
	}
	
	public HashMap<String, ILMSummaryRecord>getILMSummaryGridData(String hostId, String userFilter, Date valueDate , String dbLink, String includeSOD, String includeCR, String includeOpenMovements,
			String includeSumByCutoff, String hideNonSumAcct, String applyCcyMultiplier, HashMap<String, Integer> orderMap, String ccyPattern,String userId) throws SwtException{
		
		return ilmAnalysisMonitorDAO.getILMSummaryGridData(hostId, userFilter, valueDate , dbLink, includeSOD, includeCR, includeOpenMovements,
				includeSumByCutoff, hideNonSumAcct, applyCcyMultiplier, orderMap, ccyPattern,userId);
	}
	
	public String getDataState(String hostId, String entityId, String currencyId, Date selectedDate, String dbLink) throws SwtException{
		return ilmAnalysisMonitorDAO.getDataState(hostId, entityId, currencyId ,selectedDate ,  dbLink);
	}

	@Override
	public HashMap<String, ILMSummaryRecord> getILMOptionGridData(HashMap<String, Integer> orderMap, String roleId)
			throws SwtException {
		return ilmAnalysisMonitorDAO.getILMOptionGridData(orderMap, roleId);
	}


}
