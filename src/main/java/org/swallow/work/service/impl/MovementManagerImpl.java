/*
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.StringTokenizer;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.swallow.control.dao.RoleDAO;
import org.swallow.control.dao.SystemAlertMessagesDAO;
import org.swallow.control.dao.UserMaintenanceDAO;
import org.swallow.control.model.Role;
import org.swallow.control.model.UserMaintenance;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.dao.EntityDAO;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.EditableData;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityPositionLevel;
import org.swallow.maintenance.model.EntityPositionLevelTO;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.model.MsdDisplayColumns;
import org.swallow.maintenance.model.Party;
import org.swallow.maintenance.model.PositionLevelIdName;
import org.swallow.model.ScreenInfo;
import org.swallow.util.CacheManager;
import org.swallow.util.LabelValueBean;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.util.UserThreadLocalHolder;
import org.swallow.work.dao.MatchDisplayDAO;
import org.swallow.work.dao.MovementDAO;
import org.swallow.work.dao.MovementLockDAO;
import org.swallow.work.dao.NotesDAO;
import org.swallow.work.dao.SweepAlertDAO;
import org.swallow.work.model.CrossReference;
import org.swallow.work.model.Match;
import org.swallow.work.model.MatchDriver;
import org.swallow.work.model.MatchNote;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementLock;
import org.swallow.work.model.MovementMessage;
import org.swallow.work.model.MovementNote;
import org.swallow.work.model.MsdAdditionalColumns;
import org.swallow.work.model.OpenMovementSummary;
import org.swallow.work.model.SweepAlert;
import org.swallow.work.service.MatchManager;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.OpenMovementVO;

/*
 * <AUTHOR>
 * 
 * <Pre> Manager layer for Movement
 * 
 * Method to used to - Display Movement details - Get Movement details - save
 * Movement - Update Movement - Save Movement
 * 
 * </Pre>
 */
@Component("movementManager")
@Transactional
public class MovementManagerImpl implements MovementManager {
	/**
	 * To create a Log factory reference variable.
	 */
	private static final Log log = LogFactory.getLog(MovementManagerImpl.class);

	/**
	 * private variable of MovementDAO
	 */
	@Autowired
	private MovementDAO movementDAO = null;

	/**
	 * private variable of UserMaintenanceDAO
	 */
	private UserMaintenanceDAO userMaintenanceDAO = null;

	/**
	 * private variable of RoleDAO
	 */
	private RoleDAO roleDAO = null;

	/**
	 * Setter method for MovementDAO
	 * 
	 * @param movementDAO
	 * @return
	 */
	public void setMovementDAO(MovementDAO movementDAO) {
		this.movementDAO = movementDAO;
	}



	/**
	 * Method to get selected movements details
	 * 
	 * @param hostId
	 * @param entityId
	 * @param mvmtIds
	 * @param sysformat
	 * @return List - Movement list
	 * @throws SwtException
	 */
	public List<Movement> getSelectedMovements(String hostId, String entityId,
			String mvmtIds, SystemFormats sysformat) throws SwtException {

		// Method's local Variable declaration
		// Movement Instance to get selected movement details
		Movement movement = null;
		// List to get selected movement id's details
		List<Movement> movementList = null;
		// Iterator instance to iterate movements
		Iterator<Movement> movementIterator = null;
		// Collection Insttance to hold misc params
		Collection<MiscParams> collMiscParams = null;
		// Iterator instance to iterate Miscparams values
		Iterator<MiscParams> itrMiscParams = null;
		// NotesDAO Instance to get notes defined for this movement
		NotesDAO notesDAO = null;
		// Collection instance to hold notes collection
		Collection<MovementNote> movNotesColl = null;
		// simple date frormat is declared for decide date format
		SimpleDateFormat dateFormat = null;
		// misparams instance to hold the misparams values
		MiscParams miscParams = null;

		try {

			log.debug(this.getClass().getName()
					+ " -[getSelectedMovements]-Entry");

			// Condition to chcek no movememt id selected
			if (mvmtIds.length() == 0) {
				// set empty string for query
				mvmtIds = "''";
			}

			// get selected movement details
			movementList = movementDAO.getMovement(hostId, entityId, mvmtIds);
			// set collection to Iterator
			movementIterator = movementList.iterator();
			// Code Modified by Chinniah on 1-Nov-2011 for Manits 1598: Input
			// time format is disappears in the bottom Grid when refresh the MSD
			// screen
			dateFormat = new SimpleDateFormat("HH:mm:ss");
			// Loop to get movements
			while (movementIterator.hasNext()) {
				// get movement details
				movement = (Movement) movementIterator.next();

				// Condition to check match status in nor null or empty
				if (!SwtUtil.isEmptyOrNull(movement.getMatchStatus())) {

					// get values from misc params for match status
					collMiscParams = (Collection<MiscParams>) CacheManager
							.getInstance().getMiscParams("MATCHSTATUS",
									entityId);
					// Iterate mis params collection
					itrMiscParams = collMiscParams.iterator();
					// loop to get match status value from misc params table
					while (itrMiscParams.hasNext()) {
						miscParams = (MiscParams) (itrMiscParams.next());

						if (movement.getMatchStatus().equals(
								miscParams.getId().getKey2())) {
							movement.setMatchStatusDesc(miscParams
									.getParValue());
						}
					}
				}
				// set value date as String by formating Value Date
				movement.setValueDateAsString(SwtUtil.formatDate(movement
						.getValueDate(), sysformat.getDateFormatValue()));
				// set amount as String by formating amount to given currency
				// format
				movement.setAmountAsString(SwtUtil.formatCurrency(movement
						.getCurrencyCode(), movement.getAmount()));
				// set input date as String by formating input Date
				// Code Modified by Chinniah on 1-Nov-2011 for Manits 1598:
				// Input time format is disappears in the bottom Grid when
				// refresh the MSD screen

				movement.setInputDateAsString(SwtUtil.formatDate(movement
						.getInputDate(), sysformat.getDateFormatValue())
						+ " " + dateFormat.format(movement.getInputDate()));
				// set posting date as String by formating posting Date
				movement.setPostingDateAsString(SwtUtil.formatDate(movement
						.getPostingDate(), sysformat.getDateFormatValue()));
				
				movement.setExpectedSettlementDateTimeAsString(SwtUtil.formatDate(movement
						.getExpectedSettlementDateTime(), sysformat.getDateFormatValue()));
				
				movement.setSettlementDateTimeAsString(SwtUtil.formatDate(movement
						.getSettlementDateTime(), sysformat.getDateFormatValue()));
				// Get notes DAO instance
				notesDAO = (NotesDAO) SwtUtil.getBean("notesDAO");
				// get notes details doe selected movement id
				movNotesColl = notesDAO.getNoteDetails(hostId, movement.getId()
						.getMovementId());
				// set notes flag
				if (movNotesColl.size() != 0) {
					movement.setHasNotes(SwtConstants.YES);
				} else {
					movement.setHasNotes("");
				}

				// For this Movement set the Postion Level Name as defined in
				// EntityPositionLevel
				movement.setPositionLevelName(SwtUtil.getSwtMaintenanceCache()
						.getEntityPositionLevelName(entityId,
								movement.getPositionLevel()));
			}

		} catch (SwtException exp) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getSelectedMovements] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSelectedMovements", this.getClass());
		} catch (Exception exp) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getSelectedMovements] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getSelectedMovements", this.getClass());
		} finally {
			collMiscParams = null;
			itrMiscParams = null;
			movementIterator = null;
			notesDAO = null;
			movNotesColl = null;
			log.debug(this.getClass().getName()
					+ " -[getSelectedMovements]-Exit");
		}
		return movementList;
	}

	/**
	 * @param hostId -
	 *            hostId
	 * @param entityId -
	 *            entityId
	 * @param currencyCode -
	 *            currencyCode
	 * @param selectedMovList -
	 *            String containing all the movements selected in Movement
	 *            Summary Screen for Suspend
	 * @param selectedTab -
	 *            selectedTab
	 * @throws SwtException -
	 *             SwtException
	 * @return matchId - matchId of the lowest match if any of the selected
	 *         movements are in any match OR creates a new match
	 */
	public Long suspendMatch(String hostId, String entityId,
			String currencyCode, String selectedMovList, String selectedTab)
			throws SwtException {
		try {
			log.debug("Entering suspendMatch method:::: ");
			Long matchID = null;
			HashSet brokenMatchIds = new HashSet();
			ArrayList movcoll = (ArrayList) movementDAO.getAllMovement(hostId,
					entityId, selectedMovList);
			Iterator itr = null;
			Movement movement = null;
			Long lowestMatchId = new Long(Long.MAX_VALUE);
			String lowestMatchStatus = SwtConstants.OFFERD_STATUS;

			// get the lowest Match Id if any of the selected movements were
			// already in any match
			// Start of block
			Iterator itrcoll = movcoll.iterator();
			movement = new Movement();
			ArrayList positionLevelList = new ArrayList();
			while (itrcoll.hasNext()) {
				movement = (Movement) itrcoll.next();
				positionLevelList.add(movement.getPositionLevel());// For
				// populating
				// Internal
				// Match
				// Quality
				if (movement.getMatchId() != null) {
					if (lowestMatchId.longValue() > movement.getMatchId()
							.longValue()) {
						lowestMatchId = movement.getMatchId();
						lowestMatchStatus = movement.getMatchStatus();
					}
					brokenMatchIds.add(movement.getMatchId());
				}
			}

			matchID = lowestMatchId;

			// End of block
			ArrayList deleteMatchList = new ArrayList();
			ArrayList deleteMatchNotesList = new ArrayList();

			/*
			 * Populate the deleteMatchList with all the other existing matches
			 * associated with movementIds while retaining the oldest match i.e.
			 * match with lowest matchId
			 */
			MatchDisplayDAO matchDisplayDAO = (MatchDisplayDAO) SwtUtil
					.getBean("matchDisplayDAO");

			if (lowestMatchId.longValue() != Long.MAX_VALUE) {
				itr = movcoll.iterator();
				movement = new Movement();
				while (itr.hasNext()) {
					movement = (Movement) itr.next();

					if ((movement.getMatchId() != null)
							&& (movement.getMatchId().longValue() != lowestMatchId
									.longValue())) {
						Match match = new Match();
						match.getId().setHostId(hostId);
						match.getId().setEntityId(entityId);
						match.getId().setMatchId(movement.getMatchId());

						Collection matchNotes = matchDisplayDAO.getMatchNotes(
								entityId, hostId, movement.getMatchId()
										.toString());
						Iterator notesitr = matchNotes.iterator();
						MatchNote matchNote = null;

						while (notesitr.hasNext()) {
							matchNote = new MatchNote();
							matchNote = (MatchNote) notesitr.next();
							deleteMatchNotesList.add(matchNote);
						}

						deleteMatchList.add(match);
						brokenMatchIds.remove(match.getId().getMatchId());
					}
				}
			}

			movement = new Movement();

			int count = movcoll.size();

			int highestPosLev = ((Movement) movcoll.get(0)).getPositionLevel()
					.intValue();

			String user = UserThreadLocalHolder.getUser();

			Match match = new Match();

			if (lowestMatchId.longValue() != Long.MAX_VALUE) {
				match = movementDAO.getMatch(hostId, entityId, lowestMatchId);

				// This condition will cater to the case when the lowest match
				// status is Confirm and since we are suspending the Match its
				// confirm date should be set to null
				match.setConfirmedDate(null);
			}

			match.getId().setHostId(hostId);
			match.getId().setEntityId(entityId);
			match.setCurrencyCode(currencyCode);
			match.setHighestPosLev(highestPosLev);
			match.setStatusUser(user);

			// match.setStatusDate(new Date());
			match.setStatusDate(SwtUtil.getSystemDatewithTime());

			// Set match status as Suspend 'S'
			match.setStatus(SwtConstants.SUSPEND_STATUS);

			// Set the Match_InternalQuality[HighestPosLevel] to "A"
			Iterator itPositionLevelList = positionLevelList.iterator();
			while (itPositionLevelList.hasNext()) {
				switch (((Integer) itPositionLevelList.next()).intValue()) {
				case 1:
					match.setIntQuality1(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 2:
					match.setIntQuality2(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 3:
					match.setIntQuality3(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 4:
					match.setIntQuality4(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 5:
					match.setIntQuality5(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 6:
					match.setIntQuality6(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 7:
					match.setIntQuality7(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 8:
					match.setIntQuality8(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 9:
					match.setIntQuality9(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				}
			}

			/* INFO: Populating new columns of match object */
			match = populateMatchSRCDetails(match, movcoll);

			/*
			 * If no movement in selected List is already in match then Call to
			 * create a new Match record , returns created MatchId
			 */
			if (matchID.longValue() == Long.MAX_VALUE) {
				matchID = movementDAO.createMatch(match);

				/*
				 * Set the matchQuality status to Manual only when a new Match
				 * is created
				 */
				match.setMatchQuality(SwtConstants.MANUAL_MATCH_QUALITY);
			}

			Movement movObj = new Movement();
			ArrayList movCollToUpdate = new ArrayList();
			Iterator movItr = movcoll.iterator();

			while (movItr.hasNext()) {
				movObj = (Movement) movItr.next();

				// Set Movement matchId to generated matchId
				movObj.setMatchId(matchID);

				// Set Movement matchStatus to 'S'i.e Suspended
				movObj.setMatchStatus(SwtConstants.SUSPEND_STATUS);
				// Set the movement's initial predict status as it is suspended by user
				movObj.setPredictStatus(movObj.getInitialPredStatus());
				movCollToUpdate.add(movObj);
			}

			// Updates all the movements with above matchStatus and matchId
			movementDAO.updateMovements(movCollToUpdate);

			if (lowestMatchId.longValue() != Long.MAX_VALUE) {
				// update the lowest Match
				movementDAO.updateMatch(match);
				brokenMatchIds.remove(match.getId().getMatchId());
				// Delete all Other Matches associated with movementIds while
				// retaining the oldest match
				movementDAO.deleteOtherMatches(deleteMatchList,
						deleteMatchNotesList);
			}

			// UnLock all the Movements
			itr = movcoll.iterator();
			Collection lockedMovList = new ArrayList();
			while (itr.hasNext()) {
				movObj = (Movement) itr.next();
				Long movId = movObj.getId().getMovementId();
				MovementLock movementLock = new MovementLock();
				movementLock.getId().setHostId(hostId);
				movementLock.getId().setMovementId(movId);
				movementLock.getId().setEntityId(entityId);
				lockedMovList.add(movementLock);
			}
			MovementLockDAO movementLockDAOObj = (MovementLockDAO) SwtUtil
					.getBean("movementLockDAO");
			movementLockDAOObj.unLockMovement(lockedMovList);
			MatchManager matchManager = (MatchManager) SwtUtil
					.getBean("matchManager");
			matchManager.updateBrokenMatch(hostId, entityId, brokenMatchIds);
			return matchID;
		} catch (Exception exp) {
			log
					.debug("Exception Catch in MovementManagerImpl.'suspendMatch' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"suspendMatch", MovementManagerImpl.class);
		}
	} // End of suspendMatch method

	public Long confirmMatch(String hostId, String entityId,
			String currencyCode, String selectedMovList, String selectedTab)
			throws SwtException {
		try {
			Long matchID = null;
			HashSet brokenMatchIds = new HashSet();
			ArrayList movcoll = (ArrayList) movementDAO.getAllMovement(hostId,
					entityId, selectedMovList);
			ArrayList loroAccountsList = new ArrayList();

			Iterator itr = null;
			HashSet posLvlSet = new HashSet();
			Movement movement = null;
			Long lowestMatchId = new Long(Long.MAX_VALUE);
			String lowestMatchStatus = SwtConstants.OFFERD_STATUS;
			Iterator itrcoll = movcoll.iterator();

			movement = new Movement();
			String initialPredictStatus = "";

			while (itrcoll.hasNext()) {
				movement = (Movement) itrcoll.next();
				initialPredictStatus = initialPredictStatus
						+ movement.getInitialPredStatus();
				if (movement.getPositionLevel() != null) {
					posLvlSet.add(movement.getPositionLevel());
				}

				if (movement.getMatchId() != null) {
					if (lowestMatchId.longValue() > movement.getMatchId()
							.longValue()) {
						lowestMatchId = movement.getMatchId();
						lowestMatchStatus = movement.getMatchStatus();
					}
					brokenMatchIds.add(movement.getMatchId());
				}

			}
			matchID = lowestMatchId;

			ArrayList deleteMatchList = new ArrayList();
			ArrayList deleteMatchNotesList = new ArrayList();
			/*
			 * Populate the deleteMatchList with all the other existing matches
			 * associated with movementIds while retaining the oldest match
			 */
			MatchDisplayDAO matchDisplayDAO = (MatchDisplayDAO) SwtUtil
					.getBean("matchDisplayDAO");

			if (lowestMatchId.longValue() != Long.MAX_VALUE) {
				itr = movcoll.iterator();
				movement = new Movement();

				while (itr.hasNext()) {
					movement = (Movement) itr.next();

					if ((movement.getMatchId() != null)
							&& (movement.getMatchId().longValue() != lowestMatchId
									.longValue())) {
						Match match = new Match();
						match.getId().setHostId(hostId);
						match.getId().setEntityId(entityId);
						match.getId().setMatchId(movement.getMatchId());
						Collection matchNotes = matchDisplayDAO.getMatchNotes(
								entityId, hostId, movement.getMatchId()
										.toString());
						deleteMatchNotesList.addAll(matchNotes);
						deleteMatchList.add(match);
						brokenMatchIds.remove(match.getId().getMatchId());
					}
				}
			}

			movement = new Movement();

			int count = movcoll.size();

			int highestPosLev = ((Movement) movcoll.get(0)).getPositionLevel()
					.intValue();

			String user = UserThreadLocalHolder.getUser();

			Match match = new Match();

			if (lowestMatchId.longValue() != Long.MAX_VALUE) {
				match = movementDAO.getMatch(hostId, entityId, lowestMatchId);
			}

			match.getId().setHostId(hostId);
			match.getId().setEntityId(entityId);
			match.setCurrencyCode(currencyCode);
			match.setHighestPosLev(highestPosLev);
			match.setStatusUser(user);

			Date currentSystemDate = SwtUtil.getSystemDatewithTime();
			match.setConfirmedDate(currentSystemDate);
			match.setOrigConfirmedDate(currentSystemDate);
			match.setStatusDate(currentSystemDate);
			match.setStatus(SwtConstants.CONFRM_STATUS);

			// Set the Match_InternalQuality[HighestPosLevel] to "A"
			Iterator posLvlItr = posLvlSet.iterator();

			while (posLvlItr.hasNext()) {
				Integer posLvl = (Integer) posLvlItr.next();

				switch (posLvl.intValue()) {
				case 1:
					match.setIntQuality1(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 2:
					match.setIntQuality2(SwtConstants.MATCH_INTERNAL_QUALITY);

					break;

				case 3:
					match.setIntQuality3(SwtConstants.MATCH_INTERNAL_QUALITY);

					break;

				case 4:
					match.setIntQuality4(SwtConstants.MATCH_INTERNAL_QUALITY);

					break;

				case 5:
					match.setIntQuality5(SwtConstants.MATCH_INTERNAL_QUALITY);

					break;

				case 6:
					match.setIntQuality6(SwtConstants.MATCH_INTERNAL_QUALITY);

					break;

				case 7:
					match.setIntQuality7(SwtConstants.MATCH_INTERNAL_QUALITY);

					break;

				case 8:
					match.setIntQuality8(SwtConstants.MATCH_INTERNAL_QUALITY);

					break;

				case 9:
					match.setIntQuality9(SwtConstants.MATCH_INTERNAL_QUALITY);

					break;
				}
			}

			/* INFO: Populating new columns of match object */
			match = populateMatchSRCDetails(match, movcoll);

			/*
			 * If no movement in selected List is already in match then Call to
			 * create a new Match record , returns Created MatchId
			 */
			if (matchID.longValue() == Long.MAX_VALUE) {
				match.setMatchQuality(SwtConstants.MANUAL_MATCH_QUALITY);
				matchID = movementDAO.createMatch(match);

				/*
				 * MatchQuality will be set to Manual only when a new match is
				 * created
				 */
			}

			String isBookCode = SwtConstants.YES;
			String isHst = SwtConstants.STR_FALSE;
			Movement movObj = new Movement();
			ArrayList movCollPre = new ArrayList();
			Iterator movPreStaItr = movcoll.iterator();

			while (movPreStaItr.hasNext()) {
				movObj = (Movement) movPreStaItr.next();

				// Set Movement matchId to generated matchId
				movObj.setMatchId(matchID);

				// Set Movement matchStatus to 'C'i.e Confirmed
				movObj.setMatchStatus(SwtConstants.CONFRM_STATUS);

				if ((movObj.getInputSource() != null)
						&& SwtConstants.INPUT_HST.equalsIgnoreCase(movObj
								.getInputSource())) {
					isHst = SwtConstants.STR_TRUE;
				}

				if (highestPosLev == movObj.getPositionLevel().intValue()) {
					if (SwtConstants.NO.equalsIgnoreCase(movObj
							.getBookCodeAvail())) {
						isBookCode = SwtConstants.NO;
					}
					if (initialPredictStatus.indexOf('I') != -1) {
						movObj.setPredictStatus(SwtConstants.PREDICT_INC);
					}
				} else {
					movObj.setPredictStatus(SwtConstants.PREDICT_EXC);
				}

				String positionLevelInternalExternal = getPositionLevelInternalExternal(
						hostId, entityId, movObj.getPositionLevel().intValue());

				movObj.setOpenFlag(SwtConstants.NO);

				movCollPre.add(movObj);
			}

			ArrayList movCollBkCd = new ArrayList();
			Iterator itrMovBkCd = movCollPre.iterator();
			Iterator it3 = movcoll.iterator();

			if (isBookCode.equalsIgnoreCase(SwtConstants.NO)) {
				while (itrMovBkCd.hasNext()) {
					movObj = (Movement) itrMovBkCd.next();

					if ((highestPosLev == movObj.getPositionLevel().intValue())
							&& SwtConstants.NO.equalsIgnoreCase(movObj
									.getBookCodeAvail())) {
						while (it3.hasNext()) {
							Movement movTemp = (Movement) it3.next();

							if (movTemp.getBookCode() != null) {
								movObj.setBookCode(movTemp.getBookCode());

								break;
							}
						}
					}

					movCollBkCd.add(movObj);
				}
			}

			ArrayList movCollExt = new ArrayList();

			if (SwtConstants.STR_TRUE.equalsIgnoreCase(isHst)
					&& isBookCode.equalsIgnoreCase(SwtConstants.NO)) {
				Iterator itrMovExt = movCollBkCd.iterator();

				while (itrMovExt.hasNext()) {
					movObj = (Movement) itrMovExt.next();

					if (highestPosLev == movObj.getPositionLevel().intValue()) {
						// set the Extract Status of Movement with highest
						// Position level as "I"
						movObj.setExtractStatus(SwtConstants.EXTRACT_INC);
					} else {
						// set the Extract Status of Movement with highest
						// Position level as "E"
						movObj.setExtractStatus(SwtConstants.EXTRACT_EXC);
					}
				}

				movCollExt.add(movObj);
			} else {
				if (SwtConstants.STR_TRUE.equalsIgnoreCase(isHst)
						&& !isBookCode.equalsIgnoreCase(SwtConstants.NO)) {
					Iterator itrMovExt = movCollPre.iterator();

					while (itrMovExt.hasNext()) {
						movObj = (Movement) itrMovExt.next();

						if (highestPosLev == movObj.getPositionLevel()
								.intValue()) {
							// set the Extract Status of Movement with highest
							// Position level as "I"
							movObj.setExtractStatus(SwtConstants.EXTRACT_INC);
						} else {
							// set the Extract Status of Movement with highest
							// Position level as "E"
							movObj.setExtractStatus(SwtConstants.EXTRACT_EXC);
						}
					}

					movCollExt.add(movObj);
				}
			}

			// Updates all the movements with above matchStatus and matchId
			if (SwtConstants.STR_TRUE.equalsIgnoreCase(isHst)) {
				movementDAO.updateMovements(movCollExt);
			} else {
				movementDAO.updateMovements(movCollPre);
			}

			if (lowestMatchId.longValue() != Long.MAX_VALUE) {
				// update the lowest Match
				movementDAO.updateMatch(match);
				brokenMatchIds.remove(match.getId().getMatchId());

				// Delete all Other Matches associated with movementIds while
				// retaining the oldest match
				movementDAO.deleteOtherMatches(deleteMatchList,
						deleteMatchNotesList);
			}
			// UnLock all the Movements
			itr = movcoll.iterator();

			Collection lockedMovList = new ArrayList();
			while (itr.hasNext()) {
				movObj = (Movement) itr.next();
				Long movId = movObj.getId().getMovementId();
				MovementLock movementLock = new MovementLock();
				movementLock.getId().setHostId(hostId);
				movementLock.getId().setMovementId(movId);
				movementLock.getId().setEntityId(entityId);
				lockedMovList.add(movementLock);
			}
			MovementLockDAO movementLockDAOObj = (MovementLockDAO) SwtUtil
					.getBean("movementLockDAO");
			movementLockDAOObj.unLockMovement(lockedMovList);
			MatchManager matchManager = (MatchManager) SwtUtil
					.getBean("matchManager");
			matchManager.updateBrokenMatch(hostId, entityId, brokenMatchIds);

			log.debug("Exiting confirmMatch method:::: ");
			return matchID;
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementManagerImpl.'confirmMatch' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"confirmMatch", MovementManagerImpl.class);
		}
	}

	/**
	 * @param hostId -
	 *            hostId
	 * @param entityId -
	 *            entityId
	 * @param currencyCode -
	 *            currencyCode
	 * @param selectedMovList -
	 *            String containing all the movements selected in Movement
	 *            Summary Screen for Reconcile
	 * @param selectedTab -
	 *            selectedTab
	 * @throws SwtException -
	 *             SwtException
	 * @return matchId - matchId of the lowest match if any of the selected
	 *         movements are in any match OR creates a new match
	 */
	public void reconMatch(String hostId, String entityId, String currencyCode,
			String selectedMovList, String selectedTab) throws SwtException {
		try {
			log.debug("Entering reconMatch method:::: ");

			ArrayList movcoll = (ArrayList) movementDAO.getMovement(hostId,
					entityId, selectedMovList);
			Iterator movItr = movcoll.iterator();
			Movement movement = null;
			ArrayList movupdateList = new ArrayList();

			while (movItr.hasNext()) {
				movement = (Movement) movItr.next();
				movement.setMatchStatus(SwtConstants.RECONCILE_STATUS);
				movupdateList.add(movement);
			}

			movementDAO.updateMovements(movupdateList);

			// Iterator for the selected movements
			Iterator itr = movcoll.iterator();
			// Movement object
			Movement movObj = null;
			// Put all the selected moveemtns in a single collection object
			Collection lockedMovList = new ArrayList();
			// Iterating through the selected movements for reconciling the
			// match
			while (itr.hasNext()) {
				// Get the movement onject one by one
				movObj = (Movement) itr.next();
				// Stores the movement id
				Long movId = movObj.getId().getMovementId();
				// New object for movement lock
				MovementLock movementLock = new MovementLock();
				movementLock.getId().setHostId(hostId);
				movementLock.getId().setMovementId(movId);
				movementLock.getId().setEntityId(entityId);
				// Adding each locked movement from the match
				lockedMovList.add(movementLock);
			}
			// Getting the movementLock DAO Hibernate instance from pool
			MovementLockDAO movementLockDAOObj = (MovementLockDAO) SwtUtil
					.getBean("movementLockDAO");
			// Unlocking the movements with the selected list
			movementLockDAOObj.unLockMovement(lockedMovList);

			log.debug("Exiting reconMatch method:::: ");

			// return matchID;
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementManagerImpl.'reconMatch' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"reconMatch", MovementManagerImpl.class);
		}
	}

	/**
	 * @param hostId -
	 *            hostId
	 * @param entityId -
	 *            entityId
	 * @param currencyCode -
	 *            currencyCode
	 * @param selectedMovList -
	 *            String containing all the movements selected in Movement
	 *            Summary Screen for Offered
	 * @param selectedTab -
	 *            selectedTab
	 * @throws SwtException -
	 *             SwtException
	 * @return matchId - matchId of the lowest match if any of the selected
	 *         movements are in any match OR creates a new match
	 */
	public Long offeredMatch(String hostId, String entityId,
			String currencyCode, String selectedMovList, String selectedTab)
			throws SwtException {
		/* Local variable declaration */
		/* Variable Declaration for matchID. */
		Long matchID = null;
		/* Variable Declaration for brokenMatchIds. */
		HashSet brokenMatchIds = null;
		/* Variable Declaration for movcoll. */
		ArrayList movcoll = null;
		/* Variable Declaration for itr. */
		Iterator itr = null;
		/* Variable Declaration for movObj. */
		Movement movObj = null;
		/* Variable Declaration for posLvlSet. */
		HashSet posLvlSet = null;
		/* Variable Declaration for movement. */
		Movement movement = null;
		/* Variable Declaration for lowestMatchId. */
		Long lowestMatchId = null;
		/* Variable Declaration for lowestMatchStatus. */
		String lowestMatchStatus = null;
		/* Variable Declaration for itrcoll. */
		Iterator itrcoll = null;
		/* Variable Declaration for deleteMatchList. */
		ArrayList deleteMatchList = null;
		/* Variable Declaration for deleteMatchNotesList. */
		ArrayList deleteMatchNotesList = null;
		/* Variable Declaration for matchDisplayDAO. */
		MatchDisplayDAO matchDisplayDAO = null;
		/* Variable Declaration for matchNotes. */
		Collection matchNotes = null;
		/* Variable Declaration for user. */
		String user = null;
		/* Variable Declaration for movItr. */
		Iterator movItr = null;
		/* Variable Declaration for isBookCode. */
		String isBookCode = null;
		/* Variable Declaration for isHst. */
		String isHst = null;
		/* Variable Declaration for movCollPre. */
		ArrayList movCollPre = null;
		/* Variable Declaration for movPreStaItr. */
		Iterator movPreStaItr = null;
		/* Variable Declaration for movCollExt. */
		ArrayList movCollExt = null;
		/* Variable Declaration for movCollBkCd. */
		ArrayList movCollBkCd = null;
		/* Variable Declaration for itrMovBkCd. */
		Iterator itrMovBkCd = null;
		/* Variable Declaration for it3. */
		Iterator it3 = null;
		/* Variable Declaration for movCollToUpdate. */
		ArrayList movCollToUpdate = null;
		/* Variable Declaration for movId. */
		Long movId = null;
		/* Variable Declaration for movementLock. */
		MovementLock movementLock = null;
		/* Variable Declaration for movementLockDAOObj. */
		MovementLockDAO movementLockDAOObj = null;
		/* Variable Declaration for matchManager. */
		MatchManager matchManager = null;
		/* Variable Declaration for highestPosLev. */
		int highestPosLev = 0;
		try {
			log.debug(this.getClass().getName() + " - [offeredMatch] - "
					+ "Entry");

			brokenMatchIds = new HashSet();
			movcoll = (ArrayList) movementDAO.getAllMovement(hostId, entityId,
					selectedMovList);

			movObj = new Movement();
			posLvlSet = new HashSet();
			lowestMatchId = new Long(Long.MAX_VALUE);
			lowestMatchStatus = SwtConstants.OFFERD_STATUS;

			// Checking the movements if they are already in some match
			itrcoll = movcoll.iterator();
			while (itrcoll.hasNext()) {
				movement = (Movement) itrcoll.next();
				if (movement.getPositionLevel() != null) {
					posLvlSet.add(movement.getPositionLevel());
				}
				if (movement.getMatchId() != null) {
					if (lowestMatchId.longValue() > movement.getMatchId()
							.longValue()) {
						lowestMatchId = movement.getMatchId();
						lowestMatchStatus = movement.getMatchStatus();
					}
					brokenMatchIds.add(movement.getMatchId());
				}
			}

			matchID = lowestMatchId;

			deleteMatchList = new ArrayList();
			deleteMatchNotesList = new ArrayList();
			/*
			 * Populate the deleteMatchList with all the other existing matches
			 * associated with movementIds while retaining the oldest match
			 */
			matchDisplayDAO = (MatchDisplayDAO) SwtUtil
					.getBean("matchDisplayDAO");

			if (lowestMatchId.longValue() != Long.MAX_VALUE) {
				itr = movcoll.iterator();
				movement = new Movement();
				while (itr.hasNext()) {
					movement = (Movement) itr.next();
					if ((movement.getMatchId() != null)
							&& (movement.getMatchId().longValue() != lowestMatchId
									.longValue())) {
						Match match = new Match();
						match.getId().setHostId(hostId);
						match.getId().setEntityId(entityId);
						match.getId().setMatchId(movement.getMatchId());

						matchNotes = matchDisplayDAO.getMatchNotes(entityId,
								hostId, movement.getMatchId().toString());
						Iterator notesitr = matchNotes.iterator();
						MatchNote matchNote = null;

						while (notesitr.hasNext()) {
							matchNote = new MatchNote();
							matchNote = (MatchNote) notesitr.next();
							deleteMatchNotesList.add(matchNote);
						}

						deleteMatchList.add(match);
						brokenMatchIds.remove(match.getId().getMatchId());
					}
				}
			}

			int count = movcoll.size();

			highestPosLev = ((Movement) movcoll.get(0)).getPositionLevel()
					.intValue();
			user = UserThreadLocalHolder.getUser();
			Match match = new Match();

			if (lowestMatchId.longValue() != Long.MAX_VALUE) {
				match = movementDAO.getMatch(hostId, entityId, lowestMatchId);
				if (lowestMatchStatus.equals(SwtConstants.CONFRM_STATUS)) {
					match.setConfirmedDate(SwtUtil.getSystemDatewithTime());
				}
			} else {
				match.getId().setHostId(hostId);
				match.getId().setEntityId(entityId);
				match.setCurrencyCode(currencyCode);
				// In case of New Match set Match Status as offered 'M'
			}

			match.setStatus(SwtConstants.OFFERD_STATUS);
			match.setHighestPosLev(highestPosLev);
			match.setStatusUser(user);
			match.setStatusDate(SwtUtil.getSystemDatewithTime());
			// Set the Match_InternalQuality[HighestPosLevel] to "Z" as it is
			// offered Match.
			Iterator itrPosLvlSet = posLvlSet.iterator();
			while (itrPosLvlSet.hasNext()) {
				switch (((Integer) itrPosLvlSet.next()).intValue()) {
				case 1:
					match.setIntQuality1(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 2:
					match.setIntQuality2(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 3:
					match.setIntQuality3(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 4:
					match.setIntQuality4(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 5:
					match.setIntQuality5(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 6:
					match.setIntQuality6(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 7:
					match.setIntQuality7(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 8:
					match.setIntQuality8(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				case 9:
					match.setIntQuality9(SwtConstants.MANUAL_MATCH_QUALITY);
					break;
				}
			}

			/* INFO: Populating new columns of match object */
			match = populateMatchSRCDetails(match, movcoll);
			/*
			 * If no movement in selected List is already in match then Call to
			 * create a new Match record , returns Created MatchId
			 */
			if (matchID.longValue() == Long.MAX_VALUE) {
				// log.debug("creating new match");
				matchID = movementDAO.createMatch(match);

				/*
				 * set the matchQuality status to Manual only when a new match
				 * is created
				 */
				match.setMatchQuality(SwtConstants.MANUAL_MATCH_QUALITY);
			}

			movItr = movcoll.iterator();

			// Separate conditions for Movements when the lowest Match is
			// Confirmed or other than Confirmed

			if (lowestMatchStatus.equals(SwtConstants.CONFRM_STATUS)) {
				// This If block Sets the Movement details when Match Status is
				// Confirm .
				isBookCode = SwtConstants.YES;
				isHst = SwtConstants.STR_FALSE;
				movCollPre = new ArrayList();
				movPreStaItr = movcoll.iterator();
				while (movPreStaItr.hasNext()) {
					movObj = (Movement) movPreStaItr.next();
					movObj.setMatchId(matchID);
					movObj.setMatchStatus(SwtConstants.CONFRM_STATUS);
					if ((movObj.getInputSource() != null)
							&& SwtConstants.INPUT_HST.equalsIgnoreCase(movObj
									.getInputSource())) {
						isHst = SwtConstants.STR_TRUE;
					}
					movObj.setPredictStatus(movObj.getInitialPredStatus());
					movObj.setMatchStatus(SwtConstants.OFFERD_STATUS);
					movCollPre.add(movObj);
				}

				movCollBkCd = new ArrayList();
				itrMovBkCd = movCollPre.iterator();
				it3 = movcoll.iterator();

				if (isBookCode.equalsIgnoreCase(SwtConstants.NO)) {
					while (itrMovBkCd.hasNext()) {
						movObj = (Movement) itrMovBkCd.next();

						if ((highestPosLev == movObj.getPositionLevel()
								.intValue())
								&& SwtConstants.NO.equalsIgnoreCase(movObj
										.getBookCodeAvail())) {
							while (it3.hasNext()) {
								Movement movTemp = (Movement) it3.next();

								if (movTemp.getBookCode() != null) {
									movObj.setBookCode(movTemp.getBookCode());

									break;
								}
							}
						}
						movObj.setMatchStatus(SwtConstants.OFFERD_STATUS);
						movCollBkCd.add(movObj);
					}
				}

				movCollExt = new ArrayList();

				if (SwtConstants.STR_TRUE.equalsIgnoreCase(isHst)
						&& isBookCode.equalsIgnoreCase(SwtConstants.NO)) {
					Iterator itrMovExt = movCollBkCd.iterator();

					while (itrMovExt.hasNext()) {
						movObj = (Movement) itrMovExt.next();

						if (highestPosLev == movObj.getPositionLevel()
								.intValue()) {
							// set the Extract Status of Movement with highest
							// Position level as "I"
							movObj.setExtractStatus(SwtConstants.EXTRACT_INC);
						} else {
							// set the Extract Status of Movement with highest
							// Position level as "E"
							movObj.setExtractStatus(SwtConstants.EXTRACT_EXC);
						}
					}
					movObj.setMatchStatus(SwtConstants.OFFERD_STATUS);
					movCollExt.add(movObj);
				} else {
					if (SwtConstants.STR_TRUE.equalsIgnoreCase(isHst)
							&& !isBookCode.equalsIgnoreCase(SwtConstants.NO)) {
						Iterator itrMovExt = movCollPre.iterator();

						while (itrMovExt.hasNext()) {
							movObj = (Movement) itrMovExt.next();

							if (highestPosLev == movObj.getPositionLevel()
									.intValue()) {
								// set the Extract Status of Movement with
								// highest
								// Position level as "I"
								movObj
										.setExtractStatus(SwtConstants.EXTRACT_INC);
							} else {
								// set the Extract Status of Movement with
								// highest
								// Position level as "E"
								movObj
										.setExtractStatus(SwtConstants.EXTRACT_EXC);
							}
						}

						movCollExt.add(movObj);
					}
				}

				// Updates all the movements with above matchStatus and matchId
				if (SwtConstants.STR_TRUE.equalsIgnoreCase(isHst)) {
					movementDAO.updateMovements(movCollExt);
				} else {
					movementDAO.updateMovements(movCollPre);
				}

				Iterator posLvlItr = posLvlSet.iterator();

				/*
				 * This while block set the Internal Quality to 'Z' for all the
				 * position levels involved in the match in case of confirmed
				 * match.
				 */
				while (posLvlItr.hasNext()) {
					Integer posLvl = (Integer) posLvlItr.next();
					switch (posLvl.intValue()) {
					case 1:
						match.setIntQuality1(SwtConstants.MANUAL_MATCH_QUALITY);
						break;
					case 2:
						match
								.setIntQuality2(SwtConstants.MATCH_INTERNAL_QUALITY);
						break;
					case 3:
						match
								.setIntQuality3(SwtConstants.MATCH_INTERNAL_QUALITY);
						break;
					case 4:
						match
								.setIntQuality4(SwtConstants.MATCH_INTERNAL_QUALITY);
						break;
					case 5:
						match
								.setIntQuality5(SwtConstants.MATCH_INTERNAL_QUALITY);
						break;
					case 6:
						match
								.setIntQuality6(SwtConstants.MATCH_INTERNAL_QUALITY);
						break;
					case 7:
						match
								.setIntQuality7(SwtConstants.MATCH_INTERNAL_QUALITY);
						break;
					case 8:
						match
								.setIntQuality8(SwtConstants.MATCH_INTERNAL_QUALITY);
						break;
					case 9:
						match
								.setIntQuality9(SwtConstants.MATCH_INTERNAL_QUALITY);
						break;
					}
				}
			} else {
				// This else block Sets the Movement details for Match Status
				// other than Confirm Match
				movCollToUpdate = new ArrayList();

				while (movItr.hasNext()) {
					movObj = (Movement) movItr.next();
					movObj.setMatchStatus(SwtConstants.OFFERD_STATUS);

					// Set Movement matchId to generated matchId or already
					// existing Lowest Match
					movObj.setMatchId(matchID);

					// Set Movement matchStatus same as lowestMatchStatus
					// movObj.setMatchStatus(lowestMatchStatus);
					movObj.setMatchStatus(SwtConstants.OFFERD_STATUS);
					movCollToUpdate.add(movObj);
				}

				// Updates all the movements with above matchStatus and matchId
				movementDAO.updateMovements(movCollToUpdate);
			}

			if (lowestMatchId.longValue() != Long.MAX_VALUE) {
				// update the lowest Match
				movementDAO.updateMatch(match);
				brokenMatchIds.remove(match.getId().getMatchId());
				// Delete all Other Matches associated with movementIds while
				// retaining the oldest match
				movementDAO.deleteOtherMatches(deleteMatchList,
						deleteMatchNotesList);
			}

			// UnLock all the Movements
			itr = movcoll.iterator();
			Collection lockedMovList = new ArrayList();
			while (itr.hasNext()) {
				movObj = (Movement) itr.next();
				movId = movObj.getId().getMovementId();
				movementLock = new MovementLock();
				movementLock.getId().setHostId(hostId);
				movementLock.getId().setMovementId(movId);
				movementLock.getId().setEntityId(entityId);
				lockedMovList.add(movementLock);
			}
			movementLockDAOObj = (MovementLockDAO) SwtUtil
					.getBean("movementLockDAO");
			movementLockDAOObj.unLockMovement(lockedMovList);
			matchManager = (MatchManager) SwtUtil.getBean("matchManager");
			matchManager.updateBrokenMatch(hostId, entityId, brokenMatchIds);
			return matchID;
		} catch (Exception exp) {
			log
					.debug("Exception Catch in MovementManagerImpl.'offeredMatch' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"offeredMatch", MovementManagerImpl.class);
		} finally {
			log.debug(this.getClass().getName() + " - [offeredMatch] - "
					+ "Exit");
			/* null the objects created already. */
			brokenMatchIds = null;
			movcoll = null;
			itr = null;
			movObj = null;
			posLvlSet = null;
			movement = null;
			lowestMatchId = null;
			lowestMatchStatus = null;
			itrcoll = null;
			deleteMatchList = null;
			deleteMatchNotesList = null;
			matchDisplayDAO = null;
			matchNotes = null;
			user = null;
			movItr = null;
			isBookCode = null;
			isHst = null;
			movCollPre = null;
			movPreStaItr = null;
			movCollExt = null;
			movCollBkCd = null;
			itrMovBkCd = null;
			it3 = null;
			movCollToUpdate = null;
			movId = null;
			movementLock = null;
			movementLockDAOObj = null;
			matchManager = null;
		}
	} // End of offeredMatch method

	/**
	 * 
	 * @param movementId
	 * @return
	 * @throws SwtException
	 */
	public Movement getMovementDetails(String hostId, Long movementId)
			throws SwtException {
		return movementDAO.getMovementDetails(hostId, movementId);
	}

	/**
	 * DOCUMENT ME!
	 * 
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param movementId
	 *            DOCUMENT ME!
	 * @param systemInfo
	 *            DOCUMENT ME!
	 * @param systemFormats
	 *            DOCUMENT ME!
	 * 
	 * @return DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Movement getMovementDetails(String hostId, Long movementId,
			SystemInfo systemInfo, SystemFormats systemFormats)
			throws SwtException {

		Movement movement = null;
        String matchQlty=null;
		movement = movementDAO.getMovementDetails(hostId, movementId);
		Collection coll = null;
		if (movement == null) {
			coll = (Collection) CacheManager.getInstance().getMiscParams(
					"MATCHSTATUS", null);
		} else {
			coll = (Collection) CacheManager.getInstance().getMiscParams(
					"MATCHSTATUS", movement.getId().getEntityId());
		}
		Collection collNotes = movementDAO.getNoteDetails(hostId, movementId);
		if (movement != null) {
			Iterator itr = coll.iterator();

			while (itr.hasNext()) {
				MiscParams mp = (MiscParams) (itr.next());

				if ((movement.getMatchStatus() != null)
						&& !(movement.getMatchStatus().equals(""))) {
					if (movement.getMatchStatus().equals(mp.getId().getKey2())) {
						movement.setMatchStatusDesc(mp.getParValue());
					}
				}
				if (collNotes.size() > 0) {
					movement.setHasNotes("Y");
				} else {
					movement.setHasNotes("N");
				}
			}

			movement.setUpdateDateAsString(SwtUtil.formatDate(movement
					.getUpdateDate(), systemFormats.getDateFormatValue()));

			// set update time
			if (movement.getUpdateDate() != null) {
				SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
				movement.setUpdateTimeAsString(sdf.format(movement
						.getUpdateDate()));
			}

			movement.setValueDateAsString(SwtUtil.formatDate(movement
					.getValueDate(), systemFormats.getDateFormatValue()));

			movement.setAmountAsString(SwtUtil.formatCurrency(movement
					.getCurrencyCode(), movement.getAmount()));

			// appending matchid,updatedate,updateuser with matchDesc
			String completeMatchDesc = new String("");

			if (movement.getMatchStatus() != null) {
				completeMatchDesc = movement.getMatchStatusDesc() + " ";
			}
			matchQlty=movementDAO.getMatchQuality(movement.getMatchId(), hostId, movement.getId().getEntityId());
			completeMatchDesc = completeMatchDesc+ matchQlty + " ";

			if (movement.getMatchId() != null) {
				completeMatchDesc = completeMatchDesc
						+ movement.getMatchId().toString() + " ";
			}

			if (movement.getUpdateDate() != null) {
				completeMatchDesc = completeMatchDesc
						+ movement.getUpdateDateAsString() + " ";
				completeMatchDesc = completeMatchDesc
						+ movement.getUpdateTimeAsString() + " ";
			}

			if (movement.getUpdateUser() != null) {
				completeMatchDesc = completeMatchDesc
						+ movement.getUpdateUser() + " ";
			}

			movement.setMatchStatusDesc(completeMatchDesc);

			if (movement.getPositionLevel() != null) {
				movement.setPositionLevelAsString(movement.getPositionLevel()
						.toString());
			} else {
				movement.setPositionLevelAsString("");
			}

			Collection collMessages = movementDAO.getMovementMessageId(hostId,
					movement.getId().getMovementId());

			movement.setMessageId("");

			if (collMessages != null) {
				if (collMessages.size() == 1) {
					Iterator itrMessageColl = collMessages.iterator();

					while (itrMessageColl.hasNext()) {
						MovementMessage ms = (MovementMessage) (itrMessageColl
								.next());

						if (ms.getMessageId() != null) {
							movement.setMessageId(ms.getMessageId().toString());
						}
					}
				} else if (collMessages.size() > 1) {
					movement.setIsManyMessages("Y");
				}
			}

			return movement;
		} else {
			return null;
		}
	}

	/**
	 * 
	 */
	public Collection getBookCodeList(String hostId, String entityId)
			throws SwtException {
		return movementDAO.getBookCodeList(hostId, entityId);
	}

	/**
	 * DOCUMENT ME!
	 * 
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 * 
	 * @return DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getPartyList(String hostId, String entityId)
			throws SwtException {
		return movementDAO.getPartyList(hostId, entityId);
	}



	/**
	 * DOCUMENT ME!
	 * 
	 * @param movement
	 *            DOCUMENT ME!
	 * @param matchDriver
	 *            DOCUMENT ME!
	 * @param matchDriverOperation
	 *            DOCUMENT ME!
	 * @param sessionNotesDetails
	 *            DOCUMENT ME!
	 * @param systemInfo
	 *            DOCUMENT ME!
	 * @param sysformat
	 *            DOCUMENT ME!
	 * 
	 * @return DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public String saveMovementDetails(Movement movement,
			MatchDriver matchDriver, String matchDriverOperation,
			Collection sessionNotesDetails, SystemInfo systemInfo,
			SystemFormats sysformat) throws SwtException {
		try {
			movement.setAmount(SwtUtil.parseCurrency(movement
					.getAmountAsString(), sysformat.getCurrencyFormat()));

			if ((movement.getValueDateAsString() == null)
					|| movement.getValueDateAsString().equals("")) {
				movement.setValueDate(SwtUtil.getSystemDatewithoutTime());
			} else {
				movement.setValueDate(SwtUtil
						.parseDate(movement.getValueDateAsString(), sysformat
								.getDateFormatValue()));
			}
			if (!SwtUtil.isEmptyOrNull(movement.getExpectedSettlementDateTimeAsString())){
				movement.setExpectedSettlementDateTime(SwtUtil
						.parseDate(movement.getExpectedSettlementDateTimeAsString(), sysformat
								.getDateFormatValue().concat("HH:mm:ss")));
			}
			
			if (!SwtUtil.isEmptyOrNull(movement.getSettlementDateTimeAsString())){
				movement.setSettlementDateTime(SwtUtil
						.parseDate(movement.getSettlementDateTimeAsString(), sysformat
								.getDateFormatValue().concat("HH:mm:ss")));
			}
			
			movementDAO.saveMovementDetails(movement);

			// Saving Notes Details
			NotesDAO notesDAO = (NotesDAO) SwtUtil.getBean("notesDAO");

			if (sessionNotesDetails != null) {
				Iterator itr = sessionNotesDetails.iterator();

				while (itr.hasNext()) {

					MovementNote movNot = (MovementNote) (itr.next());
					String movementIdAsString = movement.getId()
							.getMovementId().toString();
					movNot.getId().setMovementId(
							movement.getId().getMovementId());
					movementDAO.saveNotesDetails(movNot);
				}
			}

			// Interaction with matchDriver table
			if (!movement.getMatchStatus().equalsIgnoreCase("L")) {
				// Inserting records in p_sweep_alerts when 'MT' record is
				// present in p_alerts
				String hostId = movement.getId().getHostId();

				SystemAlertMessagesDAO systemAlertMessagesDAO = (SystemAlertMessagesDAO) (SwtUtil
						.getBean("sysAlertMessagesDAO"));
				final boolean MT_FLAG = systemAlertMessagesDAO.alertAllow(
						movement.getId().getHostId(),
						SwtConstants.MOVEMENT_ALERT_STAGE_MT);

				if (MT_FLAG) {
					SweepAlertDAO sweepAlertDAO = (SweepAlertDAO) (SwtUtil
							.getBean("sweepAlertDAO"));
					sweepAlertDAO
							.createMovementAlert(getMovementAlertInstance(movement));
				}
			}

			log.debug("Exiting 'saveMovementDetails' method");

			return movement.getId().getMovementId().toString();
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementManagerImpl.'saveMovementDetails' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveMovementDetails", MovementManagerImpl.class);
		}
	}

	/**
	 * Method use to create a fresh instance of SweepAlert class.
	 * 
	 * @param movement
	 * @return
	 */
	private SweepAlert getMovementAlertInstance(Movement movement) {
		SweepAlert sweepAlert = new SweepAlert();
		sweepAlert.getId().setHostId(movement.getId().getHostId());
		sweepAlert.getId().setEntityId(movement.getId().getEntityId());
		sweepAlert.setCurrencyCode(movement.getCurrencyCode());
		sweepAlert.setSweepId(movement.getId().getMovementId());
		sweepAlert.setSweepAmount(movement.getAmount());
		sweepAlert.setAlertStage(SwtConstants.MOVEMENT_ALERT_STAGE_MT);
		sweepAlert.setAlertDate(SwtUtil.getSystemDatewithoutTime());
		sweepAlert.setStatus(SwtConstants.ALERT_STATUS_PENDING);

		return sweepAlert;
	}

	/**
	 * DOCUMENT ME!
	 * 
	 * @param movement
	 *            DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public void updateMovementDetails(Movement movement, String identifier,
			SystemFormats sysformat) throws SwtException {
		try {
			log.debug("Entering 'updateMovementDetails' method");

			if (identifier.equals("updateAllFields")) {
				// parsing the amount field
				movement.setAmount(SwtUtil.parseCurrency(movement
						.getAmountAsString(), sysformat.getCurrencyFormat()));

				if ((movement.getValueDateAsString() == null)
						|| movement.getValueDateAsString().equals("")) {
					movement.setValueDate(SwtUtil.getSystemDatewithoutTime());
				} else {
					movement.setValueDate(SwtUtil.parseDate(movement
							.getValueDateAsString(), sysformat
							.getDateFormatValue()));
				}
			}

			movementDAO.updateMovementDetails(movement);
			log.debug("Exiting 'updateMovementDetails' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementManagerImpl.'updateMovementDetails' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMovementDetails", MovementManagerImpl.class);
		}
	}

	/**
	 * DOCUMENT ME!
	 * 
	 * @param hostId
	 *            DOCUMENT ME!
	 * 
	 * @return DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getMatchDriverDetails(String hostId) throws SwtException {
		log.debug("Entring getMatchDriverDetails Inside MovementManagerImpl");

		Collection records = movementDAO.getMatchDriverDetails(hostId);

		log.debug("Exiting 'getMatchDriverDetails' method");

		return records;
	}

	/**
	 * DOCUMENT ME!
	 * 
	 * @param matchDriver
	 *            DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public void updateMatchDriverDetails(MatchDriver matchDriver)
			throws SwtException {
		try {
			log.debug("Entering 'updateMatchDriverDetails' method");
			movementDAO.updateMatchDriverDetails(matchDriver);

			log.debug("Exiting 'updateMatchDriverDetails' method");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementManagerImpl.'updateMatchDriverDetails' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"updateMatchDriverDetails", MovementManagerImpl.class);
		}
	}

	/**
	 * This function is now modified to pass the filtering parameters to the Dao
	 * so that they can be appended to the quert while hitting p_Account
	 */
	public Collection getAccountIdDropDown(String hostId, String entityId,
			String currencyCode, String movType, String inputSource)
			throws SwtException {
		Collection accountIdDropDown = new ArrayList();
		accountIdDropDown.add(new LabelValueBean("", ""));

		Collection records = movementDAO.getAccountIdDetails(hostId, entityId,
				currencyCode, movType, inputSource);

		if (records != null) {
			Iterator itr = records.iterator();

			while (itr.hasNext()) {
				AcctMaintenance am = (AcctMaintenance) (itr.next());
				accountIdDropDown.add(new LabelValueBean(am.getAcctname(), am
						.getId().getAccountId()));
			}
		}
		return accountIdDropDown;
	}

	/**
	 * DOCUMENT ME!
	 * 
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 * @param counterPartyId
	 *            DOCUMENT ME!
	 * 
	 * @return DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getCounterPartyRecord(String hostId, String entityId,
			String counterPartyId) throws SwtException {
		log.debug("Entring getCounterPartyRecord Inside MovementManagerImpl");
		if ((counterPartyId != null) && !counterPartyId.equals("")) {
			Collection record = movementDAO.getCounterPartyRecord(hostId,
					entityId, counterPartyId);

			return record;
		} else {
			log
					.debug("Exiting getCounterPartyRecord Inside MovementManagerImpl");
			return null;
		}
	}

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param counterPartyId
	 * @return
	 * @throws SwtException
	 */
	public Party getPartyRecord(String hostId, String entityId,
			String partyId) throws SwtException {
		log.debug("Entring getPartyRecord Inside MovementManagerImpl");
		if ((partyId != null) && !partyId.equals("")) {
			Party record = movementDAO.getPartyRecord(hostId, entityId, partyId);

			return record;
		} else {
			log
					.debug("Exiting getPartyRecord Inside MovementManagerImpl");
			return null;
		}
	}

	/**
	 * DOCUMENT ME!
	 * 
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 * @param beneficiaryId
	 *            DOCUMENT ME!
	 * 
	 * @return DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getBeneficiaryRecord(String hostId, String entityId,
			String beneficiaryId) throws SwtException {
		log.debug("Entring getBeneficiaryRecord Inside MovementManagerImpl");

		if ((beneficiaryId != null) && !beneficiaryId.equals("")) {
			Collection record = movementDAO.getCounterPartyRecord(hostId,
					entityId, beneficiaryId);

			return record;
		} else {
			log
					.debug("Exiting getBeneficiaryRecord Inside MovementManagerImpl");

			return null;
		}
	}

	/**
	 * DOCUMENT ME!
	 * 
	 * @param hostId
	 *            DOCUMENT ME!
	 * @param entityId
	 *            DOCUMENT ME!
	 * @param custodianId
	 *            DOCUMENT ME!
	 * 
	 * @return DOCUMENT ME!
	 * 
	 * @throws SwtException
	 *             DOCUMENT ME!
	 */
	public Collection getCustodianRecord(String hostId, String entityId,
			String custodianId) throws SwtException {
		log.debug("Entring getCustodianRecord Inside MovementManagerImpl");

		if ((custodianId != null) && !custodianId.equals("")) {
			Collection record = movementDAO.getCounterPartyRecord(hostId,
					entityId, custodianId);

			return record;
		} else {
			log.debug("Exiting getCustodianRecord Inside MovementManagerImpl");

			return null;
		}
	}

	/**
	 * This method used to get the Matching party records from the data base
	 * 
	 * @param hostId
	 * @param entityId
	 * @param custodianId
	 * @throws SwtException
	 * @return collection
	 */
	public Collection getMatchingPartyRecord(String hostId, String entityId,
			String matchingParty) throws SwtException {
		log.debug(this.getClass().getName() + " - getMatchingPartyRecord() - "
				+ " Entry ");

		if ((matchingParty != null) && !matchingParty.equals("")) {
			Collection record = movementDAO.getMatchingPartyRecord(hostId,
					entityId, matchingParty);

			return record;
		} else {
			log.debug(this.getClass().getName()
					+ " - getMatchingPartyRecord() - " + " Exit ");

			return null;
		}
	}

	/**
	 * This method is used to retrieve Role ID for a given User ID
	 * 
	 * @param hostId
	 *            String
	 * @param userId
	 *            String
	 * 
	 * @return String roleId
	 * 
	 * @throws SwtException
	 *             User defined.
	 */
	public String getUserDetails(String hostId, String userId)
			throws SwtException {
		log.debug("inside getUserDetails");

		ArrayList currencyDetails = new ArrayList();
		userMaintenanceDAO = (UserMaintenanceDAO) (SwtUtil
				.getBean("usermaintenanceDAO"));

		UserMaintenance userMaintenance = new UserMaintenance();

		try {
			userMaintenance = (userMaintenanceDAO.fetchUserDetail(hostId,
					userId));
		} catch (Exception exp) {
			log
					.debug("Exception Catch in MovementManagerImpl.'getUserDetails' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getUserDetails", MovementManagerImpl.class);
		}

		return userMaintenance.getRoleId();
	}

	/**
	 * This method is basically used to retrieve Authorize flag for a given
	 * RoleID from S_ROLE table.
	 * 
	 * @param roleId
	 *            String
	 * 
	 * @return Authorizr flag for corresponding roleId from S_ROLE table.
	 * 
	 * @throws SwtException
	 *             User defined.Throws in case of any exception
	 */
	public String getRoleDetails(String roleId) throws SwtException {
		log.debug("inside getUserDetails");

		ArrayList currencyDetails = new ArrayList();
		roleDAO = (RoleDAO) (SwtUtil.getBean("roleDAO"));

		try {
			Collection roleColl = roleDAO.getRoleDetails();
			Iterator itr = (Iterator) roleColl.iterator();

			while (itr.hasNext()) {
				Role role = (Role) itr.next();

				if (role.getRoleId().equals(roleId)) {
					return role.getAuthorizeInput();
				}
			}
		} catch (Exception exp) {
			log
					.debug("Exception Catch in MovementManagerImpl.'getRoleDetails' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getRoleDetails", MovementManagerImpl.class);
		}

		return null;
	}

	public String saveRolledoverMovement(Movement movementNew,
			Movement movementExisting, SystemInfo systemInfo,
			SystemFormats sysformat) throws SwtException {
		try {
			log.debug("Entering into saveRolledoverMovement() ");

			String originalValueDateAsString = SwtUtil.formatDate(movementNew
					.getValueDate(), sysformat.getDateFormatValue());
			movementNew.setValueDate(SwtUtil.parseDate(movementNew
					.getNewValueDate(), sysformat.getDateFormatValue()));

			Collection collNotes = new ArrayList();
			NotesDAO notesDAO = (NotesDAO) SwtUtil.getBean("notesDAO");
			MovementNote movNote = new MovementNote();
			String movementIdAsString = "";
			String noteText = new String("");
			String textFromPropFile = new String("");
			Long previousMovementId = movementNew.getId().getMovementId();
			List arraylist = new ArrayList();
			StringTokenizer st = null;

			movementNew.getId().setMovementId(null);

			movementNew.setInputDate(SwtUtil.getSystemDatewithTime());

			movementDAO.saveMovementDetails(movementNew);

			if (movementNew.getId().getMovementId() != null) {
				movementIdAsString = movementNew.getId().getMovementId()
						.toString();
			}

			movNote.getId().setHostId(movementNew.getId().getHostId());
			movNote.getId().setEntityId(movementNew.getId().getEntityId());
			movNote.getId().setMovementId(movementNew.getId().getMovementId());

			textFromPropFile = SwtUtil.getMessage("movementRolledNoteText", null);
			st = new StringTokenizer(textFromPropFile, "?");

			while (st.hasMoreTokens())
				arraylist.add(st.nextToken());

			noteText = (String) arraylist.get(0) + previousMovementId
					+ (String) arraylist.get(1) + originalValueDateAsString;

			movNote.setNoteText(noteText);

			movementDAO.saveNotesDetails(movNote);

			movNote = new MovementNote();
			movNote.getId().setHostId(movementNew.getId().getHostId());
			movNote.getId().setEntityId(movementNew.getId().getEntityId());
			movNote.getId().setMovementId(previousMovementId);

			textFromPropFile = SwtUtil.getMessage("movementOriginalNoteText", null);
			st = new StringTokenizer(textFromPropFile, "?");

			arraylist = new ArrayList();

			while (st.hasMoreTokens())
				arraylist.add(st.nextToken());

			noteText = (String) arraylist.get(0) + movementIdAsString
					+ (String) arraylist.get(1) + movementNew.getNewValueDate();

			movNote.setNoteText(noteText);

			movementDAO.saveNotesDetails(movNote);

			// updating the existing movement(Only movement status is changed)
			movementDAO.updateMovementDetails(movementExisting);

			log.debug("Exiting from saveRolledoverMovement() ");
			return movementIdAsString;
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementManagerImpl.'saveRolledoverMovement' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveRolledoverMovement", MovementManagerImpl.class);
		}
	}

	/**
	 * @desc This method finds all the open movements
	 * @param hostId -
	 *            HostID
	 * @param entityId -
	 *            EntityID
	 * @param currencyGrpId -
	 *            Currency Group Id
	 * @param entityPosNamesColl -
	 *            ArrayList
	 * @param roleId -
	 *            Role ID
	 * @param selectedTabIndex
	 * @param sysformat -
	 *            SystemFormats
	 * @param applyCurrencyThreshold -
	 *            Apply Currency Threshold
	 * @param searchDateAsString -
	 *            String
	 * @return OpenMovementVO - OpenMovementVO object
	 * @throws SwtException -
	 *             SwtException
	 */
	public OpenMovementVO getOpenMovementsTotal(String hostId, String entityId,
			String currencyGrpId, ArrayList entityPosNamesColl, String roleId,
			String selectedTabIndex, SystemFormats sysformat,
			String applyCurrencyThreshold, String searchDateAsString)
			throws SwtException {
		// This block creates a hashtable for EntityPosLvl with key as
		// PosLvlId and PosLvlName as Value
		Hashtable posLvlhashTable = null;
		// EntityPositionLevelTO instance
		EntityPositionLevelTO entPosTO = null;
		// To hold collection for entity position level name
		ArrayList entPosLvlNamesColl = null;
		// Iterator for entity position level
		Iterator itrPosLvl = null;
		// initialize the highest position level
		int highestPosLvl = 0;
		// To hold hashtable for currency
		Hashtable currHashTable = null;
		// To hold list for currency
		ArrayList currencyList = null;
		// To hold list for openMovement
		ArrayList openMovementList = null;
		// To hold the systemDate for various tab
		Date systemDate = null;
		// To hold date in string
		String dateAsString = null;
		// To hold Yes or No flag
		String isAll = null;
		// OpenMovementVO instance
		OpenMovementVO openMovementVO = null;
		// Collection entity position level
		ArrayList entPosLvlColl = null;
		// Collection of position level name
		Collection<EntityPositionLevel> posLvlNamesColl = null;
		// Position level name list
		ArrayList posLvlNamesList = null;
		// Hashset for currency
		HashSet currhashSet = null;
		// Iterator for postion level
		Iterator itrPosLvlList = null;
		// Start the Currency loop
		Iterator hashSetItr = null;
		// variable to hold tabflag
		String tabFlag = null;
		// Currency Code
		String currencyCode = null;
		// OpenMovementSummary instance
		OpenMovementSummary openMovementSummary = null;
		// Iterate for entity position level list
		Iterator entPosLvlListItr = null;
		// List to maintain the position level name by currency
		ArrayList posLvlNamesByCurrList = null;
		// To hold the system date
		Date systemDBDate = null;
		try {
			log.debug(this.getClass().getName()
					+ "-[getOpenMovementsTotal]-Entry");
			// This block creates a hashtable for EntityPosLvl with key as
			// PosLvlId and PosLvlName as Value
			posLvlhashTable = new Hashtable();
			// get the entity postion level object
			entPosTO = SwtUtil.getSwtMaintenanceCache()
					.getEntityPositionLevelObject(entityId);
			// get the collection of the entity position level
			entPosLvlNamesColl = (ArrayList) entPosTO.getPositionLevels();
			// iterate the entity position level
			itrPosLvl = entPosLvlNamesColl.iterator();
			// Instantiate the currency hash table
			currHashTable = new Hashtable();
			// Instantiate the currency list
			currencyList = new ArrayList();
			// Instantiate the openMovement list
			openMovementList = new ArrayList();
			// Instantiate OpenMovementVO instance
			openMovementVO = new OpenMovementVO();
			while (itrPosLvl.hasNext()) {
				// get the position level name
				PositionLevelIdName posLvlIdName = (PositionLevelIdName) itrPosLvl
						.next();
				// add the postion level id and name
				posLvlhashTable.put(posLvlIdName.getPosLevelId(), posLvlIdName
						.getPosLevelName());
				// assign the highest postion level
				if (highestPosLvl < (posLvlIdName.getPosLevelId()).intValue()) {
					highestPosLvl = (posLvlIdName.getPosLevelId()).intValue();
				}
			}
			// get the system date.
			// System date calcuated based on entity offset,modfied by vivek on
			// 13-Jul-2012 for Mantis 1991
			systemDBDate = SwtUtil.getSysParamDateWithEntityOffset(entityId);
			if (selectedTabIndex.equals("1")) {
				// get the date representing the calendar time value with given
				// 0 dateIncrement and systemDBDate
				systemDate = SwtUtil.getDBSysDatewithoutTime(0, systemDBDate);
				dateAsString = SwtUtil.getDBSysDatewithoutTimeAsString(0,
						systemDBDate);
				isAll = SwtConstants.NO;
			} else if (selectedTabIndex.equals("2")) {
				// get the date representing the calendar time value with given
				// 1 dateIncrement and systemDBDate
				systemDate = SwtUtil.getDBSysDatewithoutTime(1, systemDBDate);
				dateAsString = SwtUtil.getDBSysDatewithoutTimeAsString(1,
						systemDBDate);
				isAll = SwtConstants.NO;
			} else if (selectedTabIndex.equals("3")) {
				// get the date representing the calendar time value with given
				// 2 dateIncrement and systemDBDate
				systemDate = SwtUtil.getDBSysDatewithoutTime(2, systemDBDate);
				dateAsString = SwtUtil.getDBSysDatewithoutTimeAsString(2,
						systemDBDate);
				isAll = SwtConstants.NO;
			}
			// Get the date, dateAsString and set the 'All' tab flag
			else if (selectedTabIndex.equals("4")) {
				// get the date representing the calendar time value with given
				// 3 dateIncrement and systemDBDate
				systemDate = SwtUtil.getDBSysDatewithoutTime(3, systemDBDate);
				dateAsString = SwtUtil.getDBSysDatewithoutTimeAsString(3,
						systemDBDate);
				isAll = SwtConstants.NO;
			} else if (selectedTabIndex.equals("5")) {
				// get the date representing the calendar time value with given
				// 4 dateIncrement and systemDBDate
				systemDate = SwtUtil.getDBSysDatewithoutTime(4, systemDBDate);
				dateAsString = SwtUtil.getDBSysDatewithoutTimeAsString(4,
						systemDBDate);
				isAll = SwtConstants.NO;
			} else if (selectedTabIndex.equals("6")) {
				// get the date representing the calendar time value with given
				// 5 dateIncrement and systemDBDate
				systemDate = SwtUtil.getDBSysDatewithoutTime(5, systemDBDate);
				dateAsString = SwtUtil.getDBSysDatewithoutTimeAsString(5,
						systemDBDate);
				isAll = SwtConstants.NO;
			} else if (selectedTabIndex.equals("7")) {
				// get the date representing the calendar time value with given
				// 6 dateIncrement and systemDBDate
				systemDate = SwtUtil.getDBSysDatewithoutTime(6, systemDBDate);
				dateAsString = SwtUtil.getDBSysDatewithoutTimeAsString(6,
						systemDBDate);
				isAll = SwtConstants.NO;
			} else if (selectedTabIndex.equals("8")) {
				// get the date representing the calendar time value with given
				// 0 dateIncrement and systemDBDate
				systemDate = SwtUtil.getDBSysDatewithoutTime(0, systemDBDate);
				dateAsString = SwtConstants.ALL_TAB;
				isAll = SwtConstants.YES;
			} else if (selectedTabIndex.equals("9")) {
				// get the parsed date
				systemDate = SwtUtil.parseDate(searchDateAsString, sysformat
						.getDateFormatValue());
				dateAsString = searchDateAsString;
				isAll = SwtConstants.NO;
			} else {
				// get the parsed date
				systemDate = SwtUtil.parseDate(searchDateAsString, sysformat
						.getDateFormatValue());
				dateAsString = searchDateAsString;
				isAll = SwtConstants.NO;
			}
			entPosLvlColl = movementDAO.getOpenMovementsCountdate(hostId,
					entityId, roleId, currencyGrpId, systemDate, highestPosLvl,
					isAll, applyCurrencyThreshold);

			// To get the array Collection and store the tabflag variable
			tabFlag = (String) entPosLvlColl.get(0);
			// To set the bean object for tabflag
			openMovementVO.setTabFlag(tabFlag);
			// get the collection for position level name
			posLvlNamesColl = (Collection<EntityPositionLevel>) entPosLvlColl
					.get(1);
			// Instantiate the position level name list
			posLvlNamesList = new ArrayList();
			// hashSet contains the distinct Currency Values
			currhashSet = new HashSet();
			// Iterate the position level name
			itrPosLvlList = posLvlNamesColl.iterator();
			// get the list for position level name
			posLvlNamesList = getEntityPosLvlList(itrPosLvlList, currhashSet,
					currHashTable, posLvlhashTable, dateAsString);
			// Start the Currency loop
			hashSetItr = currhashSet.iterator();
			while (hashSetItr.hasNext()) {
				// get the currency code
				currencyCode = (String) hashSetItr.next();
				// Instantiate the OpenMovementSummary
				openMovementSummary = new OpenMovementSummary();
				// set the CurrencyCode
				openMovementSummary.setCurrencyCode(currencyCode);
				// set the CurrencyName
				openMovementSummary.setCurrencyName((String) currHashTable
						.get(currencyCode));
				// Iterate the entity position level
				entPosLvlListItr = posLvlNamesList.iterator();
				// get the position level name by currency list
				posLvlNamesByCurrList = getEntityPosLvlListByCurr(
						entPosLvlListItr, entityId, currencyCode, highestPosLvl);
				// set the position level name
				openMovementSummary.setPosLvlNames(posLvlNamesByCurrList);
				// add the OpenMovementSummary
				openMovementList.add(openMovementSummary);
			} // End of the currency loop
			// set the openMovementList
			openMovementVO.setOpenMovListDetailsToday(openMovementList);
			log.debug(this.getClass().getName()
					+ "-[getOpenMovementsTotal]-Exit");
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementManagerImpl.'getOpenMovementsTotal' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getOpenMovementsTotal", MovementManagerImpl.class);
		} finally {
			// nullify objects
			posLvlhashTable = null;
			entPosTO = null;
			entPosLvlNamesColl = null;
			itrPosLvl = null;
			highestPosLvl = 0;
			currHashTable = null;
			currencyList = null;
			openMovementList = null;
			systemDate = null;
			dateAsString = null;
			isAll = null;
			entPosLvlColl = null;
			posLvlNamesColl = null;
			posLvlNamesList = null;
			currhashSet = null;
			itrPosLvlList = null;
			hashSetItr = null;
			tabFlag = null;
			currencyCode = null;
			openMovementSummary = null;
			entPosLvlListItr = null;
			posLvlNamesByCurrList = null;
			systemDBDate = null;
		}
		return openMovementVO;
	}

	/**
	 * @desc : This method returns the Collection of EntityPositionLevel Objects
	 *       by Iterating through the itr after setting the CurrencyName and
	 *       PositionLevelName Attribute for each Obj
	 * @param itr -
	 *            Iterator
	 * @param currhashSet -
	 *            HashSet
	 * @param currhashTable -
	 *            Hashtable
	 * @param posLvlhashTable -
	 *            Hashtable
	 * @param dateAsString -
	 *            date
	 * @return - ArrayList of EntityPositionLevel Objects
	 */
	private ArrayList getEntityPosLvlList(Iterator itr, HashSet currhashSet,
			Hashtable currhashTable, Hashtable posLvlhashTable,
			String dateAsString) {
		log.debug("Entering getEntityPosLvlList method");
		ArrayList entityPosLvlList = new ArrayList();
		EntityPositionLevel entPosLvl = null;

		while (itr.hasNext()) {
			entPosLvl = new EntityPositionLevel();
			entPosLvl = (EntityPositionLevel) itr.next();
			entPosLvl.setPositionLevelName((String) posLvlhashTable
					.get(entPosLvl.getId().getPositionLevel()));

			entPosLvl.setDate(dateAsString);
			// The EntityPositionLevel obj with all the necessary properties set
			// are added to posLvlNamesTodayPlusOneList ArrayList
			entityPosLvlList.add(entPosLvl);

			// This is will get the distinct currency Code
			currhashSet.add(entPosLvl.getCurrencyCode());

			currhashTable.put(entPosLvl.getCurrencyCode(), entPosLvl
					.getCurrencyName());
		}

		log.debug("Exiting getEntityPosLvlList method");
		return entityPosLvlList;
	} // End of getEntityPosLvlList Method

	/**
	 * @desc : This method returns the Collection of EntityPositionLevel Objects
	 *       for the currencyCode by Iterating through the itr
	 * @param itr -
	 *            Iterator
	 * @param entityId -
	 *            EntityId
	 * @param currencyCode -
	 *            currencyCode
	 * @param highestPosLvl -
	 *            highest Position Level defined for the entity
	 * @return
	 */
	private ArrayList getEntityPosLvlListByCurr(Iterator itr, String entityId,
			String currencyCode, int highestPosLvl) {
		log.debug("Entering getEntityPosLvlListByCurr Method");
		ArrayList entityPosLvlList = new ArrayList();
		final String TOTAL_COLUMN = "Total";
		long totalCount = 0;
		EntityPositionLevel entPosLvlTotal = new EntityPositionLevel();
		EntityPositionLevel entPosLvl = null;

		while (itr.hasNext()) {
			entPosLvl = new EntityPositionLevel();
			entPosLvl = (EntityPositionLevel) itr.next();

			if (entPosLvl.getCurrencyCode().equals(currencyCode)) {
				totalCount += entPosLvl.getMovementTotal().longValue();

				if (highestPosLvl == (entPosLvl.getId().getPositionLevel())
						.intValue()) {
					entPosLvlTotal.getId().setEntityId(entityId);
					entPosLvlTotal.getId().setPositionLevel(
							new Integer(highestPosLvl));
					entPosLvlTotal.setPositionLevelName(TOTAL_COLUMN);
					entPosLvlTotal.setCurrencyName(entPosLvl.getCurrencyName());
					entPosLvlTotal.setCurrencyCode(entPosLvl.getCurrencyCode());
					entPosLvlTotal.setDate(entPosLvl.getDate());

					if (totalCount > 0) {
						entPosLvlTotal
								.setMovementTotalAsString("" + totalCount);
					} else {
						entPosLvlTotal.setMovementTotalAsString("");
					}

					entityPosLvlList.add(0, entPosLvlTotal);
				}

				entityPosLvlList.add(entPosLvl);
			}
		}

		log.debug("Exiting getEntityPosLvlListByCurr Method");
		return entityPosLvlList;
	} // End of getEntityPosLvlListByCurr Method

	/**
	 * @desc This method locks all the Movements which will be ultimately
	 *       involved in the Match/Suspend/Confirm operations or displays an
	 *       appropriate message if movement is already locked by another user.
	 *       This method is called from in Movement Summary screen
	 * @param hostId -
	 *            HostId
	 * @param entityId -
	 *            entityId
	 * @param selectedMovList -
	 *            string containing all the selected Movements' Id
	 * @param userId -
	 *            UserId
	 * @return boolean - return false if any of the involved Movements is
	 *         already Locked by another user else returns true
	 * @throws SwtException -
	 *             SwtException
	 */
	public boolean checkMvmtLockStatus(String hostId, String entityId,
			String selectedMovList, String userId) throws SwtException {
		log.debug("entering checkMvmtLockStatus method ");
		Long movementId = null;
		boolean isLocked = true;

		try {
			ArrayList movcoll = (ArrayList) movementDAO.getAllMovement(hostId,
					entityId, selectedMovList);

			Iterator itr = movcoll.iterator();
			Movement mvmnt = new Movement();
			ArrayList usersList = new ArrayList();
			MovementLockDAO movementLockDAOObj = (MovementLockDAO) SwtUtil
					.getBean("movementLockDAO");

			while (itr.hasNext()) {
				mvmnt = (Movement) itr.next();
				movementId = mvmnt.getId().getMovementId();

				String updateUser = movementLockDAOObj.getLockUpdateUser(
						hostId, movementId);
				usersList.add(updateUser);

				if ((updateUser != null)
						&& (!(updateUser.equalsIgnoreCase(userId)))) {
					return false;
				}
			}

			itr = movcoll.iterator();

			int i = 0;
			String updateUser = "";

			while (itr.hasNext()) {
				mvmnt = (Movement) itr.next();
				movementId = mvmnt.getId().getMovementId();

				if (usersList.get(i) != null) {
				} else {
					MovementLock movementLockObj = new MovementLock();
					movementLockObj.getId().setMovementId(movementId);
					movementLockObj.getId().setHostId(hostId);
					movementLockObj.setUpdateUser(userId);
					movementLockObj.setUpdateDate(SwtUtil
							.getSystemDatewithTime());
					movementLockObj.getId().setEntityId(entityId);

					isLocked = movementLockDAOObj.lockMovement(movementLockObj);

				}

				i++;
			}
		} catch (Exception exp) {
			log
					.debug("Exception Catch in MovementManagerImpl.'checkMvmtLockStatus' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"checkMvmtLockStatus", MovementManagerImpl.class);
		}

		log.debug("exiting checkMvmtLockStatus method ");
		return isLocked;
	}

	/**
	 * This method returns the collection of messages attached with the movemetn
	 * with given movementId.
	 * 
	 * @param movementId
	 * @param sysformat
	 * @return
	 * @throws SwtException
	 */
	public Collection getMovMessageColl(String movementId,
			SystemFormats sysformat) throws SwtException {
		log.debug("Entering 'getMovMessageColl()' method");
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
		Collection movMessageColl = movementDAO.getMovMessageColl(movementId);

		if ((movMessageColl != null) && (movMessageColl.size() > 0)) {
			Iterator itrMessageColl = movMessageColl.iterator();

			while (itrMessageColl.hasNext()) {
				MovementMessage movMessage = (MovementMessage) itrMessageColl
						.next();
				movMessage.setInputDateAsString(SwtUtil.formatDate(movMessage
						.getInputDate(), sysformat.getDateFormatValue()));
				movMessage.setInputTime(sdf.format(movMessage.getInputDate()));

			}
		}

		log.debug("Exiting 'getMovMessageColl()' method");
		return movMessageColl;
	}

	public Match getMatchDetails(String hostId, String entityId, Long matchId)
			throws SwtException {
		log.debug("Entering getMatchDetails method");
		Match match = movementDAO.getMatch(hostId, entityId, matchId);

		return match;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.service.MovementManager#getMovementsForMatch(java.lang.String,
	 *      java.lang.String, java.lang.Long)
	 */
	public Collection getMovementsForMatch(String hostId, String entityId,
			Long matchId) throws SwtException {
		log.debug("Entering getMovementsForMatch method");
		Collection movementsColl = movementDAO.getMovementsForMatch(hostId,
				entityId, matchId);

		return movementsColl;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.service.MovementManager#saveMatchId(org.swallow.work.model.Match)
	 */
	public Long saveAndUpdateRolledMatch(Match newMatch, Match oldMatch)
			throws SwtException {
		log.debug("Entering saveMatchId method");
		try {
			movementDAO.saveRolledMatch(newMatch);

			Long newMatchId = null;

			if (newMatch.getId().getMatchId() != null) {
				newMatchId = newMatch.getId().getMatchId();
			}

			MatchNote matchNote = new MatchNote();

			matchNote.getId().setHostId(newMatch.getId().getHostId());
			matchNote.getId().setEntityId(newMatch.getId().getEntityId());
			matchNote.getId().setMatchId(newMatch.getId().getMatchId());
			matchNote.getId().setUpdateDate(SwtUtil.getSystemDatewithTime());

			String textFromPropFile = new String("");
			StringTokenizer st = null;

			String oldMatchId = oldMatch.getId().getMatchId().toString();

			String noteText = new String("");
			List arraylist = new ArrayList();

			textFromPropFile = SwtUtil.getMessage("matchRolledNoteText", null);
			st = new StringTokenizer(textFromPropFile, "?");

			while (st.hasMoreTokens())
				arraylist.add(st.nextToken());

			noteText = (String) arraylist.get(0) + oldMatchId;

			matchNote.setNoteText(noteText);

			movementDAO.saveMatchNotes(matchNote);

			matchNote = new MatchNote();
			matchNote.getId().setHostId(newMatch.getId().getHostId());
			matchNote.getId().setEntityId(newMatch.getId().getEntityId());
			matchNote.getId().setMatchId(oldMatch.getId().getMatchId());
			matchNote.getId().setUpdateDate(SwtUtil.getSystemDatewithTime());

			textFromPropFile = SwtUtil.getMessage("matchOriginalNoteText", null);
			st = new StringTokenizer(textFromPropFile, "?");

			arraylist = new ArrayList();

			while (st.hasMoreTokens())
				arraylist.add(st.nextToken());

			noteText = (String) arraylist.get(0) + newMatchId;

			matchNote.setNoteText(noteText);

			movementDAO.saveMatchNotes(matchNote);

			movementDAO.updateMatch(oldMatch);

			return newMatchId;
		} catch (Exception exp) {
			log
					.debug("Exception Catch in MovementManagerImpl.'saveAndUpdateRolledMatch' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"saveAndUpdateRolledMatch", MovementManagerImpl.class);
		}
	}

	/**
	 * 
	 * @param hostId
	 * @param entityId
	 * @param dateFormat
	 * @param movementList
	 * @throws SwtException
	 */
	private void setMvmntsForMvmntSummaryDisplay(String hostId,
			String entityId, SystemFormats formats, ArrayList movementList)
			throws SwtException {
		Movement movement = new Movement();

		Iterator itr = movementList.iterator();

		while (itr.hasNext()) {
			movement = (Movement) itr.next();
			Collection collMiscParams = (Collection) CacheManager.getInstance()
					.getMiscParams("MATCHSTATUS", entityId);
			Iterator itrMiscParams = collMiscParams.iterator();

			while (itrMiscParams.hasNext()) {
				MiscParams mp = (MiscParams) (itrMiscParams.next());

				if ((movement.getMatchStatus() != null)
						|| (movement.getMatchStatus() != SwtConstants.EMPTY_STRING)) {
					if (movement.getMatchStatus().equals(mp.getId().getKey2())) {
						movement.setMatchStatusDesc(mp.getParValue());
					}
				}
			}
			String dateFormat = SwtUtil
					.getCurrentDateFormat(UserThreadLocalHolder
							.getUserSession());
			movement.setValueDateAsString(SwtUtil.formatDate(movement
					.getValueDate(), dateFormat));
			movement.setAmountAsString(SwtUtil.formatCurrency(movement
					.getCurrencyCode(), movement.getAmount()));

			SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");

			if (movement.getInputDate() != null) {
				movement.setInputDateAsString(SwtUtil.formatDate(movement
						.getInputDate(), dateFormat)
						+ " " + sdf.format(movement.getInputDate()));
			}

			if ((movement.getNotesCount()).intValue() == 0) {
				movement.setHasNotes(SwtConstants.EMPTY_STRING);
			} else {
				movement.setHasNotes((movement.getNotesCount()).intValue()
						+ SwtConstants.EMPTY_STRING);
			}

			// For this Movement set the Postion Level Name as defined in
			// EntityPositionLevel
			movement.setPositionLevelName(SwtUtil.getSwtMaintenanceCache()
					.getEntityPositionLevelName(entityId,
							movement.getPositionLevel()));
		}
	} // End of setMvmntsForMvmntSummaryDisplay() method

	/**
	 * This method gets movement summary details as well as total no of
	 * movements
	 * 
	 * @param hostId
	 * @param entityId
	 * @param currCode
	 * @param accountId
	 * @param valueDate
	 * @param accountType
	 * @param balType
	 * @param posLevelId
	 * @param currGrpId
	 * @param roleId
	 * @param pageSize
	 * @param currentPage
	 * @param isAllStr
	 * @param totalCount
	 * @param acctMonitorMvmntList
	 * @param filterSortStatus
	 * @param sourceScreen
	 * @param dataFetchIndicator
	 * @param applyCurrencyThreshold
	 * @return int
	 * @throws SwtException
	 */
	// Start:Code Modified By ASBalaji for mantis 2032 on 15-08-2012
	public HashMap<String, Object> getMonitorMovements(String hostId, String entityId,
			String currCode, String accountId, Date valueDate,
			String accountType, String balType, Integer posLevelId,
			String currGrpId, String roleId, int pageSize, int currentPage,
			String isAllStr, String openMovementFlag,
			List<Movement> acctMonitorMvmntList, String filterSortStatus,
			String sourceScreen, String dataFetchIndicator,
			String applyCurrencyThreshold, String userId, String profileId, String...scenarioId) throws SwtException {
		// Filter and sort criteria
		String[] filterSort = null;
		// variable to hold filter criteria
		String filterCriteria = null;
		// variabale to hold sort criteria
		String sortCriteria = null;
		// Position level
		int posLvlId = 0;
		// To store "All" parameters
		StringBuffer sbAllParams = null;
		// varible to hold valueAll
		String valueAll = null;
		
		HashMap<String, Object> totalMap = null;

		try {
			log.debug(this.getClass().getName()
					+ " - [ getMonitorMovements ] - Entry ");
			// Set balance type short form, based on the value, because stored
			// procedures uses short form
			if (balType.equals(SwtConstants.ACCT_MONITOR_BALTYPE_PREDICTED)) {
				balType = "P";
			} else if (balType
					.equals(SwtConstants.ACCT_MONITOR_BALTYPE_UNSETTLED)) {
				balType = "S";
			} else if (balType
					.equals(SwtConstants.ACCT_MONITOR_BALTYPE_UNEXPECTED)) {
				balType = "U";
			} else if (balType
					.equals(SwtConstants.ACCT_MONITOR_BALTYPE_EXTERNAL)) {
				balType = "E";
			} else if (balType.equals(SwtConstants.ACCT_MONITOR_BALTYPE_LORO)) {
				balType = "L";
			} else if (balType
					.equals(SwtConstants.ACCT_MONITOR_BALTYPE_OPEN_UNEXPECTED)) {
				balType = "O";
			}

			// Set source(parent) screen short form, based on the value, because
			// stored procedures uses short form
			if ((sourceScreen != null)
					&& sourceScreen.equals(SwtConstants.ACCOUNT_MONITOR)) {
				// Stored procedure accepts 'A' for AccountMonitor
				sourceScreen = "A";
			} else if (sourceScreen.equals(SwtConstants.CURRENCY_MONITOR)) {
				// Stored procedure accepts 'C' for Currency Monitor
				sourceScreen = "C";
				balType = "P";
			} else if (sourceScreen.equals(SwtConstants.BOOK_MONITOR)) {
				// Stored procedure accepts 'K' for Account Book Monitor
				sourceScreen = "K";
				balType = "P";
			} else if (sourceScreen
					.equals(SwtConstants.ACCOUNT_BREAKDOWN_MONITOR)
					|| sourceScreen.equals(SwtConstants.CENTRAL_BANK_MONITOR)) {
				sourceScreen = "B";
			} else if (sourceScreen.equals("E")) {
				// Set position level
				posLvlId = posLevelId.intValue();
				// If account type not found, make it "All"
				if (SwtUtil.isEmptyOrNull(accountType)) {
					// Initialize string buffer to add parameters
					sbAllParams = new StringBuffer();
					for (int paramCount = 0; paramCount < 25; paramCount++) {
						sbAllParams.append("All|");
					}
					accountType = sbAllParams.toString();
				}
			} else if (sourceScreen.equals("W")) {
				// Position level
				posLvlId = posLevelId.intValue();
				// in case of workflowmonitor, roleId is passed as a parameter
				// at no.15
				accountId = roleId;
				// in case of workflowmonitor, roleId is passed as a parameter
				// at no.16
				accountType = currGrpId+"\n#\n"+accountType;
			}else if (sourceScreen.equals("X")){
				// in case of workflowmonitor, roleId is passed as a parameter
				// at no.15
				accountId = roleId;
				// in case of workflowmonitor, roleId is passed as a parameter
				// at no.16
				accountType = currGrpId+"\n#\n"+accountType;
			}
			// backend requires empty value of open movement flag if it is
			// null.The open movement flag will be null if MSD opened without
			// 'filter' settings.
			if (openMovementFlag == null) {
				openMovementFlag = "";
			}
			// Get filter and sort criteria
			filterSort = filterSortStatus.split(",");
			filterCriteria = filterSort[0].toString();
			sortCriteria = filterSort[1].toString();
			String selectedScenario  = null;
			if(scenarioId!=null)
				if(scenarioId.length>0)
					{
						selectedScenario = scenarioId[0];
						if(!SwtUtil.isEmptyOrNull(selectedScenario))
							balType = "X";
					}

			// Get no of movements
			totalMap = movementDAO.getMonitorMovements(hostId, entityId,
					currCode, accountId, valueDate, accountType, balType,
					posLvlId, pageSize, currentPage, isAllStr, filterCriteria,
					openMovementFlag, sortCriteria, sourceScreen,
					(ArrayList) acctMonitorMvmntList, dataFetchIndicator,
					applyCurrencyThreshold, userId, profileId, selectedScenario, roleId);
			
			return totalMap;
		} catch (SwtException ex) {
			if(!ex.getMessage().contains("generatedException")){
				// log error message
				log.error(this.getClass().getName()
						+ " - [getMonitorMovements] - SwtException - "
						+ ex.getMessage());
			}
			// Re-throw an exception
			throw ex;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					+ " - [getMonitorMovements] - Exception - "
					+ ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex,
					"getMonitorMovements", MovementManagerImpl.class);
		} finally {
			// nullify object(s)
			filterSort = null;
			filterCriteria = null;
			sortCriteria = null;
			sbAllParams = null;
			valueAll = null;
			// log debug message
			log.debug(this.getClass().getName()
					+ " - [ getMonitorMovements ] - Exit ");
		}

	}

	// End:Code Modified By ASBalaji for mantis 2032 on 15-08-2012

	/**
	 * This method is used to frame the filter parameter string.
	 * 
	 * @param matchStatus
	 * @param accountClass
	 * @param amountOver
	 * @param amountUnder
	 * @param fromDate
	 * @param toDate
	 * @param currencyCode
	 * @param msgType
	 * @param counterPartyId
	 * @param beneficiaryId
	 * @param custodianId
	 * @param bookCode
	 * @param group
	 * @param metaGroup
	 * @param inputDate
	 * @param timeFrom
	 * @param timeTo
	 * @param accountId
	 * @param reference
	 * @param posLevel
	 * @param sign
	 * @param predictStatus
	 * @param movType
	 * @param matchingParty
	 * @param productType
	 * @param postingDateFrom
	 * @param postingDateTo
	 * @param openFlag
	 * @param extBalStatus
	 * @return String
	 */
	public String concatenateSearchParams(String matchStatus,
			String accountClass, Double amountOver, Double amountUnder,
			String fromDate, String toDate, String currencyCode,
			String msgType, String counterPartyId,
			String beneficiaryId, String custodianId, String bookCode,
			String group, String metaGroup, String inputDate, String timeFrom,
			String timeTo, String accountId, String reference,
			Integer posLevel, String sign, String predictStatus,
			String movType, String matchingParty, String productType,
			String postingDateFrom, String postingDateTo, String openFlag,
			String extBalStatus, String extraFilter, String uetr) throws SwtException {
		/* Local variable declaration. */
		/* Variable Declaration for paramString. */
		StringBuffer paramString = null;
		/* Variable Declaration for amountOverAsString. */
		String amountOverAsString = null;
		/* Variable Declaration for amountUnderAsString. */
		String amountUnderAsString = null;
		/* Variable Declaration for valueDateFromAsString. */
		String valueDateFromAsString = null;
		/* Variable Declaration for valueDateToAsString. */
		String valueDateToAsString = null;
		/* Variable Declaration for inputDateAsString. */
		String inputDateAsString = null;
		/* Variable Declaration for postingFromDateAsString. */
		String postingFromDateAsString = null;
		/* Variable Declaration for postingToDateAsString. */
		String postingToDateAsString = null;
		/* Variable Declaration for posLevelAsString. */
		String posLevelAsString = null;
		try {
			log.debug("Entering into the concatenateSearchParams()");

			paramString = new StringBuffer("");

			// Appending movementStatus to paramString
			if ((matchStatus != null) && matchStatus.equalsIgnoreCase("D")) {
				matchStatus = "AM"; // Stored procedure accepts "AM" for 'All

				// Matched' option
			} else if (matchStatus.equalsIgnoreCase("X")
					|| matchStatus.equals("")) {
				matchStatus = "All"; // Stored procedure accepts 'All' for
				// 'All'

				// option
			}

			if (accountClass.equals("")) {
				accountClass = "All";
			}

			amountOverAsString = "";

			if ((amountOver == null) || amountOver.equals(Double.valueOf(0.0))) {
				amountOverAsString = "All";
			} else {
				amountOverAsString = amountOver.toString();
			}

			amountUnderAsString = "";

			if ((amountUnder == null)
					|| amountUnder.equals(Double.valueOf(0.0))) {
				amountUnderAsString = "All";
			} else {
				amountUnderAsString = amountUnder.toString();
			}

			valueDateFromAsString = "";

			if ((fromDate != null) && !fromDate.equalsIgnoreCase("")) {
				valueDateFromAsString = fromDate;
			} else {
				valueDateFromAsString = "All";
			}

			valueDateToAsString = "";

			if ((toDate != null) && !toDate.equalsIgnoreCase("")) {
				valueDateToAsString = toDate;
			} else {
				valueDateToAsString = "All";
			}

			if ((currencyCode == null) || currencyCode.equalsIgnoreCase("")) {
				currencyCode = "All";
			}

			if ((msgType == null) || msgType.equalsIgnoreCase("")) {
				msgType = "All";
			}

			if ((counterPartyId == null) || counterPartyId.equalsIgnoreCase("")) {
				counterPartyId = "All";
			}

			if ((beneficiaryId == null) || beneficiaryId.equalsIgnoreCase("")) {
				beneficiaryId = "All";
			}

			if ((custodianId == null) || custodianId.equalsIgnoreCase("")) {
				custodianId = "All";
			}

			if ((bookCode == null) || bookCode.equalsIgnoreCase("")) {
				bookCode = "All";
			}

			if ((group == null) || group.equalsIgnoreCase("")) {
				group = "All";
			}

			if ((metaGroup == null) || metaGroup.equalsIgnoreCase("")) {
				metaGroup = "All";
			}

			inputDateAsString = "";

			if ((inputDate != null) && !inputDate.equalsIgnoreCase("")) {
				inputDateAsString = inputDate;
			} else {
				inputDateAsString = "All";
			}

			if ((timeFrom == null) || timeFrom.equalsIgnoreCase("")) {
				timeFrom = "All";
			}

			if ((timeTo == null) || timeTo.equalsIgnoreCase("")) {
				timeTo = "All";
			}

			if ((accountId == null) || accountId.equalsIgnoreCase("")) {
				accountId = "All";
			}

			if ((matchingParty == null) || matchingParty.equalsIgnoreCase("")) {
				matchingParty = "All";
			}

			if ((productType == null) || productType.equalsIgnoreCase("")) {
				productType = "All";
			}

			postingFromDateAsString = "";

			if ((postingDateFrom != null)
					&& !postingDateFrom.equalsIgnoreCase("")) {
				postingFromDateAsString = postingDateFrom;
			} else {
				postingFromDateAsString = "All";
			}

			postingToDateAsString = "";

			if ((postingDateTo != null) && !postingDateTo.equalsIgnoreCase("")) {
				postingToDateAsString = postingDateTo;
			} else {
				postingToDateAsString = "All";
			}

			posLevelAsString = "";

			if (posLevel.equals(Integer.valueOf(0))) {
				posLevelAsString = "All";
			} else {
				posLevelAsString = posLevel.toString();
			}

			if (sign.equals("B") || sign.equals("")) {
				sign = "All";
			}

			if (predictStatus.equals("A") || predictStatus.equals("")) {
				predictStatus = "All";
			}
			/* check whether extBalStatus is Null or empty or 'A', set 'All' */
			if (SwtUtil.isEmptyOrNull(extBalStatus) || extBalStatus.equals("A")) {
				extBalStatus = "All";
			}

			if (movType.equals("B") || movType.equals("")) {
				movType = "All";
			}

			if (openFlag != null && openFlag.equalsIgnoreCase("O")) {
				openFlag = "Y";
			} else if (openFlag != null && openFlag.equalsIgnoreCase("A")) {
				openFlag = "All";
			}
			// If MSD screen refreshed based on filtered data-through filter
			// button
			// click,openflag set to ALL.
			else {
				openFlag = "All";
			}
			if ((uetr == null) || uetr.equalsIgnoreCase("")) {
				uetr = "All";
			}
			paramString = paramString.append(matchStatus + "|");
			paramString = paramString.append(accountClass + "|");
			paramString = paramString.append(amountOverAsString + "|");
			paramString = paramString.append(amountUnderAsString + "|");
			paramString = paramString.append(valueDateFromAsString + "|");
			paramString = paramString.append(valueDateToAsString + "|");
			paramString = paramString.append(currencyCode + "|");
			paramString = paramString.append(msgType + "|");
			paramString = paramString.append(counterPartyId + "|");
			paramString = paramString.append(beneficiaryId + "|");
			paramString = paramString.append(custodianId + "|");
			paramString = paramString.append(bookCode + "|");
			paramString = paramString.append(group + "|");
			paramString = paramString.append(metaGroup + "|");
			paramString = paramString.append(inputDateAsString + "|");
			paramString = paramString.append(timeFrom + "|");
			paramString = paramString.append(timeTo + "|");
			paramString = paramString.append(accountId + "|");
			paramString = paramString.append(reference + "|");
			paramString = paramString.append(posLevelAsString + "|");
			paramString = paramString.append(sign + "|");
			paramString = paramString.append(movType + "|");
			paramString = paramString.append(predictStatus + "|");
			paramString = paramString.append(matchingParty + "|");
			paramString = paramString.append(productType + "|");
			paramString = paramString.append(postingFromDateAsString + "|");
			paramString = paramString.append(postingToDateAsString + "|");
			paramString = paramString.append(openFlag + "|");
			/* append the extBalStatus in filter string. */
			paramString = paramString.append(extBalStatus + "|");
			if(!SwtUtil.isEmptyOrNull(extraFilter)) {
			paramString = paramString.append(extraFilter + "|");
			}else {
				paramString = paramString.append("All" + "|");//additional ALL to make uetr in position 31
			}
			paramString = paramString.append(uetr + "|");

			log.debug("Exiting the concatenateSearchParams()");
		} catch (Exception exp) {
			log
					.error(this.getClass().getName()
							+ " - Exception Catched in [concatenateSearchParams] method : - "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"concatenateSearchParams", MovementManagerImpl.class);
		} finally {
			/* null the objects created already. */
			amountOverAsString = null;
			amountUnderAsString = null;
			valueDateFromAsString = null;
			valueDateToAsString = null;
			inputDateAsString = null;
			postingFromDateAsString = null;
			postingToDateAsString = null;
			posLevelAsString = null;
		}
		return paramString.toString();
	}

	public String getValueDateField(String hostId, String entityId,
			Long movementId) throws SwtException {
		log.debug("Entering and Exiting into getValueDateField");

		return movementDAO.getValueDateField(hostId, entityId, movementId);
	}

	public void updateOpenUnopenFlag(String hostId, String entityId,
			Long movementId, String openFlag, String updateUser)
			throws SwtException {
		log.debug("Entering into updateOpenUnopenFlag");

		movementDAO.updateOpenUnopenFlag(hostId, entityId, movementId,
				openFlag, updateUser);

		log.debug("Exiting into updateOpenUnopenFlag");
	}

	public String getPositionLevelInternalExternal(String hostId,
			String entityId, int positionLevel) throws SwtException {
		log.debug("Entering and Exiting into getPositionLevelInternalExternal");

		return movementDAO.getPositionLevelInternalExternal(hostId, entityId,
				positionLevel);
	}

	public Hashtable getEditFlagDetails(String hostId, String entityId,
			String inputSource) throws SwtException {
		log.debug("Entering into setEditFlagDetails");

		Hashtable editableData = new Hashtable();

		try {
			EntityDAO entDAO = (EntityDAO) SwtUtil.getBean("entityDAO");

			Collection editDetailsColl = entDAO.getEditableDataDetails(hostId,
					entityId);

			if ((editDetailsColl != null) && (editDetailsColl.size() > 0)) {
				Iterator itrEdit = editDetailsColl.iterator();

				while (itrEdit.hasNext()) {
					EditableData eData = (EditableData) (itrEdit.next());
					
					if ((eData.getEditable() != null) && eData.getEditable().equals("2")) {
						editableData.put(eData.getId().getMovementField(), 1);
					} else if (inputSource != null) {
						if ((eData.getEditable() != null)
								&& eData.getEditable().equals("1")
								&& (inputSource
										.equals(SwtConstants.MOVEMENT_SOURCE_DEFAULT) || inputSource
										.equals(SwtConstants.MOVEMENT_SOURCE_PREADVICE))) {
							editableData.put(eData.getId().getMovementField(), 1);
						}else {
							editableData.put(eData.getId().getMovementField(), 0);
						}
					}else {
						editableData.put(eData.getId().getMovementField(), 0);
					}
				}
			}
			return editableData;
		} catch (Exception exp) {
			log
					.error("Exception Catch in MovementManagerImpl.'getEditFlagDetails' method : "
							+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getEditFlagDetails", MovementManagerImpl.class);
		}
	}

	private Match populateMatchSRCDetails(Match match,
			ArrayList movementObjectList) {
		MatchManager matchManager = (MatchManager) SwtUtil
				.getBean("matchManager");
		HashMap matchSRCDetails = matchManager
				.getMatchSRCDetsils(movementObjectList);
		match.setMaxAmount((Double) matchSRCDetails.get("maxAmount"));
		match.setMaxValueDate((Date) matchSRCDetails.get("highestValueDate"));
		match.setLowestPosLev(((Integer) (matchSRCDetails
				.get("lowestPositionLevel"))).intValue());
		match.setHighestPosLev(((Integer) (matchSRCDetails
				.get("highestPositionLevel"))).intValue());
		match.setPredictStatusFlag(matchSRCDetails.get("predictStatusFlag")
				.toString());
		return match;
	}

	/**
	 * @param movementIds
	 * @param entityId
	 * @param hostId
	 * @return
	 */
	public boolean checkMovementStatus(String movementIds, String entityId,
			String hostId) throws SwtException {
		return movementDAO.checkMovementStatus(movementIds, entityId, hostId);
	}

	/**
	 * Method to check external position level to change the external balance.
	 * 
	 * @param entity
	 * @return boolean
	 */
	public boolean checkExternalPositionLevel(Entity entity)
			throws SwtException {
		// flag to ensure external balance presence
		boolean posFlag = false;
		try {
			posFlag = movementDAO.checkExternalPositionLevel(entity);
		} catch (SwtException exp) {
			log.error("Error in " + this.getClass().getName()
					+ " - [checkExternalPositionLevel] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getPositionLevelInternalExternal", this.getClass());
		} catch (Exception exp) {
			log.error("Error in " + this.getClass().getName()
					+ " - [checkExternalPositionLevel] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getPositionLevelInternalExternal", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " -[checkExternalPositionLevel]-Exit");
		}
		// return flag value
		return posFlag;
	}// End of checkExternalPositionLevel

	/**
	 * This Method is used to return the Cross Reference Object List for
	 * corresponding Movement
	 * 
	 * @param host
	 * @param entity
	 * @param movement
	 * @throws SwtException
	 */
	public ArrayList<CrossReference> getCrossReference(String host,
			String entity, Long movement) throws SwtException {
		try {
			log
					.debug(this.getClass().getName()
							+ " -[getCrossReference]-Enter");
			return movementDAO.getCrossReference(host, entity, movement);
		} catch (Exception exp) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getCrossReference] - " + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getCrossReference", this.getClass());
		}
	}

	/**
	 * Method to get External Balance for selected entity
	 * 
	 * @param entity
	 * @return ExternalBalance
	 */
	public String getEntityDefaultPositionlevel(Entity entity)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " -[getEntityDefaultPositionlevel]-Enter");
			return movementDAO.getEntityDefaultPositionlevel(entity);
		} catch (Exception exp) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getEntityDefaultPositionlevel] - "
					+ exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"getEntityDefaultPositionlevel", this.getClass());
		}
	}

	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method used to get the archived movements from the archive schema
	 * 
	 * @param entityId
	 * @param movementId
	 * @param archiveId
	 * @param systemInfo
	 * @param sysformat
	 * @return Movement
	 * @throws SwtException
	 */
	public Movement getArchiveMovementDetails(String entityId, Long movementId,
			String archiveId, SystemInfo systemInfo, SystemFormats sysformat)
			throws SwtException {
		// Declared to hold the Movement
		Movement movement = null;
		// Collection declared to hold the Movement details
		Collection matchStatusColl = null;
		// This collection to hold the notes
		Collection collNotes = null;
		// declared to hold the NotesDAO object
		NotesDAO notesDAO = null;
		// declared to iterate match values
		Iterator matchStatusItr = null;
		// declared to hold the MiscParams object
		MiscParams miscParams = null;
		try {
			log.debug(this.getClass().getName()
					+ " -[getArchiveMovementDetails]-Enter");
			// get details from DAO
			movement = movementDAO.getArchiveMovementDetails(entityId,
					movementId, archiveId);
			// get Notes detail from DAO
			notesDAO = (NotesDAO) SwtUtil.getBean("notesDAO");
			collNotes = notesDAO.getArchiveNoteDetails(entityId, movementId,
					archiveId);
			if (movement != null) {
				// get the match status collection
				if (SwtUtil.isEmptyOrNull(movement.getId().getEntityId())) {
					matchStatusColl = (Collection) CacheManager.getInstance()
							.getMiscParams("MATCHSTATUS", null);
				} else {
					matchStatusColl = (Collection) CacheManager.getInstance()
							.getMiscParams("MATCHSTATUS",
									movement.getId().getEntityId());
				}
				// compare the match status to their key
				matchStatusItr = matchStatusColl.iterator();
				while (matchStatusItr.hasNext()) {
					miscParams = (MiscParams) (matchStatusItr.next());
					if ((movement.getMatchStatus() != null)
							&& !(movement.getMatchStatus().equals(""))) {
						if (movement.getMatchStatus().equals(
								miscParams.getId().getKey2())) {
							movement.setMatchStatusDesc(miscParams
									.getParValue());
						}
					}
					// set notes available
					if (collNotes.size() > 0) {
						movement.setHasNotes("Y");
					} else {
						movement.setHasNotes("N");
					}

				}
				// setting position level,value date,Amount String, update
				// Date,Posting Date
				if (movement.getPositionLevel() != null) {
					movement.setPositionLevelAsString(movement
							.getPositionLevel().toString());
				} else {
					movement.setPositionLevelAsString("");
				}
				movement.setValueDateAsString(SwtUtil.formatDate(movement
						.getValueDate(), sysformat.getDateFormatValue()));

				movement.setAmountAsString(SwtUtil.formatCurrency(movement
						.getCurrencyCode(), movement.getAmount()));
				movement.setUpdateDateAsString(SwtUtil.formatDate(movement
						.getUpdateDate(), sysformat.getDateFormatValue()));
				movement.setPostingDateAsString(SwtUtil.formatDate(movement
						.getPostingDate(), sysformat.getDateFormatValue()));
			}
		} catch (Exception e) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getArchiveMovementDetails] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getArchiveMovementDetails", this.getClass());
		} finally {
			// nullify
			matchStatusColl = null;
			log.debug(this.getClass().getName()
					+ " -[getArchiveMovementDetails]-Exit");
		}
		return movement;
	}

	/**
	 * This method used to get the archived movements from the archive schema
	 * 
	 * @param entityId
	 * @param movementId
	 * @param archiveId
	 * @return Movement
	 * @throws SwtException
	 */
	public Movement getArchiveMovementDetails(String entityId, Long movementId,
			String archiveId) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					+ " -[getArchiveMovementDetails]-Enter");
			// get values from DAO
			return movementDAO.getArchiveMovementDetails(entityId, movementId,
					archiveId);
		} catch (Exception e) {
			log.error("Error in " + this.getClass().getName()
					+ " - [getArchiveMovementDetails] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"getArchiveMovementDetails", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " -[getArchiveMovementDetails]-Exit");
		}
	}
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data Archive
	 * setup: Remove redundant fields from Archive setup screen
	 */

	/**
	 * This method used to set the original counter party_id if the counter party_id is changed
	 * 
	 * @param hostId
	 * @param entityId
	 * @param movementId
	 * @param originalCounterPartyId
	 * @throws SwtException
	 */
	public void updateOriginalValues(Movement newMovement, Movement oldMovement) throws SwtException {

		try {
			log.debug(this.getClass().getName()
					+ " -[updateOriginalCounterPartyId]-Enter");
			movementDAO.updateOriginalValues(newMovement, oldMovement);
		} catch (Exception e) {
			log.error("Error in " + this.getClass().getName()
					+ " - [updateOriginalCounterPartyId] - " + e.getMessage());
			throw SwtErrorHandler.getInstance().handleException(e,
					"updateOriginalCounterPartyId", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					+ " -[updateOriginalCounterPartyId]-Exit");
		}
	}


public void saveProfileAdditionalCols(String hostId, String userId, String profileId, List<MsdAdditionalColumns> listMsd) throws SwtException {

	try {
		log.debug(this.getClass().getName()
				+ " -[saveProfileAdditionalCols]-Enter");
		movementDAO.saveProfileAdditionalCols(hostId, userId, profileId, listMsd);
	} catch (Exception e) {
		log.error("Error in " + this.getClass().getName()
				+ " - [saveProfileAdditionalCols] - " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e,
				"saveProfileAdditionalCols", this.getClass());
	} finally {
		log.debug(this.getClass().getName()
				+ " -[saveProfileAdditionalCols]-Exit");
	}
}

public List getSavedProfiles(String hostId, String userId) throws SwtException {
	try {
		log.debug(this.getClass().getName() + " -[getSavedProfiles]-Enter");
		return movementDAO.getSavedProfiles(hostId, userId);
	} catch (Exception e) {
		log.error("Error in " + this.getClass().getName() + " - [getSavedProfiles] - " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e, "getSavedProfiles", this.getClass());
	} finally {
		log.debug(this.getClass().getName() + " -[getSavedProfiles]-Exit");
	}
}


public Collection<MsdAdditionalColumns> getAdditionalsColsList(String hostId, String profileId, String userId) throws SwtException{
	try {
		log.debug(this.getClass().getName() + " -[getAdditionalsColsList]-Enter");
		return movementDAO.getAdditionalsColsList(hostId, profileId, userId);
	} catch (Exception e) {
		log.error("Error in " + this.getClass().getName() + " - [getAdditionalsColsList] - " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e, "getAdditionalsColsList", this.getClass());
	} finally {
		log.debug(this.getClass().getName() + " -[getAdditionalsColsList]-Exit");
	}
}

public void deleteProfileData(String hostId, String profileId, String userId) throws SwtException{
	try {
		log.debug(this.getClass().getName() + " -[deleteProfileData]-Enter");
		movementDAO.deleteProfileData(hostId, profileId, userId);
	} catch (Exception e) {
		log.error("Error in " + this.getClass().getName() + " - [deleteProfileData] - " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e, "deleteProfileData", this.getClass());
	} finally {
		log.debug(this.getClass().getName() + " -[deleteProfileData]-Exit");
	}	
}


public void crudAddColsMapping(List<MsdAdditionalColumns> listColAdd, List<MsdAdditionalColumns> listColUpdate,
		List<MsdAdditionalColumns> listColDelete) throws SwtException  {
	try {
		if (!listColAdd.isEmpty() || !listColUpdate.isEmpty() || !listColDelete.isEmpty()) {
			movementDAO.crudAddColsMapping(listColAdd, listColUpdate, listColDelete);
		}
	} catch (SwtException e) {
		log.error(this.getClass().getName() + "- [crudAddColsMapping] - Exception " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e, "crudAddColsMapping", this.getClass());
	}
}


public List getTableColumns(String table) throws SwtException {
	try {
			return movementDAO.getTableColumns(table);
	} catch (SwtException e) {
		log.error(this.getClass().getName() + "- [getTableColumns] - Exception " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e, "getTableColumns", this.getClass());
	}
}

public List getTableNames() throws SwtException {
	try {
		return movementDAO.getTableNames();
	} catch (SwtException e) {
		log.error(this.getClass().getName() + "- [getTableColumns] - Exception " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e, "getTableColumns", this.getClass());
	}
}


public Collection<MsdDisplayColumns> getMsdDisplayColsList() throws SwtException {
	try {
		return movementDAO.getMsdDisplayColsList();
	} catch (SwtException e) {
		log.error(this.getClass().getName() + "- [getMsdDisplayColsList] - Exception " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e, "getMsdDisplayColsList", this.getClass());
	}
}

public HashMap<String, String> getMsdDisplayColumnsLbl(String tableName) throws SwtException {
	try {
		return movementDAO.getMsdDisplayColumnsLbl(tableName);
	} catch (SwtException e) {
		log.error(this.getClass().getName() + "- [getMsdDisplayColumnsLbl] - Exception " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e, "getMsdDisplayColumnsLbl", this.getClass());
	}
}

public 	ArrayList<String> getEntityPosLevelList(String entityId) throws SwtException {
	try {
		return movementDAO.getEntityPosLevelList(entityId);
	} catch (Exception e) {
		e.printStackTrace();
		log
				.error("Exception occurred in MovementManagerImpl.getEntityPosLevelList()");

		throw SwtErrorHandler.getInstance().handleException(e,
				"getEntityPosLevelList", MovementManagerImpl.class);
	}
}

	public ArrayList<String> getSourceList(Date startDate, Date endDate) throws SwtException {
		try {
			return movementDAO.getSourceList(startDate, endDate);
		} catch (Exception e) {
			log.error("Error getting source list", e);
			throw new SwtException("Error getting source list", e);
		}
	}

	public ArrayList<String> getFormatList(Date startDate, Date endDate) throws SwtException {
		try {
			return movementDAO.getFormatList(startDate, endDate);
		} catch (Exception e) {
			log.error("Error getting format list", e);
			throw new SwtException("Error getting format list", e);
		}
	}


/**
 * Method to delete the screen info
 * 
 * @throws SwtException Collection<ScreenInfo>
 */
@SuppressWarnings("unchecked")
public void deleteMsdScreenInfo(List<ScreenInfo> listScreenInfo) throws SwtException {

	try {
		log.debug(this.getClass().getName() + " -[deleteMsdScreenInfo]-Enter");
		movementDAO.deleteMsdScreenInfo(listScreenInfo);
	} catch (Exception e) {
		log.error("Error in " + this.getClass().getName() + " - [deleteMsdScreenInfo] - " + e.getMessage());
		throw SwtErrorHandler.getInstance().handleException(e, "deleteMsdScreenInfo", this.getClass());
	} finally {
		log.debug(this.getClass().getName() + " -[deleteMsdScreenInfo]-Exit");
	}
}

}
// End of MovementManagerImpl class
