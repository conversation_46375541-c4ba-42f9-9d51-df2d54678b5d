<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.work.model.CentralBankMonitor"
		table="P_CB_MONITOR_WHATIF">
		<composite-id
			class="org.swallow.work.model.CentralBankMonitor$Id" name="id"
			unsaved-value="any">
			<key-property name="entityId" access="field"
				column="ENTITY_ID" />
			<key-property name="userId" access="field"
			column="USER_ID" />
			<key-property name="valueDate" access="field"
				column="VALUE_DATE" />
		</composite-id>
		<property name="amount" column="AMOUNT" not-null="false" />
		<property name="updateDate" column="UPDATE_DATE" not-null="false" />
		<property name="updateUser" column="UPDATE_USER" not-null="false" />
	</class>
</hibernate-mapping>
