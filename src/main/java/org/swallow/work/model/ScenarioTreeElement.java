package org.swallow.work.model;

import java.io.Serializable;

public class ScenarioTreeElement implements Serializable {
	
	private String categoryName=null;
	private int categoryOrder=0 ;
	private String scenarioId=null ; 
	private String categoryDescription  = null;
	private int scenarioOrder=0;
	private String scenarioName =null; 
	private String scenarioDescription =null;
	private int count=0;
	private String cutOffTime=null;
	private String isAlertable =null;
	/**
	 * @return the categoryName
	 */
	public String getCategoryName() {
		return categoryName;
	}
	/**
	 * @param categoryName the categoryName to set
	 */
	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}
	/**
	 * @return the categoryOrder
	 */
	public int getCategoryOrder() {
		return categoryOrder;
	}
	/**
	 * @param categoryOrder the categoryOrder to set
	 */
	public void setCategoryOrder(int categoryOrder) {
		this.categoryOrder = categoryOrder;
	}
	/**
	 * @return the scenarioId
	 */
	public String getScenarioId() {
		return scenarioId;
	}
	/**
	 * @param scenarioId the scenarioId to set
	 */
	public void setScenarioId(String scenarioId) {
		this.scenarioId = scenarioId;
	}
	/**
	 * @return the scenarioOrder
	 */
	public int getScenarioOrder() {
		return scenarioOrder;
	}
	/**
	 * @param scenarioOrder the scenarioOrder to set
	 */
	public void setScenarioOrder(int scenarioOrder) {
		this.scenarioOrder = scenarioOrder;
	}
	/**
	 * @return the scenarioName
	 */
	public String getScenarioName() {
		return scenarioName;
	}
	/**
	 * @param scenarioName the scenarioName to set
	 */
	public void setScenarioName(String scenarioName) {
		this.scenarioName = scenarioName;
	}
	/**
	 * @return the count
	 */
	public int getCount() {
		return count;
	}
	/**
	 * @param count the count to set
	 */
	public void setCount(int count) {
		this.count = count;
	}

	/**
	 * @return the cutOffTime
	 */
	public String getCutOffTime() {
		return cutOffTime;
	}
	/**
	 * @param cutOffTime the cutOffTime to set
	 */
	public void setCutOffTime(String cutOffTime) {
		this.cutOffTime = cutOffTime;
	}
	/**
	 * @return the scenarioDescription
	 */
	public String getScenarioDescription() {
		return scenarioDescription;
	}
	/**
	 * @param scenarioDescription the scenarioDescription to set
	 */
	public void setScenarioDescription(String scenarioDescription) {
		this.scenarioDescription = scenarioDescription;
	}
	/**
	 * @return the isAlertable
	 */
	public String getIsAlertable() {
		return isAlertable;
	}
	/**
	 * @param isAlertable the isAlertable to set
	 */
	public void setIsAlertable(String isAlertable) {
		this.isAlertable = isAlertable;
	}
	/**
	 * @return the categoryDescription
	 */
	public String getCategoryDescription() {
		return categoryDescription;
	}
	/**
	 * @param categoryDescription the categoryDescription to set
	 */
	public void setCategoryDescription(String categoryDescription) {
		this.categoryDescription = categoryDescription;
	}

	
}
