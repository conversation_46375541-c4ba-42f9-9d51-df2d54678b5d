<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="org.swallow.work.model.PrevMatch" table="P_PREV_MATCH">				
		<composite-id name="id" class="org.swallow.work.model.PrevMatch$Id" unsaved-value="any">
			<key-property name="entityId" access="field" column="ENTITY_ID" />        
        	<key-property name="movementId" access="field" column="MOVEMENT_ID"/>	   
		    <key-property name="prevMatchedWith" access="field" column="PREV_MATCHED_WITH"/>		   		   		   
		 </composite-id>		
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	

    </class>
</hibernate-mapping>
