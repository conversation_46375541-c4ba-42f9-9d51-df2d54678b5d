/*
 * Created on Feb 4, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;
import java.util.Date;
import org.swallow.model.BaseObject;
import org.swallow.util.SwtUtil;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class MatchNote extends BaseObject{
	
	
	private String noteText;
	private String compressedNoteText;
	private String updateUser;
	
	private Id id = new Id();
	public static class Id extends BaseObject{
		private String hostId;
		private String entityId;
		private Long matchId;		
		private Date  updateDate =new Date();
		private String  updateDateIso =new String();
		private String updateDateAsString;
		private String updateTimeAsString;
		
		
		/**
		 * 
		 */
		public Id() {
			super();
			// TODO Auto-generated constructor stub
		}
		
		
		
		
		
		
		/**
		 * @param hostId
		 * @param entityId
		 * @param matchId
		 * @param updateDate
		 * @param updateDateAsString
		 * @param updateTimeAsString
		 */
		public Id(String hostId, String entityId, Long matchId,
				Date updateDate, String updateDateAsString,
				String updateTimeAsString) {
			super();
			this.hostId = hostId;
			this.entityId = entityId;
			this.matchId = matchId;
			this.updateDate = updateDate;
			this.updateDateAsString = updateDateAsString;
			this.updateTimeAsString = updateTimeAsString;
		}
		
		
		
		
		/**
		 * @return Returns the entityId.
		 */
		public String getEntityId() {
			return entityId;
		}
		/**
		 * @param entityId The entityId to set.
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}
		/**
		 * @return Returns the hostId.
		 */
		public String getHostId() {
			return hostId;
		}
		/**
		 * @param hostId The hostId to set.
		 */
		public void setHostId(String hostId) {
			this.hostId = hostId;
		}
		/**
		 * @return Returns the matchId.
		 */
		public Long getMatchId() {
			return matchId;
		}
		/**
		 * @param movementId The matchId to set.
		 */
		public void setMatchId(Long matchId) {
			this.matchId = matchId;
		}
		/**
		 * @return Returns the updateDate.
		 */
		public Date getUpdateDate() {
			return updateDate;
		}
		/**
		 * @param updateDate The updateDate to set.
		 */
		public void setUpdateDate(Date updateDate) {
			this.updateDate = updateDate;
		}
		/**
		 * @return Returns the updateDateAsString.
		 */
		public String getUpdateDateAsString() {
			return updateDateAsString;
		}
		/**
		 * @param updateDateAsString The updateDateAsString to set.
		 */
		public void setUpdateDateAsString(String updateDateAsString) {
			this.updateDateAsString = updateDateAsString;
		}
		/**
		 * @return Returns the updateTimeAsString.
		 */
		public String getUpdateTimeAsString() {
			return updateTimeAsString;
		}
		/**
		 * @param updateTimeAsString The updateTimeAsString to set.
		 */
		public void setUpdateTimeAsString(String updateTimeAsString) {
			this.updateTimeAsString = updateTimeAsString;
		}






		public String getUpdateDateIso() {
			return SwtUtil.formatDate(updateDate, "yyyy-MM-dd HH:mm:ss");
		}




		public void setUpdateDateIso(String updateDateIso) {
			this.updateDateIso = updateDateIso;
		}
	}
	
	
	/**
	 * 
	 */
	public MatchNote() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	
	
	
	
	/**
	 * @param noteText
	 * @param compressedNoteText
	 * @param updateUser
	 * @param id
	 */
	public MatchNote(String noteText, String compressedNoteText,
			String updateUser, Id id) {
		super();
		this.noteText = noteText;
		this.compressedNoteText = compressedNoteText;
		this.updateUser = updateUser;
		this.id = id;
	}
	/**
	 * @return Returns the id.
	 */
	public Id getId() {
		return id;
	}
	/**
	 * @param id The id to set.
	 */
	public void setId(Id id) {
		this.id = id;
	}
	/**
	 * @return Returns the noteText.
	 */
	public String getNoteText() {
		return noteText;
	}
	/**
	 * @param noteText The noteText to set.
	 */
	public void setNoteText(String noteText) {
		this.noteText = noteText;
	}
	/**
	 * @return Returns the updateUser.
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	/**
	 * @param updateUser The updateUser to set.
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	/**
	 * @return Returns the compressedNoteText.
	 */
	public String getCompressedNoteText() {
		return compressedNoteText;
	}
	/**
	 * @param compressedNoteText The compressedNoteText to set.
	 */
	public void setCompressedNoteText(String compressedNoteText) {
		this.compressedNoteText = compressedNoteText;
	}
}



