<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.Sweep" table="P_SWEEP">
    
<!-- 	<id name="id" access="field" type="org.swallow.work.model.SweepIdType"> -->
<!-- 	   <column name="HOST_ID" not-null="true" sql-type="hostId"/> -->
<!-- 	   <column name="SWEEP_ID" not-null="true" sql-type="sweepId"/> -->
<!-- 	   <generator class="org.swallow.work.model.SweepSequenceGenerator"> -->
<!-- 	    <param name="sequencetable">P_SEQUENCE</param> -->
<!-- 	   </generator> -->
<!-- 	  </id> -->
	      	       	 <composite-id name="id" class="org.swallow.work.model.Sweep$Id" unsaved-value="any">
	   <key-property name="hostId" access="field" column="HOST_ID"/>
	   <key-property name="sweepId" access="field" column="SWEEP_ID"/>
	   </composite-id>
      
       <property name="sweepGroupId" column="SWEEP_GROUP_ID" not-null="false"/>	
       <property name="currencyCode" column="CURRENCY_CODE" not-null="false"/>	
        <property name="movementIdCr" column="MOVEMENT_ID_CR" not-null="false"/>
	    <property name="movementIdDr" column="MOVEMENT_ID_DR" not-null="false"/>	
	    <property name="originalSweepAmt" column="ORIGINAL_SWEEP_AMT" not-null="false"/>
	    <property name="submitSweepAmt" column="SUBMIT_SWEEP_AMT" not-null="false"/>
        <property name="authorizeSweepAmt" column="AUTHORIZE_SWEEP_AMOUNT" not-null="false"/>	
        <property name="sweepType" column="SWEEP_TYPE" not-null="false"/>
	    <property name="sweepStatus" column="SWEEP_STATUS" not-null="false"/>	
	    <property name="inputDateTime" column="INPUT_DATE_TIME" not-null="false"/>
	    <property name="authDateTime" column="AUTHORIZED_DATE_TIME" not-null="false"/>
	    <property name="inputUser" column="INPUT_USER" not-null="false"/>
	    <property name="submitUser" column="SUBMIT_USER" not-null="false"/>	
	    <property name="authorizedUser" column="AUTHORIZED_USER" not-null="false"/>
	    <property name="cancelUser" column="CANCEL_USER" not-null="false"/>
	    <property name="accountIdCr" column="ACCOUNT_ID_CR" not-null="true"/>	
	    <property name="accountIdDr" column="ACCOUNT_ID_DR" not-null="true"/>
	    <property name="alignAccountId" column="ALIGN_ACCOUNT_ID" not-null="true"/>
	    <property name="valueDate" column="VALUE_DATE" not-null="false"/>
        <property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
	    <property name="updateUser" column="UPDATE_USER" not-null="false"/>
	    <property name="submitDateTime" column="SUBMIT_DATE_TIME" not-null="false"/>
	    <property name="cancelDateTime" column="CANCEL_DATE_TIME" not-null="false"/>
	    <property name="entityIdCr" column="ENTITY_ID_CR" not-null="false"/>
	    <property name="entityIdDr" column="ENTITY_ID_DR" not-null="false"/>
	    <property name="settleMethodCR" column="SETTL_METHOD_CR" not-null="false"/>
	    <property name="settleMethodDR" column="SETTL_METHOD_DR" not-null="false"/>
	    <property name="bookCodeCR" column="BOOKCODE_CR" not-null="false"/>
	    <property name="bookCodeDR" column="BOOKCODE_DR" not-null="false"/>
	    <property name="additionalReference" column="ADDITIONAL_REFERENCE" not-null="false"/>
   	    <property name="targetBalance" column="TARGET_BALANCE" not-null="false"/>
   	    <property name="targetBalanceType" column="TARGET_BALANCE_TYPE" not-null="false"/>
   	    <property name="minAmount" column="MIN_AMOUNT" not-null="false"/>
   	     <property name="accountIdOrigin" column="ACCOUNT_ID_ORIGIN" not-null="false"/>
   	     <property name="entityIdOrigin" column="ENTITY_ID_ORIGIN" not-null="false"/>
   	     <property name="sweepFromBalanceTypeCr" column="SWEEP_FROM_BALANCE_TYPE_CR" not-null="false"/>
   	     <property name="sweepFromBalanceTypeDr" column="SWEEP_FROM_BALANCE_TYPE_DR" not-null="false"/>
   	     <property name="targetBalanceTypId" column="TARGET_BALANCE_TYPE_ID" not-null="false"/>

<property name="sweepUetr1" column="SWEEP_UETR1" not-null="false"/>
         <property name="sweepUetr2" column="SWEEP_UETR2" not-null="false"/>
<!-- 	    <many-to-one -->
<!-- 				name="entityCr" -->
<!-- 				update="false" -->
<!-- 				insert="false" -->
<!-- 				cascade="none" -->
<!-- 				class="org.swallow.maintenance.model.Entity" -->
<!-- 				not-null="true" -->
<!-- 				outer-join="true" -->
<!-- 				foreign-key="FK_P_SWEEP_S_ENTITY"> -->
<!-- 		<column name="HOST_ID"/> -->
<!-- 		<column name="ENTITY_ID"/> -->
<!-- 	   </many-to-one>	 -->
	   
<!-- 	   	    <many-to-one -->
<!-- 				name="entityDr" -->
<!-- 				update="false" -->
<!-- 				insert="false" -->
<!-- 				cascade="none" -->
<!-- 				class="org.swallow.maintenance.model.Entity" -->
<!-- 				not-null="true" -->
<!-- 				outer-join="true" -->
<!-- 				foreign-key="FK_P_SWEEP_S_ENTITY"> -->
<!-- 		<column name="HOST_ID"/> -->
<!-- 		<column name="ENTITY_ID"/> -->
<!-- 	   </many-to-one>	 -->
	   
	    <many-to-one lazy="false"
				name="accountCr"
				update="false"
				insert="false"
				cascade="none"
				class="org.swallow.maintenance.model.AcctMaintenance"
				not-null="true"
				outer-join="true"
				foreign-key="FK_P_SWEEP_P_ACCOUNT_CR">
		<column name="HOST_ID"/>
		<column name="ENTITY_ID_CR"/>
		<column name="ACCOUNT_ID_CR"/>
	   </many-to-one>	
	   
	   <many-to-one lazy="false"
				name="accountDr"
				update="false"
				insert="false"
				cascade="none"
				class="org.swallow.maintenance.model.AcctMaintenance"
				not-null="true"
				outer-join="true"
				foreign-key="FK_P_SWEEP_P_ACCOUNT_DR">
	   <column name="HOST_ID"/>
	   <column name="ENTITY_ID_DR"/>
	   <column name="ACCOUNT_ID_DR"/>
	   </many-to-one>	
	        
<!-- 	    <many-to-one -->
<!-- 				name="currency" -->
<!-- 				update="false" -->
<!-- 				insert="false" -->
<!-- 				cascade="none" -->
<!-- 				class="org.swallow.maintenance.model.Currency" -->
<!-- 				not-null="true" -->
<!-- 				outer-join="true" -->
<!-- 				foreign-key="FK_P_SWEEP_S_CURRENCY"> -->
<!-- 	   <column name="HOST_ID"/> -->
<!-- 	   <column name="ENTITY_ID"/> -->
<!-- 	   <column name="CURRENCY_CODE"/> -->
<!-- 	   </many-to-one>	      -->
	        
	</class>
</hibernate-mapping>
