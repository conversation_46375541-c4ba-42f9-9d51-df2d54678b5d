package org.swallow.work.model;

import java.math.BigDecimal;
import java.util.Date;

import org.swallow.model.BaseObject;

/**
 * CorporateAccount.java
 * 
 * This java bean has getters and setters for CentralBankMonitor details
 * 
 * <AUTHOR> A
 * @date July 12, 2010
 */
public class CorporateAccount extends BaseObject {

	/** Default version id */
	private static final long serialVersionUID = 1L;
	/* Start Code:corporate sequence number added and removed primary keys for mantis 1256 by sami on 28-Sep-2010 */ 
	private Long corporateSeqNo;
	//Amount
	private BigDecimal amount = null;
	// Amount as String
	private String amountAsString = null;
	// Update Date
	private Date updateDate;
	//Update User
	private String updateUser = null;	
	// Corporate Name
	private String corporateName = null;	
	// Entity Id
	private String entityId = null;
	// Value Date
	private Date valueDate;		
		

		/**
		 * Method to get the Value Date
		 * @return Date
		 */
		public Date getValueDate() {
			return valueDate;
		}

		/**
		 * Method to set the value date
		 * @param valueDate
		 */
		public void setValueDate(Date valueDate) {
			this.valueDate = valueDate;
		}

		

		/** Method returns the entityId
		 * @return the entityId
		 */
		public String getEntityId() {
			return entityId;
		}

		/** Method to set the entityId
		 * @param entityId the entityId to set
		 */
		public void setEntityId(String entityId) {
			this.entityId = entityId;
		}	


	/**
	 * Method to get the amount
	 * @return BigDecimal
	 */
	public BigDecimal getAmount() {
		return amount;
	}

	/**
	 * Method to set the amount for the corporate name and value date
	 * @param amount
	 */
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	
	/**
	 * Method to get the update user
	 * @return String
	 */
	public String getUpdateUser() {
		return updateUser;
	}
	
	/**
	 * Method to set the update user
	 * @param updateUser
	 */
	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/** Method to get the update date
	 * @return the updateDate
	 */
	public Date getUpdateDate() {
		return updateDate;
	}

	/** Method to set the update date 
	 * @param updateDate the updateDate to set
	 */
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	/** Method to get the amountAsString
	 * @return the amountAsString
	 */
	public String getAmountAsString() {
		return amountAsString;
	}

	/** Method to set the amountAsString 
	 * @param amountAsString the amountAsString to set
	 */
	public void setAmountAsString(String amountAsString) {
		this.amountAsString = amountAsString;
	}
	/**
	 * Method returns the corporateName
	 * @return String
	 */
	public String getCorporateName() {
		return corporateName;
	}

	/**
	 * Method to set the corporate name
	 * @param corporateName
	 */
	public void setCorporateName(String corporateName) {
		this.corporateName = corporateName;
	}
	
	/**
	 * Method returns the corporateSeqNo
	 * @return Long
	 */

	public Long getCorporateSeqNo() {
		return corporateSeqNo;
	}
	
	/**
	 * Method to set   corporateSeqNo
	 * @return void
	 */

	public void setCorporateSeqNo(Long corporateSeqNo) {
		this.corporateSeqNo = corporateSeqNo;		
	}
	/* End Code:corporate sequence number added and removed primary keys for mantis 1256 by sami on 28-Sep-2010 */	
}
