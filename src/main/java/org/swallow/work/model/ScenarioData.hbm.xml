<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="org.swallow.work.model.ScenarioData"
		table="P_FCAST_SCENARIODATA">
		<composite-id class="org.swallow.work.model.ScenarioData$Id" name="id" unsaved-value="any">
			<key-property name="hostId" access="field"
				column="HOST_ID" />
			<key-property name="entityId" access="field"
				column="ENTITY_ID" />
			<key-property name="userId" access="field"
				column="USER_ID" />
			<key-property name="templateId" access="field"
				column="TEMPLATE_ID" />
			<key-property name="currencyCode" access="field"
				 column="CURRENCY_CODE" />
			<key-property name="valueDate" access="field"
				 column="VALUE_DATE" />
		</composite-id>
		<property name="scenariosAmount" column="SCENARIOS_AMT" not-null="true" />
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
	</class>
</hibernate-mapping>
