<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

<!-- Define the sequence -->


    <class name="org.swallow.work.model.Match" table="P_MATCH">
				
<!-- 		<id name="id" access="field" type="org.swallow.work.model.MatchIdType"> -->
<!-- 		   <column name="HOST_ID" not-null="true" sql-type="hostId"/> -->
<!-- 		   <column name="ENTITY_ID" not-null="true" sql-type="entityId"/> -->
<!-- 		   <column name="MATCH_ID" not-null="true" sql-type="matchId"/> -->
<!-- 		   <generator class="org.swallow.work.model.MatchSequenceGenerator"> -->
<!-- 		    <param name="sequencetable">P_SEQUENCE</param> -->
<!-- 		   </generator> -->
<!-- 		 </id> -->
			 	 <composite-id name="id" class="org.swallow.work.model.Match$Id" unsaved-value="any">
		   <key-property name="hostId" access="field" column="HOST_ID"/>
		   <key-property name="entityId" access="field" column="ENTITY_ID" />
		   <key-property name="matchId" access="field" column="MATCH_ID"/>
		</composite-id>
		
	
		<property name="currencyCode" column="CURRENCY_CODE" not-null="false"/>
		<property name="highestPosLev" column="HIGHEST_POSITION_LEVEL" not-null="false"/>
		<!-- START: Code added for mapping new columns on 2/11/2008-->
		<property name="lowestPosLev" column="LOWEST_POSITION_LEVEL" not-null="false"/>	
		<property name="maxAmount" column="MATCH_HI_AMOUNT" not-null="false"/>
		<property name="predictStatusFlag" column="PREDICT_STATUS_INTERNAL_FLAG" not-null="false"/>
		<property name="maxValueDate" column="MATCH_HI_VALUE_DATE" not-null="false"/>
		<!-- END: Code added for mapping new columns on 2/11/2008-->
		<property name="matchQuality" column="MATCH_QUALITY" not-null="false"/>	
		<property name="status" column="STATUS" not-null="false"/>	
		<property name="statusDate" column="STATUS_DATE" not-null="false"/>        
		<property name="confirmedDate" column="CONFIRMED_DATE" not-null="false"/>
		<!-- intquality1 property  added for Mantis 711 8/11/2008-->
		<property name="intQuality1" column="INTERNAL_QUALITY1" not-null="false"/>
		<property name="intQuality2" column="INTERNAL_QUALITY2" not-null="false"/>
		<property name="intQuality3" column="INTERNAL_QUALITY3" not-null="false"/>
		<property name="intQuality4" column="INTERNAL_QUALITY4" not-null="false"/>
		<property name="intQuality5" column="INTERNAL_QUALITY5" not-null="false"/>
		<property name="intQuality6" column="INTERNAL_QUALITY6" not-null="false"/>
		<property name="intQuality7" column="INTERNAL_QUALITY7" not-null="false"/>
		<property name="intQuality8" column="INTERNAL_QUALITY8" not-null="false"/>
		<property name="intQuality9" column="INTERNAL_QUALITY9" not-null="false"/>	
		<property name="origConfirmedDate" column="ORIG_CONFIRMED_DATE" not-null="false"/>
		<property name="statusUser" column="STATUS_USER" not-null="false"/>
		<property name="updateDate" column="UPDATE_DATE" not-null="false"/>	
		<property name="updateUser" column="UPDATE_USER" not-null="false"/>	
		

    </class>
</hibernate-mapping>
