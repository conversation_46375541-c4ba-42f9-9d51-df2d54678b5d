<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
    <class name="org.swallow.work.model.Sequence" table="P_SEQUENCE">
				
		 
		 <composite-id class="org.swallow.work.model.Sequence$Id" name="id" unsaved-value="any">
		 	 	<key-property name="hostId" access="field" column="HOST_ID"/>
		  	 	<key-property name="sequenceName" access="field" column="SEQUENCE_NAME"/>
		  </composite-id>
		
	
		<property name="sequenceValue" column="SEQUENCE_VALUE" not-null="false"/>
      
    </class>
</hibernate-mapping>
