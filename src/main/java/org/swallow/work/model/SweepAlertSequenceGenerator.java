/*
 * Created on Sep 14, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.model;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.HibernateException;
import org.hibernate.MappingException;
import org.hibernate.dialect.Dialect;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.Configurable;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.type.Type;
import org.swallow.util.Batcher;
import org.swallow.util.JDBCExceptionReporter;
import org.swallow.util.PropertiesHelper;


/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class SweepAlertSequenceGenerator implements IdentifierGenerator, Configurable, Serializable{
	
	private final Log log = LogFactory.getLog(MatchSequenceGenerator.class); // Log object 	
	
	public static final String HIBERNATE_SEQUENCETABLE = "P_SEQUENCE";	 // Default value of sequence table
	
    public static final String SCHEMA = "schema"; 						// constant string for schema word
    
    public static final String SEQUENCETABLE = "sequencetable";			// constant string for sequencetable
    
    public static final String SEQUENCEVALUECOLUMN = "sequence_value";	// constant string for sequence_value column in db table

    public static final String SEQUENCENAMECOLUMN = "sequence_name";	// constant string for sequence_name column in db table
    
    public static final String HOSTIDCOLUMN = "host_id";				// constant string for host_id column in db table
    
    public static final String SEQUENCENAMEVALUE = "alert";				// constant string for movement value in sequence_name column in db table
    
    private String _sequenceTableName = null;							//  sequence table name 
    
    private String _hostId = null;										//  host id value
    
    private String _query = null;										//  sql select query
    
    private String _update = null;       								// sql update query
    
    

	/* (non-Javadoc)
	 * @see net.sf.hibernate.id.IdentifierGenerator#generate(net.sf.hibernate.engine.SessionImplementor, java.lang.Object)
	 */
	public Serializable generate(SessionImplementor session, Object obj) 
	throws SQLException, HibernateException { 
		
		log.debug("Inside SweepSequenceGenerator.generateMethod ");
				// Movement Object 
		SweepAlert  sweepAlertObj = null;
		
		// Movement Id Object
		SweepAlert.Id  sweepAlertIdObj = null;
		
		// PreparedStatement for select query
	    PreparedStatement stSelect = Batcher.of(session).prepareStatement(_query);
	    
    	// PreparedStatement for update query		    
	    PreparedStatement stUpdate = Batcher.of(session).prepareStatement(_update);

	    // Resultset for select query result
	    ResultSet rsSelect = null;
		
    	try 
		{	    		
		    String hostId = "";
			 			   
		    if(obj == null || !obj.getClass().equals(SweepAlert.class))
		    {
		    	throw new HibernateException("Model object is null or object type is not org.swallow.work.model.SweepAlert ");
		    }
		    else
		    {
		    	// typecasting the object into Movement class 
		    	sweepAlertObj = (SweepAlert)obj;
		    	
		    	// Getting the Id oject from Match Object
		    	sweepAlertIdObj = sweepAlertObj.getId();
		    	
		    	// Getting the host id from MatchId Object 
		    	hostId = sweepAlertIdObj.getHostId();
		    	log.debug("Host Id - " + hostId);
		    	// Setting the host id into select query
		    	stSelect.setString(1,hostId);

		        // Executing  the sql select query 
		    	rsSelect = stSelect.executeQuery();
		        
		    	long sequenceValue = -1 ;
		    	
		    	if(rsSelect.next())
		    		sequenceValue = rsSelect.getLong(1);
		    	if(sequenceValue == -1)
		    		throw new HibernateException("Could not fecth the sequence number");
		    	
		    	log.debug("Got the next sequence value - " + sequenceValue + " ,  host_id -" + hostId + " , sequence_name - " + SEQUENCENAMEVALUE); 
		    	
		    	// Setting the movementid in movementid object
		    	sweepAlertIdObj.setAlertId(new Long(sequenceValue));
		    	
		    	// incrementing the sequenceValue by one
		    	sequenceValue = sequenceValue + 1;
		    	
		    	// Setting the sequence value into update query
		    	stUpdate.setLong(1,sequenceValue);
		    	
		    	// Setting the hostid into update query			    	
		    	stUpdate.setString(2,hostId);
		    	
		    	// Executing the update query
		    	stUpdate.executeUpdate();
		    	
		    }
		    
		    log.debug("Exit SweepSequenceGenerator.generateMethod ");    
		} 
		catch (SQLException sqle) 
		{ 
		       JDBCExceptionReporter.logExceptions(sqle); 
		       throw sqle; 
		} 
		finally 
		{ 
			if(rsSelect != null && stSelect != null)
				Batcher.of(session).closeQueryStatement(stSelect,rsSelect);
			
			if(stUpdate != null)
				Batcher.of(session).closeStatement(stUpdate);
	}
	    		    	    	        
		return sweepAlertObj;
}

	/* (non-Javadoc)
	 * @see net.sf.hibernate.id.Configurable#configure(net.sf.hibernate.type.Type, java.util.Properties, net.sf.hibernate.dialect.Dialect)
	 */
	public void configure(Type type, Properties params, Dialect dialect) 
	throws MappingException {
		log.debug("Inside SweepSequenceGenerator.configure ");
		 // get the name of the table name defined in xml file 
		 _sequenceTableName = PropertiesHelper.getString(SEQUENCETABLE, params, HIBERNATE_SEQUENCETABLE);
		 
		 //get the schema name
		 String schemaName = params.getProperty(SCHEMA);
		 
		 //appends the schema name in table name if doesn't exits
		 if(schemaName != null && _sequenceTableName.indexOf('.') < 0){
			_sequenceTableName = schemaName + '.' + _sequenceTableName;
		 }
		 
		 // preparing the sql select query
		 _query  = "select " + SEQUENCEVALUECOLUMN + " from  " +  _sequenceTableName + " where " + HOSTIDCOLUMN + "   = ? and  "
			   + SEQUENCENAMECOLUMN + " = '" + SEQUENCENAMEVALUE + "'";
		    
		 // preparing the sql update query    
		 _update = "update  " + _sequenceTableName + " set " + SEQUENCEVALUECOLUMN + " = ? where  " + HOSTIDCOLUMN + "   = ? and  "
						+ SEQUENCENAMECOLUMN + " = '" + SEQUENCENAMEVALUE + "'";

		 log.debug("sql select query - " + _query);
		 log.debug("sql update query - " + _update);
		 log.debug("Exit SweepSequenceGenerator.configure ");
	}

	
	@Override
	public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
		try {
			return generate((SessionImplementor)session, object);
		} catch (Exception e) {
			throw new HibernateException(e);
		}
	}
}
