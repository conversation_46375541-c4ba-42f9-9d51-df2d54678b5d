/*
 * @(#)PreAdviceInputAction.java 1.0
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.web;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;





import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.swallow.config.springMVC.BaseController;
import org.swallow.config.springMVC.RequestObjectMapper;
import org.swallow.control.model.AccountAccess;
import org.swallow.control.model.EntityCurrencyGroupAccess;
import org.swallow.control.model.RoleTO;
import org.swallow.control.service.AccountAccessManager;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.Entity;
import org.swallow.maintenance.model.EntityUserAccess;
import org.swallow.maintenance.model.MiscParams;
import org.swallow.maintenance.model.Party;
import org.swallow.maintenance.service.AcctMaintenanceManager;
import org.swallow.maintenance.service.BookCodeManager;
import org.swallow.maintenance.service.CurrencyManager;
import org.swallow.model.User;
import org.swallow.pcm.maintenance.model.core.ColumnInfo;
import org.swallow.pcm.maintenance.model.core.OptionInfo;
import org.swallow.pcm.maintenance.model.core.PageInfo;
import org.swallow.pcm.maintenance.model.core.SelectInfo;
import org.swallow.pcm.maintenance.web.ResponseHandler;
import org.swallow.util.pcm.PCMConstant;
import org.swallow.util.xml.SwtResponseConstructor;
import org.swallow.util.xml.SwtXMLWriter;
import org.swallow.util.CacheManager;
import org.swallow.util.CommonDataManager;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtUtil;
import org.swallow.util.SystemFormats;
import org.swallow.util.SystemInfo;
import org.swallow.work.dao.MovementDAO;
import org.swallow.work.model.InputAuthoriseSelection;
import org.swallow.work.model.MatchDriver;
import org.swallow.work.model.Movement;
import org.swallow.work.model.MovementNote;
import org.swallow.work.model.ScreenOption;
import org.swallow.work.service.MovementManager;
import org.swallow.work.service.PreAdviceInputManager;
import org.swallow.work.service.ScreenOptionManager;

//import com.ibm.icu.text.DateFormat;
//import com.ibm.icu.text.SimpleDateFormat;
import org.swallow.util.LabelValueBean;
import org.swallow.util.struts.TokenHelper;


/**
 * <pre>
 *
 * This PreAdviceInputAction class is used to perform pre-advice movement related actions
 *  - display movement for given pre-advice movement id.
 * 	- generate the new pre-advice movement.
 * 	- update the old pre-advice movement details.
 * 	- open to display the pre-advice movement message.
 * 	- used to update the pre-advice movement status to open/unopen.
 *  - open to display the Cross Reference for corresponding pre-advice movement.
 *  - used to authorize the pre-advice movement.
 *
 *   Modified by Karthik on 15-Feb-2012
 * </pre>
 *
 */




import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/preadviceinput", "/preadviceinput.do"})
public class PreAdviceInputAction extends BaseController {
	private static final Map<String, String> viewMap = new HashMap<>();
	static {
		viewMap.put("fail", "error");
		viewMap.put("successJsp", "jsp/work/preadvice");
		viewMap.put("search", "jsp/work/preadvice");
		viewMap.put("data", "jsp/data");
		viewMap.put("success", "jsp/work/preAdviceInput");
		viewMap.put("importMvt", "jsp/work/preadviceimportmovement");
		viewMap.put("statechange", "jsp/flexstatechange");
		viewMap.put("inputAuthorise", "jsp/work/inputauthorise");
		viewMap.put("partysearch", "jsp/pc/work/partysearch");
	}

	private String getView(String resultName) {
		return viewMap.getOrDefault(resultName, "error");
	}

	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
	public String execute(@RequestParam(value = "method", required = false) String method,
						  HttpServletRequest request, HttpServletResponse response) throws SwtException {
		method = String.valueOf(method);
		switch (method) {
			case "unspecified":
				return unspecified();
			case "display":
				return display();
			case "delete":
				return delete();
			case "displayPreadvice":
				return displayPreadvice();
			case "search":
				return search();
			case "displayInputAuthorise":
				return displayInputAuthorise();
			case "savePreAdvice":
				return savePreAdvice();
			case "save":
				return save();
			case "updatePreAdvice":
				return updatePreAdvice();
			case "update":
				return update();
			case "updateOpenFlag":
				return updateOpenFlag();
			case "checkMovementId":
				return checkMovementId();
			case "getAccountsList":
				return getAccountsList();
			case "getBookCodeList":
				return getBookCodeList();
			case "getLists":
				return getLists();
			case "getUpdatedAccountList":
				return getUpdatedAccountList();
			case "getMvtType":
				return getMvtType();
			case "saveAll":
				return saveAll();
			case "importMvt":
				return importMvt();
			case "checkPreAdviceInputAccess":
				return checkPreAdviceInputAccess();
			case "displayDataDef":
				return displayDataDef();
			case "saveUserSettings":
				return saveUserSettings();
			case "deleteUserSettings":
				return deleteUserSettings();
			case "partySearch":
				return partySearch();
			case "partySearchDisplay":
				return partySearchDisplay();
			case "partySearchResult":
				return partySearchResult();
		}


		return unspecified();
	}


	private Movement movement;
	public Movement getMovement() {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		movement = RequestObjectMapper.getObjectFromRequest(Movement.class, request);
		return movement;
	}

	public void setMovement(Movement movement) {
		this.movement = movement;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		request.setAttribute("movement", movement);
	}


	/**
	 * Initializing menuItenId object
	 */
	private final String menuItemId = "" + SwtConstants.PREADVICEINPUT_ID;

	/**
	 * Reference Variable of Movement Manager
	 */
	@Autowired
	private PreAdviceInputManager preAdviceInputManager;

	/**
	 * Reference variable of log factory
	 */
	private Log log = LogFactory.getLog(PreAdviceInputAction.class);

	/**
	 * Initializing empty string
	 */
	private final String EMPTYSTRING = "";

	/**
	 * Initializing default search string
	 */
	private final String DEFAULTSEARCHSTRING = "All";

	/**
	 * @param movementManager
	 *            The movementManager to set.
	 */
	public void setPreAdviceInputManager(
			PreAdviceInputManager preAdviceInputManager) {
		this.preAdviceInputManager = preAdviceInputManager;
	}

	/**
	 *
	 * This action method is invoked while opening the pre advice input & input
	 * authorize and based on the request param it will return the action
	 * forward
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String unspecified()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Variable Declaration for token. */
		String token = null;
		try {
			log.debug(this.getClass().getName() + " - [unspecified] - Entry");
			// set notes details in session
			request.getSession().setAttribute("sessionNotesDetails", null);
			// based on the value attribute it will display the input authorise
			// screen or preadvice input
			if ((request.getParameter("value") != null)
				&& request.getParameter("value").equals("A")) {
				return displayInputAuthorise();
			} else {


				/*
				 * Generate token and assign token value in session attribute
				 * for later validation.
				 */
//				token = generateToken(request);
//				if (!SwtUtil.isEmptyOrNull(token)) {
//					request.getSession().setAttribute(
//							Globals.TRANSACTION_TOKEN_KEY, token);
//				}
				token  = TokenHelper.setToken();
				// Note: setToken() already sets both TOKEN_NAME_FIELD and the token value in session

				//return display(mapping, form, request, response);
				return getView("success");
			}
		} catch (SwtException ex) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'unspecified' method : "
						   + ex.getMessage());
			SwtUtil.logException(ex, request, "");
			throw ex;
		} catch (Exception ex) {
			log
					.error("Exception Catch in PreAdviceInputAction.'unspecified' method : "
						   + ex.getMessage());
			SwtUtil
					.logException(SwtErrorHandler.getInstance()
							.handleException(ex, "unspecified",
									PreAdviceInputAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			token = null;
			log.debug(this.getClass().getName() + " - [unspecified] - Exit");
		}
	}

	/**
	 * This method is used to load the pre-advice input screen and it will be
	 * invoked while changing entity on screen to get the corresponding details.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String display()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		/* Local variable declaration */
		// To host for application
		String hostId = null;
		// To hold the current entity from screen
		String entityId = null;
		// To hold selected currency in screen
		String currencyCode = null;
		// To hold selected currency in screen
		String defaultMovType = null;
		// To hold date format
		String dateFormat = null;
		// To hold the default currency for entity
		String defaultCurrency = null;
		// To hold the role for current user
		String roleId = null;
		// To get the form values
		// DynaValidatorForm dynaForm = null;
		// To hold movement details from form
		Movement movement = null;
		// To identify which screen
		String screenIdentifier = null;
		// To identify the user id
		String userId = null;
		// To identify the entity label of the bottom section
		String entity1 = null;
		// To identify the input by value
		String inputBy = null;
		// to check if the entity has been changed
		boolean entityChanged = false;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		Collection<BookCode> collBookCode = null;
		Iterator<BookCode> itrBookCode = null;
		Collection<Movement> listPreAdvice = new ArrayList<Movement>();
		String amountFormat = null;
		SystemFormats sysFormat=null;

		try {
			log.debug(this.getClass().getName() + " - [display()] - Entry");

			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			dateFormat= SwtUtil.getCurrentDateFormat(request.getSession());
			amountFormat=sysFormat.getCurrencyFormat();
			//get the entity label of the bottom section
			entity1=request.getParameter("entityId1");
			//get the input by value of the bottom section
			inputBy=request.getParameter("inputBy");
			// type cast form value to dyna validator form
			// to get the movement details from form
			movement = (Movement) getMovement();
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			// get the entity id for movement
			entityId = movement.getId().getEntityId();
			// get role id to populate the entity collection
			roleId = preAdviceInputManager.getUserDetails(hostId, SwtUtil
					.getCurrentUserId(request.getSession()));
			// Start:code modified by Prasenjit Maji for Mantis 1874 on
			// 21/09/2012: Pre advice Input:Currency drop down should load
			// currencies for default entity if cancel button is pressed
			// set the current user entity if form entity is null or cancel
			// button is pressed on pre advice input screen.
			if (SwtUtil.isEmptyOrNull(entityId)
				|| (!SwtUtil.isEmptyOrNull(request
					.getParameter("cancleStatus")))) {

				entityId = SwtUtil.getUserCurrentEntity(request.getSession());

				movement.getId().setEntityId(entityId);
			}

			entityChanged =  (request.getParameter("entityChanged") != null && request.getParameter("entityChanged").equals("true"));
			// End:code modified by Prasenjit Maji for Mantis 1874 on
			// 21/09/2012: Pre advice Input:Currency drop down should load
			// currencies for default entity if cancel button is pressed
			// set the value date as system date if form's value date is null
			if (SwtUtil.isEmptyOrNull(movement.getValueDateAsString()) || entityChanged) {

				movement.setValueDateAsString(SwtUtil.formatDate(SwtUtil
						.getSysParamDateWithEntityOffset(entityId), SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue()));
			}
			// get currency code from screen to get the account list
			currencyCode = request.getParameter("currencyCode");
			// get the default MovType
			defaultMovType = request.getParameter("movType");
			// get the default currency for entity id
			defaultCurrency = SwtUtil.getDomesticCurrencyForUser(request,
					hostId, entityId);
			// populate the currency list in drop down box
			putCurrencyFullAccessListInReq(request, hostId, entityId);
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();

			//get the list of pre-advice movements that have been input in the last 24 hours
			listPreAdvice=preAdviceInputManager.getLast24PreAdviceInput(hostId,userId,entity1,inputBy);
			// getting currency code is null then set the default currency for
			// movement
			if (SwtUtil.isEmptyOrNull(currencyCode)) {
				currencyCode = defaultCurrency;
				movement.setCurrencyCode(currencyCode);
			} else {
				movement.setCurrencyCode(currencyCode);
			}
			// Start:code modified by Prasenjit Maji for Mantis 1874 on
			// 21/09/2012: Pre advice Input:Currency drop down should load
			// currencies for default entity if cancel button is pressed
			// code to handle for performing cancel operation on pre advice
			// input screen.
			if (!SwtUtil.isEmptyOrNull(request.getParameter("cancleStatus"))) {
				// empty the session notes details
				request.getSession().setAttribute("sessionNotesDetails", null);
				// Instantiate the movement and set the default value for entity
				// id,currency code,value date
				movement = new Movement();
				movement.getId().setEntityId(entityId);
				movement.setCurrencyCode(defaultCurrency);
				movement.setValueDateAsString(SwtUtil.formatDate(SwtUtil
						.getSystemDatewithoutTime(), SwtUtil
						.getCurrentSystemFormats(request.getSession())
						.getDateFormatValue()));
			}
			// End:code modified by Prasenjit Maji for Mantis 1874 on
			// 21/09/2012: Pre advice Input:Currency drop down should load
			// currencies for default entity if cancel button is pressed
			// update movement details in form
			setMovement(movement);
			// get the screen identifier flag based on that flag set the
			// attribute for input screen which is used to decide components in
			// screen
			screenIdentifier = (((request.getParameter("inputScreen") != null) && request
					.getParameter("inputScreen").equalsIgnoreCase("S")) ? request
					.getParameter("inputScreen")
					: EMPTYSTRING);
			if (screenIdentifier.equalsIgnoreCase("S")) {
				request.setAttribute("inputscreen", "search");
			} else if ((!SwtUtil.isEmptyOrNull(request
					.getParameter("displayPreadvice")))
					   && request.getParameter("displayPreadvice")
							   .equalsIgnoreCase("true")) {
				request.setAttribute("inputscreen", "update");
			} else {
				request.setAttribute("inputscreen", "insert");
			}
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.PRE_ADVICE_INPUT);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("displayedDate", SwtUtil.formatDate(movement
					.getValueDate(), SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue()));
			responseConstructor.createElement("defaultEntity",
					entityId);
			responseConstructor.createElement("defaultCurrency",
					defaultCurrency);
			responseConstructor.createElement("dateFormat",
					dateFormat);
			responseConstructor.createElement(SwtConstants.CURRENCYPATTERN,
					SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			/***** Entity Combo Start ***********/
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection entityList = putFullAcessEntityListInReq(request,roleId);
			Iterator j = entityList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList", lstOptions));
			/***** Entity Combo End ***********/

			/***** Entity Combo 1 Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection entityList1 = putFullOrViewAcessEntityListInReq(request,roleId);
			j = entityList1.iterator();
			row = null;
			lstOptions.add(new OptionInfo(SwtConstants.ALL_VALUE,SwtConstants.ALL_VALUE, false));
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("entityList1", lstOptions));
			/***** Entity Combo 1 End ***********/

			/***** Positionl level Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection PositionLevelList = putPreadvicePositionLevelInReq(request, hostId, movement.getId()
					.getEntityId());
			j = PositionLevelList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("PositionLevelList", lstOptions));
			/***** Positionl level Combo End ***********/


			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection currencyList = putCurrencyFullAccessListInReq(request, hostId, entityId);
			j = currencyList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyList", lstOptions));
			/***** Currency Combo End ***********/

			/***** Sign Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection signList = putSignListInReq(request);
			j = signList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("signList", lstOptions));
			/***** Sign Combo End ***********/


			/***** Account list Combo Start ***********/
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection collAcctList = preAdviceInputManager.getAccountIdDropDown(
					CacheManager.getInstance().getHostId(), entityId,
					currencyCode, defaultMovType);
			j = collAcctList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("collAcctList", lstOptions));
			/***** Account list Combo End ***********/

			/***** Book Code list Combo Start ***********/
			// get the collection of book code for host id,entity id
			collBookCode = ((MovementManager) (SwtUtil.getBean("movementManager")))
					.getBookCodeList(CacheManager.getInstance().getHostId(), entityId);
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			if (collBookCode != null) {
				// iterate the book code collection
				itrBookCode = collBookCode.iterator();
				lstOptions.add(new OptionInfo("","", false));
				while (itrBookCode.hasNext()) {
					// get the bookcode
					BookCode bookCode = itrBookCode.next();
					lstOptions.add(new OptionInfo(bookCode.getId().getBookCode(),bookCode.getBookName(), false));

				}
				lstSelect.add(new SelectInfo("collBookCode", lstOptions));
			}
			/***** Book Code list Combo End ***********/


			responseConstructor.formSelect(lstSelect);

			/******* preAdviceInputGrid ******/
			responseConstructor.formGridStart("preAdviceInputGrid");
			responseConstructor.formColumn(getGridColumns(width, columnOrder, hiddenColumns));
			// form rows (records)
			responseConstructor.formRowsStart(listPreAdvice.size());
			for (Iterator<Movement> it = listPreAdvice.iterator(); it.hasNext();) {
				// Obtain rules definition tag from iterator
				Movement mvt = (Movement) it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.ENTITY_ID, mvt.getId().getEntityId());
				responseConstructor.createRowElement(SwtConstants.MOVEMENT, mvt.getId().getMovementId().toString());
				responseConstructor.createRowElement(SwtConstants.PRED_STATUS, getStatusDesc(mvt.getPredictStatus()));
				responseConstructor.createRowElement(SwtConstants.STATUS, getMatchStatusDesc(mvt.getMatchStatus()));
				responseConstructor.createRowElement(SwtConstants.CCY,mvt.getCurrencyCode());
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ID1, mvt.getAccountId());
				responseConstructor.createRowElement(SwtConstants.VALUE_DATE,   SwtUtil.formatDate(mvt.getValueDate(), dateFormat ));
				responseConstructor.createRowElement(SwtConstants.AMOUNT, SwtUtil.formatCurrency(mvt
						.getCurrencyCode(), new BigDecimal(mvt.getAmount()),amountFormat));
				responseConstructor.createRowElement(SwtConstants.SIGN, mvt.getSign());
				responseConstructor.createRowElement(SwtConstants.REFERENCE, mvt.getReference1());
				responseConstructor.createRowElement(SwtConstants.COUNTER_PARTY_ID, mvt.getCounterPartyId());
				responseConstructor.createRowElement(SwtConstants.CPAERTY_TEXT, mvt.getCounterPartyText1());
				responseConstructor.createRowElement(SwtConstants.MATCH_PARTY, mvt.getMatchingParty());
				responseConstructor.createRowElement(SwtConstants.PRODUCT_TYPE, mvt.getProductType());
				responseConstructor.createRowElement(SwtConstants.BOOK_CODE, mvt.getBookCode());
				responseConstructor.createRowElement(SwtConstants.POST_DATE,(mvt.getPostingDate() == null || mvt.getPostingDate().equals("")) ? "" :  SwtUtil.formatDate(mvt.getPostingDate(), dateFormat ));
				responseConstructor.createRowElement(SwtConstants.INPUT_BY, mvt.getInputUser());
				responseConstructor.createRowElement(SwtConstants.INPUT_AT, (mvt.getInputDate() == null || mvt.getInputDate().equals("")) ? "" : SwtUtil.formatDate(mvt.getInputDate(), dateFormat+ " " + ("HH:mm:ss")) );

				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.PRE_ADVICE_INPUT);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			getMenuEntityCurrGrpAccess(request, null, null);
			log.debug(this.getClass().getName() + " - [display()] - Exit");
			return getView("data");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'display' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'display' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "display", PreAdviceInputAction.class), request, "");
			return getView("fail");
		} finally {
			/* null the objects created already. */
			hostId = null;
			entityId = null;
			currencyCode = null;
			defaultCurrency = null;
			roleId = null;
			movement = null;
			screenIdentifier = null;
		}
	}


	private String getStatusDesc(String status) {
		String statusCode = null;

		switch (status) {
			case "I":
				statusCode = "Included";
				break;
			case "E":
				statusCode = "Excluded";
				break;
			case "C":
				statusCode = "Cancelled";
				break;
			case "L":
				statusCode = "Outstanding";
				break;
			case "A":
				statusCode = "To Authorise";
				break;
			default:
				break;
		}

		return statusCode;

	}

	private String getMatchStatusDesc(String status) {
		String statusCode = null;

		switch (status) {
			case "C":
				statusCode = "Confirmed";
				break;
			case "M":
				statusCode = "Offered";
				break;
			case "E":
				statusCode = "Reconciled";
				break;
			case "S":
				statusCode = "Suspended";
				break;
			case "L":
				statusCode = "Outstanding";
				break;
			case "A":
				statusCode = "To Authorise";
				break;
			default:
				break;
		}

		return statusCode;

	}


	private String  getAmountFormat(String pattern) {
		String format = null;

		switch (pattern) {
			case "currencyPat1":
				format = "999,999.00";
				break;
			case "currencyPat2":
				format = "999.999,00";
				break;
			default:
				break;
		}

		return format;
	}

	private String  getHeaderName(String header) {
		String headerDesc = null;

		switch (header) {
			case "Entity_ID":
				headerDesc = "Entity";
				break;
			case "Account_ID":
				headerDesc = "Account ID";
				break;
			case "Value_Date":
				headerDesc = "Value Date";
				break;
			case "Pred_Status":
				headerDesc = "Pred Status";
				break;
			case "Book_Code":
				headerDesc = "Book Code";
				break;
			case "Post_Date":
				headerDesc = "Post Date";
				break;
			case "CounterParty_ID":
				headerDesc = "CounterParty ID";
				break;
			case "Cparty_Text":
				headerDesc = "Cparty Text";
				break;
			case "Match_Party":
				headerDesc = "Match Party";
				break;
			case "Sign":
				headerDesc = "Sign";
				break;
			case "Reference":
				headerDesc = "Reference";
				break;
			case "Product_Type":
				headerDesc = "Product Type";
				break;
			case "Amount":
				headerDesc = "Amount";
				break;
			case "Ccy":
				headerDesc = "Ccy";
				break;
			default:
				break;
		}

		return headerDesc;
	}



	private List<ColumnInfo> getGridColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {
		/* Array list to hold column order */
		ArrayList<String> orders = null;
		/* String array variable to hold column order property */
		String[] columnOrderProp = null;
		/* Iterator variable to hold column order */
		Iterator<String> columnOrderItr = null;
		/* Hash map to hold column width */
		HashMap<String, String> widths = null;
		/* String array variable to hold width property */
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;

		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getGridColumns ] - Entry");
			/* Condition to check width is null */

			if (SwtUtil.isEmptyOrNull(width)) {

				width =  SwtConstants.MOVEMENT + "=80,"
						 + SwtConstants.PRED_STATUS + "=120,"
						 + SwtConstants.STATUS + "=100,"
						 + SwtConstants.ENTITY_ID + "=120,"
						 + SwtConstants.CCY+ "=80,"
						 + SwtConstants.ACCOUNT_ID1 + "=150,"
						 + SwtConstants.VALUE_DATE + "=135,"
						 + SwtConstants.AMOUNT + "=150,"
						 + SwtConstants.SIGN + "=80,"
						 + SwtConstants.REFERENCE + "=120,"
						 + SwtConstants.COUNTER_PARTY_ID + "=150,"
						 + SwtConstants.CPAERTY_TEXT + "=120,"
						 + SwtConstants.MATCH_PARTY + "=120,"
						 + SwtConstants.PRODUCT_TYPE + "=130,"
						 + SwtConstants.BOOK_CODE + "=120,"
						 + SwtConstants.POST_DATE + "=120,"
						 + SwtConstants.INPUT_BY + "=100,"
						 + SwtConstants.INPUT_AT + "=150";

			}

			/* Obtain width for each column */
			columnWidthProperty = width.split(",");

			/* Loop to insert each column in hash map */
			widths = new HashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				/* Condition to check index of = is -1 */

				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");

					widths.put(propval[0], propval[1]);
				}
			}

			/* Condition to check column order is null or empty */
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				/* Default values for column order */
				columnOrder =  SwtConstants.MOVEMENT+ ","
							   + SwtConstants.PRED_STATUS + ","
							   + SwtConstants.STATUS  + ","
							   + SwtConstants.ENTITY_ID+ ","
							   + SwtConstants.CCY + ","
							   + SwtConstants.ACCOUNT_ID1 + ","
							   + SwtConstants.VALUE_DATE + ","
							   + SwtConstants.AMOUNT + ","
							   + SwtConstants.SIGN + ","
							   + SwtConstants.REFERENCE + ","
							   + SwtConstants.COUNTER_PARTY_ID + ","
							   + SwtConstants.CPAERTY_TEXT + ","
							   + SwtConstants.MATCH_PARTY + ","
							   + SwtConstants.PRODUCT_TYPE + ","
							   + SwtConstants.BOOK_CODE + ","
							   + SwtConstants.POST_DATE + ","
							   + SwtConstants.INPUT_BY + ","
							   + SwtConstants.INPUT_AT ;
			}
			orders = new ArrayList<String>();
			/* Split the columns using , and save in string array */
			columnOrderProp = columnOrder.split(",");
			/* Loop to enter column order in array list */

			for (int i = 0; i < columnOrderProp.length; i++) {
				/* Adding the Column values to ArrayList */
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet()
						.iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();
			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				if (order.equals(SwtConstants.MOVEMENT)) {
					// column Alert Date
					lstColumns.add(new ColumnInfo(
							SwtConstants.MOVEMENT_HEADER,
							SwtConstants.MOVEMENT,
							SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths
									.get(SwtConstants.MOVEMENT)),
							false, true, true));
				} else if (order.equals(SwtConstants.PRED_STATUS)) {
					// column Alert Date
					lstColumns.add(new ColumnInfo(
							SwtConstants.PRED_STATUS_HEADER,
							SwtConstants.PRED_STATUS,
							SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths
									.get(SwtConstants.PRED_STATUS)),
							false, true, hiddenColumnsMap
							.get(SwtConstants.PRED_STATUS)));
				} else if (order.equals(SwtConstants.STATUS )) {
					// column Alert Date
					lstColumns.add(new ColumnInfo(
							SwtConstants.STATUS_HEADER,
							SwtConstants.STATUS ,
							SwtConstants.COLUMN_TYPE_STRING, 2,
							Integer.parseInt(widths
									.get(SwtConstants.STATUS )),
							false, true, hiddenColumnsMap
							.get(SwtConstants.STATUS )));
				} else if (order.equals(SwtConstants.ENTITY_ID)) {
					// column Alert Date
					lstColumns.add(new ColumnInfo(
							SwtConstants.ENTITY_ID_HEADER,
							SwtConstants.ENTITY_ID,
							SwtConstants.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths
									.get(SwtConstants.ENTITY_ID)),
							false, true, true));
				}else if (order.equals(SwtConstants.CCY)) {
					// column Alert Time
					lstColumns.add(new ColumnInfo(
							SwtConstants.CCY_HEADER,
							SwtConstants.CCY,
							SwtConstants.COLUMN_TYPE_STRING, 4,
							Integer.parseInt(widths
									.get(SwtConstants.CCY)),
							false, true, hiddenColumnsMap
							.get(SwtConstants.CCY)));
				} else if (order.equals(SwtConstants.ACCOUNT_ID1)) {
					// column Alert Status
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.ACCOUNT_ID1_HEADER,
									SwtConstants.ACCOUNT_ID1,
									SwtConstants.COLUMN_TYPE_STRING,
									5,
									Integer.parseInt(widths
											.get(SwtConstants.ACCOUNT_ID1)),
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.ACCOUNT_ID1)));
				} else if (order.equals(SwtConstants.VALUE_DATE )) {
					// column Alert Type
					lstColumns.add(new ColumnInfo(
							SwtConstants.VALUE_DATE_HEADER,
							SwtConstants.VALUE_DATE ,
							SwtConstants.COLUMN_TYPE_STRING, 6,
							Integer.parseInt(widths
									.get(SwtConstants.VALUE_DATE )),
							false, true, hiddenColumnsMap
							.get(SwtConstants.VALUE_DATE )));
				} else if (order
						.equals(SwtConstants.AMOUNT)) {
					// column Risk Weight
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.AMOUNT_HEADER,
									SwtConstants.AMOUNT,
									SwtConstants.COLUMN_TYPE_NUMBER,
									7,
									Integer.parseInt(widths
											.get(SwtConstants.AMOUNT)),
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.AMOUNT)));
				} else if (order
						.equals(SwtConstants.SIGN)) {
					// column Account ID
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.SIGN_HEADER,
									SwtConstants.SIGN,
									SwtConstants.COLUMN_TYPE_STRING,
									8,
									Integer.parseInt(widths
											.get(SwtConstants.SIGN)),
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.SIGN)));
				} else if (order
						.equals(SwtConstants.REFERENCE)) {
					// column Client ID
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.REFERENCE_HEADER,
									SwtConstants.REFERENCE,
									SwtConstants.COLUMN_TYPE_STRING,
									9,
									Integer.parseInt(widths
											.get(SwtConstants.REFERENCE)),
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.REFERENCE)));
				} else if (order
						.equals(SwtConstants.COUNTER_PARTY_ID)) {
					// column Client Name
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.COUNTER_PARTY_ID_HEADER,
									SwtConstants.COUNTER_PARTY_ID,
									SwtConstants.COLUMN_TYPE_STRING,
									10,
									Integer.parseInt(widths
											.get(SwtConstants.COUNTER_PARTY_ID)),
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.COUNTER_PARTY_ID)));

				} else if (order
						.equals(SwtConstants.CPAERTY_TEXT)) {
					// column Client Name
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.CPAERTY_TEXT_HEADER,
									SwtConstants.CPAERTY_TEXT,
									SwtConstants.COLUMN_TYPE_STRING,
									11,
									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.CPAERTY_TEXT))
											? Integer.parseInt(widths.get(SwtConstants.CPAERTY_TEXT))
											: 125,
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.CPAERTY_TEXT)));
				}else if (order
						.equals(SwtConstants.MATCH_PARTY)) {
					// column Client Name
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.MATCH_PARTY_HEADER,
									SwtConstants.MATCH_PARTY,
									SwtConstants.COLUMN_TYPE_STRING,
									12,
									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MATCH_PARTY))
											? Integer.parseInt(widths.get(SwtConstants.MATCH_PARTY))
											: 125,
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.MATCH_PARTY)));
				}
				else if (order
						.equals(SwtConstants.PRODUCT_TYPE)) {
					// column Client Name
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.PRODUCT_TYPE_HEADER,
									SwtConstants.PRODUCT_TYPE,
									SwtConstants.COLUMN_TYPE_STRING,
									13,
									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.PRODUCT_TYPE))
											? Integer.parseInt(widths.get(SwtConstants.PRODUCT_TYPE))
											: 125,
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.PRODUCT_TYPE)));
				}	else if (order
						.equals(SwtConstants.BOOK_CODE)) {
					// column Client Name
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.BOOK_CODE_HEADER,
									SwtConstants.BOOK_CODE,
									SwtConstants.COLUMN_TYPE_STRING,
									14,
									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.BOOK_CODE))
											? Integer.parseInt(widths.get(SwtConstants.BOOK_CODE))
											: 125,
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.BOOK_CODE)));
				}	else if (order
						.equals(SwtConstants.POST_DATE)) {
					// column Client Name
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.POST_DATE_HEADER,
									SwtConstants.POST_DATE,
									SwtConstants.COLUMN_TYPE_STRING,
									15,
									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.POST_DATE))
											? Integer.parseInt(widths.get(SwtConstants.POST_DATE))
											: 125,
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.POST_DATE)));
				}else if (order
						.equals(SwtConstants.INPUT_BY)) {
					// column Client Name
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.INPUT_BY_HEADER,
									SwtConstants.INPUT_BY,
									SwtConstants.COLUMN_TYPE_STRING,
									16,
									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.INPUT_BY))
											? Integer.parseInt(widths.get(SwtConstants.INPUT_BY))
											: 125,
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.INPUT_BY)));
				}else if (order
						.equals(SwtConstants.INPUT_AT)) {
					// column Client Name
					lstColumns
							.add(new ColumnInfo(
									SwtConstants.INPUT_AT_HEADER,
									SwtConstants.INPUT_AT,
									SwtConstants.COLUMN_TYPE_STRING,
									17,
									!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.INPUT_AT))
											? Integer.parseInt(widths.get(SwtConstants.INPUT_AT))
											: 125,
									false,
									true,
									hiddenColumnsMap
											.get(SwtConstants.INPUT_AT)));
				}


			}
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Caught in [getGridColumns] method : - "
					  + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getGridColumns", this.getClass());

		} finally {
			// Nullify objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;

			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getGridColumns ] - Exit");
		}
		// return xml column
		return lstColumns;
	}



	private List<ColumnInfo> getDataDefGridColumns(String width, String columnOrder, String hiddenColumns,Boolean isSourceGrid ,Boolean draggable)
			throws SwtException {
		/* Array list to hold column order */
		ArrayList<String> orders = null;
		/* String array variable to hold column order property */
		String[] columnOrderProp = null;
		/* Iterator variable to hold column order */
		Iterator<String> columnOrderItr = null;
		/* Hash map to hold column width */
		HashMap<String, String> widths = null;
		/* String array variable to hold width property */
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;

		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getDataDefGridColumns ] - Entry");
			/* Condition to check width is null */

			if (SwtUtil.isEmptyOrNull(width)) {

				width =     SwtConstants.PREADVCIE_ENTITY_ID+ "=120,"
							+ SwtConstants.CCY+ "=80,"
							+ SwtConstants.ACCOUNT_ID1 + "=120,"
							+ SwtConstants.VALUE_DATE + "=135,"
							+ SwtConstants.AMOUNT + "=100,"
							+ SwtConstants.SIGN + "=80,"
							+ SwtConstants.PRED_STATUS + "=120,"
							+ SwtConstants.REFERENCE + "=120,"
							+ SwtConstants.BOOK_CODE + "=120,"
							+ SwtConstants.POST_DATE + "=120,"
							+ SwtConstants.COUNTER_PARTY_ID + "=150,"
							+ SwtConstants.CPAERTY_TEXT + "=120,"
							+ SwtConstants.MATCH_PARTY + "=120,"
							+ SwtConstants.PRODUCT_TYPE + "=130";

			}

			/* Obtain width for each column */
			columnWidthProperty = width.split(",");

			/* Loop to insert each column in hash map */
			widths = new HashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				/* Condition to check index of = is -1 */

				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");

					widths.put(propval[0], propval[1]);
				}
			}

			/* Condition to check column order is null or empty */
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				/* Default values for column order */
				columnOrder =  SwtConstants.PREADVCIE_ENTITY_ID + ","
							   + SwtConstants.CCY + ","
							   + SwtConstants.ACCOUNT_ID1 + ","
							   + SwtConstants.VALUE_DATE  + ","
							   + SwtConstants.AMOUNT + ","
							   + SwtConstants.SIGN + ","
							   + SwtConstants.PRED_STATUS + ","
							   + SwtConstants.REFERENCE + ","
							   + SwtConstants.BOOK_CODE + ","
							   + SwtConstants.POST_DATE + ","
							   + SwtConstants.COUNTER_PARTY_ID + ","
							   + SwtConstants.CPAERTY_TEXT + ","
							   + SwtConstants.MATCH_PARTY + ","
							   + SwtConstants.PRODUCT_TYPE ;
			}
			orders = new ArrayList<String>();
			/* Split the columns using , and save in string array */
			columnOrderProp = columnOrder.split(",");
			/* Loop to enter column order in array list */

			for (int i = 0; i < columnOrderProp.length; i++) {
				/* Adding the Column values to ArrayList */
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet()
						.iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();
			ColumnInfo tmpColumnInfo = null;
			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				if (order.equals(SwtConstants.PREADVCIE_ENTITY_ID)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.ENTITY_ID_HEADER,
							SwtConstants.PREADVCIE_ENTITY_ID ,
							SwtConstants.COLUMN_TYPE_STRING, false, 0,
							Integer.parseInt(widths
									.get(SwtConstants.PREADVCIE_ENTITY_ID )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.PREADVCIE_ENTITY_ID )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);


				}else if (order.equals(SwtConstants.CCY)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.CCY_HEADER,
							SwtConstants.CCY ,
							SwtConstants.COLUMN_TYPE_STRING, false, 1,
							Integer.parseInt(widths
									.get(SwtConstants.CCY )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.CCY )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);
				}else if (order.equals(SwtConstants.ACCOUNT_ID1)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.ACCOUNT_ID1_HEADER,
							SwtConstants.ACCOUNT_ID1 ,
							SwtConstants.COLUMN_TYPE_STRING, false, 2,
							Integer.parseInt(widths
									.get(SwtConstants.ACCOUNT_ID1 )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.ACCOUNT_ID1 )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);

				}else if (order.equals(SwtConstants.VALUE_DATE )) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.VALUE_DATE_HEADER,
							SwtConstants.VALUE_DATE ,
							isSourceGrid?SwtConstants.COLUMN_TYPE_COMBO:SwtConstants.COLUMN_TYPE_STRING, false, 3,
							Integer.parseInt(widths
									.get(SwtConstants.VALUE_DATE )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.VALUE_DATE )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					tmpColumnInfo.setDataProvider("listValDates");
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.AMOUNT)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.AMOUNT_HEADER,
							SwtConstants.AMOUNT ,
							isSourceGrid?SwtConstants.COLUMN_TYPE_COMBO:SwtConstants.COLUMN_TYPE_STRING, false, 4,
							Integer.parseInt(widths
									.get(SwtConstants.AMOUNT )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.AMOUNT )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					tmpColumnInfo.setDataProvider("listAmount");
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SIGN)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.SIGN_HEADER,
							SwtConstants.SIGN ,
							SwtConstants.COLUMN_TYPE_STRING, false, 5,
							Integer.parseInt(widths
									.get(SwtConstants.SIGN )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.SIGN )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);

				}    else if (order.equals(SwtConstants.PRED_STATUS)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.PRED_STATUS_HEADER,
							SwtConstants.PRED_STATUS ,
							SwtConstants.COLUMN_TYPE_STRING, false, 6,
							Integer.parseInt(widths
									.get(SwtConstants.PRED_STATUS )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.PRED_STATUS )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);


				}else if (order
						.equals(SwtConstants.REFERENCE)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.REFERENCE_HEADER,
							SwtConstants.REFERENCE ,
							SwtConstants.COLUMN_TYPE_STRING, false, 7,
							Integer.parseInt(widths
									.get(SwtConstants.REFERENCE )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.REFERENCE )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);


				} 	else if (order
						.equals(SwtConstants.BOOK_CODE)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.BOOK_CODE_HEADER,
							SwtConstants.BOOK_CODE ,
							SwtConstants.COLUMN_TYPE_STRING, false, 8,
							Integer.parseInt(widths
									.get(SwtConstants.BOOK_CODE )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.BOOK_CODE )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);


				}else if (order
						.equals(SwtConstants.POST_DATE)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.POST_DATE_HEADER,
							SwtConstants.POST_DATE ,
							isSourceGrid?SwtConstants.COLUMN_TYPE_COMBO:SwtConstants.COLUMN_TYPE_STRING,false, 9,
							Integer.parseInt(widths
									.get(SwtConstants.POST_DATE )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.POST_DATE )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					tmpColumnInfo.setDataProvider("listPostDates");
					lstColumns.add(tmpColumnInfo);
				}	else if (order
						.equals(SwtConstants.COUNTER_PARTY_ID)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.COUNTER_PARTY_ID_HEADER,
							SwtConstants.COUNTER_PARTY_ID ,
							SwtConstants.COLUMN_TYPE_STRING,false, 10,
							Integer.parseInt(widths
									.get(SwtConstants.COUNTER_PARTY_ID )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.COUNTER_PARTY_ID )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);



				} else if (order
						.equals(SwtConstants.CPAERTY_TEXT)) {

					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.CPAERTY_TEXT_HEADER,
							SwtConstants.CPAERTY_TEXT ,
							SwtConstants.COLUMN_TYPE_STRING,false, 11,
							Integer.parseInt(widths
									.get(SwtConstants.CPAERTY_TEXT )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.CPAERTY_TEXT )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);



				}else if (order
						.equals(SwtConstants.MATCH_PARTY)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.MATCH_PARTY_HEADER,
							SwtConstants.MATCH_PARTY ,
							SwtConstants.COLUMN_TYPE_STRING,false, 12,
							Integer.parseInt(widths
									.get(SwtConstants.MATCH_PARTY )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.MATCH_PARTY )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);

				}
				else if (order
						.equals(SwtConstants.PRODUCT_TYPE)) {
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.PRODUCT_TYPE_HEADER,
							SwtConstants.PRODUCT_TYPE ,
							SwtConstants.COLUMN_TYPE_STRING,false, 13,
							Integer.parseInt(widths
									.get(SwtConstants.PRODUCT_TYPE )),
							draggable, false, hiddenColumnsMap
							.get(SwtConstants.PRODUCT_TYPE )));
					if(!isSourceGrid) {
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
					}
					lstColumns.add(tmpColumnInfo);

				}


			}
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Caught in [getDataDefGridColumns] method : - "
					  + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getDataDefGridColumns", this.getClass());

		} finally {
			// Nullify objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;

			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getDataDefGridColumns ] - Exit");
		}
		// return xml column
		return lstColumns;
	}




	private List<ColumnInfo> getSavedGridColumns(String width, String columnOrder, String hiddenColumns,Boolean isPreviewGrid, Boolean isSourceGrid, String savedColumns, Boolean draggable)
			throws SwtException {
		/* Array list to hold column order */
		ArrayList<String> orders = null;
		/* String array variable to hold column order property */
		String[] columnOrderProp = null;
		/* Iterator variable to hold column order */
		Iterator<String> columnOrderItr = null;
		/* Hash map to hold column width */
		HashMap<String, String> widths = null;
		/* String array variable to hold width property */
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		String[] savedColumnsArray =null;
		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;
		width="";
		columnOrder="";
		ArrayList<String> comboFields = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getSavedGridColumns ] - Entry");
			comboFields = new ArrayList<String>(Arrays.asList("Amount", "Value_Date", "Post_Date"));
			savedColumnsArray=savedColumns.split(",");
			/* Condition to check width is null */

			if (SwtUtil.isEmptyOrNull(width)) {

				for (int i = 0; i < savedColumnsArray.length-1; i++) {
					width=width.concat(savedColumnsArray[i]+ "=120,");
				}
				width = width.substring(0,width.length() - 1);
			}


			/* Obtain width for each column */
			columnWidthProperty = width.split(",");

			/* Loop to insert each column in hash map */
			widths = new HashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				/* Condition to check index of = is -1 */

				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");

					widths.put(propval[0], propval[1]);
				}
			}

			/* Condition to check column order is null or empty */
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				for (int i = 0; i < savedColumnsArray.length; i++) {
					/* Default values for column order */
					columnOrder =  columnOrder+savedColumnsArray[i] + ",";
				}
				columnOrder=columnOrder.substring(0,columnOrder.length() - 1);
			}
			orders = new ArrayList<String>();
			/* Split the columns using , and save in string array */
			columnOrderProp = columnOrder.split(",");
			/* Loop to enter column order in array list */

			for (int i = 0; i < columnOrderProp.length; i++) {
				/* Adding the Column values to ArrayList */
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet()
						.iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();

			lstColumns = new ArrayList<ColumnInfo>();
			ColumnInfo tmpColumnInfo = null;
			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				for (int i = 0; i < savedColumnsArray.length; i++) {
					if (order.equalsIgnoreCase(savedColumnsArray[i]) && !SwtUtil.isEmptyOrNull(savedColumnsArray[i])  ) {
						tmpColumnInfo = (new ColumnInfo(getHeaderName(savedColumnsArray[i]), savedColumnsArray[i],
								(isSourceGrid &&  (comboFields.contains(savedColumnsArray[i]))) ? SwtConstants.COLUMN_TYPE_COMBO:((isPreviewGrid  && "Amount".equalsIgnoreCase(savedColumnsArray[i]) )? SwtConstants.COLUMN_TYPE_NUMBER : SwtConstants.COLUMN_TYPE_STRING),isPreviewGrid?true:false, i,
								getColumnWidth(savedColumnsArray[i]),draggable, isPreviewGrid?true:false, hiddenColumnsMap.get(savedColumnsArray[i])));
						tmpColumnInfo.setEditable(true);
						tmpColumnInfo.setMaxChars("40");
						if("Amount".equalsIgnoreCase(savedColumnsArray[i])){
							tmpColumnInfo.setDataProvider("listAmount");
						}else {
							tmpColumnInfo.setDataProvider("listPostDates");
						}
						lstColumns.add(tmpColumnInfo);
					}
				}

			}
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Caught in [getSavedGridColumns] method : - "
					  + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getSavedGridColumns", this.getClass());

		} finally {
			// Nullify objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			savedColumnsArray = null;
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getSavedGridColumns ] - Exit");
		}
		return lstColumns;
	}


	private int getColumnWidth(String column) {
		int colWidth = 0;

		switch (column) {
			case "Entity_ID":
				colWidth = 120;
				break;
			case "Account_ID":
				colWidth = 120;
				break;
			case "Value_Date":
				colWidth = 135;
				break;
			case "Pred.Status":
				colWidth = 120;
				break;
			case "Book_Code":
				colWidth = 120;
				break;
			case "Post_Date":
				colWidth = 120;
				break;
			case "CounterParty_ID":
				colWidth = 150;
				break;
			case "Cparty_Text":
				colWidth = 120;
				break;
			case "Match_Party":
				colWidth = 120;
				break;
			case "Sign":
				colWidth = 80;
				break;
			case "Reference":
				colWidth = 120;
				break;
			case "Product_Type":
				colWidth = 130;
				break;
			case "Amount":
				colWidth = 100;
				break;
			case "Ccy":
				colWidth = 80;
			default:
				break;
		}

		return colWidth;
	}


	private List<ColumnInfo> getDataPreviewGridColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {
		/* Array list to hold column order */
		ArrayList<String> orders = null;
		/* String array variable to hold column order property */
		String[] columnOrderProp = null;
		/* Iterator variable to hold column order */
		Iterator<String> columnOrderItr = null;
		/* Hash map to hold column width */
		HashMap<String, String> widths = null;
		/* String array variable to hold width property */
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;

		/* Hash map to hold column hidden_Columns */
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		/* Array list to hold hidden Column array */
		ArrayList<String> lstHiddenColunms = null;
		/* String array variable to hold hidden columns property */
		String[] hiddenColumnsProp = null;

		try {
			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getDataPreviewGridColumns ] - Entry");
			/* Condition to check width is null */

			if (SwtUtil.isEmptyOrNull(width)) {

				width =     SwtConstants.PREADVCIE_ENTITY_ID+ "=120,"
							+ SwtConstants.CCY+ "=80,"
							+ SwtConstants.ACCOUNT_ID1 + "=120,"
							+ SwtConstants.VALUE_DATE + "=135,"
							+ SwtConstants.AMOUNT + "=100,"
							+ SwtConstants.SIGN + "=80,"
							+ SwtConstants.PRED_STATUS + "=120,"
							+ SwtConstants.REFERENCE + "=120,"
							+ SwtConstants.BOOK_CODE + "=120,"
							+ SwtConstants.POST_DATE + "=120,"
							+ SwtConstants.COUNTER_PARTY_ID + "=150,"
							+ SwtConstants.CPAERTY_TEXT + "=120,"
							+ SwtConstants.MATCH_PARTY + "=120,"
							+ SwtConstants.PRODUCT_TYPE + "=130";

			}

			/* Obtain width for each column */
			columnWidthProperty = width.split(",");

			/* Loop to insert each column in hash map */
			widths = new HashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				/* Condition to check index of = is -1 */

				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");

					widths.put(propval[0], propval[1]);
				}
			}

			/* Condition to check column order is null or empty */
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				/* Default values for column order */
				columnOrder =  SwtConstants.PREADVCIE_ENTITY_ID + ","
							   + SwtConstants.CCY + ","
							   + SwtConstants.ACCOUNT_ID1 + ","
							   + SwtConstants.VALUE_DATE  + ","
							   + SwtConstants.AMOUNT + ","
							   + SwtConstants.SIGN + ","
							   + SwtConstants.PRED_STATUS + ","
							   + SwtConstants.REFERENCE + ","
							   + SwtConstants.BOOK_CODE + ","
							   + SwtConstants.POST_DATE + ","
							   + SwtConstants.COUNTER_PARTY_ID + ","
							   + SwtConstants.CPAERTY_TEXT + ","
							   + SwtConstants.MATCH_PARTY + ","
							   + SwtConstants.PRODUCT_TYPE ;
			}
			orders = new ArrayList<String>();
			/* Split the columns using , and save in string array */
			columnOrderProp = columnOrder.split(",");
			/* Loop to enter column order in array list */

			for (int i = 0; i < columnOrderProp.length; i++) {
				/* Adding the Column values to ArrayList */
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet()
						.iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					// boolean found = false;
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();
			ColumnInfo tmpColumnInfo = null;
			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();
				if (order.equals(SwtConstants.PREADVCIE_ENTITY_ID)) {
					// column Alert Date
					tmpColumnInfo=(new ColumnInfo(
							SwtConstants.ENTITY_ID_HEADER,
							SwtConstants.PREADVCIE_ENTITY_ID,
							SwtConstants.COLUMN_TYPE_STRING, 0,
							Integer.parseInt(widths
									.get(SwtConstants.PREADVCIE_ENTITY_ID)),
							false, true, hiddenColumnsMap
							.get(SwtConstants.PREADVCIE_ENTITY_ID)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}else if (order.equals(SwtConstants.CCY)) {
					// column Alert Date
					tmpColumnInfo=(new ColumnInfo(
							SwtConstants.CCY_HEADER,
							SwtConstants.CCY,
							SwtConstants.COLUMN_TYPE_STRING, 1,
							Integer.parseInt(widths
									.get(SwtConstants.CCY)),
							false, true, hiddenColumnsMap
							.get(SwtConstants.CCY)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}else if (order.equals(SwtConstants.ACCOUNT_ID1)) {
					// column Alert Status
					tmpColumnInfo=(new ColumnInfo(
							SwtConstants.ACCOUNT_ID1_HEADER,
							SwtConstants.ACCOUNT_ID1,
							SwtConstants.COLUMN_TYPE_STRING,
							2,
							Integer.parseInt(widths
									.get(SwtConstants.ACCOUNT_ID1)),
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.ACCOUNT_ID1)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}else if (order.equals(SwtConstants.VALUE_DATE )) {
					// column Alert Type
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.VALUE_DATE_HEADER,
							SwtConstants.VALUE_DATE ,
							SwtConstants.COLUMN_TYPE_STRING, 3,
							Integer.parseInt(widths
									.get(SwtConstants.VALUE_DATE )),
							false, true, hiddenColumnsMap
							.get(SwtConstants.VALUE_DATE )));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.AMOUNT)) {
					// column Risk Weight
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.AMOUNT_HEADER,
							SwtConstants.AMOUNT ,
							SwtConstants.COLUMN_TYPE_NUMBER, 4,
							Integer.parseInt(widths
									.get(SwtConstants.AMOUNT )),
							false, true, hiddenColumnsMap
							.get(SwtConstants.AMOUNT )));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				} else if (order
						.equals(SwtConstants.SIGN)) {
					// column Account ID
					tmpColumnInfo=(new ColumnInfo(
							SwtConstants.SIGN_HEADER,
							SwtConstants.SIGN,
							SwtConstants.COLUMN_TYPE_STRING,
							5,
							Integer.parseInt(widths
									.get(SwtConstants.SIGN)),
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.SIGN)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}    else if (order.equals(SwtConstants.PRED_STATUS)) {
					// column Alert Date
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.PRED_STATUS_HEADER,
							SwtConstants.PRED_STATUS,
							SwtConstants.COLUMN_TYPE_STRING, 6,
							Integer.parseInt(widths
									.get(SwtConstants.PRED_STATUS)),
							false, true, hiddenColumnsMap
							.get(SwtConstants.PRED_STATUS)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}else if (order
						.equals(SwtConstants.REFERENCE)) {
					// column Client ID
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.REFERENCE_HEADER,
							SwtConstants.REFERENCE,
							SwtConstants.COLUMN_TYPE_STRING,
							7,
							Integer.parseInt(widths
									.get(SwtConstants.REFERENCE)),
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.REFERENCE)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				} 	else if (order
						.equals(SwtConstants.BOOK_CODE)) {
					// column Client Name
					tmpColumnInfo=(new ColumnInfo(
							SwtConstants.BOOK_CODE_HEADER,
							SwtConstants.BOOK_CODE,
							SwtConstants.COLUMN_TYPE_STRING,
							8,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.BOOK_CODE))
									? Integer.parseInt(widths.get(SwtConstants.BOOK_CODE))
									: 125,
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.BOOK_CODE)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}else if (order
						.equals(SwtConstants.POST_DATE)) {
					// column Alert Type
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.POST_DATE_HEADER,
							SwtConstants.POST_DATE ,
							SwtConstants.COLUMN_TYPE_STRING, 9,
							Integer.parseInt(widths
									.get(SwtConstants.POST_DATE )),
							false, true, hiddenColumnsMap
							.get(SwtConstants.POST_DATE )));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}	else if (order
						.equals(SwtConstants.COUNTER_PARTY_ID)) {
					// column Client Name
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.COUNTER_PARTY_ID_HEADER,
							SwtConstants.COUNTER_PARTY_ID,
							SwtConstants.COLUMN_TYPE_STRING,
							10,
							Integer.parseInt(widths
									.get(SwtConstants.COUNTER_PARTY_ID)),
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.COUNTER_PARTY_ID)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);

				} else if (order
						.equals(SwtConstants.CPAERTY_TEXT)) {
					// column Client Name
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.CPAERTY_TEXT_HEADER,
							SwtConstants.CPAERTY_TEXT,
							SwtConstants.COLUMN_TYPE_STRING,
							11,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.CPAERTY_TEXT))
									? Integer.parseInt(widths.get(SwtConstants.CPAERTY_TEXT))
									: 125,
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.CPAERTY_TEXT)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}else if (order
						.equals(SwtConstants.MATCH_PARTY)) {
					// column Client Name
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.MATCH_PARTY_HEADER,
							SwtConstants.MATCH_PARTY,
							SwtConstants.COLUMN_TYPE_STRING,
							12,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.MATCH_PARTY))
									? Integer.parseInt(widths.get(SwtConstants.MATCH_PARTY))
									: 125,
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.MATCH_PARTY)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}
				else if (order
						.equals(SwtConstants.PRODUCT_TYPE)) {
					// column Client Name
					tmpColumnInfo= (new ColumnInfo(
							SwtConstants.PRODUCT_TYPE_HEADER,
							SwtConstants.PRODUCT_TYPE,
							SwtConstants.COLUMN_TYPE_STRING,
							13,
							!SwtUtil.isEmptyOrNull(widths.get(SwtConstants.PRODUCT_TYPE))
									? Integer.parseInt(widths.get(SwtConstants.PRODUCT_TYPE))
									: 125,
							false,
							true,
							hiddenColumnsMap
									.get(SwtConstants.PRODUCT_TYPE)));
					tmpColumnInfo.setEditable(true);
					tmpColumnInfo.setMaxChars("40");
					lstColumns.add(tmpColumnInfo);
				}


			}
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Caught in [getDataPreviewGridColumns] method : - "
					  + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex,
					"getDataPreviewGridColumns", this.getClass());

		} finally {
			// Nullify objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;

			// log debug message
			log.debug(this.getClass().getName()
					  + " - [ getDataPreviewGridColumns ] - Exit");
		}
		// return xml column
		return lstColumns;
	}





	/**
	 * This method is used to set the access permission of SAVE button.
	 *
	 * @param request
	 * @param entityId
	 * @param currencyCode
	 */
	private void putSaveButtonStatus(HttpServletRequest request,
									 String entityId, String currencyCode) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					  + " - [putSaveButtonStatus] - Entering");
			// Condition to check access value for menu,entity,currency group is
			// full access
			if (getMenuEntityCurrGrpAccess(request, entityId, currencyCode) == 0) {
				// if access is full then set the enable status for save button.
				request.setAttribute("SaveButton", "true");
			}
			log.debug(this.getClass().getName()
					  + " - [putSaveButtonStatus] - Existing");
		} catch (Exception e) {
			log
					.error(this.getClass().getName()
						   + " - [putSaveButtonStatus] - Exception -"
						   + e.getMessage());
			throw new SwtException(e.getMessage());
		}
	}

	/**
	 * This method is used to set the access permission of DELETE button.
	 *
	 * @param request
	 * @param entityId
	 * @param currencyCode
	 * @param matchStatus
	 */
	private void putDeleteButtonStatus(HttpServletRequest request,
									   String entityId, String currencyCode, String matchStatus)
			throws SwtException {
		try {
			log.debug(this.getClass().getName()
					  + " - [putDeleteButtonStatus] - Entering");
			// Condition to check access is full and match status is referred
			if ((getMenuEntityCurrGrpAccess(request, entityId, currencyCode) == 0)
				&& matchStatus
						.equalsIgnoreCase(SwtConstants.REFERRED_STATUS)) {
				// if full access and match status is referred
				// then enable the delete button
				request.setAttribute("deleteButton", "true");
			}
			log.debug(this.getClass().getName()
					  + " - [putDeleteButtonStatus] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putDeleteButtonStatus] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		}
	}

	/**
	 * This action method is invoked while clicking delete button on pre advice
	 * display screen and used to delete the given Movement from the database.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String delete()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To hold Entity Id
		String entityId = null;
		// To hold movement Id
		String movementId = null;
		// dynavalidator form to get and set the form values
		// DynaValidatorForm dynaForm = null;
		// Movement bean used to get the form values
		Movement movement = null;
		try {
			log.debug(this.getClass().getName() + " - [delete] - Entering");
			// get the DynaValidatorForm to get all form values
			// get the movement id from form
			movementId = (!SwtUtil.isEmptyOrNull(request
					.getParameter("movementId")) ? request.getParameter(
					"movementId").trim() : "0");
			// get the movement details from form
			movement = (Movement) getMovement();
			entityId = (!SwtUtil.isEmptyOrNull(movement.getId().getEntityId()) ? movement
					.getId().getEntityId().trim()
					: EMPTYSTRING);
			// concatenate the movement id
			movementId = movementId + "|";
			// delete movement for given movement id
			preAdviceInputManager.deleteMovement(CacheManager.getInstance()
					.getHostId(), entityId, movementId);
			// empty the notes details in session
			request.getSession().setAttribute("sessionNotesDetails", null);
			// empty the form details in screen to set the new movement bean
			// in form
			setMovement(new Movement());
			// set the access level for menu in request
			getMenuEntityCurrGrpAccess(request, null, null);
			// set the attribute to identify which screen is opened
			request.setAttribute("inputscreen", "search");
			request.setAttribute("methodName", "search");
			log.debug(this.getClass().getName() + " - [delete] - Existing");
			return getView("search");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'delete' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'delete' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "delete", PreAdviceInputAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			entityId = null;
			movementId = null;
			movement = null;
		}
	}

	/**
	 * This action method is invoked while clicking change button on pre advice
	 * display screen and used to change the details of the pre advice movement.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayPreadvice() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To host for application
		String hostId = null;
		// variable to hold the pojo class
		Movement movement = null;
		// To get all form details
		// DynaValidatorForm dynaForm = null;
		// To hold the form movement
		Movement movForm = null;
		// collecion of misc params
		Collection<MiscParams> collMiscParams = null;
		// editable flag array
		Hashtable<String, Integer> editFlagArr = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [displayPreadvice] - Entering");
			// getting current hostId
			hostId = CacheManager.getInstance().getHostId();
			// get the form details
			// get the movement details from form
			movForm = (Movement) (getMovement());
			// get the movement details from data base for given movement id
			movement = ((MovementManager) (SwtUtil.getBean("movementManager")))
					.getMovementDetails(hostId,
							movForm.getId().getMovementId(), new SystemInfo(),
							SwtUtil.getCurrentSystemFormats(request
									.getSession()));
			// if movement having notes then get all notes details from data
			// base and set it into request
			if (movement.getHasNotes().trim().equalsIgnoreCase("Y")) {
				request.setAttribute("sessionNotesDetails",
						((MovementDAO) (SwtUtil.getBean("movementDAO")))
								.getNoteDetails(hostId, movForm.getId()
										.getMovementId()));
			}
			// if getting currency code is not null then set the same otherwise
			// taken from db currency
			movement.setCurrencyCode((!SwtUtil.isEmptyOrNull(movForm
					.getCurrencyCode())) ? movForm.getCurrencyCode() : movement
					.getCurrencyCode());
			// if getting movement type is not null then set the same otherwise
			// taken from db movement type
			movement.setMovementType((!SwtUtil.isEmptyOrNull(movForm
					.getMovementType())) ? movForm.getMovementType() : movement
					.getMovementType());
			// Condition to check if form value date is null
			if (SwtUtil.isEmptyOrNull(movForm.getValueDateAsString())) {
				// set value date of MOvement details from DB
				movement.setValueDateAsString(SwtUtil.formatDate(movement
						.getValueDate(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue()));

			} else {
				// if form date is not null set the same
				movement.setValueDateAsString(movForm.getValueDateAsString());
			}
			// get the movement posting date and convert the date as per in
			// system format and set the same in movement bean
			movement.setPostingDateAsString((SwtUtil.formatDate(movement
					.getPostingDate(), SwtUtil.getCurrentSystemFormats(
					request.getSession()).getDateFormatValue())));
			// get the movement amount and convert the amount as per in
			// currency format and set the same in movement bean
			movement.setAmountAsString(SwtUtil.formatCurrency(movement
					.getCurrencyCode(), movement.getAmount()));
			// set the position level as string to display
			movement.setPositionLevelAsString(movement.getPositionLevel()
					.toString());
			// put the position level,entity,sign,currency in request
			// to populate the
			// corresponding select box in screen.
			putPreadvicePositionLevelInReq(request, hostId, movement.getId()
					.getEntityId());
			putEntityListInReq(request);
			putCurrencyFullAccessListInReq(request, movement.getId()
					.getHostId(), movement.getId().getEntityId());
			putSignListInReq(request);
			// get the collection of movement field which has assigned to
			// corresponding entity id
			collMiscParams = CacheManager.getInstance().getMiscParams(
					"MOVEMENTFIELD", movForm.getId().getEntityId());
			// Instantiate the editable flag array
			editFlagArr = new Hashtable<String, Integer>();
			// get the all editable flag for movement field and set it
			// into request
			editFlagArr = ((MovementManager) (SwtUtil
					.getBean("movementManager"))).getEditFlagDetails(hostId,
					movForm.getId().getEntityId(), movement.getInputSource());
			putEditFlagsInRequest(request, editFlagArr, movement
					.getInputSource());
			// put description in request for corresponding select box element
			// which are used to be displayed in adjacent
			putDescriptionsInRequest(request, movement);
			// set the movement bean in dyna form and request which used to
			// display the details for movement on screen
			setMovement(movement);
			request.setAttribute("movement", movement);
			// set the attribute to identify which screen is opened
			request.setAttribute("methodName", "success");
			request.setAttribute("inputscreen", "update");
			// if open flag is null for movement then set 'N' flag and set
			// the same in request
			if (SwtUtil.isEmptyOrNull(movement.getOpenFlag())) {
				movement.setOpenFlag("N");
			}
			request.setAttribute("openFlag", movement.getOpenFlag());
			if (!movement.getMatchStatus().equals("A")) {
				// set the authorizeStatus attribute which is used to display
				// the authorize status for role
				request.setAttribute("authorizeStatus", preAdviceInputManager
						.getRoleDetails(SwtUtil.getCurrentUser(
								request.getSession()).getRoleId()));
			}
			// set the access level for menu in request
			getMenuEntityCurrGrpAccess(request, null, null);
			log.debug(this.getClass().getName()
					  + " - [displayPreadvice] - Existing");
			return getView("successJsp");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'displayPreadvice' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'displayPreadvice' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayPreadvice", PreAdviceInputAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			hostId = null;
			movement = null;
			movForm = null;
			collMiscParams = null;
			editFlagArr = null;
		}
	}

	/**
	 * This method is invoked while press enter/tab key on movement id text box
	 * in pre advice display screen after enter valid pre advice movement and
	 * used to display the details of entered movement
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String search()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable declaration for Host Id
		String hostId = null;
		// Variable declaration for movementId
		String movementId = null;
		// Variable declaration for movement
		Movement movement = null;
		// Variable declaration for inputscreen
		String inputscreen = null;
		// Variable declaration for valueDate
		Date valueDate = null;
		// Variable declaration for cal
		Calendar valueDtCal = null;
		// Variable declaration for dyForm
		// DynaValidatorForm dynaForm = null;
		// to hold the current user's role
		String roleId = null;
		// To get Entity Access list
		Collection<EntityUserAccess> entityUserAccessList = null;
		// To iterate entity access list collection
		Iterator<EntityUserAccess> entityUserAccessItr = null;
		// To get EntityUserAccess
		EntityUserAccess entityUserAccess = null;
		// To Get Entity Access
		String currEntityAccess = null;
		try {
			log.debug(this.getClass().getName() + " - [search] - Entering");
			// Type casting ActionForm with // DynaValidatorForm
			// Gets Host id from following funtion
			hostId = CacheManager.getInstance().getHostId();
			// Gets movemntId from request if it null then set zero
			movementId = (!SwtUtil.isEmptyOrNull(request
					.getParameter("movementId")) ? request.getParameter(
					"movementId").trim() : "0");
			// empty the session details in session
			request.getSession().setAttribute("sessionNotesDetails", null);
			// get the movement details for given movement id from db
			movement = ((MovementManager) (SwtUtil.getBean("movementManager")))
					.getMovementDetails(hostId, new Long(movementId),
							new SystemInfo(), SwtUtil
									.getCurrentSystemFormats(request
											.getSession()));
			// if entered movement not in pre advice then empty the movement
			if ((movement != null)
				&& (movement.getInputSource() != null)
				&& !movement.getInputSource().equalsIgnoreCase(
					SwtConstants.MOVEMENT_SOURCE_PREADVICE)) {
				movement = null;
			}
			// Gets the role id
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// Get the role entity user access list
			entityUserAccessList = SwtUtil.getSwtMaintenanceCache()
					.getEntityAccessCollection(new RoleTO(roleId));
			// Set default value as no
			currEntityAccess = SwtConstants.NO;
			// Checks entity user access list
			if (movement != null && entityUserAccessList != null) {
				// Gets the iterator
				entityUserAccessItr = entityUserAccessList.iterator();
				while (entityUserAccessItr.hasNext()) {
					entityUserAccess = entityUserAccessItr.next();
					// Checks current entity id match with user accessible
					// entity list
					if (entityUserAccess.getEntityId().equals(
							movement.getId().getEntityId())) {
						currEntityAccess = SwtConstants.YES;
						break;
					} else {
						currEntityAccess = SwtConstants.NO;
					}
				}
				// Check currency access
				if (currEntityAccess == SwtConstants.YES
					&& SwtUtil.getSwtMaintenanceCache().getCurrencyAccess(
						roleId, movement.getId().getEntityId(),
						movement.getCurrencyCode()) == SwtConstants.CURRENCYGRP_NO_ACCESS) {
					currEntityAccess = SwtConstants.NO;
				}
			}
			// put the entity,sign list in request to populate the
			// corresponding select box in screen.
			putEntityListInReq(request);
			putSignListInReq(request);
			// Gets the value based on the movement Id
			if (movement != null && currEntityAccess.equals(SwtConstants.YES)) {
				// set the match description in request which is used to display
				// the movement match status in screen
				request
						.setAttribute("matchDesc", movement
								.getMatchStatusDesc());
				// convert the value date,position date, amount as per in
				// system format and set the same in movement bean
				movement.setValueDateAsString(SwtUtil.formatDate(movement
						.getValueDate(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue()));
				movement.setPostingDateAsString((SwtUtil.formatDate(movement
						.getPostingDate(), SwtUtil.getCurrentSystemFormats(
						request.getSession()).getDateFormatValue())));
				movement.setAmountAsString(SwtUtil.formatCurrency(movement
						.getCurrencyCode(), movement.getAmount()));
				movement.setPositionLevelAsString(movement.getPositionLevel()
						.toString());
				// if movement has notes then set flag to denote it
				request.setAttribute("hasNotes", movement.getHasNotes().trim()
						.equalsIgnoreCase(SwtConstants.YES) ? "Y" : "N");
				// if open flag is null for movement then set 'N' flag and set
				// the same in request
				if (SwtUtil.isEmptyOrNull(movement.getOpenFlag())) {
					movement.setOpenFlag("N");
				}
				request.setAttribute("openFlag", movement.getOpenFlag());
				// set the attribute to currGrpAccess to enable/disable button
				// based on access
				roleId = ((CommonDataManager) request.getSession()
						.getAttribute(SwtConstants.CDM_BEAN)).getUser()
						.getRoleId();
				request.setAttribute("currGrpAccess", SwtUtil
						.getSwtMaintenanceCache().getCurrencyAccess(roleId,
								movement.getId().getEntityId(),
								movement.getCurrencyCode()));
				// converting value date in the same format as that of today
				// date
				valueDtCal = Calendar.getInstance();
				valueDtCal.setTime(movement.getValueDate());
				/**
				 * set the empty value for hour,hour of
				 * day,minute,second,millisecond for value date.
				 */
				valueDtCal.set(Calendar.HOUR, 0);
				valueDtCal.set(Calendar.HOUR_OF_DAY, 0);
				valueDtCal.set(Calendar.MINUTE, 0);
				valueDtCal.set(Calendar.SECOND, 0);
				valueDtCal.set(Calendar.MILLISECOND, 0);
				valueDate = valueDtCal.getTime();
				// perform comparison between the value date and system
				// date and based on
				// comparison set the value date index
				if (valueDate.compareTo(SwtUtil.getSystemDatewithoutTime()) <= 0) {
					request.setAttribute("valueDateInd", "1");
				} else {
					request.setAttribute("valueDateInd", "0");
				}
				// used to populate the position level collection in position
				// level
				// combo box
				putPreadvicePositionLevelInReq(request, hostId, movement
						.getId().getEntityId());
				// put description in request for corresponding select box
				// element
				// which are used to be displayed in adjacent
				putDescriptionsInRequest(request, movement);
				// set the access status for button
				putSaveButtonStatus(request, movement.getId().getEntityId(),
						movement.getCurrencyCode());
				// set the movement bean in form to display the value in screen.
				setMovement(movement);
				// To denote the screen idenitifier
				inputscreen = "display";
				// put flag for delete button to enable/disable based on the
				// access
				putDeleteButtonStatus(request, movement.getId().getEntityId(),
						movement.getCurrencyCode(), movement.getMatchStatus());
			} else {
				// set the identifier for which screen should be opened.
				inputscreen = "search";
				// empty the form details
				setMovement(new Movement());
			}
			// canclestatus is use to set as a flag which indicates the click of
			// cancle button so remove the notes from session and set a fresh
			// movement
			// to the dynaform.
			if ((request.getParameter("cancleStatus") != null)
				&& (request.getParameter("cancleStatus").trim().length() > 0)) {
				request.getSession().setAttribute("sessionNotesDetails", null);
				setMovement(new Movement());
			}
			// if entered movement id is not in the database then set the popup
			// msg to be showed for user
			if (!(movementId.equalsIgnoreCase("0")) && (movement == null)) {
				request.setAttribute("search", "true");
				request
						.setAttribute("searchStatus",
								"preadvice.alert.notfound");
			} else if (!(movementId.equalsIgnoreCase("0"))
					   && currEntityAccess.equals(SwtConstants.NO)) {
				request.setAttribute("search", "true");
				request
						.setAttribute("searchStatus",
								"preAdvice.alert.noAccess");
			}
			// set the identifier for which screen is opened in request
			request.setAttribute("inputscreen", inputscreen);
			request.setAttribute("methodName", "search");
			setMovement(movement);
			// set the access for menu ,entity, currency group
			getMenuEntityCurrGrpAccess(request, null, null);
			log.debug(this.getClass().getName() + " - [search] - Existing");
			return getView("search");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'search' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'search' method : "
						   + exp.getMessage());
			// set the error status to user about problem in searching movement
			request.setAttribute("inputscreen", "search");
			request.setAttribute("error", "true");
			request.setAttribute("errorStatus",
					"Problem in searching Movement Id : " + movementId
					+ " 'Please enter valid Id.'");
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "search", PreAdviceInputAction.class), request, "");
			return getView("search");
		} finally {
			// nullify objects
			hostId = null;
			movementId = null;
			movement = null;
			inputscreen = null;
			valueDate = null;
			valueDtCal = null;
			roleId = null;
			entityUserAccessList = null;
			entityUserAccessItr = null;
			entityUserAccess = null;
			currEntityAccess = null;
		}
	}

	/**
	 * This Method is used to diplay Input Authorise SelectionList to the
	 * screen.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String displayInputAuthorise() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		// Local variable declaration
		// String variable to hold hostId
		String hostId = null;
		// String variable to hold entityId
		String entityId = null;
		// String variable to hold currency group
		String currencyGroup = null;
		// Movement Instance
		Movement movement = null;
		// DynaValidatorForm Instance
		// DynaValidatorForm dyForm = null;
		// User Instance
		User user = null;
		/* Variable Declaration for calledFrom */
		String calledFrom = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [displayInputAuthorise()] - Entry");
			// Get form instance
			// Get movement form
			movement = (Movement) getMovement();
			// read host Id
			// Modified for mantis 1443
			if (!SwtUtil.isEmptyOrNull(request.getParameter("hostId")))
				hostId = request.getParameter("hostId");
			else
				hostId = CacheManager.getInstance().getHostId();

			// get currenct user
			user = SwtUtil.getCurrentUser(request.getSession());
			// read entity from request
			entityId = request.getParameter("selectedEntityId");
			// if entity from request is null get user default entity
			entityId = (((entityId != null) && (entityId.trim().length() > 0)) ? entityId
					: SwtUtil.getUserCurrentEntity(request.getSession()));
			// set entity in bean
			movement.getId().setEntityId(entityId);
			// TODO check // set value date as String
			movement.setValueDateAsString(SwtUtil.getSystemDateString());
			// set currency group list
			putCurrencyGroupListInReq(request, hostId, entityId);
			// get selected currency group id from request
			/*
			 * if screen is excuted from genric than append selectedCurrencyGroup from currencyGroup of the selected currencyId
			 */
			calledFrom = request.getParameter("calledFrom");
			if(calledFrom!=null&&calledFrom.equals("generic")){

				currencyGroup =  SwtUtil.getCurrencyGroup(entityId,request
						.getParameter("currencyId" ) );
			}else {
				currencyGroup = request.getParameter("currencyGroup");
			}
			// Condition to check selected currency group is null
			if (SwtUtil.isEmptyOrNull(currencyGroup)) {
				// selected user default currency group
				currencyGroup = user.getCurrentCcyGrpId();
			}
			// Condition to check currency group os no access
			if (SwtUtil.getSwtMaintenanceCache().getCurrencyGroupAccess(
					user.getRoleId(), entityId, currencyGroup) == SwtConstants.CURRENCYGRP_NO_ACCESS) {
				// Set All as currency group
				currencyGroup = "All";
			}
			// set currency group in movement
			movement.setCurrencyCode(currencyGroup);
			// set entity list in request
			putEntityListInReq(request);
			// set authoize and referred details in request
			putAllInputAuthoriseList(request, hostId, entityId, currencyGroup,
					user.getId().getUserId());
			// set form details
			setMovement(movement);
			// bind tab info
			bindTabInfoInRequest(request);
			// set details in request
			request.setAttribute("showMovementId", "yes");
			request.setAttribute("methodName", "displayInputAuthorise");
			SwtUtil.getMenuEntityCurrGrpAccessWithoutCurrency(request,
					entityId, currencyGroup);
			request.setAttribute("lastRefTime", SwtUtil.getLastRefTimeOnGMTOffset(request,
					entityId));
			return getView("inputAuthorise");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'displayInputAuthorise' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");

			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'displayInputAuthorise' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayInputAuthorise", PreAdviceInputAction.class),
					request, "");

			return getView("fail");
		} finally {
			/* null the objects created already. */
			log.debug(this.getClass().getName()
					  + " - [displayInputAuthorise()] - Exit");
			hostId = null;
			entityId = null;
			currencyGroup = null;
		}
	}

	/**
	 * This method is used to put Input Authorize Selection list into the
	 * request that is further displayed on the UI.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param currencyGroup
	 * @param userId
	 * @return
	 * @throws SwtException
	 */
	private void putAllInputAuthoriseList(HttpServletRequest request,
										  String hostId, String entityId, String currencyGroup, String userId)
			throws SwtException {
		// Methods local variable declaration
		// String to hold selected tab,it returns 0 if the user selected the
		// first tab
		String selectedTabIndex = null;
		// Variable to hold tab
		int tabIndicator = 1;
		// String to hold dateformat
		String dateFormat = null;
		// String to hold today date
		String todayDate = null;
		// Collection instance to hold input authorize list
		Collection<InputAuthoriseSelection> inputAuthoriseList = null;
		// String to hold date to display in tab
		String dateToDisplay = null;
		try {
			log.debug(this.getClass().getName()
					  + "-[putAllInputAuthoriseList]-Entry");

			// Get selected tab index from request
			selectedTabIndex = request.getParameter("selectedTabIndex");
			// Condition to check selectedTabIndex is not null
			if (!SwtUtil.isEmptyOrNull(selectedTabIndex)) {
				// get selected tab index
				tabIndicator = Integer.valueOf(selectedTabIndex).intValue();
			}
			// get current date format from session
			dateFormat = SwtUtil.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue();
			// get sysparams date with offset time as string with date format
			todayDate = SwtUtil.formatDate(SwtUtil
					.getSysParamDateWithEntityOffset(entityId), dateFormat);
			// Condition to check todayDate is not null
			if (!SwtUtil.isEmptyOrNull(todayDate)) {
				// get date / month or month/date to display in tab
				dateToDisplay = todayDate.substring(0, 5);
			}

			// switch tabIndicator to gets elected tab details
			switch (tabIndicator) {
				// selected tab is current date with offset time
				case 1: {
					// get authorize list for system date with offset date
					inputAuthoriseList = preAdviceInputManager
							.getInputAuthoriseListToday(hostId, entityId,
									currencyGroup, "1", userId, todayDate,
									dateFormat);

					break;
				}
				// selected tab is ALL
				case 2: {
					// get authorize list for All days
					inputAuthoriseList = preAdviceInputManager
							.getInputAuthoriseListToday(hostId, entityId,
									currencyGroup, EMPTYSTRING, userId,
									EMPTYSTRING, dateFormat);

					break;
				}
			}

			// set authorize list in request
			request.setAttribute("inputAuthoriseListToday",
					((inputAuthoriseList != null) ? inputAuthoriseList
							: new ArrayList<InputAuthoriseSelection>()));
			// set date to display in request
			request.setAttribute("dateToDisplay", dateToDisplay);

			log
					.debug("Exit PreAdviceInputAction.putAllInputAuthoriseList method");
		} catch (Exception exp) {
			log.error("Exception caught in " + this.getClass().getName()
					  + "-[putAllInputAuthoriseList]-" + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putAllInputAuthoriseList", this.getClass());
		} finally {
			log.debug(this.getClass().getName()
					  + "-[putAllInputAuthoriseList]-Exit");
		}
	}

	/**
	 * This method is used to set hostId into request object.
	 *
	 * @param request
	 * @return string hostId
	 * @throws SwtException
	 */
	private String putHostIdListInReq(HttpServletRequest request)
			throws SwtException {
		log.debug("Inside PreAdviceInputAction.putHostIdListInReq method");

		return CacheManager.getInstance().getHostId();
	}

	/**
	 * This method is used to put associated CurrencyGroup list into the request
	 * that is further displayed on the UI.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 */
	private void putCurrencyGroupListInReq(HttpServletRequest request,
										   String hostId, String entityId) throws SwtException {
		log
				.debug("Inside PreAdviceInputAction.putCurrencyGroupListInReq method");

		Collection currencyGroupList = new ArrayList();

		CurrencyGroup currencyGroup = new CurrencyGroup();

		String defaultCurrencyGroup = EMPTYSTRING;

		String roleId = preAdviceInputManager.getUserDetails(hostId, SwtUtil
				.getCurrentUserId(request.getSession()));

		Collection groupList = SwtUtil.getSwtMaintenanceCache()
				.getCurrencyGroupViewORFullAcess(roleId, entityId);

		if ((groupList != null) && (groupList.size() > 0)) {
			currencyGroupList.add(new LabelValueBean(SwtConstants.ALL_LABEL,
					SwtConstants.ALL_VALUE));
		}
		Iterator itGroupList = groupList.iterator();

		while (itGroupList.hasNext()) {
			EntityCurrencyGroupAccess entityCurrencyGroupAccess = (EntityCurrencyGroupAccess) itGroupList
					.next();

			currencyGroupList.add(new LabelValueBean(entityCurrencyGroupAccess
					.getCurrencyGroupName(), entityCurrencyGroupAccess
					.getCurrencyGroupId()));
		}

		request.setAttribute("currencyGroupList", currencyGroupList);

		log.debug("Exit PreAdviceInputAction.putCurrencyGroupListInReq method");

		// return defaultCurrencyGroup;
	}

	/**
	 * This method is used to set parameters for Tab info to the request.
	 *
	 * @param request
	 */
	private void bindTabInfoInRequest(HttpServletRequest request) {
		String selectedTabIndex = request.getParameter("selectedTabIndex");
		String selectedTabName = request.getParameter("selectedTabName");

		if (SwtUtil.isEmptyOrNull(selectedTabIndex)) {
			request.setAttribute("selectedTabIndex", "1");
			request.setAttribute("selectedTabName", "MatchQueueTodayParent");
		} else {
			request.setAttribute("selectedTabIndex", selectedTabIndex);
			request.setAttribute("selectedTabName", selectedTabName);
		}
	}

	/**
	 * This method is called on the click of 'Save' button on PreAdvice Input
	 * screen and is used to save the given movement details to the database.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String savePreAdvice()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable to get the host id
		String hostId = null;
		// variable to get the entity id
		String entityId = null;
		// variable to get the currency code
		String currencyCode = null;
		// variable to get the movement type
		String movType = null;
		// variable to get the user id
		String userId = null;
		// variable to get the role id
		String roleId = null;
		// variable to get the authorize status
		String authorizeStatus = null;
		// variable to get the matchDriverOperation
		String matchDriverOperation = null;
		// variable to get the savedMovementId
		String savedMovementId = null;
		// variable to get the saveStatus
		String saveStatus = null;
		// variable to get the form
		// DynaValidatorForm dynaForm = null;
		// variable to get the movement
		Movement movement = null;
		// variable to get the system format
		SystemFormats sysFormat = null;
		// Instance the Entity object
		Entity entity = null;
		// boolean to hold position flag
		boolean positionFlag;
		// variable to get the iterator value
		Iterator<MovementNote> itrMovementNote = null;
		// variable to get the sessionNotesDetails
		Collection<MovementNote> sessionNotesDetails = null;
		// variable to get the matchDriver
		MatchDriver matchDriver = null;
		// variable to get the movementManager
		MovementManager movementManager = null;
		// variable to get the movementNote
		MovementNote movementNote = null;
		// variable to get the token
		String token = null;
		try {
			log.debug(this.getClass().getName() + " - [savePreAdvice] - Entry");
			// get the form and assign to // DynaValidatorForm
			// get movement object from form
			movement = (Movement) getMovement();
			// get current system formats to format the posting date
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get host id for application and set value in movement bean
			hostId = CacheManager.getInstance().getHostId();
			movement.getId().setHostId(hostId);
			// if form movement bean entity id is null then get the current
			// entity id from session and set it into movement bean
			entityId = ((SwtUtil.isEmptyOrNull(movement.getId().getEntityId())) ? SwtUtil
					.getUserCurrentEntity(request.getSession())
					: movement.getId().getEntityId());
			movement.getId().setEntityId(entityId);
			// if form movement bean movement type is null then set the default
			// value as 'c'
			movType = ((SwtUtil.isEmptyOrNull(movement.getMovementType())) ? SwtConstants.MOVEMENT_TYPE_CASH
					: movement.getMovementType());
			// if form movement bean currency code is null then set the default
			// value as 'All'
			currencyCode = ((SwtUtil.isEmptyOrNull(movement.getCurrencyCode())) ? DEFAULTSEARCHSTRING
					: movement.getCurrencyCode());
			// get current used id to get the role for user
			userId = SwtUtil.getCurrentUserId(request.getSession());
			roleId = preAdviceInputManager.getUserDetails(hostId, userId);
			// get authorize status for given role
			authorizeStatus = preAdviceInputManager.getRoleDetails(roleId);
			// set default value for extract status,book code avail while saving
			// the pre advice movement
			movement.setExtractStatus("E");
			movement.setBookCodeAvail("N");
			// if role having the authorize flag then set the match status as
			// 'authorise' otherwise 'outstanding'
			movement
					.setMatchStatus((((authorizeStatus != null) && authorizeStatus
							.equalsIgnoreCase("Y")) ? SwtConstants.AUTHORISE_STATUS
							: SwtConstants.OUTSTANDING_STATUS));
			// if role having the authorize flag then set to match as
			// 'N' otherwise 'Y'
			movement.setToMatch((((authorizeStatus != null) && authorizeStatus
					.equalsIgnoreCase("Y")) ? SwtConstants.NO
					: SwtConstants.YES));
			// set essential information like input date,input role,input
			// user,input source,initial predict status,postion level,update
			// date,update user to movement bean for saving the movement
			movement.setInputDate(SwtUtil.getSystemDatewithTime());
			movement.setInputRole(roleId);
			movement.setInputUser(userId);
			movement.setInputSource(SwtConstants.MOVEMENT_SOURCE_PREADVICE);

			if (movement.getPredictStatus() != null) {
				movement.setInitialPredStatus(movement.getPredictStatus());
			}
			movement.setPositionLevel(new Integer(movement
					.getPositionLevelAsString()));
			movement.setUpdateDate(SwtUtil.getSystemDatewithTime());
			movement.setUpdateUser(userId);
			// get sessionNotesDetails from session to save the movement note
			// details
			sessionNotesDetails = (Collection<MovementNote>) request
					.getSession().getAttribute("sessionNotesDetails");
			// check whether sessionNotesDetails is not null, then iterate.
			if (sessionNotesDetails != null) {
				itrMovementNote = sessionNotesDetails.iterator();
				while (itrMovementNote.hasNext()) {
					movementNote = (MovementNote) (itrMovementNote.next());
					movementNote.getId().setHostId(hostId);
					movementNote.getId().setEntityId(entityId);
				}
			}
			// get match driver value for given host id,entity id,currency code
			matchDriver = preAdviceInputManager.existMatchDriver(hostId,
					entityId, currencyCode);
			// check whether matchDriver is null, then set default value.
			if (matchDriver == null) {
				// Instantiate the matchDriver
				matchDriver = new MatchDriver();
				// set the host,entity,currency ,movement flag,processing flag
				// in match driver which is to be saved along with movement
				matchDriver.getId().setHostId(hostId);
				matchDriver.getId().setEntityId(entityId);
				matchDriver.getId().setCurrencyCode(movement.getCurrencyCode());
				matchDriver.setNewMoveFlag("Y");
				matchDriver.setProcessingFlag("N");
				// indicate the save operation for match driver
				matchDriverOperation = "save";
			} else {
				matchDriver.setNewMoveFlag("Y");
				// indicate the update operation for match driver
				matchDriverOperation = "update";
			}
			// set the default open status flag,notes count for movement
			movement.setOpenFlag(SwtConstants.NO);
			movement.setNotesCount(0);
			// if posting date is not null then format the date and set the same
			if (!SwtUtil.isEmptyOrNull(movement.getPostingDateAsString())) {
				movement.setPostingDate((SwtUtil.parseDate(movement
						.getPostingDateAsString(), sysFormat
						.getDateFormatValue())));
			}
			// create entity bean and set the corresponding value to get the
			// external position level flag
			entity = new Entity();
			entity.getId().setHostId(hostId);
			entity.getId().setEntityId(entityId);
			entity.setExternalBalance(movement.getPositionLevel());
			// Instantiate the movement manager
			movementManager = (MovementManager) SwtUtil
					.getBean("movementManager");
			// Check whether the given position level is External balance
			positionFlag = movementManager.checkExternalPositionLevel(entity);
			if (positionFlag) {
				// If not exist set the value as E
				movement.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
			} else {
				// If exist set value as I
				movement.setExtBalStatus(SwtConstants.EXT_BAL_INC);
			}
			movement.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
			// save the movement details and return the same movement id
			savedMovementId = preAdviceInputManager.saveMovementDetails(
					movement, matchDriver, matchDriverOperation,
					sessionNotesDetails, new SystemInfo(), SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			// remove attribute for sessionNotesDetails from session
			request.getSession().removeAttribute("sessionNotesDetails");
			// set the alert message for created movement
			saveStatus = SwtUtil.getMessage("label.preadviceCreatedWithMovementID",request)+" "
						 + savedMovementId;
			// if role having the authorize flag then change the alert message
			// for created movement
			if (authorizeStatus.equalsIgnoreCase("Y")) {
				saveStatus = saveStatus
							 + SwtUtil.getMessage("label.movementHasBeenPlaced",request);
			}
			// Prepares the screen data for display after save method get
			// called.
			prepareScreenDataSave(hostId, entityId, roleId,
					currencyCode, movType);
			// set the saveStatus in request for displaying alert msg to user
			// after saving the pre advice movement
			request.setAttribute("saveStatus", saveStatus);
			// used to enable the save button after generating movement
			request.setAttribute("save", "true");
			/*
			 * if token is valid re assign token value in session attribute and
			 * create movement.
			 */
//			token = generateToken(request);
			//"org.apache.struts.action.TOKEN"
//			request.getSession().setAttribute(Globals.TRANSACTION_TOKEN_KEY,
//					token);
			token  = TokenHelper.setToken();
			// Note: setToken() already sets both TOKEN_NAME_FIELD and the token value in session

			//return getView("success");
			return sendSaveResponse(saveStatus,hostId, new Long(savedMovementId));

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'savePreAdvice' method : "
						   + swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("success");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'savePreAdvice' method : "
						   + exp.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(exp, "savePreAdvice",
							PreAdviceInputAction.class), request, ""));
			return getView("success");
		} finally {
			/* null the objects created already. */
			hostId = null;
			entityId = null;
			currencyCode = null;
			movType = null;
			userId = null;
			roleId = null;
			authorizeStatus = null;
			matchDriverOperation = null;
			savedMovementId = null;
			saveStatus = null;
			movement = null;
			sysFormat = null;
			entity = null;
			itrMovementNote = null;
			sessionNotesDetails = null;
			matchDriver = null;
			movementManager = null;
			movementNote = null;
			token = null;
			log.debug(this.getClass().getName() + " - [savePreAdvice] - Exit");
		}
	}

	public String sendSaveResponse(String Status, String hostId, Long movmentId) throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		// variable to get the movementManager
		MovementManager movementManager = null;
		// Instantiate the movement manager
		movementManager = (MovementManager) SwtUtil
				.getBean("movementManager");
		// debug message
		log.debug(this.getClass().getName()
				  + " - [ sendSaveResponse ] - Entry");


		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
		Movement addedMvt=movementManager.getMovementDetails(hostId,movmentId);
		responseConstructor = new SwtResponseConstructor();

		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "PreAdviceInput";
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
		xmlWriter.addAttribute(SwtConstants.CURRUSER, userId);
		xmlWriter.addAttribute("status", Status);
		xmlWriter.startElement("preAdviceInput");
		xmlWriter.clearAttribute();
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement("preAdviceInput");

		request.setAttribute("data", xmlWriter.getData());
		return getView("data");
	}


	/**
	 * This method is called on the click of 'Save' button on PreAdvice Input
	 * screen and is used to save the given movement details to the database.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String save()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// variable to get the host id
		String hostId = null;
		// variable to get the entity id
		String entityId = null;
		// variable to get the currency code
		String currencyCode = null;
		// variable to get the movement type
		String movType = null;
		// variable to get the user id
		String userId = null;
		// variable to get the role id
		String roleId = null;
		// variable to get the authorize status
		String authorizeStatus = null;
		// variable to get the matchDriverOperation
		String matchDriverOperation = null;
		// variable to get the savedMovementId
		String savedMovementId = null;
		// variable to get the saveStatus
		String saveStatus = null;
		// variable to get the form
		// DynaValidatorForm dynaForm = null;
		// variable to get the movement
		Movement movement = null;
		// variable to get the system format
		SystemFormats sysFormat = null;
		// Instance the Entity object
		Entity entity = null;
		// boolean to hold position flag
		boolean positionFlag;
		// variable to get the iterator value
		Iterator<MovementNote> itrMovementNote = null;
		// variable to get the sessionNotesDetails
		Collection<MovementNote> sessionNotesDetails = null;
		// variable to get the matchDriver
		MatchDriver matchDriver = null;
		// variable to get the movementManager
		MovementManager movementManager = null;
		// variable to get the movementNote
		MovementNote movementNote = null;
		// variable to get the token
		String token = null;
		try {
			log.debug(this.getClass().getName() + " - [save] - Entry");
			// get the form and assign to // DynaValidatorForm
			/*
			 * check whether token is valid, when refresh the pre advice input
			 * screen. if token is valid then only create movement, otherwise
			 * retain the pre advice input screen.
			 */

//			if (!isTokenValid(request)) {
			if(!TokenHelper.validToken()) {
				// Instantiate the new movement and set into form to avoid of
				// retaining the form details while pressing F5
				movement = new Movement();
				setMovement(movement);
				// call display method to avoid save operation while pressing F5
				// after form submission
				return display();
			}
			// get movement object from form
			movement = (Movement) getMovement();
			// get current system formats to format the posting date
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// get host id for application and set value in movement bean
			hostId = CacheManager.getInstance().getHostId();
			movement.getId().setHostId(hostId);
			// if form movement bean entity id is null then get the current
			// entity id from session and set it into movement bean
			entityId = ((SwtUtil.isEmptyOrNull(movement.getId().getEntityId())) ? SwtUtil
					.getUserCurrentEntity(request.getSession())
					: movement.getId().getEntityId());
			movement.getId().setEntityId(entityId);
			// if form movement bean movement type is null then set the default
			// value as 'c'
			movType = ((SwtUtil.isEmptyOrNull(movement.getMovementType())) ? SwtConstants.MOVEMENT_TYPE_CASH
					: movement.getMovementType());
			// if form movement bean currency code is null then set the default
			// value as 'All'
			currencyCode = ((SwtUtil.isEmptyOrNull(movement.getCurrencyCode())) ? DEFAULTSEARCHSTRING
					: movement.getCurrencyCode());
			// get current used id to get the role for user
			userId = SwtUtil.getCurrentUserId(request.getSession());
			roleId = preAdviceInputManager.getUserDetails(hostId, userId);
			// get authorize status for given role
			authorizeStatus = preAdviceInputManager.getRoleDetails(roleId);
			// set default value for extract status,book code avail while saving
			// the pre advice movement
			movement.setExtractStatus("E");
			movement.setBookCodeAvail("N");
			// if role having the authorize flag then set the match status as
			// 'authorise' otherwise 'outstanding'
			movement
					.setMatchStatus((((authorizeStatus != null) && authorizeStatus
							.equalsIgnoreCase("Y")) ? SwtConstants.AUTHORISE_STATUS
							: SwtConstants.OUTSTANDING_STATUS));
			// if role having the authorize flag then set to match as
			// 'N' otherwise 'Y'
			movement.setToMatch((((authorizeStatus != null) && authorizeStatus
					.equalsIgnoreCase("Y")) ? SwtConstants.NO
					: SwtConstants.YES));
			// set essential information like input date,input role,input
			// user,input source,initial predict status,postion level,update
			// date,update user to movement bean for saving the movement


			movement.setInputDate(SwtUtil.getSystemDatewithTime());


			movement.setInputRole(roleId);
			movement.setInputUser(userId);
			movement.setInputSource(SwtConstants.MOVEMENT_SOURCE_PREADVICE);

			if (movement.getPredictStatus() != null) {
				movement.setInitialPredStatus(movement.getPredictStatus());
			}
			movement.setPositionLevel(new Integer(movement
					.getPositionLevelAsString()));
			movement.setUpdateDate(SwtUtil.getSystemDatewithTime());
			movement.setUpdateUser(userId);
			// get sessionNotesDetails from session to save the movement note
			// details
			sessionNotesDetails = (Collection<MovementNote>) request
					.getSession().getAttribute("sessionNotesDetails");
			// check whether sessionNotesDetails is not null, then iterate.
			if (sessionNotesDetails != null) {
				itrMovementNote = sessionNotesDetails.iterator();
				while (itrMovementNote.hasNext()) {
					movementNote = (MovementNote) (itrMovementNote.next());
					movementNote.getId().setHostId(hostId);
					movementNote.getId().setEntityId(entityId);
				}
			}
			// get match driver value for given host id,entity id,currency code
			matchDriver = preAdviceInputManager.existMatchDriver(hostId,
					entityId, currencyCode);
			// check whether matchDriver is null, then set default value.
			if (matchDriver == null) {
				// Instantiate the matchDriver
				matchDriver = new MatchDriver();
				// set the host,entity,currency ,movement flag,processing flag
				// in match driver which is to be saved along with movement
				matchDriver.getId().setHostId(hostId);
				matchDriver.getId().setEntityId(entityId);
				matchDriver.getId().setCurrencyCode(movement.getCurrencyCode());
				matchDriver.setNewMoveFlag("Y");
				matchDriver.setProcessingFlag("N");
				// indicate the save operation for match driver
				matchDriverOperation = "save";
			} else {
				matchDriver.setNewMoveFlag("Y");
				// indicate the update operation for match driver
				matchDriverOperation = "update";
			}
			// set the default open status flag,notes count for movement
			movement.setOpenFlag(SwtConstants.NO);
			movement.setNotesCount(0);
			// if posting date is not null then format the date and set the same
			if (!SwtUtil.isEmptyOrNull(movement.getPostingDateAsString())) {
				movement.setPostingDate((SwtUtil.parseDate(movement
						.getPostingDateAsString(), sysFormat
						.getDateFormatValue())));
			}
			// create entity bean and set the corresponding value to get the
			// external position level flag
			entity = new Entity();
			entity.getId().setHostId(hostId);
			entity.getId().setEntityId(entityId);
			entity.setExternalBalance(movement.getPositionLevel());
			// Instantiate the movement manager
			movementManager = (MovementManager) SwtUtil
					.getBean("movementManager");
			// Check whether the given position level is External balance
			positionFlag = movementManager.checkExternalPositionLevel(entity);
			if (positionFlag) {
				// If not exist set the value as E
				movement.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
			} else {
				// If exist set value as I
				movement.setExtBalStatus(SwtConstants.EXT_BAL_INC);
			}
			movement.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
			// save the movement details and return the same movement id
			savedMovementId = preAdviceInputManager.saveMovementDetails(
					movement, matchDriver, matchDriverOperation,
					sessionNotesDetails, new SystemInfo(), SwtUtil
							.getCurrentSystemFormats(request.getSession()));
			// remove attribute for sessionNotesDetails from session
			request.getSession().removeAttribute("sessionNotesDetails");
			// set the alert message for created movement
			saveStatus = SwtUtil.getMessage("label.preadviceCreatedWithMovementID",request)+" "
						 + savedMovementId;
			// if role having the authorize flag then change the alert message
			// for created movement
			if (authorizeStatus.equalsIgnoreCase("Y")) {
				saveStatus = saveStatus
							 + SwtUtil.getMessage("label.movementHasBeenPlaced",request);
			}
			// Prepares the screen data for display after save method get
			// called.
			prepareScreenDataSave(hostId, entityId, roleId,
					currencyCode, movType);
			// set the saveStatus in request for displaying alert msg to user
			// after saving the pre advice movement
			request.setAttribute("saveStatus", saveStatus);
			// used to enable the save button after generating movement
			request.setAttribute("save", "true");
			/*
			 * if token is valid re assign token value in session attribute and
			 * create movement.
			 */
//			token = generateToken(request);
//			request.getSession().setAttribute(Globals.TRANSACTION_TOKEN_KEY,
//					token);

			token  = TokenHelper.setToken();
			// Note: setToken() already sets both TOKEN_NAME_FIELD and the token value in session

			return getView("successJsp");


		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'save' method : "
						   + swtexp.getMessage());
			saveErrors(request, SwtUtil.logException(swtexp, request, ""));
			return getView("successJsp");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'save' method : "
						   + exp.getMessage());
			saveErrors(request, SwtUtil.logException(SwtErrorHandler
					.getInstance().handleException(exp, "save",
							PreAdviceInputAction.class), request, ""));
			return getView("successJsp");
		} finally {
			/* null the objects created already. */
			hostId = null;
			entityId = null;
			currencyCode = null;
			movType = null;
			userId = null;
			roleId = null;
			authorizeStatus = null;
			matchDriverOperation = null;
			savedMovementId = null;
			saveStatus = null;
			movement = null;
			sysFormat = null;
			entity = null;
			itrMovementNote = null;
			sessionNotesDetails = null;
			matchDriver = null;
			movementManager = null;
			movementNote = null;
			token = null;
			log.debug(this.getClass().getName() + " - [save] - Exit");
		}
	}
	/**
	 * Prepares the screen data for display after save method get called.
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @param roleId
	 * @param currencyCode
	 * @param movType
	 */
	private void prepareScreenDataSave(String hostId, String entityId,
									   String roleId, String currencyCode, String movType)
			throws SwtException {
		// To hold the movement details
		Movement movement = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();

		try {
			log.debug(this.getClass().getName()
					  + " - [prepareScreenDataSave()] - Entering");
			// Instantiate the movement
			movement = new Movement();
			// set the entity, currency,movement type,value date details in
			// movement which is used to set these value in screen after save
			// operation has been completed
			movement.getId().setEntityId(entityId);
			movement.setCurrencyCode(currencyCode);
			movement.setMovementType(movType);
			movement.setValueDate(SwtUtil.getSystemDatewithoutTime());
			movement.setValueDateAsString(SwtUtil.formatDate(SwtUtil
					.getSysParamDateWithEntityOffset(entityId), SwtUtil
					.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue()));
			// put the position level,entity,currency,sign,
			// in request to populate the
			// corresponding select box in pre advice input screen.
			putPreadvicePositionLevelInReq(request, hostId, movement.getId()
					.getEntityId());
			putFullAcessEntityListInReq(request, roleId);
			putCurrencyFullAccessListInReq(request, hostId, entityId);
			putSignListInReq(request);
			// set the attribute for methodName in form
			request.setAttribute("methodName", "success");
			// set the attribute to identify which screen is opened
			request.setAttribute("inputscreen", "insert");
			// remove the notes details from session once save operation is
			// completed
			request.getSession().removeAttribute("sessionNotesDetails");
			// set the access for menu ,entity, currency group
			getMenuEntityCurrGrpAccess(request, null, null);
			// set the movement bean into form to display the form values.
			setMovement(movement);
			log.debug(this.getClass().getName()
					  + " - [prepareScreenDataSave()] - Existing");
		} catch (Exception e) {
			log
					.error("Exception Catch in PreAdviceInputAction.'prepareScreenDataSave' method : "
						   + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify object
			movement = null;
		}
	}

	/**
	 * This method is called while clicking the save button on pre advice change
	 * screen and used to update the pre advice movement details which are
	 * changed
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String updatePreAdvice()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold hostId
		String hostId = null;
		// Variable to hold userId
		String userId = null;
		// Variable to hold roleId
		String roleId = null;
		// Variable to hold authorizeStatus
		String authorizeStatus = null;
		// Movement instance to hold the form values
		Movement movement = null;
		// Movement instance to hold the database values
		Movement targetMovement = null;
		// SystemFormats instance
		SystemFormats sysFormat = null;
		// Dyna Validator form
		// DynaValidatorForm dynaForm = null;
		// Collection get the Notes details in session
		Collection<MovementNote> sessionNotesDetails = null;
		// used to iterate the notes collection
		Iterator<MovementNote> itrNotes = null;
		// MovementNote instance
		MovementNote movNote = null;
		// variable to get the saveStatus
		String updateStatus = null;

		try {
			log.debug(this.getClass().getName() + " - [updatePreAdvice] - Entry");
			// To get the Current system date format from SwtUtil
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Obtain action form
			// Get the movement object from dynavalidator form
			movement = (Movement) getMovement();
			// get the current host id
			hostId = CacheManager.getInstance().getHostId();
			// get the movement details for given movement id
			targetMovement = preAdviceInputManager.getMovement(hostId, movement.getId().getMovementId(), request);
			// get the current user id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the role id to get the authorise status
			roleId = preAdviceInputManager.getUserDetails(hostId, userId);
			// To get the authorize status preadvice input manager
			authorizeStatus = preAdviceInputManager.getRoleDetails(roleId);
			// set the currency code(which got from form) to target movement
			targetMovement.setCurrencyCode(movement.getCurrencyCode());
			if (authorizeStatus != null
				&& authorizeStatus.equalsIgnoreCase("Y")) {
				// update the authorize status in movement
				preAdviceInputManager.manageMovementAlert(targetMovement,
						targetMovement.getMatchStatus(),
						SwtConstants.AUTHORISE_STATUS);
				// set the match status,update user in target movement to update
				// the value in db
				targetMovement.setMatchStatus(SwtConstants.AUTHORISE_STATUS);
				targetMovement.setUpdateUser(userId);
			} else {
				// if there is no authorize then no need to set the update user
				targetMovement.setUpdateUserNeedUpdated(false);
			}
			// set the predict status,value date in target movement to update
			// the value in db
			targetMovement.setPredictStatus(movement.getPredictStatus());
			// Condition to check value date is null
			if (SwtUtil.isEmptyOrNull(movement.getValueDateAsString())) {
				// Set system date if value date is null
				targetMovement.setValueDate(SwtUtil.getSystemDatewithoutTime());
			} else {
				// set value date
				targetMovement.setValueDate(SwtUtil
						.parseDate(movement.getValueDateAsString(), sysFormat
								.getDateFormatValue()));

			}
			// set the form values for currency,sign,movement type,amount, open
			// flag,account id,reference,counter party,position level in target
			// movement to update the value in db
			targetMovement.setCurrencyCode(movement.getCurrencyCode());
			targetMovement.setSign(movement.getSign());
			targetMovement.setMovementType(movement.getMovementType());
			targetMovement.setAmount(SwtUtil.parseCurrency(movement
					.getAmountAsString(), sysFormat.getCurrencyFormat()));
			targetMovement.setOpenFlag(movement.getOpenFlag());
			targetMovement.setAccountId(movement.getAccountId());
			targetMovement.setReference1(movement.getReference1());
			targetMovement.setCounterPartyId(movement.getCounterPartyId());
			targetMovement
					.setCounterPartyText1(movement.getCounterPartyText1());
			targetMovement.setPositionLevel(new Integer(movement
					.getPositionLevelAsString().trim()));
			targetMovement.setExtractStatus("E");
			// set the flag for book code to denote the availability for book
			// code
			if (SwtUtil.isEmptyOrNull(movement.getBookCode())) {
				targetMovement.setBookCodeAvail("N");
			}
			targetMovement.setBookCode(movement.getBookCode());
			// get the note details from session and set the host id and entity
			// id which is to be saved along with movement
			sessionNotesDetails = (Collection<MovementNote>) request
					.getSession().getAttribute("sessionNotesDetails");
			// To check the collection for session notes details
			if (sessionNotesDetails != null) {
				// To iterator the session notes details collection
				itrNotes = sessionNotesDetails.iterator();
				while (itrNotes.hasNext()) {
					// To create the movement notes object
					movNote = (MovementNote) (itrNotes.next());
					movNote.getId().setHostId(hostId);
					movNote.getId().setEntityId(movement.getId().getEntityId());
				}
			}
			// set the notescount for movement on the form
			movement
					.setNotesCount(((movement.getNotesCount() != null) && (movement
																				   .getNotesCount() > 0)) ? movement.getNotesCount()
							: 0);
			// To Check the match party,product type,posting date and set these
			// into
			// target movement which would be saved in db
			if (!SwtUtil.isEmptyOrNull(movement.getMatchingParty()))
				targetMovement.setMatchingParty(movement.getMatchingParty());
			if (!SwtUtil.isEmptyOrNull(movement.getProductType()))
				targetMovement.setProductType(movement.getProductType());
			if ((movement.getPostingDateAsString() != null)
				&& !movement.getPostingDateAsString().equals("")) {
				targetMovement.setPostingDate((SwtUtil.parseDate(movement
						.getPostingDateAsString(), sysFormat
						.getDateFormatValue())));
			} else {
				targetMovement.setPostingDate(null);
			}
			// update the changed movement details
			preAdviceInputManager.updateMovementDetails(targetMovement,
					sessionNotesDetails);
			// empty the form details once update done
			//setMovement(new Movement()));
			// To set the Screen name for request to denote the screen
			// identifier
			request.setAttribute("inputscreen", "search");
			// To set the Method name for request
			request.setAttribute("methodName", "success");
			// set the alert message for created movement
			updateStatus = SwtUtil.getMessage("label.preadviceUpdated",request);
			// set the access for menu ,entity, currency group
			getMenuEntityCurrGrpAccess(request, null, null);
			log.debug(this.getClass().getName() + " - [updatePreAdvice] - Exit");
			//return getView("search");
			return sendSaveResponse(updateStatus,hostId, movement.getId().getMovementId());

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'updatePreAdvice' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'updatePreAdvice' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "updatePreAdvice", PreAdviceInputAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			hostId = null;
			userId = null;
			roleId = null;
			authorizeStatus = null;
			targetMovement = null;
			sysFormat = null;
			sessionNotesDetails = null;
			itrNotes = null;
			movNote = null;
		}
	}

	/**
	 * This method is called while clicking the save button on pre advice change
	 * screen and used to update the pre advice movement details which are
	 * changed
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */
	public String update()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Variable to hold hostId
		String hostId = null;
		// Variable to hold userId
		String userId = null;
		// Variable to hold roleId
		String roleId = null;
		// Variable to hold authorizeStatus
		String authorizeStatus = null;
		// Movement instance to hold the form values
		Movement movement = null;
		// Movement instance to hold the database values
		Movement targetMovement = null;
		// SystemFormats instance
		SystemFormats sysFormat = null;
		// Dyna Validator form
		// DynaValidatorForm dynaForm = null;
		// Collection get the Notes details in session
		Collection<MovementNote> sessionNotesDetails = null;
		// used to iterate the notes collection
		Iterator<MovementNote> itrNotes = null;
		// MovementNote instance
		MovementNote movNote = null;



		try {
			log.debug(this.getClass().getName() + " - [update] - Entry");
			// To get the Current system date format from SwtUtil
			sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			// Obtain action form
			// Get the movement object from dynavalidator form
			movement = (Movement) getMovement();
			// get the current host id
			hostId = CacheManager.getInstance().getHostId();
			// get the movement details for given movement id
			targetMovement = preAdviceInputManager.getMovement(hostId, movement
					.getId().getMovementId(), request);
			// get the current user id from session
			userId = SwtUtil.getCurrentUserId(request.getSession());
			// get the role id to get the authorise status
			roleId = preAdviceInputManager.getUserDetails(hostId, userId);
			// To get the authorize status preadvice input manager
			authorizeStatus = preAdviceInputManager.getRoleDetails(roleId);
			// set the currency code(which got from form) to target movement
			targetMovement.setCurrencyCode(movement.getCurrencyCode());
			if (authorizeStatus != null
				&& authorizeStatus.equalsIgnoreCase("Y")) {
				// update the authorize status in movement
				preAdviceInputManager.manageMovementAlert(targetMovement,
						targetMovement.getMatchStatus(),
						SwtConstants.AUTHORISE_STATUS);
				// set the match status,update user in target movement to update
				// the value in db
				targetMovement.setMatchStatus(SwtConstants.AUTHORISE_STATUS);
				targetMovement.setUpdateUser(userId);
			} else {
				// if there is no authorize then no need to set the update user
				targetMovement.setUpdateUserNeedUpdated(false);
			}
			// set the predict status,value date in target movement to update
			// the value in db
			targetMovement.setPredictStatus(movement.getPredictStatus());
			// Condition to check value date is null
			if (SwtUtil.isEmptyOrNull(movement.getValueDateAsString())) {
				// Set system date if value date is null
				targetMovement.setValueDate(SwtUtil.getSystemDatewithoutTime());
			} else {
				// set value date
				targetMovement.setValueDate(SwtUtil
						.parseDate(movement.getValueDateAsString(), sysFormat
								.getDateFormatValue()));

			}
			// set the form values for currency,sign,movement type,amount, open
			// flag,account id,reference,counter party,position level in target
			// movement to update the value in db
			targetMovement.setCurrencyCode(movement.getCurrencyCode());
			targetMovement.setSign(movement.getSign());
			targetMovement.setMovementType(movement.getMovementType());
			targetMovement.setAmount(SwtUtil.parseCurrency(movement
					.getAmountAsString(), sysFormat.getCurrencyFormat()));
			targetMovement.setOpenFlag(movement.getOpenFlag());
			targetMovement.setAccountId(movement.getAccountId());
			targetMovement.setReference1(movement.getReference1());
			targetMovement.setCounterPartyId(movement.getCounterPartyId());
			targetMovement
					.setCounterPartyText1(movement.getCounterPartyText1());
			targetMovement.setPositionLevel(new Integer(movement
					.getPositionLevelAsString().trim()));
			targetMovement.setExtractStatus("E");
			// set the flag for book code to denote the availability for book
			// code
			if (SwtUtil.isEmptyOrNull(movement.getBookCode())) {
				targetMovement.setBookCodeAvail("N");
			}
			targetMovement.setBookCode(movement.getBookCode());
			// get the note details from session and set the host id and entity
			// id which is to be saved along with movement
			sessionNotesDetails = (Collection<MovementNote>) request
					.getSession().getAttribute("sessionNotesDetails");
			// To check the collection for session notes details
			if (sessionNotesDetails != null) {
				// To iterator the session notes details collection
				itrNotes = sessionNotesDetails.iterator();
				while (itrNotes.hasNext()) {
					// To create the movement notes object
					movNote = (MovementNote) (itrNotes.next());
					movNote.getId().setHostId(hostId);
					movNote.getId().setEntityId(movement.getId().getEntityId());
				}
			}
			// set the notescount for movement on the form
			movement
					.setNotesCount(((movement.getNotesCount() != null) && (movement
																				   .getNotesCount() > 0)) ? movement.getNotesCount()
							: 0);
			// To Check the match party,product type,posting date and set these
			// into
			// target movement which would be saved in db
			if (!SwtUtil.isEmptyOrNull(movement.getMatchingParty()))
				targetMovement.setMatchingParty(movement.getMatchingParty());
			if (!SwtUtil.isEmptyOrNull(movement.getProductType()))
				targetMovement.setProductType(movement.getProductType());
			if ((movement.getPostingDateAsString() != null)
				&& !movement.getPostingDateAsString().equals("")) {
				targetMovement.setPostingDate((SwtUtil.parseDate(movement
						.getPostingDateAsString(), sysFormat
						.getDateFormatValue())));
			} else {
				targetMovement.setPostingDate(null);
			}
			// update the changed movement details
			preAdviceInputManager.updateMovementDetails(targetMovement,
					sessionNotesDetails);
			// empty the form details once update done
			//setMovement(new Movement()));
			// To set the Screen name for request to denote the screen
			// identifier
			request.setAttribute("inputscreen", "search");
			// To set the Method name for request
			request.setAttribute("methodName", "success");


			// set the access for menu ,entity, currency group
			getMenuEntityCurrGrpAccess(request, null, null);
			log.debug(this.getClass().getName() + " - [update] - Exit");
			return getView("search");


		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'update' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'update' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "update", PreAdviceInputAction.class), request, "");
			return getView("fail");
		} finally {
			// nullify objects
			hostId = null;
			userId = null;
			roleId = null;
			authorizeStatus = null;
			targetMovement = null;
			sysFormat = null;
			sessionNotesDetails = null;
			itrNotes = null;
			movNote = null;
		}
	}

	/**
	 * This method is used to update the pre advice movement status to
	 * open/unopen while clicking on open/unopen button in pre advice display
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException -
	 */
	public String updateOpenFlag()
			throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// To get the form values
		// DynaValidatorForm dynaForm = null;
		// To hold movement
		Movement movement = null;
		// To hold the open status flag
		String openFlag = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [updateOpenFlag] - Entering");
			// Instantiate the // DynaValidatorForm
			// get the movement bean from form
			movement = (Movement) getMovement();
			// get the open/unopen flag from request
			openFlag = request.getParameter("openFlag");
			// update the open/unopen flag for movement
			preAdviceInputManager.updateOpenUnopenFlag(CacheManager
							.getInstance().getHostId(), movement.getId().getEntityId(),
					movement.getId().getMovementId(), openFlag, SwtUtil
							.getCurrentUserId(request.getSession()));
			log.debug(this.getClass().getName()
					  + " - [updateOpenFlag] - Existing");
			// forward the search action once th update the open/unopen status
			return search();
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'updateOpenFlag' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			return getView("fail");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'updateOpenFlag' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "updateOpenFlag", PreAdviceInputAction.class),
					request, "");
			return getView("fail");
		} finally {
			// nullify objects
			openFlag = null;
			movement = null;
		}
	}

	/**
	 * This method is used to put the collection of position level list for
	 * given entity in request and it will be populated in the select box
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @throws SwtException
	 */
	private Collection putPreadvicePositionLevelInReq(HttpServletRequest request,
													  String hostId, String entityId) throws SwtException {
		Collection positionLevelList = null;

		try {
			log.debug(this.getClass().getName()
					  + " - [putPreadvicePositionLevelInReq] - Entering");
			// get the collection of position level for given entity id,host id
			// and set the same in request to populate the select box for
			// position
			positionLevelList= preAdviceInputManager.getPreadvicePositionLevelOfEntity(hostId, entityId);
			request.setAttribute("positionLevelList",positionLevelList);
			log.debug(this.getClass().getName()
					  + " - [putPreadvicePositionLevelInReq] - Existing");
			return positionLevelList;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putPreadvicePositionLevelInReq] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		}
	}

	/**
	 * This method is used to set the collection of sign list in request to
	 * decide credit/debit for movement input/change
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection putSignListInReq(HttpServletRequest request)
			throws SwtException {
		// To hold the credit/debit value
		Collection<LabelValueBean> signList = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [putSignListInReq] - Entering");
			// Instantiate the collecion for sign
			signList = new ArrayList<LabelValueBean>();
			// add the credit value in sign collection
			signList.add(new LabelValueBean(SwtConstants.CREDIT,
					SwtConstants.CREDIT));
			// add the debit value in sign collection
			signList.add(new LabelValueBean(SwtConstants.DEBIT,
					SwtConstants.DEBIT));
			// set the signlist collection in request.
			request.setAttribute("signList", signList);
			log.debug(this.getClass().getName()
					  + " - [putSignListInReq] - Existing");
			return signList;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putSignListInReq] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());

		} finally {
			// nullify objects
			signList = null;
		}
	}

	/**
	 * This method is used to put collection of entity list into request.
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection putEntityListInReq(HttpServletRequest request)
			throws SwtException {
		try {
			Collection entityList = null;

			log.debug(this.getClass().getName()
					  + " - [putEntityListInReq] - Entering");
			// get the collection of entity which having full/view access and
			// set the same in request to populate the entity select box
			entityList =  SwtUtil
					.convertEntityAcessCollectionLVL(SwtUtil
									.getUserEntityAccessList(request.getSession()),
							request.getSession());
			request.setAttribute("entities",entityList);
			log.debug(this.getClass().getName()
					  + " - [putEntityListInReq] - Existing");
			return entityList;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putEntityListInReq] - Exception -" + e.getMessage());
			throw new SwtException(e.getMessage());

		}
	}

	/**
	 * This method is used to get the collection of entities which are having
	 * access for current user's role
	 *
	 * @param request
	 * @throws SwtException
	 */
	private Collection putFullAcessEntityListInReq(HttpServletRequest request,
												   String roleId) throws SwtException {
		try {
			Collection entityList = null;
			log.debug(this.getClass().getName()
					  + " - [putFullAcessEntityListInReq] - Entering");
			// get the collection of entity which having full access and
			// set the same in request to populate the entity select box
			entityList=SwtUtil.getSwtMaintenanceCache().getFullEntityAccessCollectionLVL(roleId);
			request.setAttribute("entities",entityList );
			log.debug(this.getClass().getName()
					  + " - [putFullAcessEntityListInReq] - Existing");
			return entityList;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putFullAcessEntityListInReq] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		}
	}


	/**
	 * This method is used to get the collection of entities which are having
	 * access for current user's role
	 *
	 * @param request
	 * @return
	 * @throws SwtException
	 */
	private Collection putFullOrViewAcessEntityListInReq(HttpServletRequest request,
														 String roleId) throws SwtException {
		try {
			Collection entityList = null;
			log.debug(this.getClass().getName()
					  + " - [putFullOrViewAcessEntityListInReq] - Entering");
			// get the collection of entity which having full access and
			// set the same in request to populate the entity select box
			entityList=SwtUtil.getSwtMaintenanceCache().getFullOrViewEntityAccessCollectionLVL(roleId);
			request.setAttribute("entities", entityList);
			log.debug(this.getClass().getName()
					  + " - [putFullOrViewAcessEntityListInReq] - Existing");

			return entityList;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putFullOrViewAcessEntityListInReq] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		}
	}


	/**
	 * This method is used to get the collection of currency full access list
	 * for given host id and entity id and set it into request which is used to
	 * populate the currency list in select box
	 *
	 * @param request
	 * @param hostId
	 * @param entityId
	 * @throws SwtException
	 */
	private Collection putCurrencyFullAccessListInReq(HttpServletRequest request,
													  String hostId, String entityId) throws SwtException {
		// collection of currency drop down
		Collection<LabelValueBean> currencyDropDown = null;
		// Map for currency id , currency name
		Map<String, String> currencyMap = null;
		// To iterate currency key
		Iterator<String> itrCurrencyKey = null;
		// hold the role id
		String roleId = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [putCurrencyFullAccessListInReq] - Entering");
			// Instantiate the currency drop down
			currencyDropDown = new ArrayList<LabelValueBean>();
			// get the role id for current user
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the map for currency with full access
			currencyMap = SwtUtil.getCurrencyFullAccessMap(roleId, hostId,
					entityId);
			if (currencyMap != null && currencyMap.size() > 0) {
				// iterate the currency map key values
				itrCurrencyKey = currencyMap.keySet().iterator();
				while (itrCurrencyKey.hasNext()) {
					// get the currency id from map
					String currencyId = itrCurrencyKey.next();
					// add labelvaluebean for currency id
					currencyDropDown.add(new LabelValueBean(currencyId,
							currencyId));
				}
			}
			// set the currency drop down collection in request
			request.setAttribute("currencies", currencyDropDown);
			log.debug(this.getClass().getName()
					  + " - [putCurrencyFullAccessListInReq] - Existing");
			return currencyDropDown;
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putCurrencyFullAccessListInReq] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			currencyDropDown = null;
			currencyMap = null;
			itrCurrencyKey = null;
			roleId = null;
		}
	}

	/**
	 * This function puts the description corresponding to accountId, book code,
	 * entityId ,position level in the request and these values will be
	 * displayed adjacent to that corresponding id.
	 *
	 * @param request
	 * @param movement
	 * @throws SwtException
	 */
	private void putDescriptionsInRequest(HttpServletRequest request,
										  Movement movement) throws SwtException {
		// hold collection of position level
		Collection<LabelValueBean> collPosLevel = null;
		// hold account id for movement
		String accountId = null;
		// hold entity id for movement
		String entityId = null;
		// hold postion level for movement
		String posLevel = null;
		// Iterate the postion level
		Iterator<LabelValueBean> itrPositionLvl = null;
		// AccountMaintenanceManager instance
		AcctMaintenanceManager acctManager = null;
		// AcctMaintenance instance
		AcctMaintenance acctMaintenance = null;
		// BookCodeManager instance
		BookCodeManager bookCodeManager = null;
		// BookCode instance
		BookCode bookCodeBean = null;
		// collection of counter party list
		Collection<Party> collCounterParty = null;
		// collection of Matching Party list
		Collection<Party> collMatchingParty = null;
		// Iterate the counter party
		Iterator<Party> itrCounterParty = null;
		// Iterate the matching party list
		Iterator<Party> itrMatchingParty = null;
		// To hold counter party name
		String counterPartyDesc = null;
		// To hold matching party name
		String matchingPartyDesc = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [putDescriptionsInRequest] - Entering");
			// Initialize the all party details to empty
			counterPartyDesc = "";
			matchingPartyDesc = "";
			// get the collection of postion level list to get the postion level
			// description
			collPosLevel = (Collection<LabelValueBean>) request
					.getAttribute("positionLevelList");
			if (movement != null) {
				/**
				 * get the account id ,entity id, postion level for movement to
				 * take the corresponding name
				 */
				accountId = movement.getAccountId();
				entityId = movement.getId().getEntityId();
				posLevel = movement.getPositionLevelAsString();
				if ((movement.getHasNotes() != null)
					&& movement.getHasNotes().equals("Y")) {
					request.setAttribute("hasNotes", "Y");
				}
			}
			/**
			 * Initialize the account id,entity id,pos level with empty string
			 * if the value has null value.
			 */
			if (accountId == null) {
				accountId = "";
			}
			if (entityId == null) {
				entityId = "";
			}
			if (posLevel == null) {
				posLevel = "";
			}
			// putting the entityDescription in the request to display
			// adjacent to the entity id in screen
			if (!SwtUtil.isEmptyOrNull(movement.getEntityName()))
				request.setAttribute("entityDesc", movement.getEntityName());
			// putting the position Level Description in the request to
			// display adjacent to the position level id in screen
			if (collPosLevel != null) {
				itrPositionLvl = collPosLevel.iterator();
				while (itrPositionLvl.hasNext()) {
					// get the label value bean for position level
					LabelValueBean lblValueBean = itrPositionLvl.next();
					if (lblValueBean.getValue().equals(posLevel)) {
						request.setAttribute("posLevelName", lblValueBean
								.getLabel());
						break;
					}
				}
			}

			// Instantiate the AcctMaintenanceManager
			acctManager = (AcctMaintenanceManager) SwtUtil
					.getBean("acctMaintenanceManager");
			// get the account details for given account id
			acctMaintenance = acctManager.getMainOrLinkAccount(entityId,
					CacheManager.getInstance().getHostId(), accountId);
			// set the account name in request to dispaly in screen
			if (acctMaintenance != null)
				request.setAttribute("accountDesc", acctMaintenance
						.getAcctname());
			// Instantiate the bookCodeManager
			bookCodeManager = (BookCodeManager) SwtUtil
					.getBean("bookCodeManager");
			// get the book code details for given book code
			bookCodeBean = bookCodeManager.getEditableData(CacheManager
					.getInstance().getHostId(), entityId, movement
					.getBookCode());
			// set the book code name in request to display in screen
			if (bookCodeBean != null)
				request
						.setAttribute("bookCodeDesc", bookCodeBean
								.getBookName());
			/**
			 * set the counterparty name in request for given counterparty id
			 * which is used to display the value adjacent to the counter party
			 * id in pre advice display/change screen.
			 */
			if (!SwtUtil.isEmptyOrNull(movement.getCounterPartyId())) {
				collCounterParty = preAdviceInputManager.getCounterPartyRecord(
						movement.getId().getHostId(), entityId, movement
								.getCounterPartyId());
				if (collCounterParty != null) {
					itrCounterParty = collCounterParty.iterator();
					while (itrCounterParty.hasNext()) {
						counterPartyDesc = itrCounterParty.next()
								.getPartyName();
						break;
					}
				}
			}
			request.setAttribute("counterPartyDesc", counterPartyDesc);
			/**
			 * set the matching party name in request for given matching party
			 * id which is used to display the value adjacent to the matching
			 * party id in pre advice display/change screen.
			 */
			if (!SwtUtil.isEmptyOrNull(movement.getMatchingParty())) {
				collMatchingParty = preAdviceInputManager
						.getMatchingPartyRecord(movement.getId().getHostId(),
								entityId, movement.getMatchingParty());
				if (collMatchingParty != null) {
					itrMatchingParty = collMatchingParty.iterator();
					while (itrMatchingParty.hasNext()) {
						matchingPartyDesc = itrMatchingParty.next()
								.getPartyName();
						break;
					}
				}
			}
			request.setAttribute("matchingPartyDesc", matchingPartyDesc);
			log.debug(this.getClass().getName()
					  + " - [putDescriptionsInRequest] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [putDescriptionsInRequest] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			collPosLevel = null;
			accountId = null;
			entityId = null;
			posLevel = null;
			itrPositionLvl = null;
			acctManager = null;
			acctMaintenance = null;
			bookCodeManager = null;
			bookCodeBean = null;
			collCounterParty = null;
			collMatchingParty = null;
			itrCounterParty = null;
			itrMatchingParty = null;
			counterPartyDesc = null;
			matchingPartyDesc = null;
		}
	}

	/**
	 * checkMovementId() This method is used to check the movement id which is
	 * existing or not. if given movement id is available then set true flag in
	 * response otherwise set false flag
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	public String checkMovementId() throws SwtException {
		// Declare the movement object
		Movement movement = null;
		// To hold the movement from request
		Long movementId = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();
		try {
			log.debug(this.getClass().getName()
					  + "- [checkMovementId] - Entering ");
			// convert the movementId as Long
			movementId = new Long(request.getParameter("movId"));
			// Get the movement details for given movement id
			movement = ((MovementManager) (SwtUtil.getBean("movementManager")))
					.getMovementDetails(CacheManager.getInstance().getHostId(),
							movementId, new SystemInfo(), SwtUtil
									.getCurrentSystemFormats(request
											.getSession()));
			// if movement is available then set true flag otherwise set false
			// flag
			if (movement != null
				&& movement.getInputSource().equalsIgnoreCase(
					SwtConstants.MOVEMENT_SOURCE_PREADVICE)) {
				response.getWriter().print(true);
			} else {
				response.getWriter().print(false);
			}

			log.debug(this.getClass().getName()
					  + "- [checkMovementId] - Exiting ");
		} catch (SwtException swtexp) {
            try {
                response.getWriter().print(swtexp.getMessage());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            SwtUtil.logException(swtexp, request, "");
			log.error(this.getClass().getName()
					  + "- [checkMovementId] - SwtException "
					  + swtexp.getMessage());
		} catch (Exception exp) {
			log.error(this.getClass().getName()
					  + "- [checkMovementId] - Exception " + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
					exp, "checkMovementId", PreAdviceInputAction.class), request, "");
            try {
                response.getWriter().print(exp.getMessage());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } finally {
			// nullify objects
			movement = null;
			movementId = null;
		}
		return null;
	}

	/**
	 * putEditFlagsInRequest() This method is used to set the movement fields
	 * for the pre advice change screen based on these value component will be
	 * decided at runtime
	 *
	 * @param request
	 * @param editFlagArr
	 * @param inputSource
	 * @throws Exception
	 */
	private void putEditFlagsInRequest(HttpServletRequest request,
									   Hashtable<String, Integer> editFlagArr, String inputSource) throws SwtException {
		try {
			log.debug(this.getClass().getName()
					  + " - [putEditFlagsInRequest] - Entering ");
			request.setAttribute("valueDateEditStatus", editFlagArr.get("Value Date")!=null && editFlagArr.get("Value Date") == 1?"false":"true");
			request.setAttribute("amountEditStatus", editFlagArr.get("Amount")!=null && editFlagArr.get("Amount") == 1?"false":"true");
			request.setAttribute("posLevelEditStatus", editFlagArr.get("Position Level")!=null && editFlagArr.get("Position Level") == 1?"false":"true");
			request.setAttribute("accountEditStatus", editFlagArr.get("Account")!=null && editFlagArr.get("Account") == 1?"false":"true");
			request.setAttribute("bookCodeEditStatus", editFlagArr.get("Book Code")!=null && editFlagArr.get("Book Code") == 1?"false":"true");
			request.setAttribute("preStatusEditStatus", editFlagArr.get("Predict Status") !=null && editFlagArr.get("Predict Status") == 1?"false":"true");
			request.setAttribute("refEditStatus", editFlagArr.get("References") !=null && editFlagArr.get("References") == 1?"false":"true");
			request.setAttribute("cpartyEditStatus", editFlagArr.get("Counterparty")!=null && editFlagArr.get("Counterparty") == 1?"false":"true");
			request.setAttribute("benEditStatus", editFlagArr.get("Beneficiary") != null && editFlagArr.get("Beneficiary") == 1?"false":"true");
			request.setAttribute("custEditStatus", editFlagArr.get("Custodian")!= null && editFlagArr.get("Custodian") == 1?"false":"true");
			request.setAttribute("extBalStatus", editFlagArr.get("External Status") != null && editFlagArr.get("External Status") == 1?"false":"true");
			request.setAttribute("MatchPartyEditStatus", editFlagArr.get("Matching Party")!=null && editFlagArr.get("Matching Party") == 1?"false":"true");
			request.setAttribute("PrdTypeEditStatus", editFlagArr.get("Product Type")!=null && editFlagArr.get("Product Type") == 1?"false":"true");
			request.setAttribute("PostDateEditStatus", editFlagArr.get("Posting Date")!=null && editFlagArr.get("Posting Date") == 1?"false":"true");
			request.setAttribute("ExpSettEditStatus", editFlagArr.get("Exp Sett Date")!=null && editFlagArr.get("Exp Sett Date") == 1?"false":"true");
			request.setAttribute("ActualSettEditStatus", editFlagArr.get("Act Sett Date")!=null&& editFlagArr.get("Act Sett Date") == 1?"false":"true");
			request.setAttribute("CritPayTypeEditStatus", editFlagArr.get("Crit Pay Type")!=null && editFlagArr.get("Crit Pay Type") == 1?"false":"true");
			request.setAttribute("UETREditStatus", editFlagArr.get("UETR") !=null && editFlagArr.get("UETR") == 1?"false":"true");

			request.setAttribute("OrederCustEditStatus", editFlagArr.get("Ordering Customer")!=null && editFlagArr.get("Ordering Customer") == 1?"false":"true");
			request.setAttribute("OrederInstEditStatus", editFlagArr.get("Ordering Institution")!=null && editFlagArr.get("Ordering Institution") == 1?"false":"true");
			request.setAttribute("SendCorresEditStatus", editFlagArr.get("Sender's Corresp")!=null && editFlagArr.get("Sender's Corresp") == 1?"false":"true");
			request.setAttribute("ReceivCorrespEditStatus", editFlagArr.get("Receiver's Corresp")!=null && editFlagArr.get("Receiver's Corresp") == 1?"false":"true");
			request.setAttribute("IntermInstitEditStatus", editFlagArr.get("Intermed. Institution")!=null && editFlagArr.get("Intermed. Institution") == 1?"false":"true");
			request.setAttribute("ActWithInstitEditStatus", editFlagArr.get("Act with Institution")!=null && editFlagArr.get("Act with Institution") == 1?"false":"true");
			request.setAttribute("BenfCustomEditStatus", editFlagArr.get("Beneficiary Customer")!=null && editFlagArr.get("Beneficiary Customer") == 1?"false":"true");
			request.setAttribute("SendToReceivInfEditStatus", editFlagArr.get("Send To Receiv")!=null && editFlagArr.get("Send To Receiv") == 1?"false":"true");

			log.debug(this.getClass().getName()
					  + " - [putEditFlagsInRequest] - Existing ");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'putEditFlagsInRequest' method : "
						   + exp.getMessage());
			throw SwtErrorHandler.getInstance().handleException(exp,
					"putEditFlagsInRequest", PreAdviceInputAction.class);
		}
	}

	/**
	 * This method is used to get the account list for given entity id,currency
	 * code and it will be invoked while clicking account select box button.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getAccountsList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Entity Id
		String entityId = null;
		// Currency Code
		String currencyCode = null;
		// Movement Type
		String movType = null;
		// text for sending response
		StringBuffer responseText = null;
		// Main Account list
		Collection<LabelValueBean> collAcctList = null;
		// Iterator for account list
		Iterator<LabelValueBean> itrAcctList = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getAccountsList] - Entering");
			// get the entityId,currencyCode,movType,inputSource from request
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			movType = request.getParameter("movType");
			// Initialize the string buffer
			responseText = new StringBuffer("");
			// get the AccountList
			collAcctList = preAdviceInputManager.getAccountIdDropDown(
					CacheManager.getInstance().getHostId(), entityId,
					currencyCode, movType);
			// Iterate the collection
			itrAcctList = collAcctList.iterator();
			while (itrAcctList.hasNext()) {
				// get the LabelValueBean
				LabelValueBean lBean = itrAcctList.next();
				responseText.append(lBean.getLabel()).append("~~~").append(
						lBean.getValue()).append("\n");
			}
			// write the account list into response
			response.getWriter().print(responseText.toString());
			log.debug(this.getClass().getName()
					  + " - [getAccountsList] - Existing");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'getAccountsList' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'getAccountsList' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "getAccountsList", PreAdviceInputAction.class),
					request, "");
		} finally {
			// nullify objects
			entityId = null;
			currencyCode = null;
			movType = null;
			responseText = null;
			collAcctList = null;
			itrAcctList = null;
		}
		return null;
	}

	/**
	 * This method is used to get the book code list for given entity
	 * id,currency code and it will be invoked while clicking book code select
	 * box button.
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward
	 * @throws SwtException
	 */
	public String getBookCodeList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Entity Id
		String entityId = null;
		// text for sending response
		StringBuffer responseText = null;
		// Main Account list
		Collection<BookCode> collBookCode = null;
		// Iterator for book code
		Iterator<BookCode> itrBookCode = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getBookCodeList] - Entering");
			// get the entityId,currencyCode from request
			entityId = request.getParameter("entityId");
			// get the collection of book code for host id,entity id
			collBookCode = ((MovementManager) (SwtUtil
					.getBean("movementManager"))).getBookCodeList(CacheManager
					.getInstance().getHostId(), entityId);
			// Initialize the string buffer
			responseText = new StringBuffer("");
			// add empty book name and code
			responseText.append("~~~").append("").append("\n");
			if (collBookCode != null) {
				// iterate the book code collection
				itrBookCode = collBookCode.iterator();
				while (itrBookCode.hasNext()) {
					// get the bookcode
					BookCode bookCode = itrBookCode.next();
					// add the book name and code
					responseText.append(bookCode.getBookName()).append("~~~")
							.append(bookCode.getId().getBookCode())
							.append("\n");
				}
			}
			// write the book code list into response
			response.getWriter().print(responseText.toString());
			log.debug(this.getClass().getName()
					  + " - [getBookCodeList] - Existing");
		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'getBookCodeList' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'getBookCodeList' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "getBookCodeList", PreAdviceInputAction.class),
					request, "");
		} finally {
			// nullify objects
			entityId = null;
			responseText = null;
			collBookCode = null;
			itrBookCode = null;
		}
		return null;
	}



	public String getLists() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Entity Id
		String entityId = null;
		// Currency Code
		String currencyCode = null;
		// Movement Type
		String movType = null;
		// To hold the default currency for entity
		String defaultCurrency = null;
		// To host for application
		String hostId = null;
		// Iterator for account list
		Iterator<LabelValueBean> itrAcctList = null;
		Collection<BookCode> collBookCode = null;
		Iterator<BookCode> itrBookCode = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		Movement movement = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getLists] - Entering");
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			// get the entityId,currencyCode,movType,inputSource from request
			entityId = request.getParameter("entityId");
			movType = request.getParameter("movType");
			// get the default currency for entity id
			defaultCurrency = SwtUtil.getDomesticCurrencyForUser(request,
					hostId, entityId);
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.REFRESH_COMBO_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			responseConstructor.createElement("defaultCurrency",
					defaultCurrency);

			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			/***** Account list Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection collAcctList = preAdviceInputManager.getAccountIdDropDown(
					CacheManager.getInstance().getHostId(), entityId,
					defaultCurrency, movType);
			Iterator j = collAcctList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("collAcctList", lstOptions));
			/***** Account list Combo End ***********/


			/***** Book Code list Combo Start ***********/
			// get the collection of book code for host id,entity id
			collBookCode = ((MovementManager) (SwtUtil.getBean("movementManager")))
					.getBookCodeList(CacheManager.getInstance().getHostId(), entityId);
			// options drop down list
			lstOptions = new ArrayList<OptionInfo>();
			if (collBookCode != null) {
				// iterate the book code collection
				itrBookCode = collBookCode.iterator();
				lstOptions.add(new OptionInfo("","", false));
				while (itrBookCode.hasNext()) {
					// get the bookcode
					BookCode bookCode = itrBookCode.next();
					lstOptions.add(new OptionInfo(bookCode.getId().getBookCode(),bookCode.getBookName(), false));

				}
				lstSelect.add(new SelectInfo("collBookCode", lstOptions));
			}
			/***** Book Code list Combo End ***********/

			/***** Currency Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection currencyList = putCurrencyFullAccessListInReq(request, CacheManager.getInstance().getHostId(),entityId);
			j = currencyList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("currencyList", lstOptions));


			/***** Positionl level Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			row = null;
			Collection PositionLevelList = putPreadvicePositionLevelInReq(request, hostId, entityId);
			j = PositionLevelList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("PositionLevelList", lstOptions));
			/***** Positionl level Combo End ***********/

			/***** Currency Combo End ***********/
			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(SwtConstants.REFRESH_COMBO_LIST);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName()
					  + " - [getLists] - Existing");
			return getView("data");

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'getLists' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'getLists' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "getLists", PreAdviceInputAction.class),
					request, "");
		} finally {
			// nullify objects
			entityId = null;
			currencyCode = null;
			movType = null;
			itrAcctList = null;
		}
		return null;
	}


	public String getUpdatedAccountList() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		// Entity Id
		String entityId = null;
		// Currency Code
		String currencyCode = null;
		// Movement Type
		String movType = null;
		// To hold the default currency for entity
		String defaultCurrency = null;
		// To host for application
		String hostId = null;
		// Iterator for account list
		Iterator<LabelValueBean> itrAcctList = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		Movement movement = null;
		try {
			log.debug(this.getClass().getName() + " - [getUpdatedAccountList] - Entering");
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			// get the entityId,currencyCode,movType,inputSource from request
			entityId = request.getParameter("entityId");
			currencyCode = request.getParameter("currencyCode");
			movType = request.getParameter("movType");
			// get the default currency for entity id
			defaultCurrency = SwtUtil.getDomesticCurrencyForUser(request, hostId, entityId);
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.REFRESH_COMBO_LIST);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			responseConstructor.createElement("defaultCurrency", defaultCurrency);

			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			/***** Account list Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			LabelValueBean row = null;
			Collection collAcctList = preAdviceInputManager.getAccountIdDropDown(CacheManager.getInstance().getHostId(),
					entityId, currencyCode, movType);
			Iterator j = collAcctList.iterator();
			row = null;
			while (j.hasNext()) {
				row = (LabelValueBean) j.next();
				lstOptions.add(new OptionInfo(row.getValue(), row.getLabel(), false));
			}
			lstSelect.add(new SelectInfo("collAcctList", lstOptions));
			/***** Account list Combo End ***********/

			responseConstructor.formSelect(lstSelect);
			xmlWriter.endElement(SwtConstants.REFRESH_COMBO_LIST);
			request.setAttribute("data", xmlWriter.getData());
			log.debug(this.getClass().getName() + " - [getUpdatedAccountList] - Existing");
			return getView("data");

		} catch (SwtException swtexp) {
			log.error("SwtException Catch in PreAdviceInputAction.'getUpdatedAccountList' method : " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log.error("Exception Catch in PreAdviceInputAction.'getAccountsList' method : " + exp.getMessage());
			SwtUtil.logException(
					SwtErrorHandler.getInstance().handleException(exp, "getUpdatedAccountList", PreAdviceInputAction.class),
					request, "");
		} finally {
			// nullify objects
			entityId = null;
			currencyCode = null;
			movType = null;
			itrAcctList = null;
		}
		return null;
	}



	public String getMvtType() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String componentId = null;
		String mvtType = null;
		String account = null;
		// debug message
		log.debug(this.getClass().getName()
				  + " - [ getMvtType ] - Entry");
		account = request.getParameter("accountId");
		mvtType= preAdviceInputManager.getMvtFromAccountTab(account);
		responseConstructor = new SwtResponseConstructor();

		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "PreAdviceInput";
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
		xmlWriter.addAttribute("mvtType", mvtType);
		xmlWriter.startElement("preAdviceInput");
		xmlWriter.clearAttribute();
		responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
		xmlWriter.endElement("preAdviceInput");

		request.setAttribute("data", xmlWriter.getData());
		return getView("data");
	}

	/**
	 * Prepares the screen data for display after save method get called.
	 *
	 * @param request
	 * @param hostId
	 */
	public String saveAll() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		Movement  movement = null;
		// variable to get the role id
		String roleId = null;
		// variable to get the authorize status
		String authorizeStatus = null;
		// To hold the array of preAdvices
		String data;
		// variable to get the host id
		String hostId = null;
		// variable to get the entity id
		String entityId = null;
		// variable to get the user id
		String userId = null;
		String mvtType = null;
		String account = null;
		String currencyCode= null;
		String predictStatus= null;
		String postD = null;
		String valD = null;
		String postDateFormat = null;
		String valueDateFormat = null;
		String amountFormat = null;
		String matchParty=null;
		String reference=null;
		String bookCode=null;
		String cParty=null;
		String cPartyText=null;
		String productType=null;
		String amount=null;
		String sign=null;
		//SystemFormats  sysFormat = null;
		// Instance the Entity object
		Entity entity = null;
		// boolean to hold position flag
		boolean positionFlag;
		// variable to get the iterator value
		Iterator<MovementNote> itrMovementNote = null;
		// variable to get the sessionNotesDetails
		Collection<MovementNote> sessionNotesDetails = null;
		// variable to get the matchDriver
		MatchDriver matchDriver = null;
		// variable to get the movementManager
		MovementManager movementManager = null;
		// variable to get the movementNote
		MovementNote movementNote = null;
		// variable to get the matchDriverOperation
		String matchDriverOperation = null;
		ArrayList<Movement> listMvt = new ArrayList<Movement>();
		HashMap<String,String> savedPostitionLevelByEntity = new HashMap<String, String>();
		try {
			log.debug(this.getClass().getName() + " - [saveAll()] - Entering");
			// get current used id to get the role for user
			userId = SwtUtil.getCurrentUserId(request.getSession());
			hostId = CacheManager.getInstance().getHostId();
			SystemFormats sysFormat = SwtUtil.getCurrentSystemFormats(request.getSession());
			postDateFormat=request.getParameter("newPostDateFormat");
			valueDateFormat=request.getParameter("newValDateFormat");
			amountFormat=request.getParameter("newAmountFormat");
			//get the array of preAdvices from request
			data = request.getParameter("preAdviceList");
			JSONArray mvtJSONArray = new JSONArray(data);
			String postionLevel = null;
			for (int i = 0; i < mvtJSONArray.length(); i++) {
				movement = new Movement();
				entityId= mvtJSONArray.getJSONObject(i).has("Entity_ID")?mvtJSONArray.getJSONObject(i).getString("Entity_ID"):"";
				currencyCode= mvtJSONArray.getJSONObject(i).has("Ccy")?mvtJSONArray.getJSONObject(i).getString("Ccy"):"";
				account=mvtJSONArray.getJSONObject(i).has("Account_ID")?mvtJSONArray.getJSONObject(i).getString("Account_ID"):"";
				predictStatus=mvtJSONArray.getJSONObject(i).has("Pred_Status")?mvtJSONArray.getJSONObject(i).getString("Pred_Status"):"";
				postD=mvtJSONArray.getJSONObject(i).has("Post_Date")?mvtJSONArray.getJSONObject(i).getString("Post_Date"):"";
				valD=mvtJSONArray.getJSONObject(i).has("Value_Date")?mvtJSONArray.getJSONObject(i).getString("Value_Date"):"";
				matchParty=mvtJSONArray.getJSONObject(i).has("Match_Party")?mvtJSONArray.getJSONObject(i).getString("Match_Party"):"";
				reference=mvtJSONArray.getJSONObject(i).has("Reference")?mvtJSONArray.getJSONObject(i).getString("Reference"):"";
				bookCode=mvtJSONArray.getJSONObject(i).has("Book_Code")?mvtJSONArray.getJSONObject(i).getString("Book_Code"):"";
				cParty=mvtJSONArray.getJSONObject(i).has("CounterParty_ID")?mvtJSONArray.getJSONObject(i).getString("CounterParty_ID"):"";
				cPartyText=mvtJSONArray.getJSONObject(i).has("Cparty_Text")?mvtJSONArray.getJSONObject(i).getString("Cparty_Text"):"";
				productType=mvtJSONArray.getJSONObject(i).has("Product_Type")?mvtJSONArray.getJSONObject(i).getString("Product_Type"):"";
				amount=mvtJSONArray.getJSONObject(i).has("Amount")?mvtJSONArray.getJSONObject(i).getString("Amount"):"";
				sign=mvtJSONArray.getJSONObject(i).has("Sign")?mvtJSONArray.getJSONObject(i).getString("Sign"):"";
				mvtType= preAdviceInputManager.getMvtFromAccountTab(account);
				roleId = preAdviceInputManager.getUserDetails(hostId, userId);
				// get authorize status for given role
				authorizeStatus = preAdviceInputManager.getRoleDetails(roleId);
				// set default value for extract status,book code avail while saving
				// the pre advice movement
				movement.setExtractStatus("E");
				movement.setBookCodeAvail("N");
				// if role having the authorize flag then set the match status as
				// 'authorise' otherwise 'outstanding'
				movement
						.setMatchStatus((((authorizeStatus != null) && authorizeStatus
								.equalsIgnoreCase("Y")) ? SwtConstants.AUTHORISE_STATUS
								: SwtConstants.OUTSTANDING_STATUS));
				// if role having the authorize flag then set to match as
				// 'N' otherwise 'Y'
				movement.setToMatch((((authorizeStatus != null) && authorizeStatus
						.equalsIgnoreCase("Y")) ? SwtConstants.NO
						: SwtConstants.YES));

				if(savedPostitionLevelByEntity.containsKey(entityId)) {
					try {
						movement.setPositionLevel(Integer.parseInt(savedPostitionLevelByEntity.get(entityId)));
					} catch (Exception e) {
					}
				}else {

					Collection positionLevelList = preAdviceInputManager.getPreadvicePositionLevelOfEntity(hostId, entityId);
					if(positionLevelList.size()>0) {
						postionLevel = ((LabelValueBean)positionLevelList.iterator().next()).getValue();
						savedPostitionLevelByEntity.put(entityId, postionLevel);
						try {
							movement.setPositionLevel(Integer.parseInt(postionLevel));
						} catch (Exception e) {
						}
					}
				}
				movement.setInputDate(SwtUtil.getSystemDatewithTime());

				movement.setInputRole(roleId);
				movement.setInputUser(userId);
				movement.setInputSource(SwtConstants.MOVEMENT_SOURCE_PREADVICE);

				if (predictStatus != null) {
					movement.setInitialPredStatus(predictStatus);
				}
				movement.setUpdateDate(SwtUtil.getSystemDatewithTime());
				movement.setUpdateUser(userId);
				// get sessionNotesDetails from session to save the movement note
				// details
				sessionNotesDetails = (Collection<MovementNote>) request.getSession()
						.getAttribute("sessionNotesDetails");
				// check whether sessionNotesDetails is not null, then iterate.
				if (sessionNotesDetails != null) {
					itrMovementNote = sessionNotesDetails.iterator();
					while (itrMovementNote.hasNext()) {
						movementNote = (MovementNote) (itrMovementNote.next());
						movementNote.getId().setHostId(hostId);
						movementNote.getId().setEntityId(entityId);
					}
				}
				// get match driver value for given host id,entity id,currency code
				matchDriver = preAdviceInputManager.existMatchDriver(hostId, entityId, currencyCode);
				// check whether matchDriver is null, then set default value.
				if (matchDriver == null) {
					// Instantiate the matchDriver
					matchDriver = new MatchDriver();
					// set the host,entity,currency ,movement flag,processing flag
					// in match driver which is to be saved along with movement
					matchDriver.getId().setHostId(hostId);
					matchDriver.getId().setEntityId(entityId);
					matchDriver.getId().setCurrencyCode(movement.getCurrencyCode());
					matchDriver.setNewMoveFlag("Y");
					matchDriver.setProcessingFlag("N");
					// indicate the save operation for match driver
					matchDriverOperation = "save";
				} else {
					matchDriver.setNewMoveFlag("Y");
					// indicate the update operation for match driver
					matchDriverOperation = "update";
				}
				// set the default open status flag,notes count for movement
				movement.setOpenFlag(SwtConstants.NO);
				movement.setNotesCount(0);
				// if posting date is not null then format the date and set the same
				if (!SwtUtil.isEmptyOrNull(movement.getPostingDateAsString())) {
					movement.setPostingDate(
							(SwtUtil.parseDate(movement.getPostingDateAsString(), postDateFormat)));
				}
				// create entity bean and set the corresponding value to get the
				// external position level flag
				entity = new Entity();
				entity.getId().setHostId(hostId);
				entity.getId().setEntityId(entityId);
				entity.setExternalBalance(movement.getPositionLevel());
				// Instantiate the movement manager
				movementManager = (MovementManager) SwtUtil.getBean("movementManager");
				// Check whether the given position level is External balance
				positionFlag = movementManager.checkExternalPositionLevel(entity);
				if (positionFlag) {
					// If not exist set the value as E
					movement.setExtBalStatus(SwtConstants.EXT_BAL_EXC);
				} else {
					// If exist set value as I
					movement.setExtBalStatus(SwtConstants.EXT_BAL_INC);
				}
				movement.getId().setEntityId(entityId);
				movement.setMovementType(mvtType);
				movement.setAccountId(account);
				movement.setMatchingParty(matchParty);
				movement.setAmount(SwtUtil.parseCurrency(amount, amountFormat));
				movement.setReference1(reference);
				movement.setProductType(productType);
				movement.setPredictStatus(predictStatus);
				movement.setBookCode(bookCode);
				movement.setCounterPartyId(cParty);
				Date postDate=!(("").equalsIgnoreCase(postD))?new SimpleDateFormat(getDateFormat(postDateFormat)).parse(postD):null;
				movement.setPostingDate(postDate);
				Date valDate=!(("").equalsIgnoreCase(valD))?new SimpleDateFormat(getDateFormat(valueDateFormat)).parse(valD):null;
				movement.setValueDate(valDate);
				movement.setSign(sign);
				movement.setCounterPartyText1(cPartyText);
				movement.setCurrencyCode(currencyCode);
				movement.setInputDate(SwtUtil.getSystemDatewithTime());
				movement.setInputUser(userId);
				movement.getId().setHostId(hostId);
				listMvt.add(movement);
			}

			preAdviceInputManager.saveAll(listMvt);
			log.debug(this.getClass().getName() + " - [saveAll()] - Existing");
			//return getView("data");
		} catch (Exception e) {
			log.error("Exception Catch in PreAdviceInputAction.'saveAll' method : " + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify object
			data = null;
		}
		return null;
	}

	// added to format correctly the dates
	public String getDateFormat(String format) {
		String newFormat = null;
		if (format.equalsIgnoreCase("DD/MM/YYYY")) {
			newFormat = "dd/MM/yyyy";
		} else if (format.equalsIgnoreCase("MM/DD/YYYY")) {
			newFormat = "MM/dd/yyyy";
		} else {
			newFormat = "yyyy-MM-dd";
		}
		return newFormat;
	}


	public String importMvt() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));

		return getView("importMvt");
	}


	public String checkPreAdviceInputAccess() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		Integer currencyaccess = null;;
		Integer accountaccess=null;;
		Integer accountopen =null;
		Integer accountForEntity=null;
		Integer bookCodeAccess=null;
		Integer entityAccess = null;
		Integer entityFound=null;
		Integer bookCodeFound=null;
		Integer accountFound=null;
		Integer currencyFound=null;
		// To hold user entity access
		Long UserEntityAccess;
		String entityId=null;
		String accountId=null;
		String currencyCode= null;
		String bookCode= null;
		String reference = null;
		String cParty = null;
		String cPartyTxt = null;
		String matchParty = null;
		String prodType = null;
		String postDate = null;
		String predStatus = null;
		String sign = null;
		String amount = null;
		String valDate = null;

		String componentId = null;
		Movement  movement = null;
		String hostId=null;
		boolean accountExistsForEntity=false;
		boolean isOpenedAccount = false;
		boolean hasPreAdviceAccess = false;
		boolean checkAccountFullAccess = false;
		boolean hasAccesstoEntity=false;
		boolean bookCodeExistsForEntity=false;
		boolean entityExists=false;
		boolean bookCodeExists=false;
		boolean accountExists=false;
		boolean currencyExists=false;
		long time=System.currentTimeMillis();
		String message=null;
		Integer length=null;
		String valDateFormat=null;
		String postDateFormat=null;
		String amountPattern=null;
		String amountFormat=null;
		HashMap<String, Boolean> accountExistsForEntityMap = new HashMap<String, Boolean>();
		HashMap<String, Boolean> isOpenedAccountMap = new HashMap<String, Boolean>();
		HashMap<String, Boolean> hasPreAdviceAccessMap = new HashMap<String, Boolean>();
		HashMap<String, Boolean> checkAccountFullAccessMap = new HashMap<String, Boolean>();
		HashMap<String, Boolean> currencyExistsMap = new HashMap<String, Boolean>();
		HashMap<String, Boolean> hasAccesstoEntityMap = new HashMap<String, Boolean>();
		HashMap<String, Boolean> bookCodeExistsForEntityMap = new HashMap<String, Boolean>();
		HashMap<String, Boolean> entityExistsMap = new HashMap<String, Boolean>();
		HashMap<String, Long> UserEntityAccessMap = new HashMap<String, Long>();
		// To hold the array of preAdvices
		String data;
		String TooltipMsg=null;
		// To hold the collection for user entity access list
		Collection<EntityUserAccess> collUserEntity = null;
		ArrayList<String>signVal = new ArrayList<String>(Arrays.asList("C", "D"));
		ArrayList<String>predVal = new ArrayList<String>(Arrays.asList("I","E","C"));
		try {
			// debug message
			log.debug(this.getClass().getName()
					  + " - [ checkCurrencyAccess ] - Entry");


			AcctMaintenanceManager acctMaintenanceManager = (AcctMaintenanceManager) SwtUtil
					.getBean("acctMaintenanceManager");
			BookCodeManager bookCodeManager = (BookCodeManager) SwtUtil
					.getBean("bookCodeManager");
			CurrencyManager currencyManager = (CurrencyManager) SwtUtil
					.getBean("currencyManager");
			// get the user accessible entity collection
			collUserEntity = SwtUtil.getUserEntityAccessList(request.getSession());
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			//get the array of preAdvices from request
			data = request.getParameter("preAdviceList");
			valDateFormat= request.getParameter("ValDateFormat");
			postDateFormat= request.getParameter("PostDateFormat");
			amountFormat=request.getParameter("amountFormat");
			if(!SwtUtil.isEmptyOrNull(data)) {
				JSONArray mvtJSONArray = new JSONArray(data);
				for (int i = 0; i < mvtJSONArray.length(); i++) {
					List<String>  toolTip = new ArrayList<String>();
					movement = new Movement();
					entityId=  mvtJSONArray.getJSONObject(i).has("Entity_ID")?(mvtJSONArray.getJSONObject(i).getString("Entity_ID")).trim():"";
					currencyCode= mvtJSONArray.getJSONObject(i).has("Ccy")?(mvtJSONArray.getJSONObject(i).getString("Ccy")).trim():"";
					accountId = mvtJSONArray.getJSONObject(i).has("Account_ID")?(mvtJSONArray.getJSONObject(i).getString("Account_ID")).trim():"";
					bookCode = mvtJSONArray.getJSONObject(i).has("Book_Code")?(mvtJSONArray.getJSONObject(i).getString("Book_Code")).trim():"";
					reference = mvtJSONArray.getJSONObject(i).has("Reference")?(mvtJSONArray.getJSONObject(i).getString("Reference")).trim():"";
					cParty = mvtJSONArray.getJSONObject(i).has("CounterParty_ID")?(mvtJSONArray.getJSONObject(i).getString("CounterParty_ID")).trim():"";
					cPartyTxt = mvtJSONArray.getJSONObject(i).has("Cparty_Text")?(mvtJSONArray.getJSONObject(i).getString("Cparty_Text")).trim():"";
					matchParty = mvtJSONArray.getJSONObject(i).has("Match_Party")?(mvtJSONArray.getJSONObject(i).getString("Match_Party")).trim():"";
					prodType = mvtJSONArray.getJSONObject(i).has("Product_Type")?(mvtJSONArray.getJSONObject(i).getString("Product_Type")).trim():"";
					postDate = mvtJSONArray.getJSONObject(i).has("Post_Date")?(mvtJSONArray.getJSONObject(i).getString("Post_Date")).trim():"";
					predStatus = mvtJSONArray.getJSONObject(i).has("Pred_Status")?(mvtJSONArray.getJSONObject(i).getString("Pred_Status")).trim():"";
					sign = mvtJSONArray.getJSONObject(i).has("Sign")?(mvtJSONArray.getJSONObject(i).getString("Sign")).trim():"";
					amount = mvtJSONArray.getJSONObject(i).has("Amount")?(mvtJSONArray.getJSONObject(i).getString("Amount")).trim():"";
					valDate = mvtJSONArray.getJSONObject(i).has("Value_Date")?(mvtJSONArray.getJSONObject(i).getString("Value_Date")).trim():"";

					if ((!SwtUtil.isEmptyOrNull(entityId)) && (!SwtUtil.isEmptyOrNull(currencyCode))
						&& (!SwtUtil.isEmptyOrNull(accountId))) {

						/*******check if entity exists in database******/
						if (entityExistsMap.get(hostId + entityId ) != null) {
							entityExists = entityExistsMap.get(hostId  + entityId);
						} else {
							entityExists = preAdviceInputManager.checkIfEntityExists(hostId,entityId);
							entityExistsMap.put(hostId + entityId ,entityExists);
						}

						if (entityExists) {
							entityFound = 0;
							//if entity exists then check user currency

							/*******check if currency exists in p_account table******/

							if (currencyExistsMap.get(hostId  + currencyCode + entityId) != null) {
								currencyExists = currencyExistsMap.get(hostId  + currencyCode + entityId);
							} else {
								currencyExists = currencyManager.checkIfCurrencyExists(hostId, currencyCode,entityId);
								currencyExistsMap.put(hostId  + currencyCode + entityId, currencyExists);
							}

							if (currencyExists) {
								currencyFound = 0;
								// if entity and currency exist then check account

								/*******check if account exists for relevant entity and currency******/

								if (accountExistsForEntityMap.get(hostId + entityId + currencyCode + accountId) != null) {
									accountExistsForEntity = accountExistsForEntityMap.get(hostId + entityId + currencyCode + accountId);
								} else {
									accountExistsForEntity = acctMaintenanceManager.checkIfAccountExistForEntity(hostId, entityId,
											currencyCode, accountId);
									accountExistsForEntityMap.put(hostId + entityId + currencyCode + accountId, accountExistsForEntity);
								}

								if (accountExistsForEntity) {
									accountForEntity = 0;

									//opened account
									if (isOpenedAccountMap.get(accountId) != null) {
										isOpenedAccount = isOpenedAccountMap.get(accountId);
									} else {
										isOpenedAccount = isOpenedAccount(accountId);
										isOpenedAccountMap.put( accountId, isOpenedAccount);
									}

									//preAdvice access
									if (hasPreAdviceAccessMap.get(accountId) != null) {
										hasPreAdviceAccess = hasPreAdviceAccessMap.get(accountId);
									} else {
										hasPreAdviceAccess = hasPreAdviceAccess(accountId,entityId);
										hasPreAdviceAccessMap.put( accountId, hasPreAdviceAccess);
									}

									/*******check if it's an Open account******/
									if (isOpenedAccount) {
										accountopen = 0;

										//check account id length
										if (accountId.length() > 35) {
											toolTip.add("Account Id should not exceed 35 caracters");
										}

										//check account access to preAdvice
										if (!hasPreAdviceAccess) {
											toolTip.add("Check account setting 'Allow PreAdvice Entry");
										}

									}else {
										//accountopen = 1;
										toolTip.add("It should be an Open account");
									}

								}
								else
									//accountForEntity = 1;
									toolTip.add("Account Id not exists for the relevant entity and currency");


							}
							else {
								currencyFound = 1;
								toolTip.add("Provided Currency code not exist in S_CURRENCY table");
							}
							// check all access


							/*******check entity access******/
							if (UserEntityAccessMap.get(entityId ) != null) {
								UserEntityAccess = UserEntityAccessMap.get( entityId);
							} else {
								// get the access for given entity
								UserEntityAccess = (long) SwtUtil.getUserEntityAccess(collUserEntity, entityId);
								UserEntityAccessMap.put(entityId,UserEntityAccess);
							}
							if (UserEntityAccess==0) {
								entityAccess = 0;
								if (entityId.length() > 12) {
									toolTip.add ("Entity Id should not exceed 12 caracters");
								}
							}else {
								toolTip.add("Entity Id not allowed for the user’s role access");
								entityAccess = 1;
							}
							/*******check currency access******/
							if (hasAccesstoEntityMap.get( hostId + entityId + currencyCode) != null) {
								hasAccesstoEntity = hasAccesstoEntityMap.get( hostId + entityId + currencyCode);
							} else {
								hasAccesstoEntity = SwtUtil.getFullAccesOnCurrencyAndEntity(request, hostId, entityId, currencyCode);
								hasAccesstoEntityMap.put( hostId + entityId + currencyCode,hasAccesstoEntity);
							}
							if (hasAccesstoEntity) {
								currencyaccess = 0;

							}else if (!hasAccesstoEntity && currencyFound==0 ){
								toolTip.add("Currency not allowed for the user�s role access/not exists for the relevant entity");
								currencyaccess = 1;
							}
							/*******check account access******/

							if (checkAccountFullAccessMap.get( entityId + currencyCode +accountId + "Input") != null) {
								checkAccountFullAccess = checkAccountFullAccessMap.get( entityId + currencyCode +accountId + "Input");
							} else {
								checkAccountFullAccess = checkAccountFullAccess(request, entityId, currencyCode, accountId, "Input");
								checkAccountFullAccessMap.put( entityId + currencyCode +accountId + "Input", checkAccountFullAccess);
							}

							if (checkAccountFullAccess)
								accountaccess = 0;
							else
								//accountaccess = 1;
								toolTip.add("Account not allowed for input for user�s role ");
						}
						//entity is not there in s_entity table
						else
							//entityFound = 1;
							toolTip.add("Provided Entity id not exist in S_ENTITY table");

					}
					else {
						toolTip.add("EntityID; accountID and currencyCode should not be empty");
						//mvtJSONArray.getJSONObject(i).append("ManadatoryParam",0);
					}

					if  (!SwtUtil.isEmptyOrNull(bookCode)) {

						/******* check book code access ******/
						if (bookCodeExistsForEntityMap.get(hostId + entityId + bookCode) != null) {
							bookCodeExistsForEntity = bookCodeExistsForEntityMap
									.get(hostId + entityId + bookCode);
						} else {
							bookCodeExistsForEntity = bookCodeManager.checkBookCodeAndEntity(hostId, entityId,
									bookCode);
							bookCodeExistsForEntityMap.put(hostId + entityId + bookCode,
									bookCodeExistsForEntity);
						}

						if (bookCodeExistsForEntity) {
							if (bookCode.length() > 12) {
								toolTip.add("Book Code should not exceed 12 caracters");
							}
							bookCodeAccess = 0;
						}else {
							toolTip.add("Book code not exists for the relevant entity");
							bookCodeAccess = 1;
						}

					}

					// other fields

					if (SwtUtil.isEmptyOrNull(sign)) {
						toolTip.add("Clipboard is missing Sign field");
					} else if (!signVal.contains(sign)) {
						toolTip.add("Sign value should be either C or D");
					}

					if (SwtUtil.isEmptyOrNull(predStatus)) {
						toolTip.add("PreAdvice Status id is missing");
					} else if (!predVal.contains(predStatus)) {
						toolTip.add("Predict Status value should be either I or E or C");
					}

					if  (!SwtUtil.isEmptyOrNull(reference)) {
						length=35;
						message=checkOptionalField("Reference",reference,length);
						if(!SwtUtil.isEmptyOrNull(message)) {
							toolTip.add(message);
						}
					}

					if  (!SwtUtil.isEmptyOrNull(cParty)) {
						length=12;
						message=checkOptionalField("CounterParty_ID",cParty,length);
						if(!SwtUtil.isEmptyOrNull(message)) {
							toolTip.add(message);
						}

					}


					if  (!SwtUtil.isEmptyOrNull(cPartyTxt)) {
						length=35;
						message=checkOptionalField("Cparty_Text",cPartyTxt,length);
						if(!SwtUtil.isEmptyOrNull(message)) {
							toolTip.add(message);
						}
					}

					if  (!SwtUtil.isEmptyOrNull(matchParty)) {
						length=12;
						message=checkOptionalField("Match_Party",matchParty,length);
						if(!SwtUtil.isEmptyOrNull(message)) {
							toolTip.add(message);
						}
					}

					if  (!SwtUtil.isEmptyOrNull(prodType)) {
						length=16;
						message=(checkOptionalField("Product_Type",prodType,length));
						if(!SwtUtil.isEmptyOrNull(message)) {
							toolTip.add(message);
						}

					}

					// validate Value date
					if  (SwtUtil.isEmptyOrNull(valDate)) {
						toolTip.add("Clipboard is missing Value Date field");
					}else{
						if(!valDate.matches(getDateRegex(valDateFormat))){
							toolTip.add ("Date format must match supplied Value Date" );
						}
					}


					// validate Post date
					if  (!SwtUtil.isEmptyOrNull(postDate)) {
						if(!postDate.matches(getDateRegex(postDateFormat))){
							toolTip.add ("Date format must match supplied Post Date" );
						}
					}

					// validate amount
					if  (SwtUtil.isEmptyOrNull(amount)) {
						toolTip.add("Clipboard is missing Amount field");
					} else {
						if(!amount.matches(getRegex(amountFormat))){
							toolTip.add("Amount format must match supplied pattern");
						}
					}
					TooltipMsg=toolTip.toString();
					mvtJSONArray.getJSONObject(i).append("TooltipMsg",(TooltipMsg.substring( 1, TooltipMsg.length() - 1 )).replaceAll(", ", ","));

				}

				responseConstructor = new SwtResponseConstructor();

				xmlWriter = responseConstructor.getXMLWriter();
				// Get component ID
				componentId = "PreAdviceInput";
				// Adding screen id and current user id as attributes
				xmlWriter.addAttribute(SwtConstants.SCREEN_ID, componentId);
				String encode = SwtUtil.encode64(mvtJSONArray.toString());
				xmlWriter.addAttribute("updatedArray", encode);
				xmlWriter.startElement("preAdviceInput");
				xmlWriter.clearAttribute();
				responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE), SwtConstants.DATA_FETCH_OK);
				xmlWriter.endElement("preAdviceInput");
			}
			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException swtexp) {
			log.error(this.getClass().getName() + " - Exception Catched in [checkPreAdviceInputAccess] method : - " + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", swtexp.getMessage());
			return getExceptionXmlData(swtexp.getMessage(), request);

		} catch (Exception exp) {

			log.error(this.getClass().getName() + " - Exception Catched in [checkPreAdviceInputAccess] method : - " + exp.getMessage());

			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp, "checkPreAdviceInputAccess", PreAdviceInputAction.class),
					request, "");
			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", exp.getMessage());
			return getExceptionXmlData(exp.getMessage(), request);
		} finally {
			responseConstructor = null;
			xmlWriter=null;
			currencyaccess=null;
			accountaccess=null;
			bookCodeAccess=null;
			entityId=null;
			accountId=null;
			currencyCode= null;
			bookCode= null;
			componentId = null;
			movement = null;
			hostId=null;
			accountExistsForEntity=false;
			hasAccesstoEntity=false;
			bookCodeExistsForEntity=false;

			log.debug(this.getClass().getName() + "- [checkPreAdviceInputAccess] - Exit");
		}

	}

	//check optioanl fields length
	public String checkOptionalField(String column,String field, Integer length) {
		String msg=null;
		if (field.length() > length) {
			msg= column +" should not exceed " + length + " caracters";
		}else {
			msg="";
		}
		return msg;
	}


	public  String getRegex (String format)
	{
		String regex = null;

		switch (format) {
			case "999,999.00":
				regex = "^[+-]?[0-9]{1,3}((\\,)?[0-9]{3})*(\\.[0-9]+)?$";
				break;
			case "999.999,00":
				regex = "^[+-]?[0-9]{1,3}((\\.)?[0-9]{3})*(\\,[0-9]+)?$";
				break;
			default:
				break;
		}

		return regex;
	}


	public  String getDateRegex (String format)
	{
		String regex = null;

		switch (format) {
			case "DD/MM/YYYY":
				regex = "^(3[01]|[12][0-9]|0[1-9])/(1[0-2]|0[1-9])/[0-9]{4}$";
				break;
			case "MM/DD/YYYY":
				regex = "^(1[0-2]|0[1-9])/(3[01]|[12][0-9]|0[1-9])/[0-9]{4}$";
				break;
			case "YYYY-MM-DD":
				regex =  "([12]\\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01]))";
				break;
			default:
				break;
		}

		return regex;
	}



	/**
	 * Method to generate Exception XML data
	 *
	 * @param expMesage
	 * @param mapping
	 * @param request
	 * @return ActionForward
	 */
	public String getExceptionXmlData(String expMesage, HttpServletRequest request)
			throws SwtException {
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String userId = null;
		String componentId = null;
		// debug message
		log.debug(this.getClass().getName() + " - [ getExceptionXmlData ] - Entry");

		userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
		responseConstructor = new SwtResponseConstructor();

		xmlWriter = responseConstructor.getXMLWriter();
		// Get component ID
		componentId = "preAdviceInput";
		// Adding screen id and current user id as attributes
		xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
		xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
		xmlWriter.startElement(PCMConstant.EXCEPTION_TAG);
		xmlWriter.clearAttribute();
		if (expMesage != null)
			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_FALSE), expMesage);
		else
			responseConstructor.formRequestReply(false);
		xmlWriter.endElement(PCMConstant.EXCEPTION_TAG);

		request.setAttribute("data", xmlWriter.getData());

		return getView("data");
	}


	public boolean checkAccountFullAccess(HttpServletRequest request, String entityId, String currencyId, String accountId,
										  String status) {
		String roleId;
		AccountAccess acctAccess = null;
		boolean chekAccntAccessFlag = false;
		boolean accessFlag = false;
		try {

			AccountAccessManager accountAccessManager = (AccountAccessManager) SwtUtil.getBean("accountAccessManager");
			acctAccess = new AccountAccess();
			roleId = ((CommonDataManager) request.getSession().getAttribute(SwtConstants.CDM_BEAN)).getUser().getRoleId();
			/* Setting role id using bean class */
			acctAccess.getId().setRoleId(roleId);
			/* Setting entity id using bean class */
			acctAccess.getId().setEntityId(entityId);
			/* Setting account id using bean class */
			acctAccess.getId().setAccountId(accountId);
			/* Setting host id using bean class */
			acctAccess.getId().setHostId(SwtUtil.getCurrentHostId());
			accessFlag = accountAccessManager.getRoleAccessDetails(acctAccess);
			if(accessFlag){
				chekAccntAccessFlag = accountAccessManager.checkAcctAccess(acctAccess, status);
			}
			else{
				chekAccntAccessFlag = true;
			}

		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - Exception Catched in [checkAccountFullAccess] method : - "
					  + e.getMessage());
		} finally {
			acctAccess = null;
		}
		return chekAccntAccessFlag;
	}


	public boolean isOpenedAccount(String accountId) {
		String status;
		boolean accountStatusFlag = false;
		try {
			status = preAdviceInputManager.getAccountStatus(accountId);
			if (status != null && status.equalsIgnoreCase(SwtConstants.ACCOUNT_STATUS_FLAG_OPEN)) {
				accountStatusFlag = true;
			}

		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - Exception Catched in [isOpenedAccount] method : - " + e.getMessage());
		} finally {
			status = null;
		}
		return accountStatusFlag;
	}

	public boolean hasPreAdviceAccess(String accountId, String entityId) {
		String preAdviceFlag;
		boolean allowPreAdvice= false;
		try {
			preAdviceFlag = preAdviceInputManager.checkAccountPreAdviceAccess(accountId, entityId);
			if (preAdviceFlag != null && preAdviceFlag.equalsIgnoreCase("Y")) {
				allowPreAdvice = true;
			}

		} catch (SwtException e) {
			log.error(this.getClass().getName() + " - Exception Catched in [hasPreAdviceAccess] method : - " + e.getMessage());
		} finally {
			preAdviceFlag = null;
		}
		return allowPreAdvice;
	}

	public String displayDataDef() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		Iterator<LabelValueBean> itrAcctList = null;
		ResponseHandler responseHandler = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		String width = null;
		String columnOrder = null;
		String hiddenColumns = null;
		Boolean isHeaderGrid=true;
		String userId = null;
		String hostId = null;
		String propertyValue = null;
		String header = null;
		String sourceHeader = null;
		String valueDateFormat = null;
		String postDateFormat = null;
		String currencyFormat = null;
		String headerChecked = null;
		String dataSourceType = null;
		String fromMethod = null;
		String dataType = null;
		ScreenOption PreAdviceInputScreenOption = null;
		ScreenOptionManager screenOptionManager = null;
		String draggable=null;
		HashMap<String, String> headers = new HashMap<String, String>();
		String [] array = null;
		String key=null;
		String value=null;
		String[]keyValue=null;
		Boolean headerIsSaved=false;
		try {
			log.debug(this.getClass().getName()
					  + " - [getAccountsList] - Entering");
			currencyFormat= getAmountFormat(SwtUtil.getCurrentSystemFormats(request.getSession()).getCurrencyFormat());
			valueDateFormat= SwtUtil.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue();
			postDateFormat= SwtUtil.getCurrentSystemFormats(request.getSession())
					.getDateFormatValue();
			draggable= request.getParameter("draggable");
			fromMethod= request.getParameter("fromMethod");
			dataType= request.getParameter("dataType");
			// to get the host id for application
			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			PreAdviceInputScreenOption = new ScreenOption();
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			if (("updateDataTypeConfig").equalsIgnoreCase(fromMethod) || ("updateData").equalsIgnoreCase(fromMethod)) {
				propertyValue = screenOptionManager.getPropertyValue(hostId, userId, SwtConstants.PREADVICEINPUT_ID,
						getDataSourceType(dataType));
			}else {
				// check if user has already preAdvice Input setting or not
				propertyValue=screenOptionManager.getPropertyValue(hostId, userId, SwtConstants.PREADVICEINPUT_ID, SwtConstants.PREADVICEINPUT_OPTIONS);
			}
			if (!SwtUtil.isEmptyOrNull(propertyValue)
				&& !propertyValue.equals("null")) {

				JSONObject prefJSON = new JSONObject(propertyValue);
				header  = (String) prefJSON.get("HEADER");
				sourceHeader  = (String) prefJSON.get("SOURCE_HEADER");
				valueDateFormat  = (String) prefJSON.get("VALUE_DATE_FORMAT");
				postDateFormat  = (String) prefJSON.get("POST_DATE_FORMAT");
				currencyFormat  = (String) prefJSON.get("AMOUNT_FORMAT");
				headerChecked  = prefJSON.has("HEADER_CHECKED")?(String) prefJSON.get("HEADER_CHECKED"):"true";
				dataSourceType =  prefJSON.has("DATA_SOURCE_TYPE")?(String) prefJSON.get("DATA_SOURCE_TYPE")  : SwtConstants.PREADVICEINPUT_CLIPBOARD;
				if(("false").equalsIgnoreCase(headerChecked) && ( ("onLoad").equalsIgnoreCase(fromMethod) || ("updateDataTypeConfig").equalsIgnoreCase(fromMethod))) {
					draggable="true";
				}

				if(!SwtUtil.isEmptyOrNull(sourceHeader)) {
					headerIsSaved=true;
					array = (sourceHeader.substring(1, sourceHeader.length() - 1)).split(",");
					for (int i = 0; i < array.length; i++) {
						for (int j = 0; j < array.length; j++) {
							key = ((array[i].split(":"))[0]).replace("\"", "");
							value = ((array[i].split(":"))[1]).replace("\"", "");
							headers.put(key, value);
						}
					}
				}
			}else {
				dataSourceType = dataType;
			}
			responseHandler = new ResponseHandler();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();

			xmlWriter.startElement(SwtConstants.DATA_DEFINITION);

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);
			responseConstructor.createElement("headerIsSaved", headerIsSaved.toString());
			responseConstructor.createElement("dataSourceType", dataSourceType);
			responseConstructor.createElement("headerChecked", headerChecked);
			xmlWriter.endElement(SwtConstants.SINGLETONS);
			ArrayList<SelectInfo> lstSelect = new ArrayList<SelectInfo>();
			// options drop down list
			ArrayList<OptionInfo> lstOptions = new ArrayList<OptionInfo>();

			/***** value date Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			List<String>  collValDates = new ArrayList<String>(Arrays.asList("YYYY-MM-DD", "DD/MM/YYYY", "MM/DD/YYYY"));
			for (int i = 0; i < collValDates.size(); i++) {
				lstOptions.add(new OptionInfo(collValDates.get(i), collValDates.get(i), false));
			}

			lstSelect.add(new SelectInfo("listValDates", lstOptions));
			/***** value date  Combo End ***********/


			/***** amount Combo Start ***********/
			// get the collection of book code for host id,entity id
			lstOptions = new ArrayList<OptionInfo>();
			List<String>collAmount = new ArrayList<String>(Arrays.asList("999,999.00","999.999,00"));
			for (int i = 0; i < collAmount.size(); i++) {
				lstOptions.add(new OptionInfo(collAmount.get(i), collAmount.get(i), false));
			}
			lstSelect.add(new SelectInfo("listAmount", lstOptions));
			/***** amount Combo End ***********/

			/***** Post date Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			List<String> collPostDate = new ArrayList<String>(Arrays.asList("YYYY-MM-DD", "DD/MM/YYYY", "MM/DD/YYYY"));
			for (int i = 0; i < collPostDate.size(); i++) {
				lstOptions.add(new OptionInfo(collPostDate.get(i), collPostDate.get(i), false));
			}
			lstSelect.add(new SelectInfo("listPostDates", lstOptions));
			/***** Post date Combo End ***********/

			/***** data source Combo Start ***********/
			lstOptions = new ArrayList<OptionInfo>();
			List<String> collDataSource= new ArrayList<String>(Arrays.asList("Clipboard", "CSV file", "Excel file"));
			for (int i = 0; i < collDataSource.size(); i++) {
				lstOptions.add(new OptionInfo(collDataSource.get(i), collDataSource.get(i), false));
			}
			lstSelect.add(new SelectInfo("dataSourcesList", lstOptions));
			/***** data source Combo End ***********/

			responseConstructor.formSelect(lstSelect);

			/******* Headers Grid ******/
			responseConstructor.formGridStart("HeadersGrid");
			if (!SwtUtil.isEmptyOrNull(header) && !header.equals("null")) {
				responseConstructor.formColumn(getSavedGridColumns(width, columnOrder, hiddenColumns, false, false,header,Boolean.parseBoolean(draggable)));
			}else {
				responseConstructor.formColumn(getDataDefGridColumns(width, columnOrder, hiddenColumns, false,Boolean.parseBoolean(draggable)));
			}
			responseConstructor.formRowsStart(2);
			if (!headers.isEmpty()) {
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.PREADVCIE_ENTITY_ID, headers.get("Entity_ID"));
				responseConstructor.createRowElement(SwtConstants.PRED_STATUS, headers.get("Pred_Status"));
				responseConstructor.createRowElement(SwtConstants.CCY, headers.get("Ccy"));
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ID1, headers.get("Account_ID"));
				responseConstructor.createRowElement(SwtConstants.VALUE_DATE, headers.get("Value_Date"));
				responseConstructor.createRowElement(SwtConstants.AMOUNT, headers.get("Amount"));
				responseConstructor.createRowElement(SwtConstants.SIGN, headers.get("Sign"));
				responseConstructor.createRowElement(SwtConstants.REFERENCE, headers.get("Reference"));
				responseConstructor.createRowElement(SwtConstants.COUNTER_PARTY_ID, headers.get("CounterParty_ID"));
				responseConstructor.createRowElement(SwtConstants.CPAERTY_TEXT, headers.get("Cparty_Text"));
				responseConstructor.createRowElement(SwtConstants.MATCH_PARTY, headers.get("Match_Party"));
				responseConstructor.createRowElement(SwtConstants.PRODUCT_TYPE, headers.get("Product_Type"));
				responseConstructor.createRowElement(SwtConstants.BOOK_CODE, headers.get("Book_Code"));
				responseConstructor.createRowElement(SwtConstants.POST_DATE, headers.get("Post_Date"));
				responseConstructor.formRowEnd();

			} else {
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(SwtConstants.PREADVCIE_ENTITY_ID, "Entity_ID");
				responseConstructor.createRowElement(SwtConstants.PRED_STATUS, "Pred_Status");
				responseConstructor.createRowElement(SwtConstants.CCY, "Ccy");
				responseConstructor.createRowElement(SwtConstants.ACCOUNT_ID1, "Account_ID");
				responseConstructor.createRowElement(SwtConstants.VALUE_DATE, "Value_Date");
				responseConstructor.createRowElement(SwtConstants.AMOUNT, "Amount");
				responseConstructor.createRowElement(SwtConstants.SIGN, "Sign");
				responseConstructor.createRowElement(SwtConstants.REFERENCE, "Reference");
				responseConstructor.createRowElement(SwtConstants.COUNTER_PARTY_ID, "CounterParty_ID");
				responseConstructor.createRowElement(SwtConstants.CPAERTY_TEXT, "Cparty_Text");
				responseConstructor.createRowElement(SwtConstants.MATCH_PARTY, "Match_Party");
				responseConstructor.createRowElement(SwtConstants.PRODUCT_TYPE, "Product_Type");
				responseConstructor.createRowElement(SwtConstants.BOOK_CODE, "Book_Code");
				responseConstructor.createRowElement(SwtConstants.POST_DATE, "Post_Date");
				responseConstructor.formRowEnd();
			}
			responseConstructor.formRowStart();
			responseConstructor.createRowElement(SwtConstants.PREADVCIE_ENTITY_ID, "M");
			responseConstructor.createRowElement(SwtConstants.PRED_STATUS, "M");
			responseConstructor.createRowElement(SwtConstants.CCY, "M");
			responseConstructor.createRowElement(SwtConstants.ACCOUNT_ID1, "M");
			responseConstructor.createRowElement(SwtConstants.VALUE_DATE, "M");
			responseConstructor.createRowElement(SwtConstants.AMOUNT, "M");
			responseConstructor.createRowElement(SwtConstants.SIGN, "M");
			responseConstructor.createRowElement(SwtConstants.REFERENCE, "O");
			responseConstructor.createRowElement(SwtConstants.COUNTER_PARTY_ID, "O");
			responseConstructor.createRowElement(SwtConstants.CPAERTY_TEXT, "O");
			responseConstructor.createRowElement(SwtConstants.MATCH_PARTY, "O");
			responseConstructor.createRowElement(SwtConstants.PRODUCT_TYPE, "O");
			responseConstructor.createRowElement(SwtConstants.BOOK_CODE, "O");
			responseConstructor.createRowElement(SwtConstants.POST_DATE, "O");
			responseConstructor.formRowEnd();
			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			/******* source format Grid ******/
			responseConstructor.formGridStart("SourceFormatGrid");
			if (!SwtUtil.isEmptyOrNull(header) && !header.equals("null")) {
				responseConstructor.formColumn(getSavedGridColumns(width, columnOrder, hiddenColumns,false, true,header,Boolean.parseBoolean(draggable)));
			}else {

				responseConstructor.formColumn(getDataDefGridColumns(width, columnOrder, hiddenColumns, true,Boolean.parseBoolean(draggable)));
			}
			responseConstructor.formRowsStart(1);
			responseConstructor.formRowStart();
			responseConstructor.createRowElement(SwtConstants.PREADVCIE_ENTITY_ID, "TEXT(12)");
			responseConstructor.createRowElement(SwtConstants.PRED_STATUS, "'I','E','C'");
			responseConstructor.createRowElement(SwtConstants.CCY, "TEXT(3)");
			responseConstructor.createRowElement(SwtConstants.ACCOUNT_ID1, "TEXT(35)");
			responseConstructor.createRowElement(SwtConstants.VALUE_DATE, valueDateFormat.toUpperCase());
			responseConstructor.createRowElement(SwtConstants.AMOUNT, currencyFormat);
			responseConstructor.createRowElement(SwtConstants.SIGN, "'C','D'");
			responseConstructor.createRowElement(SwtConstants.REFERENCE, "TEXT(35)");
			responseConstructor.createRowElement(SwtConstants.COUNTER_PARTY_ID, "TEXT(12)");
			responseConstructor.createRowElement(SwtConstants.CPAERTY_TEXT, "TEXT(35)");
			responseConstructor.createRowElement(SwtConstants.MATCH_PARTY, "TEXT(12)");
			responseConstructor.createRowElement(SwtConstants.PRODUCT_TYPE, "TEXT(16)");
			responseConstructor.createRowElement(SwtConstants.BOOK_CODE, "TEXT(12)");
			responseConstructor.createRowElement(SwtConstants.POST_DATE, postDateFormat.toUpperCase());
			responseConstructor.formRowEnd();

			responseConstructor.formRowsEnd();

			responseConstructor.formGridEnd();

			/******* Data Preview Grid ******/
			responseConstructor.formGridStart("DataPreviewGrid");
			if (!SwtUtil.isEmptyOrNull(header) && !header.equals("null")) {
				responseConstructor.formColumn(getSavedGridColumns(width, columnOrder, hiddenColumns,true, false,header,Boolean.parseBoolean(draggable)));
			}else {
				responseConstructor.formColumn(getDataPreviewGridColumns(width, columnOrder, hiddenColumns));
			}
			responseConstructor.formGridEnd();

			xmlWriter.endElement(SwtConstants.DATA_DEFINITION);
			request.setAttribute("data", xmlWriter.getData());

			// set the method name,last ref time and access level on request
			// attribute which are used in front end
			request.setAttribute("methodName", "success");
			// getMenuEntityCurrGrpAccess(request, null, null);
			log.debug(this.getClass().getName() + " - [display()] - Exit");
			return getView("data");

		} catch (SwtException swtexp) {
			log
					.error("SwtException Catch in PreAdviceInputAction.'displayDataDef' method : "
						   + swtexp.getMessage());
			SwtUtil.logException(swtexp, request, "");
		} catch (Exception exp) {
			log
					.error("Exception Catch in PreAdviceInputAction.'displayDataDef' method : "
						   + exp.getMessage());
			SwtUtil.logException(SwtErrorHandler.getInstance().handleException(
							exp, "displayDataDef", PreAdviceInputAction.class),
					request, "");
		} finally {
			// nullify objects
		}
		return null;

	}


	/**
	 * This method is used to save user settings in s_screen_option table
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */

	public String saveUserSettings() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String hostId = null;
		String userId = null;
		ScreenOptionManager screenOptionManager = null;
		ScreenOption PreAdviceInputScreenOption = null;
		ScreenOption PreAdviceInputDataScreenOption = null;
		String newHeader = null;
		String newValDateFormat = null;
		String newSourceHeader = null;
		String newPostDateFormat = null;
		String newAmountFormat = null;
		String oldPropertyOption =null;
		String headerChecked =null;
		String dataSourceType =null;
		Collection<ScreenOption> option =null;
		Collection<ScreenOption> dataOption =null;
		try {
			log.debug(this.getClass().getName() + "- [saveUserSettings] - starting ");
			PreAdviceInputScreenOption = new ScreenOption();
			PreAdviceInputDataScreenOption = new ScreenOption();
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			newHeader = request.getParameter("newHeader");
			newAmountFormat = request.getParameter("newAmountFormat");
			newValDateFormat = request.getParameter("newValDateFormat");
			newPostDateFormat = request.getParameter("newPostDateFormat");
			newSourceHeader = request.getParameter("newSourceHeader");
			headerChecked =request.getParameter("headerChecked");
			dataSourceType= request.getParameter("dataSourceType");

			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());

			//last options
			PreAdviceInputScreenOption.getId().setHostId(hostId);
			PreAdviceInputScreenOption.getId().setUserId(userId);
			PreAdviceInputScreenOption.getId().setScreenId(SwtConstants.PREADVICEINPUT_ID);
			PreAdviceInputScreenOption.getId().setPropertyName(SwtConstants.PREADVICEINPUT_OPTIONS);

			//last options according to data source type
			PreAdviceInputDataScreenOption.getId().setHostId(hostId);
			PreAdviceInputDataScreenOption.getId().setUserId(userId);
			PreAdviceInputDataScreenOption.getId().setScreenId(SwtConstants.PREADVICEINPUT_ID);
			PreAdviceInputDataScreenOption.getId().setPropertyName(getDataSourceType(dataSourceType));


			// Save user preferences
			JSONObject sampleObject = new JSONObject();
			sampleObject.put("HEADER", newHeader);
			sampleObject.put("SOURCE_HEADER", newSourceHeader);
			sampleObject.put("VALUE_DATE_FORMAT", newValDateFormat);
			sampleObject.put("POST_DATE_FORMAT", newPostDateFormat);
			sampleObject.put("AMOUNT_FORMAT", newAmountFormat);
			sampleObject.put("HEADER_CHECKED", headerChecked);
			sampleObject.put("DATA_SOURCE_TYPE", dataSourceType);

			PreAdviceInputScreenOption.setPropertyValue(sampleObject.toString());
			PreAdviceInputDataScreenOption.setPropertyValue(sampleObject.toString());
			// found=screenOptionManager.checkIfScreenOptionsExists(hostId, userId,SwtConstants.PREADVICEINPUT_ID, SwtConstants.PREADVICEINPUT_OPTIONS);
			option = screenOptionManager.getScreenOption(PreAdviceInputScreenOption);

			if (option != null && option.size() > 0) {
				oldPropertyOption = (screenOptionManager.getScreenOption(PreAdviceInputScreenOption)).iterator().next()
						.getPropertyValue();
				if (!oldPropertyOption.equals(sampleObject.toString())) {
					// update preAdvice input options
					screenOptionManager.savePreAdviceOptions(PreAdviceInputScreenOption, true);
				}

			} else {
				// save preAdvice input options
				screenOptionManager.savePreAdviceOptions(PreAdviceInputScreenOption, false);

			}

			dataOption = screenOptionManager.getScreenOption(PreAdviceInputDataScreenOption);

			if (dataOption != null && dataOption.size() > 0) {
				oldPropertyOption = (screenOptionManager.getScreenOption(PreAdviceInputDataScreenOption)).iterator().next()
						.getPropertyValue();
				if (!oldPropertyOption.equals(sampleObject.toString())) {
					// update preAdvice input options
					screenOptionManager.savePreAdviceOptions(PreAdviceInputDataScreenOption, true);
				}

			} else {
				// save preAdvice input options
				screenOptionManager.savePreAdviceOptions(PreAdviceInputDataScreenOption, false);

			}
			// Setting the reply_status_ok,reply_message in request object
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			log.debug(this.getClass().getName() + "- [saveUserSettings] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [saveUserSettings] - Exception - " + e.getMessage());

			request.setAttribute("reply_status_ok", "false");
			request.setAttribute("reply_message", e.getMessage());
			request.setAttribute("reply_location", e.getStackTrace()[0].getClassName() + "."
												   + e.getStackTrace()[0].getMethodName() + ":" + e.getStackTrace()[0].getLineNumber());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "saveUserSettings", PreAdviceInputAction.class);

		} finally {
			PreAdviceInputScreenOption = null;
			screenOptionManager = null;
			hostId = null;
			userId = null;
		}
		return getView("statechange");
	}


	/**
	 * 	 * This method is used to delete  user settings from  s_screen_option table
	 *
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return
	 * @throws SwtException
	 */

	public String deleteUserSettings() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String hostId = null;
		String userId = null;
		ScreenOptionManager screenOptionManager = null;
		ScreenOption PreAdviceInputScreenOption = null;
		ScreenOption PreAdviceInputScreenOptionType = null;
		Boolean found =false;
		Boolean foundType =false;
		String dataSourceType= null;
		try {
			log.debug(this.getClass().getName() + "- [deleteUserSettings] - starting ");
			PreAdviceInputScreenOption = new ScreenOption();
			PreAdviceInputScreenOptionType = new ScreenOption();
			screenOptionManager = (ScreenOptionManager) (SwtUtil.getBean("screenOptionManager"));
			hostId = CacheManager.getInstance().getHostId();
			userId = SwtUtil.getCurrentUserId(request.getSession());
			dataSourceType= request.getParameter("sourceDataType");
			PreAdviceInputScreenOption.getId().setHostId(hostId);
			PreAdviceInputScreenOption.getId().setUserId(userId);
			PreAdviceInputScreenOption.getId().setScreenId(SwtConstants.PREADVICEINPUT_ID);
			PreAdviceInputScreenOption.getId().setPropertyName(SwtConstants.PREADVICEINPUT_OPTIONS);
			found=screenOptionManager.checkIfScreenOptionsExists(hostId, userId, SwtConstants.PREADVICEINPUT_ID, SwtConstants.PREADVICEINPUT_OPTIONS);
			if(found) {
				// delete  default preAdvice input options
				screenOptionManager.deletePreAdviceInputOption(PreAdviceInputScreenOption);
			}

			//delete the selected data source type configuration
			PreAdviceInputScreenOptionType.getId().setHostId(hostId);
			PreAdviceInputScreenOptionType.getId().setUserId(userId);
			PreAdviceInputScreenOptionType.getId().setScreenId(SwtConstants.PREADVICEINPUT_ID);
			PreAdviceInputScreenOptionType.getId().setPropertyName(getDataSourceType(dataSourceType));
			foundType=screenOptionManager.checkIfScreenOptionsExists(hostId, userId, SwtConstants.PREADVICEINPUT_ID, getDataSourceType(dataSourceType));
			if(foundType) {
				// delete  preAdvice input options
				screenOptionManager.deletePreAdviceInputOption(PreAdviceInputScreenOptionType);
			}

			log.debug(this.getClass().getName() + "- [deleteUserSettings] - exiting ");
		} catch (Exception e) {
			log.error(this.getClass().getName() + " - [deleteUserSettings] - Exception - " + e.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(e, "deleteUserSettings", PreAdviceInputAction.class);

		} finally {
			PreAdviceInputScreenOption = null;
			screenOptionManager = null;
			hostId = null;
			userId = null;
		}
		return null;
	}



	/**
	 * This method return the access index zero if menu,entity,currency group
	 * having full access otherwise return one
	 *
	 * @param request
	 * @param entityId
	 * @param currencyId
	 * @return accessInd
	 * @throws SwtException
	 */
	private int getMenuEntityCurrGrpAccess(HttpServletRequest request,
										   String entityId, String currencyId) throws SwtException {
		// access index to hold access for menu,entity,currency group
		int accessInd;
		// To hold menu access
		int menuAccess;
		// To hold entity access
		int entityAccess;
		// To hold currency group access
		int currGrpAccess;
		// To hold the role id get from session
		String roleId = null;
		// To hold the menu access get from request
		String menuAccessAsStr = null;
		// To hold the collection for user entity access list
		Collection<EntityUserAccess> collUserEntity = null;
		try {
			log.debug(this.getClass().getName()
					  + " - [getMenuEntityCurrGrpAccess] - Entering");
			// get the role id for current user from session.
			roleId = ((CommonDataManager) request.getSession().getAttribute(
					SwtConstants.CDM_BEAN)).getUser().getRoleId();
			// get the access id for menu
			menuAccessAsStr = request.getParameter("menuAccessId");
			if (!SwtUtil.isEmptyOrNull(menuAccessAsStr)
				&& !menuAccessAsStr.equals("null")) {
				// set the integer value for menu access
				menuAccess = Integer.parseInt(menuAccessAsStr);
			} else {
				menuAccess = 0;
			}
			// Condition to check entity id is null
			if (SwtUtil.isEmptyOrNull(entityId)) {
				// set full access
				entityAccess = 0;

			} else {
				// get the user accessible entity collection
				collUserEntity = SwtUtil.getUserEntityAccessList(request
						.getSession());
				// get the access for given entity
				entityAccess = SwtUtil.getUserEntityAccess(collUserEntity,
						entityId);
			}
			// Condition to check currency Id is null
			if (SwtUtil.isEmptyOrNull(currencyId)) {
				// set full access
				currGrpAccess = 0;
			} else {
				// get the access index for given currency id
				currGrpAccess = SwtUtil.getSwtMaintenanceCache()
						.getCurrencyAccess(roleId, entityId, currencyId);

			}
			// IF menu,entity & currency group having full access then set the
			// access index as 0 oherwise set as 1
			if ((menuAccess == 0) && (entityAccess == 0)
				&& (currGrpAccess == 0)) {
				accessInd = 0;
			} else {
				accessInd = 1;
			}
			// set the menu access Id,menuEntityCurrGrpAccess in the request
			// attribute to get actual access for menu, entity currency group
			// based on this value button will be enabled/disabled in front end
			request.setAttribute("menuAccessId", "" + menuAccessAsStr);
			request.setAttribute("menuEntityCurrGrpAccess", "" + accessInd);
			log.debug(this.getClass().getName()
					  + " - [getMenuEntityCurrGrpAccess] - Existing");
		} catch (Exception e) {
			log.error(this.getClass().getName()
					  + " - [getMenuEntityCurrGrpAccess] - Exception -"
					  + e.getMessage());
			throw new SwtException(e.getMessage());
		} finally {
			// nullify objects
			roleId = null;
			menuAccessAsStr = null;
			collUserEntity = null;
		}
		return accessInd;
	}

	private String getDataSourceType(String type) {
		String preAdviceProperty = null;

		switch (type) {
			case "Clipboard":
				preAdviceProperty = SwtConstants.PREADVICEINPUT_CLIPBOARD;
				break;
			case "Excel file":
				preAdviceProperty = SwtConstants.PREADVICEINPUT_EXCEL;
				break;
			case "CSV file":
				preAdviceProperty = SwtConstants.PREADVICEINPUT_CSV;
				break;
			default:
				break;
		}

		return preAdviceProperty;

	}


	public String partySearch() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

		request.setAttribute("screenName", request.getParameter("screenName"));
		request.setAttribute("entityId", request.getParameter("entityId"));
		return getView("partysearch");
	}

	public String partySearchDisplay() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		String errorMessage = null;
		SystemFormats systemFormats = null;
		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;

		try {

			log.debug(this.getClass().getName() + " - [partySearchDisplay] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "PartySearch";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.startElement(PCMConstant.PARTY_SEARCH);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(null);
			// form column details
			responseConstructor.formColumn(getPartySearchGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(0);
			responseConstructor.formRowEnd();

			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.PARTY_SEARCH);
			request.setAttribute("data", xmlWriter.getData());

			// build XML response
			return getView("data");

		} catch (SwtException swtexp) {
			errorMessage = swtexp.getStackTrace()[0].getClassName() + "." + swtexp.getStackTrace()[0].getMethodName()
						   + ":" + swtexp.getStackTrace()[0].getLineNumber() + " " + swtexp.getMessage();
			log.error(this.getClass().getName() + " - [partySearchDisplay] - SwtException -" + errorMessage);
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0].getClassName() + "."
												   + swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());

		} finally {
			systemFormats = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [partySearchDisplay] - Exit");
		}
		return null;
	}

	public String partySearchResult() throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();


		List<Party> partiesDetails = new ArrayList<Party>();
		String errorMessage = null;
		SystemFormats systemFormats = null;
		String partyId = null;
		String partyName = null;
		String entityId = null;
		String selectedsort = null;
		int maxPage = 0;
		int currentPage = 0;
		int pageSize = 0;
		int totalCount = 0;
		String currPageStr = null;
		ArrayList<Party> partiesCollection = null;
		HashMap<String, Object> totalMap = null;
		try {

			log.debug(this.getClass().getName() + " - [display] - " + "Entry");
			partiesCollection = new ArrayList<Party>();
			currentPage = 1;
			partyId = request.getParameter("partyId");
			partyName = request.getParameter("partyName");
			entityId = request.getParameter("entityId");
			selectedsort = request.getParameter("selectedsort");
			currPageStr = request.getParameter("currentPage");
			pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
			if (!SwtUtil.isEmptyOrNull(currPageStr)) {
				currentPage = Integer.parseInt(currPageStr);
			}
			totalCount = preAdviceInputManager.getTotalCount(partyName, partyId, entityId);
			maxPage = setMaxPageAttribute(totalCount, pageSize);

			// Retrieves current system formats
			systemFormats = SwtUtil.getCurrentSystemFormats(request.getSession());
//			entityId="RABONL2U";
			partiesCollection = preAdviceInputManager.getPartySearchResult(partyId, partyName, entityId, pageSize, currentPage, selectedsort);
			request.setAttribute("spreadProfilesDetailsList", partiesDetails);
			request.setAttribute("menuAccessId", request.getParameter("menuAccessId"));
			request.setAttribute("reply_status_ok", SwtConstants.STR_TRUE);
			request.setAttribute("reply_message", "Data fetch OK");
			// build XML response
			if (maxPage == 0) {
				currentPage = 0;
			}
			return sendDisplayResponse(partiesCollection, systemFormats, currentPage, maxPage);

		} catch (SwtException swtexp) {
			errorMessage = swtexp.getStackTrace()[0].getClassName() + "." + swtexp.getStackTrace()[0].getMethodName()
						   + ":" + swtexp.getStackTrace()[0].getLineNumber() + " " + swtexp.getMessage();
			log.error(this.getClass().getName() + " - [display] - SwtException -" + errorMessage);
			request.setAttribute("reply_status_ok", SwtConstants.STR_FALSE);
			request.setAttribute("reply_message", swtexp.getMessage());
			request.setAttribute("reply_location", swtexp.getStackTrace()[0].getClassName() + "."
												   + swtexp.getStackTrace()[0].getMethodName() + ":" + swtexp.getStackTrace()[0].getLineNumber());

		} finally {
			systemFormats = null;
			partiesDetails = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [display] - Exit");
		}
		return null;
	}


	/**
	 * This function is used to sets maximum Page The maximum page count is done
	 * by calculation ie. The total record value got from the DB and the
	 * pageSize is got from the properties file.
	 *
	 * @param totalCount
	 * @param pageSize
	 * @return int
	 */
	private int setMaxPageAttribute(int totalCount, int pageSize) {
		log.debug(this.getClass().getName()
				  + "- [setMaxPageAttribute] - Entering");
		/* Class instance declaration */
		int maxPage;
		int remainder;
		maxPage = (totalCount) / (pageSize);
		remainder = totalCount % pageSize;
		if (remainder > 0) {
			maxPage++;
		}
		log.debug(this.getClass().getName()
				  + "- [setMaxPageAttribute] - Exiting");
		return maxPage;
	}



	/**
	 * This method forms the xml for displaying the parties list.
	 *
	 * @param width         - passing default widths
	 * @param columnOrder   - passing default order
	 * @param partiesDetails   - passing parties details
	 * @param languageId    - passing languageId
	 * @param systemFormats - passing system formats date
	 * @param currentPage
	 * @param maxPage
	 * @return
	 */
	public String sendDisplayResponse(List<Party> partiesDetails, SystemFormats systemFormats, int currentPage, int maxPage) throws SwtException {

		SwtResponseConstructor responseConstructor = null;
		SwtXMLWriter xmlWriter = null;
		// String variable to hold userId
		String userId = null;
		// Date variable for modifyDate
		Date mDate = null;
		// String variable to hold componentId
		String componentId = null;
		// String variable to hold column width
		String width = null;
		// String variable to column order
		String columnOrder = null;
		// String variable to column hidden
		String hiddenColumns = null;
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		try {

			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - " + "Entry");
			// Get currentUserId from session
			userId = SwtUtil.getCurrentUser(request.getSession()).getId().getUserId();
			responseConstructor = new SwtResponseConstructor();
			xmlWriter = responseConstructor.getXMLWriter();
			componentId = "PartySearch";

			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(PCMConstant.SCREEN_ID, componentId);
			xmlWriter.addAttribute(PCMConstant.CURRUSER, userId);
			// Adding screen id and current user id as attributes
			xmlWriter.addAttribute(SwtConstants.DATE_FORMAT, SwtUtil.getCurrentDateFormat(request.getSession()));
			xmlWriter.startElement(PCMConstant.PARTY_SEARCH);
			xmlWriter.clearAttribute();

			responseConstructor.formRequestReply(Boolean.parseBoolean(SwtConstants.STR_TRUE),
					SwtConstants.DATA_FETCH_OK);
			// forms singleton node
			xmlWriter.startElement(SwtConstants.SINGLETONS);

			mDate = SwtUtil.getTestDateFromParams(SwtUtil.getCurrentHostId());
			// String variable for test date
			String testDate = "";
			// formats the date
			if (mDate != null)
				testDate = SwtUtil.formatDate(mDate, systemFormats.getDateFormatValue());

			responseConstructor.createElement(PCMConstant.TEST_DATE, testDate);

			xmlWriter.endElement(SwtConstants.SINGLETONS);

			responseConstructor.formGridStart();
			// form paging details
			responseConstructor.formPaging(new PageInfo(maxPage,currentPage));
			// form column details
			responseConstructor.formColumn(getPartySearchGridColumns(width, columnOrder, hiddenColumns));

			// form rows (records)
			responseConstructor.formRowsStart(partiesDetails.size());
			// Iterating rules definition details
			for (Iterator<Party> it = partiesDetails.iterator(); it.hasNext();) {
				// Obtain spread Profile tag from iterator
				Party party = it.next();
				responseConstructor.formRowStart();
				responseConstructor.createRowElement(PCMConstant.PARTY_ID, party.getId().getPartyId());
				responseConstructor.createRowElement(PCMConstant.PARTY_NAME, party.getPartyName());
				responseConstructor.formRowEnd();
			}

			responseConstructor.formRowsEnd();
			responseConstructor.formGridEnd();

			xmlWriter.endElement(PCMConstant.PARTY_SEARCH);

			request.setAttribute("data", xmlWriter.getData());
			return getView("data");
		} catch (SwtException ex) {
			xmlWriter.clearData();
			// getExceptionXmlData(ex.getMessage());
			// log exception in database
//			SwtUtil.logErrorInDatabase(
//					SwtErrorHandler.getInstance().handleException(ex,
//							"sendDisplayResponse", this.getClass()), "PCM",
//					userId, request.getRemoteAddr(), "Y");
			log.error(this.getClass().getName() + " - SwtException Catched in [sendDisplayResponse] method : - "
					  + ex.getMessage());
			return getView("fail");
		} catch (Exception ex) {
			xmlWriter.clearData();
			// logs exception in data base
//			SwtUtil.logErrorInDatabase(
//					SwtErrorHandler.getInstance().handleException(ex,
//							"sendDisplayResponse", this.getClass()), "PCM",
//					userId, request.getRemoteAddr(), "Y");

			// log error message

			log.error(this.getClass().getName() + " - Exception Catched in [sendDisplayResponse] method : - "
					  + ex.getMessage());
			return getView("fail");
		} finally {
			// nullify objects
			xmlWriter.destroyWriter();
			responseConstructor = null;
			xmlWriter = null;
			userId = null;
			mDate = null;
			componentId = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [ sendDisplayResponse ] - Exit");
		}
	}

	private List<ColumnInfo> getPartySearchGridColumns(String width, String columnOrder, String hiddenColumns)
			throws SwtException {

		// Array list to hold column order
		ArrayList<String> orders = null;
		// String array variable to hold column order property
		String[] columnOrderProp = null;
		// Iterator variable to hold column order
		Iterator<String> columnOrderItr = null;
		// Hash map to hold column width
		LinkedHashMap<String, String> widths = null;
		// String array variable to hold width property
		String[] columnWidthProperty = null;
		// List for column info
		List<ColumnInfo> lstColumns = null;
		// Hash map to hold column hidden_Columns
		HashMap<String, Boolean> hiddenColumnsMap = new LinkedHashMap<String, Boolean>();
		// Array list to hold hidden Column array
		ArrayList<String> lstHiddenColunms = null;
		// String array variable to hold hidden columns property
		String[] hiddenColumnsProp = null;

		try {
			log.debug(this.getClass().getName() + " - [ getPartySearchGridColumns ] - Entry");
			// Condition to check width is null

			if (SwtUtil.isEmptyOrNull(width)) {
				// default width for columns
				width = PCMConstant.PARTY_ID + "=200," + PCMConstant.PARTY_NAME + "=300";

			}

			// Obtain width for each column
			columnWidthProperty = width.split(",");

			// Loop to insert each column in hash map
			widths = new LinkedHashMap<String, String>();
			for (int i = 0; i < columnWidthProperty.length; i++) {
				// Condition to check index of = is -1
				if (columnWidthProperty[i].indexOf("=") != -1) {
					String[] propval = columnWidthProperty[i].split("=");
					widths.put(propval[0], propval[1]);
				}
			}

			// Condition to check column order is null or empty
			if (SwtUtil.isEmptyOrNull(columnOrder)) {
				// Default values for column order
				columnOrder = PCMConstant.PARTY_ID + "," + PCMConstant.PARTY_NAME;
			}
			orders = new ArrayList<String>();
			// Split the columns using , and save in string array
			columnOrderProp = columnOrder.split(",");

			// Loop to enter column order in array list
			for (int i = 0; i < columnOrderProp.length; i++) {
				// Adding the Column values to ArrayList
				orders.add(columnOrderProp[i]);
				hiddenColumnsMap.put(columnOrderProp[i], true);
			}

			/* Condition to check column hidden is null or empty */
			if (!SwtUtil.isEmptyOrNull(hiddenColumns)) {
				lstHiddenColunms = new ArrayList<String>();
				/* Split the hidden columns , and save in string array */
				hiddenColumnsProp = hiddenColumns.split(",");

				for (int i = 0; i < hiddenColumnsProp.length; i++) {
					/* Adding the Column values to ArrayList */
					lstHiddenColunms.add(hiddenColumnsProp[i]);
				}

				Iterator<String> listKeys = hiddenColumnsMap.keySet().iterator();
				for (int i = 0; i < hiddenColumnsMap.size(); i++) {
					String columnKey = listKeys.next();
					for (int j = 0; j < lstHiddenColunms.size(); j++) {
						if (columnKey.equals(lstHiddenColunms.get(j))) {
							hiddenColumnsMap.put(columnKey, false);
							break;
						}
					}
				}
			}

			columnOrderItr = orders.iterator();
			lstColumns = new ArrayList<ColumnInfo>();

			while (columnOrderItr.hasNext()) {
				String order = (String) columnOrderItr.next();

				// Party Id
				if (order.equals(PCMConstant.PARTY_ID))
					lstColumns.add(new ColumnInfo(PCMConstant.PARTY_ID_COLUMN_HEADER, PCMConstant.PARTY_ID,
							PCMConstant.COLUMN_TYPE_STRING, 0, Integer.parseInt(widths.get(PCMConstant.PARTY_ID)),
							false, true, hiddenColumnsMap.get(PCMConstant.PARTY_ID)));

				// Party Name
				if (order.equals(PCMConstant.PARTY_NAME))
					lstColumns.add(new ColumnInfo(PCMConstant.PARTY_NAME_COLUMN_HEADER, PCMConstant.PARTY_NAME,
							PCMConstant.COLUMN_TYPE_STRING, 1, Integer.parseInt(widths.get(PCMConstant.PARTY_NAME)), false,
							false, hiddenColumnsMap.get(PCMConstant.PARTY_NAME)));
			}

		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName()
					  + " - Exception Catched in [getPartySearchGridColumns] method : - " + ex.getMessage());

			throw SwtErrorHandler.getInstance().handleException(ex, "getPartySearchGridColumns",
					this.getClass());

		} finally {
			// Nullify Objects
			orders = null;
			columnOrderProp = null;
			columnOrderItr = null;
			widths = null;
			columnWidthProperty = null;
			// log debug message
			log.debug(this.getClass().getName() + " - [getPartySearchGridColumns] - Exit");
		}
		// return XML columns
		return lstColumns;
	}

}
