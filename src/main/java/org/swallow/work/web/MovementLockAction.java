/*
 * Created on Jan 03, 2006
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package org.swallow.work.web;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import org.swallow.config.springMVC.BaseController;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.util.CacheManager;
import org.swallow.util.SwtUtil;

import org.swallow.work.service.MovementLockManager;

//Servlet Related classes
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.swallow.util.LabelValueBean;
import org.springframework.beans.factory.annotation.Autowired;



/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */



import java.util.Collection;
import java.util.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.ArrayList;
@Scope("prototype")
@Controller
@RequestMapping(value = {"/movementLock", "/movementLock.do"})
public class MovementLockAction extends BaseController {
    private static final Map<String, String> viewMap = new HashMap<>();
    static {
        viewMap.put("fail", "error");
    }

    private String getView(String resultName) {
        return viewMap.getOrDefault(resultName, "error");
    }

    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
    public String execute(@RequestParam(value = "method", required = false) String method,
                          HttpServletRequest request, HttpServletResponse response) throws SwtException {
        method = String.valueOf(method);
        switch (method) {
            case "checkLock":
                return checkLock();
            case "checkLocks":
                return checkLocks();
            case "lockMovement":
                return lockMovement();
            case "lockMatch":
                return lockMatch();
            case "unlockMovement":
                return unlockMovement();
            case "unlockMovements":
                return unlockMovements();
            case "lockMovementForMMSD":
                return lockMovementForMMSD();
        }


    return null;
}



    @Autowired
private MovementLockManager movementLockManager = null;
    private final Log log = LogFactory.getLog(MovementLockAction.class);

    public void setMovementLockManager(MovementLockManager movementLockManager) {
        this.movementLockManager = movementLockManager;
    }

	/*Mantis 813 Method Written by Saminathan to check lock exists for a movement*/
    /**    
     * @param ActionMapping
     * @param ActionForm
     * @param HttpServletRequest
     * @param HttpServletResponse 
     * @return ActionForward 
     */
    
    public String checkLock()
            throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

    	String logged_UserId,locked_userId = "";
    	try{
    		
    		
            String moevementId = request.getParameter("movementId");
            Long moevementLongId = null;
            if (moevementId != null) {
                moevementLongId = Long.valueOf(moevementId);
            }
            logged_UserId= SwtUtil.getCurrentUserId(request.getSession());  	
            locked_userId = movementLockManager.checkLock(moevementLongId);
            
            if (locked_userId != null && !locked_userId.equals(logged_UserId) ) {
                response.getWriter().print(locked_userId);
            } else {
                response.getWriter().print(true);
            }
            
    	 }catch (Exception exp) {
            log.debug(
                    "Exception Catch in MovementLockAction.'checkLock' method : " +
                    exp.getMessage());
                SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp,
                        "lockMovement", MovementLockAction.class), request, "");
                return null;
            }
    	 return null;
    }
     
    /**   This function checks if any of the passed movements ids are locked by another user
     * @param ActionMapping
     * @param ActionForm
     * @param HttpServletRequest
     * @param HttpServletResponse 
     * @return ActionForward 
     */
    
    public String checkLocks()
    				throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

    	String loggedUserId,lockedUserIds = "";
    	String movementIds = null;
    	String hostId = null;
    	try{
    		movementIds = request.getParameter("movementIds");
    		hostId = CacheManager.getInstance().getHostId();
    		if(!SwtUtil.isEmptyOrNull(movementIds)) {
    			movementIds = movementIds.substring(0, movementIds.length() - 1);
    			loggedUserId= SwtUtil.getCurrentUserId(request.getSession());  	
    			lockedUserIds = movementLockManager.getMovementsLocks(hostId, loggedUserId, movementIds);
    		}
    		if (lockedUserIds != null) {
    			response.getWriter().print(lockedUserIds);
    		} else {
    			response.getWriter().print(true);
    		}
    		
    	}catch (Exception exp) {
    		log.debug(
    				"Exception Catch in MovementLockAction.'checkLock' method : " +
    						exp.getMessage());
    		SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp,
    				"lockMovement", MovementLockAction.class), request, "");
    		return null;
    	}
    	return null;
    }

    public String lockMovement()
        throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

        log.debug("entering 'lockMovement' method");

        try {
            boolean isLocked = false;
            String userId = "";
            String moevementId = request.getParameter("movementId");
            Long moevementLongId = null;

            if (moevementId != null) {
                moevementLongId = Long.valueOf(moevementId);
            }

            userId = SwtUtil.getCurrentUserId(request.getSession());

            log.debug("moevementLongId - " + moevementLongId + ", userId - " +
                userId);

            if ((moevementLongId != null) && (userId != null) &&
                    (userId.trim().length() > 0)) {
            	
            	/**Subject			:Mantis Issue 739
    			 * Description		:Earlier only movement ID was passed to lock the
    			 *					movement.In that case in the UPDATE_USER  column 
    			 *					'SYSTEM' will be stored by default instead of locked User ID.
    			 *					To avoid that userId is taken from session and passed
    			 *					in the movementLockManager's lockMovement()	 	
    			 * *Code Modified on date	07/10/2008 by Selva Kumar.A Starts*/            	
            	
                userId = movementLockManager.lockMovement(moevementLongId,userId);
                
                /* Code fix ends for Mantis Issue 739*/
            }

            log.debug("Movement Locked - " + isLocked);

            if (userId != null) {
                response.getWriter().print(userId);
            } else {
                response.getWriter().print(true);
            }

            log.debug("exiting 'lockMovement' method");

            return null;
        } catch (SwtException swtexp) {
            log.debug(
                "SwtException Catch in MovementLockAction.'lockMovement' method : " +
                swtexp.getMessage());
            SwtUtil.logException(swtexp, request, "");

            return null;
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementLockAction.'lockMovement' method : " +
                exp.getMessage());
            SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp,
                    "lockMovement", MovementLockAction.class), request, "");

            return null;
        }
    }

    public String lockMatch()
        throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

        log.debug("entering 'lockMovement' method");

        boolean isLocked = false;

        try {
            String matchId = request.getParameter("matchId");
            String entityId = request.getParameter("entityId");

            //Long moevementLongId = null;
            Long matchLongId = null;

            if (matchId != null) {
                matchLongId = Long.valueOf(matchId);
            }

            String userId = SwtUtil.getCurrentUserId(request.getSession());

            log.debug("matchLongId - " + matchLongId + ", userId - " + userId);

            if ((matchLongId != null) && (userId != null) &&
                    (userId.trim().length() > 0)) {
                isLocked = movementLockManager.lockMatch(matchLongId, entityId,
                        userId);
            }

            log.debug("Match Locked - " + isLocked);
            response.getWriter().print(isLocked);

            log.debug("exiting 'lockMatch' method");
        } catch (SwtException swtexp) {
            log.debug(
                "SwtException Catch in MovementLockAction.'lockMatch' method : " +
                swtexp.getMessage());
            SwtUtil.logException(swtexp, request, "");
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementLockAction.'lockMatch' method : " +
                exp.getMessage());
            SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp,
                    "lockMatch", MovementLockAction.class), request, "");
        }

        return null;
    }

    public String unlockMovement()
        throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

        log.debug("entering 'unlockMovement' method");

        try {
            boolean isUnLocked = false;
            // Start: Modified by Bala on 13092010 for mantis 1258 - Added the movementId empty condition for NumberFormat Exception
            String movementId = request.getParameter("movementId");

            Long movementLongId = null;
            
            if (!SwtUtil.isEmptyOrNull(movementId)) {
                movementLongId = Long.valueOf(movementId);
            }

            String userId = SwtUtil.getCurrentUserId(request.getSession());

            log.debug("movementLongId - " + movementLongId + ", userId - " +
                userId);
            if ((movementLongId != null) && (userId != null) &&
                    (userId.trim().length() > 0)) {
                isUnLocked = movementLockManager.unLockMovement(movementLongId);
            }
            // End: Modified by Bala on 13092010 for mantis 1258 - Added the movementId empty condition for NumberFormat Exception
            log.debug("Movement UnLocked - " + isUnLocked);
            response.getWriter().print(isUnLocked);

            log.debug("exiting 'unlockMovement' method");

            return null;
        } catch (SwtException swtexp) {
            log.debug(
                "SwtException Catch in MovementLockAction.'unlockMovement' method : " +
                swtexp.getMessage());
            SwtUtil.logException(swtexp, request, "");

            return null;
        } catch (Exception exp) {
            log.debug(
                "Exception Catch in MovementLockAction.'unlockMovement' method : " +
                exp.getMessage());
            SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp,
                    "unlockMovement", MovementLockAction.class), request, "");

            return null;
        }
    }
    
    
    public String unlockMovements()
            throws SwtException {
            log.debug("entering 'unlockMovement' method");
            String movementIds = null;
    		HttpServletRequest request = SwtUtil.getCurrentRequest();
    		HttpServletResponse response = SwtUtil.getCurrentResponse();
            try {
                boolean isUnLocked = false;
                // Start: Modified by Bala on 13092010 for mantis 1258 - Added the movementId empty condition for NumberFormat Exception
            	movementIds = request.getParameter("movementIds");
        		if(!SwtUtil.isEmptyOrNull(movementIds)) {
        			if (movementIds.charAt(movementIds.length() - 1) == ',') {
        				movementIds = movementIds.substring(0, movementIds.length() - 1);
        			}
        			String[] movementIdsAsArray = movementIds.split(",");
        			for (int i = 0; i < movementIdsAsArray.length; i++) {
        				isUnLocked = movementLockManager.unLockMovement(Long.parseLong(movementIdsAsArray[i]));
					}
        		}
                // End: Modified by Bala on 13092010 for mantis 1258 - Added the movementId empty condition for NumberFormat Exception
                log.debug("Movement UnLocked - " + isUnLocked);
                response.getWriter().print(isUnLocked);

                log.debug("exiting 'unlockMovement' method");

                return null;
            } catch (SwtException swtexp) {
                log.debug(
                    "SwtException Catch in MovementLockAction.'unlockMovement' method : " +
                    swtexp.getMessage());
                SwtUtil.logException(swtexp, request, "");

                return null;
            } catch (Exception exp) {
                log.debug(
                    "Exception Catch in MovementLockAction.'unlockMovement' method : " +
                    exp.getMessage());
                SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp,
                        "unlockMovement", MovementLockAction.class), request, "");

                return null;
            }
        }
    

    /* START:Code added for Mantis 758 by Kalidass on 12-Dec-2008    */
    
    /** This method is called when user clicks the OK button of Add Movement Screen 
     * through MMSD Screen on Manual Matching Screen or MMSD Screen.
	 * 
	 * @param mapping
	 * @param form
	 * @param request
	 * @param response
	 * @return ActionForward which will specify the JSP to which the control
	 * 		   will be transferred using the mapping defined in the  struts-config.xml
	 * @throws Exception
	 */
    public String lockMovementForMMSD()
            throws SwtException {
		HttpServletRequest request = SwtUtil.getCurrentRequest();
		HttpServletResponse response = SwtUtil.getCurrentResponse();

    	log.debug(this.getClass().getName() + "- [lockMovementForMMSD] - Entering ");
    	
	         String userId = "";
	         Long movementLongId = null;
	         String movementId = null;
         
            try {
               /* Gets the movementId from request */
                movementId = request.getParameter("movementId");
                /* Converts the movementId from String to Long */
                if (movementId != null) {
                    movementLongId = Long.valueOf(movementId);
                }
                /* Gets the current userId from session */
                userId = SwtUtil.getCurrentUserId(request.getSession());

                /* Condition checked to lock the given movement */
                if ((movementLongId != null) && (userId != null) &&
                        (userId.trim().length() > 0)) {
                 	
                    userId = movementLockManager.lockMovementForMMSD(movementLongId,userId);
                    
                }

                /* If the userId is not null, then sends the userId.Otherwise true returns */
                if (userId != null) {
                    response.getWriter().print(userId);
                } else {
                    response.getWriter().print(true);
                }

            	log.debug(this.getClass().getName() + "- [lockMovementForMMSD] - Exiting ");

                return null;
            } catch (SwtException swtexp) {
            	log.debug(this.getClass().getName() + "- [lockMovementForMMSD] - SwtException "
						+ swtexp.getMessage());
				log.error(this.getClass().getName() + "- [lockMovementForMMSD] - SwtException "
						+ swtexp.getMessage());
                SwtUtil.logException(swtexp, request, "");

                return null;
            } catch (Exception exp) {
            	log.debug(this.getClass().getName() + "- [lockMovementForMMSD] - Exception "
						+ exp.getMessage());
				log.error(this.getClass().getName() + "- [lockMovementForMMSD] - Exception "
						+ exp.getMessage());
                SwtUtil.logException(SwtErrorHandler.getInstance().handleException(exp,
                        "lockMovementForMMSD", MovementLockAction.class), request, "");

                return null;
            }
        }
    /* End:Code added for Mantis 758 by Kalidass on 12-Dec-2008    */
}
