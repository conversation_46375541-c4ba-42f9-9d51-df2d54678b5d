/*
 * @(#)MatchDAO.java 11/01/07
 * 
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 * 
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.ArrayList;
import java.util.Date;

import org.swallow.dao.DAO;
import org.swallow.exception.SwtException;
import org.swallow.work.model.Match;

/*
 * <pre> DAO layer for Match display Screen Class used to - Display match
 * details - Update Existing match - Display Offered/ Suspend/confirmed queues
 * screen
 * 
 * </pre>
 */
public interface MatchDAO extends DAO {

	/**
	 * This method is used to fetch screen data for all the Match Screen's
	 * 
	 * @param entityId
	 * @param hostId
	 * @param currencyGrpId
	 * @param status
	 * @param roleId
	 * @param selectedTabIndex
	 * @param applyCurrencyThreshold
	 * @param noIncludedMovementMatches
	 * @param date
	 * @param currGrpId
	 * @param dateTabFlag
	 * 
	 * @return
	 * @throws SwtException
	 */
	public ArrayList<Object> getMatchDetailsUsingStoredProc(String hostId,
			String entityId, String currGrpId, String roleId,
			String dateTabFlag, String applyCurrencyThreshold,
			String noIncludedMovementMatches, String status, Date date)
			throws SwtException;

	/**
	 * This method repopulates and updates highest_position level, lowest
	 * position level, max value date and max amount of movements associated
	 * with given match id.
	 * 
	 * @param matchIds
	 * @throws SwtException
	 */
	public void updateMatch(Match match) throws SwtException;

	/**
	 * This method repopulates and updates highest_position level, lowest
	 * position level, max value date and max amount of movements associated
	 * with given match id.
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchIds
	 * @throws SwtException
	 */
	public void updateBrokenMatch(String hostId, String entityId, long matchId)
			throws SwtException;

	/**
	 * This method returns the Match object from P_MATCH table
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @return
	 * @throws SwtException
	 */
	public Match getMatchObject(String hostId, String entityId, String matchId)
			throws SwtException;
	/*
	 * Start:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
	/**
	 * This method is used to get the archived match from archive schema
	 * 
	 * @param hostId
	 * @param entityId
	 * @param matchId
	 * @param archiveId
	 * @return Match
	 * @throws SwtException
	 */
	public Match getArchiveMatchObject(String hostId, String entityId,
			String matchId, String archiveId) throws SwtException;
	/*
	 * End:Code Modified for Mantis 1270 by Chinniah on 29-Feb-2012:Data
	 * Archive setup: Remove redundant fields from Archive setup screen
	 */
}