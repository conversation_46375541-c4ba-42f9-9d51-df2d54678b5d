/*
 * @(#)MovementSearchDAOHibernateAction.java 1.0 
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao.hibernate;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.swallow.batchScheduler.ConnectionManager;
import org.swallow.control.model.Archive;
import org.swallow.exception.SwtErrorHandler;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.AcctMaintenance;
import org.swallow.maintenance.model.BookCode;
import org.swallow.maintenance.model.Currency;
import org.swallow.maintenance.model.Group;
import org.swallow.maintenance.model.MetaGroup;
import org.swallow.maintenance.model.Party;
import org.swallow.util.CacheManager;
import org.swallow.util.JDBCCloser;
import org.swallow.util.LabelValueBean;
import org.swallow.util.PropertiesFileLoader;
import org.swallow.util.SwtConstants;
import org.swallow.util.SwtPager;
import org.swallow.util.SwtServerSideSortFilter;
import org.swallow.util.SwtUtil;
import org.swallow.work.dao.MovementSearchDAO;
import org.springframework.context.annotation.Lazy;
import org.swallow.util.jpa.CustomHibernateDaoSupport;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.hibernate.HibernateException;
import org.hibernate.SessionFactory;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import org.hibernate.Transaction;
import org.hibernate.query.Query;
import org.hibernate.Session;

/**
 * <AUTHOR>
 * 
 *         TODO To change the template for this generated type comment go to
 *         Window -
 *         Preferences - Java - Code Style - Code Templates
 */
@Repository("movementSearchDAO")
@Transactional
public class MovementSearchDAOHibernate extends CustomHibernateDaoSupport implements
		MovementSearchDAO {
	public MovementSearchDAOHibernate(@Lazy SessionFactory sessionfactory,
			@Lazy @Qualifier("dbaEntityManager") EntityManager entityManager) {
		super(sessionfactory, entityManager);
	}

	private final Log log = LogFactory.getLog(MovementSearchDAOHibernate.class);

	public Collection getCurrencyDetails(String hostId, String entityId) {
		log.debug("Inside the Currency details method");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Currency> query = session.createQuery(
					"from Currency c where c.id.hostId = :hostId and c.id.entityId = :entityId and c.id.currencyCode != '*' order by c.id.currencyCode",
					Currency.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			List<Currency> currencyList = query.getResultList();
			log.debug("Outside the Currency details method");
			return currencyList;
		}
	}

	public Collection getBeneficiaryDetails(String hostId, String entityId) {
		log.debug("Inside the beneficiary details method");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Party> query = session.createQuery(
					"from Party p where p.id.hostId = :hostId and p.id.entityId = :entityId and p.partyType !='U' order by p.id.partyId",
					Party.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			List<Party> beneficiaryList = query.getResultList();
			log.debug("Outside the beneficiary details method");
			return beneficiaryList;
		}
	}

	public Collection getCounterPartyDetails(String hostId, String entityId) {
		log.debug("Inside the counterparty details method");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Party> query = session.createQuery(
					"from Party p where p.id.hostId = :hostId and p.id.entityId = :entityId and p.partyType !='U' order by p.id.partyId",
					Party.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			List<Party> counterpartyList = query.getResultList();
			log.debug("Outside the counterparty details method");
			return counterpartyList;
		}
	}

	public Collection getCustodianDetails(String hostId, String entityId) {
		log.debug("Inside the custodian details method");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<Party> query = session.createQuery(
					"from Party p where p.id.hostId = :hostId and p.id.entityId = :entityId and p.partyType ='U' order by p.id.partyId",
					Party.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			List<Party> custodianList = query.getResultList();
			log.debug("Outside the custodian details method");
			return custodianList;
		}
	}

	public Collection getBookCodeDetails(String hostId, String entityId) {
		log.debug("Inside the bookcode details method");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<BookCode> query = session.createQuery(
					"from BookCode book where book.id.hostId = :hostId and book.id.entityId = :entityId order by book.id.bookCode",
					BookCode.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			List<BookCode> bookList = query.getResultList();
			log.debug("Outside the bookcode details method");
			return bookList;
		}
	}

	public Collection getGroupDetails(String hostId, String entityId) {
		log.debug("Inside the getgroupdetails");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String grpHQL = "select m.id.hostId, m.id.entityId, m.id.groupId, m.groupName, s.groupLevelName " +
					"from Group m, GroupLevel s where m.id.hostId = s.id.hostId " +
					"and m.id.entityId = s.id.entityId and m.groupLvlCode = s.id.groupLevelId " +
					"and m.id.hostId = :hostId and m.id.entityId = :entityId";

			Query<Object[]> query = session.createQuery(grpHQL, Object[].class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			List<Object[]> list = query.getResultList();
			Collection<Group> groupList = new ArrayList<>();
			log.debug("noofRecords.size : " + list.size());

			// Iterating through the collection (list of groups plus groupLvl name)
			if (list != null) {
				for (Object[] row : list) {
					Group group = new Group();
					group.getId().setHostId((String) row[0]);
					group.getId().setEntityId((String) row[1]);
					group.getId().setGroupId((String) row[2]);
					group.setGroupName((String) row[3]);
					group.setGroupLvlName((String) row[4]);
					groupList.add(group);
					log.debug("The Job record from the database is==>" + group.toString());
				}
			}

			log.debug("Exiting the getgroupdetails");
			return groupList;
		}
	}

	public Collection getMetaGroupDetails(String hostId, String entityId) {
		log.debug("Inside the metagetgroupdetails");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			String metaGrpHQL = "select m.id.hostId, m.id.entityId, m.id.mgroupId, m.mgroupName, s.mgrpLvlName " +
					"from MetaGroup m, MetaGroupLevel s where m.id.hostId = s.id.hostId " +
					"and m.id.entityId = s.id.entityId and m.mgrpLvlCode = s.id.mgrpLvlCode " +
					"and m.id.hostId = :hostId and m.id.entityId = :entityId";

			Query<Object[]> query = session.createQuery(metaGrpHQL, Object[].class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);

			List<Object[]> list = query.getResultList();
			Collection<MetaGroup> metaGroupList = new ArrayList<>();
			log.debug("noofRecords.size : " + list.size());

			// Iterating through the collection (list of metagroups plus metagroupLvl name)
			if (list != null) {
				for (Object[] row : list) {
					MetaGroup metaGroup = new MetaGroup();
					metaGroup.getId().setHostId((String) row[0]);
					metaGroup.getId().setEntityId((String) row[1]);
					metaGroup.getId().setMgroupId((String) row[2]);
					metaGroup.setMgroupName((String) row[3]);
					metaGroup.setMgrpLvlName((String) row[4]);
					metaGroupList.add(metaGroup);
					log.debug("The Job record from the database is==>" + metaGroup.toString());
				}
			}

			log.debug(" metaGroup List return  :::" + metaGroupList.toString());
			log.debug("Outside the metagetgroupdetails");
			return metaGroupList;
		}
	}

	public Collection getAccountDetails(String hostId, String entityId) {
		log.debug("Inside the accountdetails");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<AcctMaintenance> query = session.createQuery(
					"from AcctMaintenance ac where ac.id.hostId = :hostId and ac.id.entityId = :entityId and ac.id.accountId != '*' order by ac.id.accountId",
					AcctMaintenance.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			List<AcctMaintenance> accountList = query.getResultList();
			log.debug("Outside the accountdetails");
			return accountList;
		}
	}

	/**
	 * @desc : fetches all the accounts defined for hostId, entityId and currCode
	 */
	public Collection getAccountDetails(String hostId, String entityId, String currCode) {
		log.debug("Entering the accountdetails");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<AcctMaintenance> query = session.createQuery(
					"from AcctMaintenance ac where ac.id.hostId = :hostId and ac.id.entityId = :entityId and ac.currcode = :currCode and ac.id.accountId != '*' order by ac.id.accountId",
					AcctMaintenance.class);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			query.setParameter("currCode", currCode);
			List<AcctMaintenance> accountList = query.getResultList();
			log.debug("Exiting the accountdetails");
			return accountList;
		}
	}

	/*
	 * Start:Code Modified For Mantis 2072 by Sudhakar on 31-10-2012:Currency
	 * change should not change Status and Account class to All
	 */
	/**
	 * This method is used to get account details based on the currency code and
	 * account class
	 * 
	 * @param hostId
	 * @param entityId
	 * @param accountClass
	 * @param currCode
	 * @return accountList
	 * @throws SwtException
	 */
	public Collection getAccountClassDetails(String hostId, String entityId,
			String accountClass, String currCode) throws SwtException {
		log.debug(this.getClass().getName() + " - [getAccountClassDetails]  - Entering ");
		try (Session session = getHibernateTemplate().getSessionFactory().openSession()) {
			TypedQuery<AcctMaintenance> query;

			// retrieve the account list,if currency code and account class is not all
			if (!accountClass.equals(SwtConstants.ALL_VALUE) && !currCode.equals(SwtConstants.ALL_VALUE)) {
				query = session.createQuery(
						"from AcctMaintenance ac where ac.id.hostId = :hostId and ac.id.entityId = :entityId " +
								"and ac.currcode = :currCode and ac.id.accountId != '*' and ac.acctClass = :acctClass "
								+
								"order by ac.id.accountId",
						AcctMaintenance.class);
				query.setParameter("hostId", hostId)
						.setParameter("entityId", entityId)
						.setParameter("currCode", currCode)
						.setParameter("acctClass", accountClass);
			}
			// retrieve the account list,if account class is all and currency code is not
			// all
			else if (accountClass.equals(SwtConstants.ALL_VALUE) && !currCode.equals(SwtConstants.ALL_VALUE)) {
				query = session.createQuery(
						"from AcctMaintenance ac where ac.id.hostId = :hostId and ac.id.entityId = :entityId " +
								"and ac.currcode = :currCode and ac.id.accountId != '*' order by ac.id.accountId",
						AcctMaintenance.class);
				query.setParameter("hostId", hostId)
						.setParameter("entityId", entityId)
						.setParameter("currCode", currCode);
			}
			// retrieve the account list,if account class is not all and currency code is
			// all
			else if (!accountClass.equals(SwtConstants.ALL_VALUE) && currCode.equals(SwtConstants.ALL_VALUE)) {
				query = session.createQuery(
						"from AcctMaintenance ac where ac.id.hostId = :hostId and ac.id.entityId = :entityId " +
								"and ac.id.accountId != '*' and ac.acctClass = :acctClass order by ac.id.accountId",
						AcctMaintenance.class);
				query.setParameter("hostId", hostId)
						.setParameter("entityId", entityId)
						.setParameter("acctClass", accountClass);
			} else {
				query = session.createQuery(
						"from AcctMaintenance ac where ac.id.hostId = :hostId and ac.id.entityId = :entityId " +
								"and ac.id.accountId != '*' order by ac.id.accountId",
						AcctMaintenance.class);
				query.setParameter("hostId", hostId)
						.setParameter("entityId", entityId);
			}

			List<AcctMaintenance> accountList = query.getResultList();
			log.debug(this.getClass().getName() + " - [ getAccountClassDetails ] - Exit ");
			return accountList;
		} catch (Exception ex) {
			// log error message
			log.error(this.getClass().getName() + " - [getAccountClassDetails] - Exception - " + ex.getMessage());
			// Re-throw an exception
			throw SwtErrorHandler.getInstance().handleException(ex, "getAccountClassDetails",
					MovementSearchDAOHibernate.class);
		}
	}

	/*
	 * End:Code Modified For Mantis 2072 by Sudhakar on 31-10-2012:Currency
	 * change should not change Status and Account class to All
	 */
	/**
	 * 
	 * @param listToPopulate
	 * @param hostid
	 * @param entityid
	 * @param status
	 * @param movementtype
	 * @param sign
	 * @param predictstatus
	 * @param amountover
	 * @param amountunder
	 * @param currencycode
	 * 
	 * @param beneficiaryId
	 * @param custodianId
	 * @param poslevel
	 * @param accountId
	 * @param group
	 * @param metaGroup
	 * @param bookCode
	 * @param sortorder
	 * @param fromdate
	 * @param todate
	 * @param reference
	 * @param msgtype
	 * @param inputdate
	 * @param counterparty
	 * @param fintrade
	 * @param timefrom
	 * @param timeto
	 * @param format
	 * @param currentPage
	 * @param incrementFactor
	 * @return
	 * @throws SwtException
	 */
	/**
	 * Fetches movement details based on various filters and criteria
	 * 
	 * @param list             List to store movement results
	 * @param hostId           Host identifier
	 * @param entityId         Entity identifier
	 * @param status           Match status filter
	 * @param movementtype     Movement type filter
	 * @param sign             Sign filter
	 * @param predictstatus    Predict status filter
	 * @param amountover       Amount over filter
	 * @param amountunder      Amount under filter
	 * @param currencycode     Currency code filter
	 * @param beneficiaryId    Beneficiary ID filter
	 * @param custodianId      Custodian ID filter
	 * @param poslevel         Position level filter
	 * @param accountId        Account ID filter
	 * @param group            Group filter
	 * @param metaGroup        Meta group filter
	 * @param bookCode         Book code filter
	 * @param sortorder        Sort order
	 * @param fromdate         From date filter
	 * @param todate           To date filter
	 * @param reference        Reference filter
	 * @param msgtype          Message type filter
	 * @param inputdate        Input date filter
	 * @param counterparty     Counterparty filter
	 * @param fintrade         Finance/trade filter
	 * @param format           Date format
	 * @param timefrom         Time from filter
	 * @param timeto           Time to filter
	 * @param currentPage      Current page number
	 * @param incrementFactor  Increment factor
	 * @param filterSortStatus Filter sort status
	 * @param totalCount       Total count of records
	 * @return Total count of records
	 * @throws SwtException If an error occurs during processing
	 */
	public int fetchDetails(List list, String hostId, String entityId,
			String status, String movementtype, String sign,
			String predictstatus, Double amountover, Double amountunder,
			String currencycode, String beneficiaryId,
			String custodianId, Integer poslevel, String accountId,
			String group, String metaGroup, String bookCode, String sortorder,
			String fromdate, String todate, String reference, String msgtype,
			String inputdate, String counterparty, String fintrade,
			String format, String timefrom, String timeto, int currentPage,
			int incrementFactor, String filterSortStatus, int totalCount)
			throws SwtException {

		ArrayList paramList = new ArrayList();
		ArrayList typeList = new ArrayList();
		boolean isNext = false;

		try (Session session = sessionFactory.openSession()) {
			// Fetch and construct account information
			ArrayList accountArray = fetchAccountInformation(session, entityId, sortorder);
			StringBuffer strAccount = buildAccountString(accountArray);

			// Build main query
			StringBuffer queryBuilder = buildBaseQuery(hostId, entityId);

			// Apply various filters
			applyBasicFilters(queryBuilder, movementtype, predictstatus, status, sign);
			applyAmountFilters(queryBuilder, amountover, amountunder);
			applyEntityFilters(queryBuilder, currencycode, beneficiaryId, custodianId, counterparty);
			applyPositionFilter(queryBuilder, poslevel);

			// Apply account filters
			applyAccountFilters(queryBuilder, sortorder, accountId, strAccount, accountArray);

			// Apply group and book code filters
			applyGroupAndBookCodeFilters(queryBuilder, bookCode, group, metaGroup, hostId, entityId, session);

			// Apply date filters
			applyDateFilters(queryBuilder, fromdate, todate, format, paramList);

			// Apply time filters
			applyTimeFilters(queryBuilder, timefrom, timeto, hostId, entityId);

			// Apply finance/trade filters
			applyFinanceTradeFilters(queryBuilder, fintrade, hostId, entityId, session);

			// Apply additional filters
			applyReferenceAndTypeFilters(queryBuilder, msgtype, inputdate, reference);

			// Apply sorting
			StringBuffer finalQuery = applySorting(queryBuilder, entityId, filterSortStatus, format);

			// Execute query and process results
			totalCount = executeQueryAndProcessResults(session, finalQuery, paramList, typeList,
					currentPage, totalCount, list);
		} catch (HibernateException hibernateException) {
			log.debug("Problem in accessing Hibernate properties;");
			hibernateException.printStackTrace();
			throw new SwtException(hibernateException.getMessage());
		}

		log.debug("Outside the fetch details");
		return totalCount;
	}

	/**
	 * Fetches account information based on entity ID and sort order
	 * 
	 * @param session   Hibernate session
	 * @param entityId  Entity identifier
	 * @param sortorder Sort order
	 * @return ArrayList of account IDs
	 */
	private ArrayList fetchAccountInformation(Session session, String entityId, String sortorder) {
		List arraylist;

		if (!(sortorder.equalsIgnoreCase("A"))) {
			TypedQuery<AcctMaintenance> query = session.createQuery(
					"from AcctMaintenance acct where acct.id.entityId = :entityId and acct.acctClass = :sortorder",
					AcctMaintenance.class);
			query.setParameter("entityId", entityId);
			query.setParameter("sortorder", sortorder);
			arraylist = query.getResultList();
		} else {
			TypedQuery<AcctMaintenance> query = session.createQuery(
					"from AcctMaintenance acct where acct.id.entityId = :entityId",
					AcctMaintenance.class);
			query.setParameter("entityId", entityId);
			arraylist = query.getResultList();
		}

		ArrayList accountArray = new ArrayList();
		Iterator itr = arraylist.iterator();

		while (itr.hasNext()) {
			AcctMaintenance AccountMaint = (AcctMaintenance) itr.next();
			accountArray.add(AccountMaint.getId().getAccountId());
		}

		return accountArray;
	}

	/**
	 * Builds a comma-separated string of account IDs
	 * 
	 * @param accountArray List of account IDs
	 * @return StringBuffer containing comma-separated account IDs
	 */
	private StringBuffer buildAccountString(ArrayList accountArray) {
		StringBuffer strAccount = new StringBuffer();
		int count = 0;

		for (int i = 0; i < accountArray.size(); i++) {
			String accountId = (String) accountArray.get(i);

			if (count == 0) {
				strAccount.append("'" + accountId + "'");
				count = 1;
			} else {
				strAccount.append(",'" + accountId + "'");
			}
		}

		return strAccount;
	}

	/**
	 * Builds the base query for movement details
	 * 
	 * @param hostId   Host identifier
	 * @param entityId Entity identifier
	 * @return StringBuffer containing the base query
	 */
	private StringBuffer buildBaseQuery(String hostId, String entityId) {
		return new StringBuffer(
				"from Movement p where p.id.hostId ='" + hostId
						+ "' and p.id.entityId='" + entityId + "'");
	}

	/**
	 * Applies basic filters to the query
	 * 
	 * @param str           Query builder
	 * @param movementtype  Movement type filter
	 * @param predictstatus Predict status filter
	 * @param status        Match status filter
	 * @param sign          Sign filter
	 */
	private void applyBasicFilters(StringBuffer str, String movementtype,
			String predictstatus, String status, String sign) {
		// Movement type
		if (!movementtype.equals("B")) {
			str.append("  and p.movementType='" + movementtype + "'");
		}

		// Predict status
		if (!predictstatus.equals("A")) {
			str.append(" and p.predictStatus='" + predictstatus + "'");
		}

		// Match Status
		if (!status.equals("X")) {
			if (status.equals("D")) {
				str.append("  and p.matchStatus IN('M','C','S')");
			} else {
				str.append(" and p.matchStatus='" + status + "'");
			}
		}

		if (!sign.equals("B")) {
			str.append(" and p.sign='" + sign + "'");
		}
	}

	/**
	 * Applies amount filters to the query
	 * 
	 * @param str         Query builder
	 * @param amountover  Amount over filter
	 * @param amountunder Amount under filter
	 */
	private void applyAmountFilters(StringBuffer str, Double amountover, Double amountunder) {
		double over = amountover.doubleValue();
		double under = amountunder.doubleValue();

		if (over == 0.0) {
			if (under != 0.0) {
				str.append(" and p.amount > " + amountover
						+ " and p.amount < " + amountunder + " ");
			}
		} else {
			if (under == 0.0) {
				str.append(" and p.amount > " + amountover + " ");
			} else {
				str.append(" and p.amount > " + amountover
						+ " and p.amount < " + amountunder + " ");
			}
		}
	}

	/**
	 * Applies entity-related filters to the query
	 * 
	 * @param str           Query builder
	 * @param currencycode  Currency code filter
	 * @param beneficiaryId Beneficiary ID filter
	 * @param custodianId   Custodian ID filter
	 * @param counterparty  Counterparty filter
	 */
	private void applyEntityFilters(StringBuffer str, String currencycode,
			String beneficiaryId, String custodianId, String counterparty) {
		// Currency Code
		if (!currencycode.equals("")) {
			str.append(" and p.currencyCode ='" + currencycode + "'");
		}

		// Beneficiary Id
		if (!beneficiaryId.equals("")) {
			str.append(" and p.beneficiaryId='" + beneficiaryId + "'");
		}

		// Custodian Id
		if (!custodianId.equals("")) {
			str.append(" and p.custodianId='" + custodianId + "'");
		}

		// CounterParty
		if (!counterparty.equals("")) {
			str.append(" and p.counterPartyId='" + counterparty + "'");
		}
	}

	/**
	 * Applies position level filter to the query
	 * 
	 * @param str      Query builder
	 * @param poslevel Position level filter
	 */
	private void applyPositionFilter(StringBuffer str, Integer poslevel) {
		int position = poslevel.intValue();

		if ((position >= 1) && (position <= 9)) {
			str.append(" and p.positionLevel=" + position );
		}
	}

	/**
	 * Applies account filters to the query
	 * 
	 * @param str          Query builder
	 * @param sortorder    Sort order
	 * @param accountId    Account ID filter
	 * @param strAccount   String of account IDs
	 * @param accountArray List of account IDs
	 */
	private void applyAccountFilters(StringBuffer str, String sortorder, String accountId,
			StringBuffer strAccount, List accountArray) {
		if (!sortorder.equalsIgnoreCase("A")) {
			if (!accountId.equals("")) {
				str.append(" and p.accountId='" + accountId + "'");
			} else {
				if (!sortorder.equals("")) {
					log.debug("Sort Order-->" + sortorder);

					if (!(accountArray.size() == 0)) {
						str.append(" and p.accountId IN (" + strAccount + ")");
					}
				}
			}
		}
	}

	/**
	 * Applies group and book code filters to the query
	 * 
	 * @param str       Query builder
	 * @param bookCode  Book code filter
	 * @param group     Group filter
	 * @param metaGroup Meta group filter
	 * @param hostId    Host identifier
	 * @param entityId  Entity identifier
	 * @param session   Hibernate session
	 */
	private void applyGroupAndBookCodeFilters(StringBuffer str, String bookCode, String group,
			String metaGroup, String hostId, String entityId, Session session) {
		if (!bookCode.equals("")) {
			str.append(" and p.bookCode='" + bookCode + "'");
		} else if (!group.equals("")) {
			// Fetch bookcodes according to the group selected
			TypedQuery<String> query = session.createQuery(
					"select book.id.bookCode from BookCode book where book.groupIdLevel1 = :group " +
							"or book.groupIdLevel2 = :group " +
							"or book.groupIdLevel3 = :group " +
							"and book.id.hostId = :hostId " +
							"and book.id.entityId = :entityId",
					String.class);
			query.setParameter("group", group);
			query.setParameter("hostId", hostId);
			query.setParameter("entityId", entityId);
			List<String> bookcodelist1 = query.getResultList();

			appendBookCodesToQuery(str, bookcodelist1);
		} else if (!metaGroup.equals("")) {
			// Fetch bookcodes according to the metagroup selected
			TypedQuery<String> groupQuery = session.createQuery(
					"select gro.id.groupId from Group gro where gro.mgroupId = :metaGroup " +
							"and gro.id.hostId = :hostId " +
							"and gro.id.entityId = :entityId",
					String.class);
			groupQuery.setParameter("metaGroup", metaGroup);
			groupQuery.setParameter("hostId", hostId);
			groupQuery.setParameter("entityId", entityId);
			List<String> grouplist = groupQuery.getResultList();

			List<String> bookcodelist = new ArrayList<>();

			for (String groupId : grouplist) {
				TypedQuery<String> bookQuery = session.createQuery(
						"select book.id.bookCode from BookCode book where book.groupIdLevel1 = :groupIdLevel " +
								"or book.groupIdLevel2 = :groupIdLevel " +
								"or book.groupIdLevel3 = :groupIdLevel " +
								"and book.id.hostId = :hostId " +
								"and book.id.entityId = :entityId",
						String.class);
				bookQuery.setParameter("groupIdLevel", groupId);
				bookQuery.setParameter("hostId", hostId);
				bookQuery.setParameter("entityId", entityId);
				List<String> temp = bookQuery.getResultList();

				bookcodelist.addAll(temp);
			}

			appendBookCodesToQuery(str, bookcodelist);
		}
	}

	/**
	 * Appends book codes to the query
	 * 
	 * @param str          Query builder
	 * @param bookCodeList List of book codes
	 */
	private void appendBookCodesToQuery(StringBuffer str, List<String> bookCodeList) {
		int listSize = bookCodeList.size();

		if (listSize == 0) {
			str.append(" and p.bookCode IN ('')");
			return;
		}

		str.append(" and p.bookCode IN (");

		for (int i = 0; i < listSize; i++) {
			if (i > 0) {
				str.append(",");
			}
			str.append("'" + bookCodeList.get(i) + "'");
		}

		str.append(")");
	}

	/**
	 * Applies date filters to the query
	 * 
	 * @param str       Query builder
	 * @param fromdate  From date filter
	 * @param todate    To date filter
	 * @param format    Date format
	 * @param paramList List to store query parameters
	 */
	private void applyDateFilters(StringBuffer str, String fromdate, String todate,
			String format, List paramList) throws SwtException {
		Date fromDt = null;
		Date toDt = null;

		if (!fromdate.equals("") && !todate.equals("")) {
			// Both From and To date are non empty
			if (format.equalsIgnoreCase("D")) {
				fromDt = SwtUtil.parseDate(fromdate, "dd/MM/yyyy");
				toDt = SwtUtil.parseDate(todate, "dd/MM/yyyy");
			} else {
				fromDt = SwtUtil.parseDate(fromdate, "MM/dd/yyyy");
				toDt = SwtUtil.parseDate(todate, "MM/dd/yyyy");
			}

			str.append(" and p.valueDate >= :param" + paramList.size() + " ");
			paramList.add(fromDt);

			str.append(" and p.valueDate <= :param" + paramList.size() + " ");
			paramList.add(toDt);
		} else if (fromdate.equals("") && todate.equals("")) {
			str.append(" and p.valueDate >= :param" + paramList.size() + " ");
			paramList.add(SwtUtil.getSystemDatewithoutTime());
		} else if (!fromdate.equals("")) {
			if (format.equalsIgnoreCase("D")) {
				fromDt = SwtUtil.parseDate(fromdate, "dd/MM/yyyy");
			} else {
				fromDt = SwtUtil.parseDate(fromdate, "MM/dd/yyyy");
			}

			str.append(" and p.valueDate >= :param" + paramList.size() + " ");
			paramList.add(fromDt);
		} else if (!todate.equals("")) {
			if (format.equalsIgnoreCase("D")) {
				toDt = SwtUtil.parseDate(todate, "dd/MM/yyyy");
			} else {
				toDt = SwtUtil.parseDate(todate, "MM/dd/yyyy");
			}

			str.append(" and p.valueDate >= :param" + paramList.size() + " ");
			paramList.add(SwtUtil.getSystemDatewithoutTime());

			str.append(" and p.valueDate <= :param" + paramList.size() + " ");
			paramList.add(toDt);
		}
	}

	/**
	 * Applies time filters to the query
	 * 
	 * @param str      Query builder
	 * @param timefrom Time from filter
	 * @param timeto   Time to filter
	 * @param hostId   Host identifier
	 * @param entityId Entity identifier
	 */
	private void applyTimeFilters(StringBuffer str, String timefrom, String timeto,
			String hostId, String entityId) {
		if (!(timefrom.equals("")) && (!(timeto.equals("")))) {
			str.append(" and ((to_date(to_char(p.inputDate,'HH24:MI'),'HH24:MI') >= to_date('"
					+ timefrom + "','HH24:MI')) and (to_date(to_char(p.inputDate,'HH24:MI'),'HH24:MI') < to_date('"
					+ timeto + "','HH24:MI')))");
		}
	}

	/**
	 * Applies finance/trade filters to the query
	 * 
	 * @param str      Query builder
	 * @param fintrade Finance/trade filter
	 * @param hostId   Host identifier
	 * @param entityId Entity identifier
	 * @param session  Hibernate session
	 */
	private void applyFinanceTradeFilters(StringBuffer str, String fintrade, String hostId,
			String entityId, Session session) {
		if (!fintrade.equals("B")) {
			String val = null;

			if (fintrade.equals("T")) {
				val = "T";
			} else if (fintrade.equals("F")) {
				val = "F";
			}

			ArrayList finalbookCollection = new ArrayList();

			// Query for MetaGroups
			TypedQuery<MetaGroup> metaGroupQuery = session.createQuery(
					"from MetaGroup m where m.id.hostId = :hostId and m.id.entityId = :entityId and m.financeTrade = :financeTrade",
					MetaGroup.class);
			metaGroupQuery.setParameter("hostId", hostId);
			metaGroupQuery.setParameter("entityId", entityId);
			metaGroupQuery.setParameter("financeTrade", val);
			List<MetaGroup> metaGroupCollection = metaGroupQuery.getResultList();

			for (MetaGroup mGroup : metaGroupCollection) {
				// Query for Groups
				TypedQuery<Group> groupQuery = session.createQuery(
						"from Group m where m.id.hostId = :hostId and m.id.entityId = :entityId and m.mgroupId = :mgroupId",
						Group.class);
				groupQuery.setParameter("hostId", hostId);
				groupQuery.setParameter("entityId", entityId);
				groupQuery.setParameter("mgroupId", mGroup.getId().getMgroupId());
				List<Group> groupCollection = groupQuery.getResultList();

				for (Group grp : groupCollection) {
					// Query for BookCodes
					TypedQuery<BookCode> bookQuery = session.createQuery(
							"from BookCode m where m.id.hostId = :hostId and m.id.entityId = :entityId " +
									"and (m.groupIdLevel1 = :groupId or m.groupIdLevel2 = :groupId or m.groupIdLevel3 = :groupId)",
							BookCode.class);
					bookQuery.setParameter("hostId", hostId);
					bookQuery.setParameter("entityId", entityId);
					bookQuery.setParameter("groupId", grp.getId().getGroupId());
					List<BookCode> bookCollection = bookQuery.getResultList();

					if (!bookCollection.isEmpty()) {
						finalbookCollection.addAll(bookCollection);
					}
				}
			}

			if (finalbookCollection.isEmpty()) {
				str.append(" and p.bookCode IN('')");
			} else {
				StringBuilder bookCodesBuilder = new StringBuilder(" and p.bookCode IN(");
				for (int i = 0; i < finalbookCollection.size(); i++) {
					if (i > 0) {
						bookCodesBuilder.append("','");
					} else {
						bookCodesBuilder.append("'");
					}
					bookCodesBuilder.append(((BookCode) finalbookCollection.get(i)).getId().getBookCode());
				}
				bookCodesBuilder.append("')");
				str.append(bookCodesBuilder.toString());
			}
		}
	}

	/**
	 * Applies reference and message type filters to the query
	 * 
	 * @param str       Query builder
	 * @param msgtype   Message type filter
	 * @param inputdate Input date filter
	 * @param reference Reference filter
	 */
	private void applyReferenceAndTypeFilters(StringBuffer str, String msgtype,
			String inputdate, String reference) {
		// Msg Type
		if (!msgtype.equals("")) {
			str.append(" and p.messageFormat like '" + msgtype + "%'");
		}

		// Input Date
		if (!inputdate.equals("")) {
			str.append(" and to_date(to_char(p.inputDate,'DD-MM-YYYY'),'DD-MM-YYYY') = to_date('"
					+ inputdate + "','DD-MM-YYYY')");
		}

		// Reference
		if (!reference.equals("")) {
			str.append(" and (p.reference1 like '" + reference
					+ "%' or p.reference2 like '" + reference
					+ "%' or p.reference3 like '" + reference
					+ "%' or p.reference4 like '" + reference + "%')");
		}
	}

	/**
	 * Applies sorting to the query
	 * 
	 * @param str              Query builder
	 * @param entityId         Entity identifier
	 * @param filterSortStatus Filter sort status
	 * @param format           Date format
	 * @return StringBuffer containing the final query with sorting applied
	 */
	private StringBuffer applySorting(StringBuffer str, String entityId,
			String filterSortStatus, String format) throws SwtException {
		return SwtServerSideSortFilter.constructQuery(entityId, str, filterSortStatus, format);
	}

	/**
	 * Executes the query and processes results
	 * 
	 * @param session     Hibernate session
	 * @param finalQuery  Final query to execute
	 * @param paramList   List of query parameters
	 * @param typeList    List of parameter types
	 * @param currentPage Current page number
	 * @param totalCount  Total count of records
	 * @param resultList  List to store results
	 * @return Updated total count
	 */
	private int executeQueryAndProcessResults(Session session, StringBuffer finalQuery,
			ArrayList paramList, ArrayList typeList,
			int currentPage, int totalCount, List resultList) throws SwtException {
		// Create count query
		StringBuffer countQuery = new StringBuffer(" select count(p.id.movementId) ");
		countQuery.append(finalQuery.toString());

		// Set parameters for count query
		TypedQuery<Long> queryCount = session.createQuery(countQuery.toString(), Long.class);
		for (int i = 0; i < paramList.size(); i++) {
			queryCount.setParameter("param" + i, paramList.get(i));
		}

		// Get total count if not provided
		if (totalCount == 0) {
			List<Long> countList = queryCount.getResultList();
			if (!countList.isEmpty()) {
				totalCount = countList.get(0).intValue();
			}
		}

		// Create and execute main query
		TypedQuery query = session.createQuery(finalQuery.toString());
		for (int i = 0; i < paramList.size(); i++) {
			query.setParameter("param" + i, paramList.get(i));
		}

		// Set pagination
		int pageSize = SwtUtil.getPageSizeFromProperty(SwtConstants.MOVEMENT_SUMMARY_SCREEN_PAGE_SIZE);
		boolean isNext = SwtPager.next(query, currentPage, totalCount, pageSize, resultList);

		return totalCount;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see org.swallow.work.dao.MovementSearchDAO#getArchiveList()
	 */
	public List getArchiveList() throws SwtException {
		log.debug("Entering getArchiveList method");
		
		String hostId = CacheManager.getInstance().getHostId();
		
		try (Session session = sessionFactory.openSession()) {
			TypedQuery<Archive> query = session.createQuery(
					"from Archive a where a.id.hostId = :hostId and a.moduleId = 'Predict'", 
					Archive.class);
			query.setParameter("hostId", hostId);
			List<Archive> archiveList = query.getResultList();
			return archiveList;
		}
	}

	/**
 * Gets the list of position levels for a host
 * 
 * @param hostId Host identifier
 * @return ArrayList of LabelValueBean objects containing position levels
 * @throws SwtException If an error occurs during processing
 */
public ArrayList<LabelValueBean> getPositionLevelList(String hostId) throws SwtException {
    log.debug(this.getClass().getName() + " - [getPositionLevelList] - " + "Entry");
    
    ArrayList<LabelValueBean> posLevels = new ArrayList<LabelValueBean>();
    String getPosLevel = "select distinct(POSITION_LEVEL) from P_POSITION_LEVEL_NAME where HOST_ID=? ORDER BY POSITION_LEVEL ASC";
    
    try (Connection conn = ConnectionManager.getInstance().databaseCon();
         PreparedStatement stmt = conn.prepareStatement(getPosLevel)) {
        
        stmt.setString(1, hostId);
        
        try (ResultSet rs = stmt.executeQuery()) {
            /* Loop to iterate result set */
            while (rs.next()) {
                posLevels.add(new LabelValueBean(rs.getString(1), rs.getString(1)));
            }
        }
        
        log.debug(this.getClass().getName() + " - [getPositionLevelList] - " + "Exit");
    } catch (Exception exp) {
        log.error(this.getClass().getName() 
                + " - Exception Catched in [getPositionLevelList] method : - "
                + exp.getMessage());
    }
    
    return posLevels;
}
}