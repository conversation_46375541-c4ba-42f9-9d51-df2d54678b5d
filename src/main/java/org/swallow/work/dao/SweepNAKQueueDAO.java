package org.swallow.work.dao;

import org.swallow.exception.SwtException;

import java.util.Collection;
import java.util.Date;


public interface SweepNAKQueueDAO {
    String getNAKQueueDetails(String entityId, String currCode, Date valueDate)
        throws SwtException;

    /**
     * This method is used to fetch the NAK (Exceptions)sweep messages.
     * @param entityId
     * @param currCode
     * @param valueDate
     * @return Collection of NAK sweep messages.
     * @throws SwtException
     */
    public Collection getNAKMessages(String entityId, String currCode,
        Date valueDate) throws SwtException;

    /**
     * This method is used to fetch the Overdue ACK sweep messages.
     * @param entityId
     * @param currCode
     * @param valueDate
     * @return Collection of Overdue ACK sweep messages
     * @throws SwtException
     */
    public Collection getOverdueACKMessages(String entityId, String currCode,
        Date valueDate) throws SwtException;

    /**
    * This method used to execute the DB procedure for fetching the sweep NAK details.
    * @param hostId
    * @param entityId
    * @param currencyGrpId
    * @param roleId
    * @param valueDate
    * @return collection of
    * @throws SwtException sweepNAKQueue objects
    */
    public Collection getNAKQueueDetailsFromProc(String hostId,
        String entityId, String currencyGrpId, String roleId, Date valueDate)
        throws SwtException;
}
