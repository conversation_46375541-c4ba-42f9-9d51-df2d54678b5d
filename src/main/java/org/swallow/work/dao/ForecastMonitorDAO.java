/*
 * @(#)ForecastMonitorDAO.java 1.0 13/04/11
 *
 * Copyright (c) 2006-2012 SwallowTech, Inc.
 * 14 Lion Yard ,Tremadoc Road,  London  UK
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * SwallowTech Inc. ("Confidential Information"). You shall not
 * disclose such Confidential Information and shall use it only in
 * accordance with the terms of the license agreement you entered into
 * with SwallowTech.
 */
package org.swallow.work.dao;

import java.util.Collection;

import org.swallow.control.model.EntityAccess;
import org.swallow.exception.SwtException;
import org.swallow.maintenance.model.CurrencyGroup;
import org.swallow.maintenance.model.CurrencyMaster;
import org.swallow.maintenance.model.ForecastMonitorTemplate;
import org.swallow.util.OpTimer;
import org.swallow.util.SystemFormats;
import org.swallow.work.model.AssumptionData;
import org.swallow.work.model.ScenarioData;
import org.swallow.work.model.UserBuckets;
import org.swallow.work.model.UserTemplate;
import org.swallow.work.web.form.ForecastRecord;

/**
 * ForecastMonitorDAO.java
 * 
 * This interface has methods that are used for accessing the persistent storage
 * such as database <br>
 * which helps client to create, retrieve and persists data to the Persistent
 * Object.<br>
 * 
 * <AUTHOR> D
 * @date Feb 13, 2011
 */

public interface ForecastMonitorDAO {

	/**
	 * This method is used to save the user template details.<br>
	 * User template details contains information like:<br>
	 * 
	 * @param UserTemplate
	 *            userTemplate
	 * @param boolean
	 *            isUpdate
	 * @return
	 * @throws SwtException
	 */
	public void saveUserTemplate(UserTemplate userTemplate, boolean isUpdate)
			throws SwtException;

	/**
	 * This method is used to persist the user bucket details in the database
	 * 
	 * @param boolean
	 *            isUpdate
	 * @param UserBuckets
	 *            userBucket
	 * @throws SwtException
	 */
	public void saveUserBucket(UserBuckets userBucket, boolean isUpdate)
			throws SwtException;

	/**
	 * This method is used to save the scenario data details.<br>
	 * 
	 * @param ScenarioData
	 *            scenarioData
	 * @return
	 * @throws SwtException
	 */
	public void saveScenarioData(ScenarioData scenarioData) throws SwtException;

	/**
	 * This method helps getting all the balances with respect to the templates.<br>
	 * This method executes a stored procedure that will retrieve all the
	 * balances.<br>
	 * 
	 * @param OpTimer
	 *            opTimer
	 * @param String
	 *            entityId
	 * @param String
	 *            currencyCode
	 * @param String
	 *            userId
	 * @param SystemFormats
	 *            format
	 * @param boolean
	 *            multiplierFlag
	 * @return ForecastRecord object
	 * @throws SwtException
	 */
	public ForecastRecord getAllBalancesUsingStoredProc(OpTimer opTimer,
			String entityId, String currencyCode, String userId,
			SystemFormats format, boolean multiplierFlag) throws SwtException;

	/**
	 * This is used to get the currency codes for the entities based on the
	 * access set by the administrator.<br>
	 * If particular entity has no access, currency code for that entity will
	 * not be shown to the client.<br>
	 * 
	 * @param String
	 *            hostId
	 * @param String
	 *            roleId
	 * @return Collection<CurrencyGroup>
	 * @throws SwtException
	 */
	public Collection<CurrencyGroup> getCurrencyGroupAccessDetails(
			String hostId, String roleId) throws SwtException;

	/**
	 * This method is used to get the entity list details for the given user<br>
	 * 
	 * @param hostId
	 * @param roleId
	 * @return Collection<EntityAccess>
	 * @throws SwtException
	 */
	public Collection<EntityAccess> getUserEntityList(String hostId,
			String roleId) throws SwtException;

	/**
	 * This method is used to get the user templates
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<UserTemplate>
	 * @throws SwtException
	 */
	public Collection<UserTemplate> getUserTemplateList(String hostId,
			String userId) throws SwtException;

	/**
	 * This method is used to get the user buckets
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<UserBuckets>
	 * @throws SwtException
	 */
	public Collection<UserBuckets> getUserBucketList(String hostId,
			String userId) throws SwtException;

	/**
	 * This method is used to check the entity access
	 * 
	 * @param hostId
	 * @param roleId
	 * @param entityId
	 * @return boolean
	 * @throws SwtException
	 */
	public boolean checkEntityAccess(String hostId, String roleId,
			String entityId) throws SwtException;

	/**
	 * This method is used to get the forecast templates
	 * 
	 * @param hostId
	 * @param userId
	 * @return Collection<ForecastMonitorTemplate>
	 * @throws SwtException
	 */
	public Collection<ForecastMonitorTemplate> getForecastTemplate(
			String hostId, String userId) throws SwtException;

	/**
	 * This method is used to get the forecast assumption
	 * 
	 * @param AssumptionData
	 *            object
	 * @return Collection<AssumptionData>
	 * @throws SwtException
	 */
	public Collection<AssumptionData> getForecastAssumption(
			AssumptionData assumptionData) throws SwtException;

	/**
	 * This method is used to delete the forecast user template details
	 * 
	 * @param userTemplate
	 *            object
	 * @return
	 * @throws SwtException
	 */
	public void deleteForecastUserTemplate(UserTemplate userTemplate)
			throws SwtException;

	/**
	 * This method is used to delete the forecast assumption details
	 * 
	 * @param AssumptionData
	 *            object
	 * @return
	 * @throws SwtException
	 */
	public void deleteForecastAssumption(AssumptionData assumptionData)
			throws SwtException;

	/**
	 * This method is used to save the forecast assumption details
	 * 
	 * @param AssumptionData
	 *            object
	 * @return
	 * @throws SwtException
	 */
	public void saveForecastAssumption(AssumptionData assumptionData)
			throws SwtException;

	/**
	 * This method is used to delete the forecast bucket details
	 * 
	 * @param userBuckets
	 *            object
	 * @param boolean
	 *            isAll
	 * @return
	 * @throws SwtException
	 */
	public void deleteForecastBucket(UserBuckets userBuckets, boolean isAll)
			throws SwtException;

	/**
	 * This is used to get the currency codes for all entities based on the
	 * access set by the administrator.<br>
	 * If particular entity has no access, currency code for that entity will
	 * not be shown to the client.<br>
	 * 
	 * @param String
	 *            userId
	 * @return Collection<CurrencyMaster>
	 * @throws SwtException
	 */
	public Collection<CurrencyMaster> getCurrencyForAllEntity(String userId)
			throws SwtException;

	/**
	 * This method is used to get the template id
	 * 
	 * @param Scenario
	 *            object
	 * @return String
	 * @throws SwtException
	 */
	public String getTemplateId(ScenarioData scenarioData) throws SwtException;

	/**
	 * This method is used to get the Forecast Monitor template details
	 * 
	 * @param ForecastMonitorTemplate
	 *            forecastMonitorTemplate
	 * @return ForecastMonitorTemplate object
	 * @throws SwtException
	 */
	public ForecastMonitorTemplate getForecastMonitorTemplateDetails(
			ForecastMonitorTemplate forecastMonitorTemplate)
			throws SwtException;

}
